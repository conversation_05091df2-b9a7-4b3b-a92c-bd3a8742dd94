# Dynamic Image Definition System

This system automatically scans the "file for pictures" directory and defines character images and background images for use in Ren'Py visual novels.

## Supported Formats
- **GIF** (.gif)
- **JPEG** (.jpg, .jpeg) 
- **PNG** (.png)

## File Naming Conventions

### Character Images
Character images should be named using the following format:
- `charactername_expression.ext` - Character with specific expression/pose
- `charactername_pose.ext` - Character with specific pose
- `charactername_naked.ext` - Character in naked state
- `charactername.ext` - Default character image

**Examples:**
- `austin_happy.png` - Austin with happy expression
- `austin_sad.gif` - <PERSON> with sad expression
- `austin_standing.jpg` - <PERSON> in standing pose
- `austin_naked.png` - Austin naked
- `austin.png` - Austin default image

### Background Images
Background images should be named using one of these formats:
- `bg_backgroundname.ext` - Background with 'bg_' prefix
- `background_backgroundname.ext` - Background with 'background_' prefix

**Examples:**
- `bg_bedroom.jpg` - Bedroom background
- `bg_forest.png` - Forest background
- `background_kitchen.gif` - Kitchen background

## Hashtag Comment References

The system also scans all .rpy script files for hashtag comments that reference images. This allows you to document image requirements directly in your scripts.

### Supported Comment Patterns

**Regular Comments:**
```renpy
# add a file (named either "netcode.png") to the images directory
# add a file (named either "bedroom.jpg") to the images directory to show it
# replace it by adding a file named "austin_happy.png" to the images directory
```

**Comments in Docstrings:**
```renpy
"""
# add a file (named either "player_smiling.gif") to the images directory
# add a file (named either "bg_forest.png") to the images directory to show it
"""
```

**Additional Patterns:**
- `# replace it by adding a file named "filename.ext" to the images directory`
- `# to the images directory to show it`
- `# images directory to show`

The system will automatically detect these references and look for the corresponding image files in both the `file for pictures` and `game/images` directories.

## Define Character Image Comments

The system also recognizes `# define character image` comments that directly link character names to image files.

### Syntax
```renpy
# define character image CharacterName = "filename.ext"
```

### Examples
```renpy
# define character image Netcode = "netcode.jpg"
# define character image Austin = "austin_happy.png"
# define character image Player = "player_smiling.gif"
# define character image Background = "bedroom.jpg"
```

### How It Works
- The system scans all .rpy files for comment lines starting with `# define character image`
- Extracts the character name and filename from the comment
- Searches for the image file in both `file for pictures` and `game/images` directories
- Automatically creates the image definition: `image netcode` for the file `netcode.jpg`
- Works with any character name and any supported image format (gif, jpg, png)
- Uses comments to avoid Ren'Py parsing errors

### Usage in Scripts
```renpy
# After defining: # define character image Netcode = "netcode.jpg"
show netcode        # Shows the linked image
show Netcode        # Also works (case variations supported)
```

## Placeholder Replacement System

The dynamic image system automatically replaces placeholder images (gray silhouettes) with actual character images when available.

### How Placeholders Work

**When you use `show Netcode` in your script:**
- ✅ **If `netcode.jpg` exists** → Shows the actual character image
- ⚪ **If no image file exists** → Shows gray placeholder silhouette

### Placeholder Replacement Process

1. **Script Scanning**: System scans all .rpy files for `show CharacterName` commands
2. **Image Search**: Looks for matching image files in both directories:
   - `file for pictures/netcode.jpg`
   - `game/images/netcode.png`
3. **Automatic Replacement**: Replaces placeholder with actual image if found
4. **Status Report**: Shows which characters will be placeholders vs actual images

### Example Scenarios

```renpy
# In your script:
show Netcode        # Will show actual image if netcode.jpg exists
show Austin         # Will show placeholder if no austin.png found
show Player         # Will show actual image if player.gif exists
```

### Console Output Example
```
✅ 'Netcode' -> ACTUAL IMAGE: file for pictures/netcode.jpg
⚪ 'Austin' -> PLACEHOLDER (gray silhouette) - no image file found
✅ 'Player' -> ACTUAL IMAGE: game/images/player.png
```

### Supported File Patterns
The system looks for these file patterns to replace placeholders:
- Exact match: `netcode.jpg` for character `Netcode`
- With expressions: `netcode_happy.png`, `netcode_sad.gif`
- Case insensitive: `NETCODE.JPG`, `Netcode.png`

## How It Works

1. **Automatic Scanning**: The system scans multiple directories for images:
   - `file for pictures` directory and subdirectories
   - `game/images` directory and subdirectories
2. **Hashtag Comment Parsing**: Scans all .rpy script files for hashtag comments that reference images:
   ```renpy
   # add a file (named either "netcode.png") to the images directory
   # add a file (named either "bedroom.jpg") to the images directory
   ```
3. **Image Definition**: Creates Ren'Py image definitions automatically:
   - Character images: `image austin happy` for `austin_happy.png`
   - Background images: `image bg bedroom` for `bg_bedroom.jpg`
   - Hashtag references: `image netcode` for files mentioned in comments
4. **Character Creation**: Automatically creates Character objects:
   - `define char_austin = Character("Austin", color="#unique_color")`
5. **Runtime Access**: Images can be used in scripts immediately:
   ```renpy
   scene bg bedroom
   show austin happy
   char_austin "Hello there!"
   ```

## Usage in Scripts

### Showing Characters
```renpy
# Show character with default image
show austin

# Show character with specific expression
show austin happy
show austin sad
show austin naked

# Show character with pose
show austin standing
```

### Setting Backgrounds
```renpy
# Set background scene
scene bg bedroom
scene bg forest

# Or without 'bg' prefix
scene bedroom
scene forest
```

### Using Character Objects
```renpy
# Characters are automatically defined as char_charactername
char_austin "This is Austin speaking!"
char_netcode "This is Netcode speaking!"
```

## Manual Refresh

If you add new images while the game is running, you can refresh the definitions:

```renpy
# Call this label to refresh all image definitions
call refresh_images
```

## Testing

Use the test label to see what images were found:

```renpy
# Test the dynamic image system
call test_dynamic_images
```

## Directory Structure Example

```
file for pictures/
├── austin_happy.png
├── austin_sad.gif
├── austin_standing.jpg
├── austin_naked.png
├── austin.png
├── netcode_blushing.png
├── netcode.jpg
├── bg_bedroom.jpg
├── bg_forest.png
├── background_kitchen.gif
└── characters/
    ├── player_smiling.png
    └── player.png
```

## Error Handling

- If the "file for pictures" directory doesn't exist, the system will create empty definitions
- Invalid image files are skipped with error messages in the console
- Character names with special characters are automatically cleaned
- Duplicate definitions are handled gracefully

## Console Output

The system provides detailed console output showing:
- Found character images and their expressions
- Found background images
- Successfully defined image tags
- Any errors encountered during processing

Check the Ren'Py console or log files to see the system's activity.
