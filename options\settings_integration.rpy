## Settings Integration
## Ensures gaming settings work with existing game systems without breaking functionality

init -1 python:
    # Settings integration state
    settings_integration = {
        'initialized': False,
        'backup_preferences': {},
        'original_screens': {},
        'integration_active': True
    }
    
    def backup_original_preferences():
        """Backup original preference values"""
        try:
            settings_integration['backup_preferences'] = {
                'fullscreen': _preferences.fullscreen,
                'music_volume': _preferences.get_volume('music'),
                'sound_volume': _preferences.get_volume('sound'),
                'voice_volume': _preferences.get_volume('voice') if config.has_voice else 1.0,
                'text_speed': _preferences.text_cps,
                'auto_forward_time': _preferences.afm_time
            }
            renpy.log("Original preferences backed up")
        except Exception as e:
            renpy.log("Error backing up preferences: {}".format(str(e)))
    
    def restore_original_preferences():
        """Restore original preference values if needed"""
        try:
            if settings_integration['backup_preferences']:
                backup = settings_integration['backup_preferences']
                _preferences.fullscreen = backup.get('fullscreen', False)
                _preferences.set_volume('music', backup.get('music_volume', 1.0))
                _preferences.set_volume('sound', backup.get('sound_volume', 1.0))
                if config.has_voice:
                    _preferences.set_volume('voice', backup.get('voice_volume', 1.0))
                _preferences.text_cps = backup.get('text_speed', 0)
                _preferences.afm_time = backup.get('auto_forward_time', 15)
                renpy.log("Original preferences restored")
        except Exception as e:
            renpy.log("Error restoring preferences: {}".format(str(e)))
    
    def safe_apply_graphics_settings():
        """Safely apply graphics settings without breaking the game"""
        try:
            # Only apply safe settings that won't break the game
            if gaming_settings.get('window_mode') == 'fullscreen':
                _preferences.fullscreen = True
            elif gaming_settings.get('window_mode') == 'windowed':
                _preferences.fullscreen = False
            
            # Apply V-Sync if supported
            if hasattr(config, 'gl_vsync'):
                config.gl_vsync = gaming_settings.get('v_sync', True)
            
            # Apply FPS limit safely
            fps_limit = gaming_settings.get('fps_limit', 60)
            if fps_limit > 0 and fps_limit <= 144:
                if hasattr(config, 'framerate'):
                    config.framerate = fps_limit
            
            renpy.log("Graphics settings applied safely")
            
        except Exception as e:
            renpy.log("Error applying graphics settings: {}".format(str(e)))
    
    def safe_apply_audio_settings():
        """Safely apply audio settings"""
        try:
            # Apply volume settings with bounds checking
            music_vol = max(0.0, min(1.0, gaming_settings.get('music_volume', 100) / 100.0))
            sound_vol = max(0.0, min(1.0, gaming_settings.get('sfx_volume', 100) / 100.0))
            voice_vol = max(0.0, min(1.0, gaming_settings.get('voice_volume', 100) / 100.0))
            
            _preferences.set_volume('music', music_vol)
            _preferences.set_volume('sound', sound_vol)
            if config.has_voice:
                _preferences.set_volume('voice', voice_vol)
            
            renpy.log("Audio settings applied safely")
            
        except Exception as e:
            renpy.log("Error applying audio settings: {}".format(str(e)))
    
    def initialize_settings_integration():
        """Initialize the settings integration system"""
        if settings_integration['initialized']:
            return
        
        try:
            # Backup original preferences
            backup_original_preferences()
            
            # Load gaming settings
            load_gaming_settings()
            
            # Detect hardware
            detect_hardware()
            
            # Apply settings safely
            safe_apply_graphics_settings()
            safe_apply_audio_settings()
            
            settings_integration['initialized'] = True
            renpy.log("Settings integration initialized successfully")
            
        except Exception as e:
            renpy.log("Settings integration initialization failed: {}".format(str(e)))
    
    # Initialize on startup
    initialize_settings_integration()

# Enhanced preferences screen that preserves original functionality
# Disabled to use original preferences screen
# screen preferences():
#     tag menu
#     if settings_integration.get('integration_active', True):
#         use enhanced_preferences
#     else:
#         use original_preferences_fallback

# Original preferences fallback
screen original_preferences_fallback():
    
    tag menu
    
    use game_menu(_("Preferences"), scroll="viewport"):
        
        vbox:
            spacing 20
            
            text "⚙️ Preferences" size 20 color "#ffffff"
            
            hbox:
                box_wrap True
                spacing 20
                
                if renpy.variant("pc") or renpy.variant("web"):
                    vbox:
                        style_prefix "radio"
                        label _("Display")
                        textbutton _("Window") action Preference("display", "window")
                        textbutton _("Fullscreen") action Preference("display", "fullscreen")
                
                vbox:
                    style_prefix "check"
                    label _("Skip")
                    textbutton _("Unseen Text") action Preference("skip", "toggle")
                    textbutton _("After Choices") action Preference("after choices", "toggle")
                    textbutton _("Transitions") action InvertSelected(Preference("transitions", "toggle"))
            
            null height 20
            
            hbox:
                style_prefix "slider"
                box_wrap True
                spacing 20
                
                vbox:
                    label _("Text Speed")
                    bar value Preference("text speed")
                    
                    label _("Auto-Forward Time")
                    bar value Preference("auto-forward time")
                
                vbox:
                    if config.has_music:
                        label _("Music Volume")
                        hbox:
                            bar value Preference("music volume")
                    
                    if config.has_sound:
                        label _("Sound Volume")
                        hbox:
                            bar value Preference("sound volume")
                            if config.sample_sound:
                                textbutton _("Test") action Play("sound", config.sample_sound)
                    
                    if config.has_voice:
                        label _("Voice Volume")
                        hbox:
                            bar value Preference("voice volume")
                            if config.sample_voice:
                                textbutton _("Test") action Play("voice", config.sample_voice)
                    
                    if config.has_music or config.has_sound or config.has_voice:
                        null height gui.pref_spacing
                        textbutton _("Mute All"):
                            action Preference("all mute", "toggle")
                            style "mute_all_button"

# Settings validation and error handling
init python:
    
    def validate_gaming_settings():
        """Validate gaming settings to prevent errors"""
        try:
            # Validate resolution
            if gaming_settings.get('resolution', '1920x1080') not in ['1920x1080', '2560x1440', '3840x2160', 'auto']:
                gaming_settings['resolution'] = '1920x1080'
            
            # Validate quality settings
            quality_options = ['low', 'medium', 'high', 'ultra']
            if gaming_settings.get('texture_quality', 'high') not in quality_options:
                gaming_settings['texture_quality'] = 'high'
            if gaming_settings.get('shadow_quality', 'high') not in quality_options:
                gaming_settings['shadow_quality'] = 'high'
            
            # Validate volume settings
            for vol_key in ['master_volume', 'music_volume', 'sfx_volume', 'voice_volume']:
                vol_val = gaming_settings.get(vol_key, 100)
                if not isinstance(vol_val, (int, float)) or vol_val < 0 or vol_val > 100:
                    gaming_settings[vol_key] = 100
            
            # Validate controller type
            valid_controllers = ['keyboard', 'xbox', 'playstation', 'nintendo', 'maple']
            if gaming_settings.get('controller_type', 'keyboard') not in valid_controllers:
                gaming_settings['controller_type'] = 'keyboard'
            
            # Validate FPS limit
            fps_limit = gaming_settings.get('fps_limit', 60)
            if not isinstance(fps_limit, int) or fps_limit < 0 or fps_limit > 144:
                gaming_settings['fps_limit'] = 60
            
            # Validate FOV
            fov = gaming_settings.get('field_of_view', 90)
            if not isinstance(fov, int) or fov < 60 or fov > 120:
                gaming_settings['field_of_view'] = 90
            
            renpy.log("Gaming settings validated successfully")
            return True
            
        except Exception as e:
            renpy.log("Gaming settings validation failed: {}".format(str(e)))
            return False
    
    def emergency_reset_settings():
        """Emergency reset if settings cause issues"""
        try:
            global gaming_settings
            gaming_settings = {
                'resolution': '1920x1080',
                'texture_quality': 'medium',
                'shadow_quality': 'medium',
                'anti_aliasing': False,
                'v_sync': True,
                'field_of_view': 90,
                'fps_limit': 60,
                'window_mode': 'windowed',
                'colorblind_mode': False,
                'mouse_sensitivity': 50,
                'mouse_acceleration': False,
                'controller_type': 'keyboard',
                'controller_vibration': True,
                'master_volume': 100,
                'sfx_volume': 100,
                'voice_volume': 100,
                'music_volume': 100,
                'subtitles_enabled': True,
                'audio_quality': 'medium'
            }
            
            # Restore original preferences
            restore_original_preferences()
            
            # Save safe settings
            save_gaming_settings()
            
            renpy.log("Emergency settings reset completed")
            renpy.notify("Settings reset to safe defaults")
            
        except Exception as e:
            renpy.log("Emergency reset failed: {}".format(str(e)))
    
    def toggle_settings_integration():
        """Toggle settings integration on/off"""
        settings_integration['integration_active'] = not settings_integration['integration_active']
        if settings_integration['integration_active']:
            renpy.notify("Enhanced settings enabled")
        else:
            renpy.notify("Enhanced settings disabled - using basic preferences")
        renpy.restart_interaction()

# Settings troubleshooting screen
screen settings_troubleshooting():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 400
        
        vbox:
            spacing 20
            
            text "🔧 Settings Troubleshooting" size 20 xalign 0.5
            
            vbox:
                spacing 15
                
                text "If you're experiencing issues with the game:" size 14
                
                textbutton "Reset to Safe Defaults" action Function(emergency_reset_settings) text_size 14
                textbutton "Validate Current Settings" action Function(validate_gaming_settings) text_size 14
                textbutton "Toggle Enhanced Settings" action Function(toggle_settings_integration) text_size 14
                textbutton "Restore Original Preferences" action Function(restore_original_preferences) text_size 14
            
            null height 20
            
            vbox:
                spacing 10
                
                text "Current Status:" size 14 color "#ffaa00"
                text "Integration: {}".format("Active" if settings_integration.get('integration_active', True) else "Disabled") size 12
                text "Initialized: {}".format("Yes" if settings_integration.get('initialized', False) else "No") size 12
                text "Controller: {}".format(gaming_settings.get('controller_type', 'keyboard').title()) size 12
            
            textbutton "Close" action Return() xalign 0.5

# Add troubleshooting access to enhanced preferences
screen enhanced_preferences():
    
    tag menu
    
    use game_menu(_("Gaming Settings"), scroll="viewport"):
        
        vbox:
            spacing 20
            
            # Settings tabs
            hbox:
                spacing 10
                xalign 0.5
                
                textbutton "Graphics" action SetScreenVariable("settings_tab", "graphics") text_size 16
                textbutton "Audio" action SetScreenVariable("settings_tab", "audio") text_size 16
                textbutton "Controls" action SetScreenVariable("settings_tab", "controls") text_size 16
                textbutton "Hardware" action SetScreenVariable("settings_tab", "hardware") text_size 16
                textbutton "Basic" action SetScreenVariable("settings_tab", "basic") text_size 16
                textbutton "Help" action SetScreenVariable("settings_tab", "help") text_size 16
            
            null height 20
            
            # Settings panels
            if settings_tab == "graphics":
                use graphics_settings_panel
            elif settings_tab == "audio":
                use audio_settings_panel
            elif settings_tab == "controls":
                use controls_settings_panel
            elif settings_tab == "hardware":
                use hardware_info_panel
            elif settings_tab == "help":
                use settings_troubleshooting
            else:
                use basic_settings_panel

# Auto-validation on settings load
label after_load:
    $ validate_gaming_settings()
    $ safe_apply_graphics_settings()
    $ safe_apply_audio_settings()
    return

# Safe shutdown
init python:
    def safe_settings_shutdown():
        """Safely save settings on game exit"""
        try:
            validate_gaming_settings()
            save_gaming_settings()
        except Exception as e:
            renpy.log("Settings shutdown error: {}".format(str(e)))
    
    config.quit_callbacks.append(safe_settings_shutdown)
