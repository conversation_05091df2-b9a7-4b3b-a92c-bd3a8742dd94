## NSFW Detection UI and Age Verification Screens

# Age verification screen
screen age_verification():
    
    modal True
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            # Warning header
            frame:
                background "#8B0000"
                padding (30, 20)
                
                vbox:
                    spacing 10
                    xalign 0.5
                    
                    text "🔞 ADULT CONTENT WARNING" size 36 color "#FFFFFF" bold True xalign 0.5
                    text "Age Verification Required" size 24 color "#FFFFFF" xalign 0.5
            
            # Content warning
            frame:
                background "#2E2E2E"
                padding (30, 25)
                xsize 800
                
                vbox:
                    spacing 15
                    
                    text "This visual novel contains adult content including:" size 18 color "#FFFFFF"
                    
                    vbox:
                        spacing 8
                        for warning in nsfw_state.get('content_warnings', []):
                            text "• {}".format(warning) size 16 color "#FF9800"
                    
                    null height 10
                    
                    text "You must be 18 years or older to continue." size 16 color "#CCCCCC"
                    text "By proceeding, you confirm that you meet the age requirement." size 14 color "#CCCCCC"
            
            # Age input
            frame:
                background "#1A1A1A"
                padding (20, 15)
                
                vbox:
                    spacing 15
                    xalign 0.5
                    
                    text "Please enter your age:" size 20 color "#FFFFFF"
                    
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        # Age input buttons
                        for age in range(16, 26):
                            textbutton str(age) action [
                                Function(handle_age_verification, age),
                                Return()
                            ] text_size 18
                    
                    null height 10
                    
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        for age in range(26, 36):
                            textbutton str(age) action [
                                Function(handle_age_verification, age),
                                Return()
                            ] text_size 18
                    
                    null height 10
                    
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        textbutton "36+" action [
                            Function(handle_age_verification, 36),
                            Return()
                        ] text_size 18
                        
                        textbutton "Under 18" action [
                            Function(handle_underage),
                            Return()
                        ] text_size 18 text_color "#FF0000"
            
            # Legal disclaimer
            text "This age verification is required by law in many jurisdictions." size 12 color "#888888" text_align 0.5

# Adult mode activation screen
screen adult_mode_activated():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        background "#1A1A1A"
        
        vbox:
            spacing 25
            
            text "🔞 ADULT MODE ACTIVATED" size 30 color "#FF6B6B" xalign 0.5 bold True
            
            frame:
                background "#2E2E2E"
                padding (20, 15)
                
                vbox:
                    spacing 10
                    
                    text "Adult content has been detected in this visual novel." size 16 color "#FFFFFF"
                    text "Adult mode has been automatically enabled." size 16 color "#FFFFFF"
                    
                    null height 10
                    
                    text "Reason: {}".format(persistent.adult_mode_reason or "NSFW content detected") size 14 color "#FF9800"
                    
                    null height 10
                    
                    text "Features now active:" size 16 color "#FFFFFF"
                    text "• Age verification required" size 14 color "#CCCCCC"
                    text "• Content warnings displayed" size 14 color "#CCCCCC"
                    text "• Parental controls enabled" size 14 color "#CCCCCC"
                    text "• Enhanced content filtering" size 14 color "#CCCCCC"
            
            if not nsfw_state.get('age_verified', False):
                text "Age verification is required to continue." size 16 color "#FF9800" xalign 0.5
                
                hbox:
                    spacing 20
                    xalign 0.5
                    
                    textbutton "Verify Age" action Call("age_verification_process")
                    textbutton "Exit Game" action Quit()
            else:
                text "Age verification: ✓ Verified" size 16 color "#4CAF50" xalign 0.5
                textbutton "Continue" action Return() xalign 0.5

# NSFW content warning screen
screen nsfw_content_warning(content_type="general"):
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#8B0000"
        
        vbox:
            spacing 20
            
            text "⚠️ CONTENT WARNING" size 28 color "#FFFFFF" xalign 0.5 bold True
            
            frame:
                background "#000000AA"
                padding (20, 15)
                
                vbox:
                    spacing 10
                    
                    if content_type == "explicit":
                        text "The following content contains explicit adult material." size 16 color="#FFFFFF"
                    elif content_type == "suggestive":
                        text "The following content contains suggestive themes." size 16 color="#FFFFFF"
                    else:
                        text "The following content may not be suitable for all audiences." size 16 color="#FFFFFF"
                    
                    text "Viewer discretion is advised." size 14 color="#CCCCCC"
            
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "Continue" action Return(True) text_color "#FFFFFF"
                textbutton "Skip Content" action Return(False) text_color "#FF9800"

# NSFW detection status screen (developer mode)
screen nsfw_status():
    
    if config.developer:
        frame:
            xpos 10
            ypos 320
            xsize 400
            ysize 200
            background "#000000AA"
            
            vbox:
                spacing 5
                
                text "NSFW Detection Status" size 16 color "#FF6B6B"
                
                text "Enabled: {}".format(NSFW_CONFIG.get('enabled', False)) size 12
                text "Adult Mode: {}".format(nsfw_state.get('adult_mode_active', False)) size 12
                text "Age Verified: {}".format(nsfw_state.get('age_verified', False)) size 12
                text "Detections: {}".format(nsfw_state.get('detection_count', 0)) size 12
                text "Content Warnings: {}".format(len(nsfw_state.get('content_warnings', []))) size 12
                
                if nsfw_state.get('content_warnings'):
                    null height 5
                    text "Recent Warnings:" size 12 color "#FF9800"
                    for warning in nsfw_state.get('content_warnings', [])[-3:]:
                        text "• {}".format(warning[:30] + "..." if len(warning) > 30 else warning) size 10

# NSFW configuration screen (developer mode)
screen nsfw_config():
    
    if config.developer:
        modal True
        
        frame:
            xalign 0.5
            yalign 0.5
            xsize 700
            ysize 600
            
            vbox:
                spacing 20
                
                text "NSFW Detection Configuration" size 24 xalign 0.5
                
                vbox:
                    spacing 15
                    
                    hbox:
                        text "NSFW Detection Enabled:" size 16
                        textbutton "{}".format(NSFW_CONFIG['enabled']) action ToggleDict(NSFW_CONFIG, 'enabled')
                    
                    hbox:
                        text "Auto Adult Mode:" size 16
                        textbutton "{}".format(NSFW_CONFIG['auto_adult_mode']) action ToggleDict(NSFW_CONFIG, 'auto_adult_mode')
                    
                    hbox:
                        text "Strict Detection:" size 16
                        textbutton "{}".format(NSFW_CONFIG['strict_detection']) action ToggleDict(NSFW_CONFIG, 'strict_detection')
                    
                    hbox:
                        text "Scan Images:" size 16
                        textbutton "{}".format(NSFW_CONFIG['scan_images']) action ToggleDict(NSFW_CONFIG, 'scan_images')
                    
                    hbox:
                        text "Scan Text:" size 16
                        textbutton "{}".format(NSFW_CONFIG['scan_text']) action ToggleDict(NSFW_CONFIG, 'scan_text')
                    
                    hbox:
                        text "Scan Audio:" size 16
                        textbutton "{}".format(NSFW_CONFIG['scan_audio']) action ToggleDict(NSFW_CONFIG, 'scan_audio')
                    
                    hbox:
                        text "Require Age Verification:" size 16
                        textbutton "{}".format(NSFW_CONFIG['require_age_verification']) action ToggleDict(NSFW_CONFIG, 'require_age_verification')
                
                null height 20
                
                hbox:
                    spacing 20
                    xalign 0.5
                    
                    textbutton "Run NSFW Scan" action Function(run_manual_nsfw_scan)
                    textbutton "Reset Adult Mode" action Function(reset_adult_mode)
                    textbutton "Force Adult Mode" action Function(force_adult_mode)
                    textbutton "Close" action Return()

# Functions for handling age verification and NSFW detection
init python:
    
    def handle_age_verification(age):
        """Handle age verification input"""
        if nsfw_detector.verify_age(age):
            renpy.notify("Age verification successful")
            if nsfw_state.get('adult_mode_active', False):
                renpy.call_screen("adult_mode_activated")
        else:
            renpy.call_screen("underage_access_denied")
    
    def handle_underage():
        """Handle underage user"""
        renpy.call_screen("underage_access_denied")
    
    def run_manual_nsfw_scan():
        """Run manual NSFW content scan"""
        renpy.notify("Running NSFW content scan...")
        scan_results = nsfw_detector.perform_full_scan()
        
        if scan_results['nsfw_detected']:
            nsfw_detector.handle_nsfw_detection(scan_results)
            renpy.notify("NSFW content detected - Adult mode activated")
        else:
            renpy.notify("No NSFW content detected")
    
    def reset_adult_mode():
        """Reset adult mode (developer only)"""
        if config.developer:
            nsfw_state['adult_mode_active'] = False
            nsfw_state['age_verified'] = False
            nsfw_state['nsfw_content_detected'] = False
            nsfw_state['detection_count'] = 0
            nsfw_state['content_warnings'] = []
            
            persistent.adult_mode_enabled = False
            persistent.age_verified = False
            
            renpy.notify("Adult mode reset")
    
    def force_adult_mode():
        """Force enable adult mode (developer only)"""
        if config.developer:
            nsfw_detector.enable_adult_mode("Developer override")
            renpy.notify("Adult mode force enabled")

# Underage access denied screen
screen underage_access_denied():
    
    modal True
    
    frame:
        xfill True
        yfill True
        background "#8B0000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            text "🚫 ACCESS DENIED" size 40 color "#FFFFFF" bold True
            
            frame:
                background "#000000AA"
                padding (30, 20)
                
                vbox:
                    spacing 15
                    xalign 0.5
                    
                    text "You must be 18 years or older to access this content." size 20 color "#FFFFFF"
                    text "This visual novel contains adult material not suitable for minors." size 16 color="#CCCCCC"
                    
                    null height 20
                    
                    text "Please return when you meet the age requirement." size 16 color="#FFFFFF"
            
            textbutton "Exit Game" action Quit() text_size 20 xalign 0.5

# Labels for NSFW system integration
label age_verification_process:
    call screen age_verification
    return

label nsfw_content_detected:
    if not nsfw_state.get('age_verified', False):
        call age_verification_process
    
    call screen adult_mode_activated
    return

label show_content_warning(content_type="general"):
    $ proceed = renpy.call_screen("nsfw_content_warning", content_type=content_type)
    return proceed
