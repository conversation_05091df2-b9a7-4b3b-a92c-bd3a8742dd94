## CPU Integrated Graphics Detection and Configuration System
## Automatically detects Intel or AMD integrated graphics and configures Ren'Py engine
## Switches from dedicated GPU to integrated graphics when needed

init python:
    import os
    import sys
    import subprocess
    import re
    import platform
    
    class CPUIntegratedGraphicsDetector:
        """
        Comprehensive CPU integrated graphics detection system
        Supports Intel and AMD processors with detailed processor-specific setup
        """
        
        def __init__(self):
            self.detected_cpu = None
            self.detected_igpu = None
            self.cpu_info = {}
            self.igpu_capabilities = {}
            self.recommended_settings = {}
            
        def detect_cpu_and_igpu(self):
            """
            Detect CPU model and integrated graphics capabilities
            """
            print("=== CPU INTEGRATED GRAPHICS DETECTION ===")
            
            try:
                # Get CPU information
                self.cpu_info = self._get_cpu_info()
                
                # Detect CPU vendor and model
                if self._is_intel_cpu():
                    self.detected_cpu = "Intel"
                    self._detect_intel_igpu()
                elif self._is_amd_cpu():
                    self.detected_cpu = "AMD"
                    self._detect_amd_igpu()
                else:
                    print("Unknown CPU vendor detected")
                    return False
                
                # Configure Ren'Py settings based on detection
                self._configure_renpy_for_igpu()
                
                return True
                
            except Exception as e:
                print(f"Error during CPU/iGPU detection: {e}")
                return False
        
        def _get_cpu_info(self):
            """
            Get detailed CPU information from system
            """
            cpu_info = {}
            
            try:
                if platform.system() == "Windows":
                    # Use wmic to get CPU information
                    result = subprocess.run(['wmic', 'cpu', 'get', 'name,manufacturer,family,model,stepping'], 
                                          capture_output=True, text=True, timeout=10)
                    cpu_info['raw_output'] = result.stdout
                    
                    # Parse CPU name
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            cpu_info['name'] = line.strip()
                            break
                
                elif platform.system() == "Linux":
                    # Use /proc/cpuinfo
                    with open('/proc/cpuinfo', 'r') as f:
                        content = f.read()
                        cpu_info['raw_output'] = content
                        
                        # Extract model name
                        for line in content.split('\n'):
                            if 'model name' in line:
                                cpu_info['name'] = line.split(':')[1].strip()
                                break
                
                print(f"Detected CPU: {cpu_info.get('name', 'Unknown')}")
                return cpu_info
                
            except Exception as e:
                print(f"Error getting CPU info: {e}")
                return {}
        
        def _is_intel_cpu(self):
            """Check if CPU is Intel"""
            cpu_name = self.cpu_info.get('name', '').lower()
            return 'intel' in cpu_name
        
        def _is_amd_cpu(self):
            """Check if CPU is AMD"""
            cpu_name = self.cpu_info.get('name', '').lower()
            return 'amd' in cpu_name
        
        def _detect_intel_igpu(self):
            """
            Detect Intel integrated graphics and processor details
            """
            print("Detecting Intel integrated graphics...")
            
            cpu_name = self.cpu_info.get('name', '').lower()
            
            # Intel processor families and their integrated graphics
            intel_processors = {
                # 13th Gen Intel (Raptor Lake)
                'i3-13': {'igpu': 'Intel UHD Graphics 730', 'generation': '13th Gen', 'architecture': 'Raptor Lake'},
                'i5-13': {'igpu': 'Intel Iris Xe Graphics', 'generation': '13th Gen', 'architecture': 'Raptor Lake'},
                'i7-13': {'igpu': 'Intel Iris Xe Graphics', 'generation': '13th Gen', 'architecture': 'Raptor Lake'},
                'i9-13': {'igpu': 'Intel UHD Graphics 770', 'generation': '13th Gen', 'architecture': 'Raptor Lake'},
                
                # 12th Gen Intel (Alder Lake)
                'i3-12': {'igpu': 'Intel UHD Graphics 730', 'generation': '12th Gen', 'architecture': 'Alder Lake'},
                'i5-12': {'igpu': 'Intel Iris Xe Graphics', 'generation': '12th Gen', 'architecture': 'Alder Lake'},
                'i7-12': {'igpu': 'Intel Iris Xe Graphics', 'generation': '12th Gen', 'architecture': 'Alder Lake'},
                'i9-12': {'igpu': 'Intel UHD Graphics 770', 'generation': '12th Gen', 'architecture': 'Alder Lake'},
                
                # 11th Gen Intel (Tiger Lake)
                'i3-11': {'igpu': 'Intel UHD Graphics', 'generation': '11th Gen', 'architecture': 'Tiger Lake'},
                'i5-11': {'igpu': 'Intel Iris Xe Graphics', 'generation': '11th Gen', 'architecture': 'Tiger Lake'},
                'i7-11': {'igpu': 'Intel Iris Xe Graphics', 'generation': '11th Gen', 'architecture': 'Tiger Lake'},
                
                # 10th Gen Intel (Ice Lake/Comet Lake)
                'i3-10': {'igpu': 'Intel UHD Graphics', 'generation': '10th Gen', 'architecture': 'Ice Lake/Comet Lake'},
                'i5-10': {'igpu': 'Intel Iris Plus Graphics', 'generation': '10th Gen', 'architecture': 'Ice Lake/Comet Lake'},
                'i7-10': {'igpu': 'Intel Iris Plus Graphics', 'generation': '10th Gen', 'architecture': 'Ice Lake/Comet Lake'},
                
                # 9th Gen Intel (Coffee Lake Refresh)
                'i3-9': {'igpu': 'Intel UHD Graphics 630', 'generation': '9th Gen', 'architecture': 'Coffee Lake Refresh'},
                'i5-9': {'igpu': 'Intel UHD Graphics 630', 'generation': '9th Gen', 'architecture': 'Coffee Lake Refresh'},
                'i7-9': {'igpu': 'Intel UHD Graphics 630', 'generation': '9th Gen', 'architecture': 'Coffee Lake Refresh'},
                
                # 8th Gen Intel (Coffee Lake)
                'i3-8': {'igpu': 'Intel UHD Graphics 630', 'generation': '8th Gen', 'architecture': 'Coffee Lake'},
                'i5-8': {'igpu': 'Intel UHD Graphics 630', 'generation': '8th Gen', 'architecture': 'Coffee Lake'},
                'i7-8': {'igpu': 'Intel UHD Graphics 630', 'generation': '8th Gen', 'architecture': 'Coffee Lake'},
            }
            
            # Detect specific processor
            detected_processor = None
            for processor_key, details in intel_processors.items():
                if processor_key in cpu_name:
                    detected_processor = details
                    self.detected_igpu = details['igpu']
                    break
            
            if detected_processor:
                print(f"✅ Intel Processor Detected:")
                print(f"   Generation: {detected_processor['generation']}")
                print(f"   Architecture: {detected_processor['architecture']}")
                print(f"   Integrated Graphics: {detected_processor['igpu']}")
                
                self.igpu_capabilities = self._get_intel_igpu_capabilities(detected_processor)
            else:
                print("⚠️  Specific Intel processor not recognized, using generic Intel iGPU settings")
                self.detected_igpu = "Intel Integrated Graphics"
                self.igpu_capabilities = self._get_generic_intel_capabilities()
        
        def _detect_amd_igpu(self):
            """
            Detect AMD integrated graphics and processor details
            """
            print("Detecting AMD integrated graphics...")
            
            cpu_name = self.cpu_info.get('name', '').lower()
            
            # AMD processor families and their integrated graphics
            amd_processors = {
                # AMD Ryzen 7000 Series (Zen 4 with RDNA 2)
                'ryzen 3 7': {'igpu': 'AMD Radeon Graphics (RDNA 2)', 'generation': 'Ryzen 7000', 'architecture': 'Zen 4'},
                'ryzen 5 7': {'igpu': 'AMD Radeon Graphics (RDNA 2)', 'generation': 'Ryzen 7000', 'architecture': 'Zen 4'},
                'ryzen 7 7': {'igpu': 'AMD Radeon Graphics (RDNA 2)', 'generation': 'Ryzen 7000', 'architecture': 'Zen 4'},
                'ryzen 9 7': {'igpu': 'AMD Radeon Graphics (RDNA 2)', 'generation': 'Ryzen 7000', 'architecture': 'Zen 4'},
                
                # AMD Ryzen 6000 Series (Zen 3+ with RDNA 2)
                'ryzen 3 6': {'igpu': 'AMD Radeon 660M', 'generation': 'Ryzen 6000', 'architecture': 'Zen 3+'},
                'ryzen 5 6': {'igpu': 'AMD Radeon 660M', 'generation': 'Ryzen 6000', 'architecture': 'Zen 3+'},
                'ryzen 7 6': {'igpu': 'AMD Radeon 680M', 'generation': 'Ryzen 6000', 'architecture': 'Zen 3+'},
                'ryzen 9 6': {'igpu': 'AMD Radeon 680M', 'generation': 'Ryzen 6000', 'architecture': 'Zen 3+'},
                
                # AMD Ryzen 5000 Series APUs (Zen 3 with Vega)
                'ryzen 3 5': {'igpu': 'AMD Radeon Vega 6', 'generation': 'Ryzen 5000', 'architecture': 'Zen 3'},
                'ryzen 5 5': {'igpu': 'AMD Radeon Vega 7', 'generation': 'Ryzen 5000', 'architecture': 'Zen 3'},
                'ryzen 7 5': {'igpu': 'AMD Radeon Vega 8', 'generation': 'Ryzen 5000', 'architecture': 'Zen 3'},
                
                # AMD Ryzen 4000 Series APUs (Zen 2 with Vega)
                'ryzen 3 4': {'igpu': 'AMD Radeon Vega 6', 'generation': 'Ryzen 4000', 'architecture': 'Zen 2'},
                'ryzen 5 4': {'igpu': 'AMD Radeon Vega 7', 'generation': 'Ryzen 4000', 'architecture': 'Zen 2'},
                'ryzen 7 4': {'igpu': 'AMD Radeon Vega 8', 'generation': 'Ryzen 4000', 'architecture': 'Zen 2'},
                
                # AMD Ryzen 3000 Series APUs (Zen+ with Vega)
                'ryzen 3 3': {'igpu': 'AMD Radeon Vega 8', 'generation': 'Ryzen 3000', 'architecture': 'Zen+'},
                'ryzen 5 3': {'igpu': 'AMD Radeon Vega 11', 'generation': 'Ryzen 3000', 'architecture': 'Zen+'},
                'ryzen 7 3': {'igpu': 'AMD Radeon Vega 10', 'generation': 'Ryzen 3000', 'architecture': 'Zen+'},
            }
            
            # Detect specific processor
            detected_processor = None
            for processor_key, details in amd_processors.items():
                if processor_key in cpu_name:
                    detected_processor = details
                    self.detected_igpu = details['igpu']
                    break
            
            if detected_processor:
                print(f"✅ AMD Processor Detected:")
                print(f"   Generation: {detected_processor['generation']}")
                print(f"   Architecture: {detected_processor['architecture']}")
                print(f"   Integrated Graphics: {detected_processor['igpu']}")
                
                self.igpu_capabilities = self._get_amd_igpu_capabilities(detected_processor)
            else:
                print("⚠️  Specific AMD processor not recognized, using generic AMD iGPU settings")
                self.detected_igpu = "AMD Integrated Graphics"
                self.igpu_capabilities = self._get_generic_amd_capabilities()

        def _get_intel_igpu_capabilities(self, processor_info):
            """
            Get Intel iGPU capabilities based on processor generation
            """
            capabilities = {
                'vendor': 'Intel',
                'generation': processor_info['generation'],
                'architecture': processor_info['architecture'],
                'igpu_name': processor_info['igpu']
            }

            # Set capabilities based on generation
            if '13th Gen' in processor_info['generation'] or '12th Gen' in processor_info['generation']:
                capabilities.update({
                    'max_resolution': '4K (3840x2160)',
                    'directx_support': 'DirectX 12',
                    'vulkan_support': 'Vulkan 1.3',
                    'opengl_support': 'OpenGL 4.6',
                    'video_decode': 'AV1, HEVC, H.264',
                    'recommended_settings': {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 4096
                    }
                })
            elif '11th Gen' in processor_info['generation']:
                capabilities.update({
                    'max_resolution': '4K (3840x2160)',
                    'directx_support': 'DirectX 12',
                    'vulkan_support': 'Vulkan 1.2',
                    'opengl_support': 'OpenGL 4.6',
                    'video_decode': 'HEVC, H.264',
                    'recommended_settings': {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 2048
                    }
                })
            else:  # Older generations
                capabilities.update({
                    'max_resolution': '1080p (1920x1080)',
                    'directx_support': 'DirectX 11',
                    'vulkan_support': 'Vulkan 1.1',
                    'opengl_support': 'OpenGL 4.5',
                    'video_decode': 'H.264',
                    'recommended_settings': {
                        'renderer': 'gl',
                        'vsync': True,
                        'texture_scaling': 'nearest',
                        'max_texture_size': 1024
                    }
                })

            return capabilities

        def _get_amd_igpu_capabilities(self, processor_info):
            """
            Get AMD iGPU capabilities based on processor generation
            """
            capabilities = {
                'vendor': 'AMD',
                'generation': processor_info['generation'],
                'architecture': processor_info['architecture'],
                'igpu_name': processor_info['igpu']
            }

            # Set capabilities based on generation
            if 'Ryzen 7000' in processor_info['generation'] or 'Ryzen 6000' in processor_info['generation']:
                capabilities.update({
                    'max_resolution': '4K (3840x2160)',
                    'directx_support': 'DirectX 12',
                    'vulkan_support': 'Vulkan 1.3',
                    'opengl_support': 'OpenGL 4.6',
                    'video_decode': 'AV1, HEVC, H.264',
                    'rdna_version': 'RDNA 2',
                    'recommended_settings': {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 4096
                    }
                })
            elif 'Ryzen 5000' in processor_info['generation'] or 'Ryzen 4000' in processor_info['generation']:
                capabilities.update({
                    'max_resolution': '1440p (2560x1440)',
                    'directx_support': 'DirectX 12',
                    'vulkan_support': 'Vulkan 1.2',
                    'opengl_support': 'OpenGL 4.6',
                    'video_decode': 'HEVC, H.264',
                    'vega_version': 'Vega 7/8',
                    'recommended_settings': {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 2048
                    }
                })
            else:  # Older generations
                capabilities.update({
                    'max_resolution': '1080p (1920x1080)',
                    'directx_support': 'DirectX 11',
                    'vulkan_support': 'Vulkan 1.1',
                    'opengl_support': 'OpenGL 4.5',
                    'video_decode': 'H.264',
                    'vega_version': 'Vega 8/11',
                    'recommended_settings': {
                        'renderer': 'gl',
                        'vsync': True,
                        'texture_scaling': 'nearest',
                        'max_texture_size': 1024
                    }
                })

            return capabilities

        def _get_generic_intel_capabilities(self):
            """Generic Intel iGPU capabilities for unrecognized processors"""
            return {
                'vendor': 'Intel',
                'generation': 'Generic',
                'igpu_name': 'Intel Integrated Graphics',
                'max_resolution': '1080p (1920x1080)',
                'directx_support': 'DirectX 11',
                'vulkan_support': 'Vulkan 1.1',
                'opengl_support': 'OpenGL 4.0',
                'recommended_settings': {
                    'renderer': 'gl',
                    'vsync': True,
                    'texture_scaling': 'nearest',
                    'max_texture_size': 1024
                }
            }

        def _get_generic_amd_capabilities(self):
            """Generic AMD iGPU capabilities for unrecognized processors"""
            return {
                'vendor': 'AMD',
                'generation': 'Generic',
                'igpu_name': 'AMD Integrated Graphics',
                'max_resolution': '1080p (1920x1080)',
                'directx_support': 'DirectX 11',
                'vulkan_support': 'Vulkan 1.1',
                'opengl_support': 'OpenGL 4.0',
                'recommended_settings': {
                    'renderer': 'gl',
                    'vsync': True,
                    'texture_scaling': 'nearest',
                    'max_texture_size': 1024
                }
            }

        def _configure_renpy_for_igpu(self):
            """
            Configure Ren'Py engine settings for optimal integrated graphics performance
            """
            print(f"\n=== CONFIGURING REN'PY FOR {self.detected_cpu} iGPU ===")

            if not self.igpu_capabilities:
                print("No iGPU capabilities detected, using safe defaults")
                return

            settings = self.igpu_capabilities.get('recommended_settings', {})

            try:
                # Configure renderer
                renderer = settings.get('renderer', 'gl')
                renpy.config.renderer = renderer
                print(f"✅ Renderer set to: {renderer}")

                # Configure VSync
                vsync = settings.get('vsync', True)
                renpy.config.gl_vsync = vsync
                print(f"✅ VSync set to: {vsync}")

                # Configure texture settings
                texture_scaling = settings.get('texture_scaling', 'linear')
                if texture_scaling == 'linear':
                    renpy.config.gl_texture_scaling = True
                else:
                    renpy.config.gl_texture_scaling = False
                print(f"✅ Texture scaling set to: {texture_scaling}")

                # Configure maximum texture size
                max_texture_size = settings.get('max_texture_size', 1024)
                renpy.config.gl_maximum_texture_size = max_texture_size
                print(f"✅ Maximum texture size set to: {max_texture_size}")

                # Additional iGPU optimizations
                self._apply_igpu_optimizations()

                print("✅ Ren'Py configuration complete for integrated graphics")

            except Exception as e:
                print(f"Error configuring Ren'Py: {e}")

        def _apply_igpu_optimizations(self):
            """
            Apply additional optimizations for integrated graphics
            """
            print("Applying iGPU-specific optimizations...")

            try:
                # Reduce memory usage
                renpy.config.gl_resize = True
                renpy.config.gl_powersave = True

                # Optimize for lower-end graphics
                renpy.config.gl_framerate = 60
                renpy.config.gl_tearing = False

                # Reduce quality for better performance on iGPU
                if self.igpu_capabilities.get('vendor') == 'Intel':
                    # Intel-specific optimizations
                    renpy.config.gl_anisotropic = False
                    renpy.config.gl_depth_size = 16
                    print("✅ Applied Intel iGPU optimizations")

                elif self.igpu_capabilities.get('vendor') == 'AMD':
                    # AMD-specific optimizations
                    renpy.config.gl_anisotropic = True  # AMD iGPUs handle this better
                    renpy.config.gl_depth_size = 24
                    print("✅ Applied AMD iGPU optimizations")

            except Exception as e:
                print(f"Error applying iGPU optimizations: {e}")

        def generate_detailed_report(self):
            """
            Generate a detailed report of detected hardware and applied settings
            """
            print(f"\n{'='*60}")
            print("CPU INTEGRATED GRAPHICS DETECTION REPORT")
            print(f"{'='*60}")

            if self.detected_cpu:
                print(f"CPU Vendor: {self.detected_cpu}")
                print(f"CPU Model: {self.cpu_info.get('name', 'Unknown')}")

            if self.detected_igpu:
                print(f"Integrated GPU: {self.detected_igpu}")

            if self.igpu_capabilities:
                caps = self.igpu_capabilities
                print(f"Generation: {caps.get('generation', 'Unknown')}")
                print(f"Architecture: {caps.get('architecture', 'Unknown')}")
                print(f"Max Resolution: {caps.get('max_resolution', 'Unknown')}")
                print(f"DirectX Support: {caps.get('directx_support', 'Unknown')}")
                print(f"Vulkan Support: {caps.get('vulkan_support', 'Unknown')}")
                print(f"OpenGL Support: {caps.get('opengl_support', 'Unknown')}")

                if 'rdna_version' in caps:
                    print(f"RDNA Version: {caps['rdna_version']}")
                if 'vega_version' in caps:
                    print(f"Vega Version: {caps['vega_version']}")

                print("\nApplied Ren'Py Settings:")
                settings = caps.get('recommended_settings', {})
                for key, value in settings.items():
                    print(f"  {key}: {value}")

            print(f"{'='*60}")

    # Initialize the detector
    igpu_detector = CPUIntegratedGraphicsDetector()

    def detect_and_configure_igpu():
        """
        Main function to detect and configure integrated graphics
        """
        success = igpu_detector.detect_cpu_and_igpu()
        if success:
            igpu_detector.generate_detailed_report()
        return success

# Automatically run detection at startup
init:
    python:
        try:
            print("Starting CPU integrated graphics detection...")
            detect_and_configure_igpu()
        except Exception as e:
            print(f"Error in iGPU detection: {e}")

# Manual detection label for testing
label test_igpu_detection:
    "Testing CPU integrated graphics detection..."

    python:
        detect_and_configure_igpu()

    "Check the console for detailed iGPU detection results!"
    return
