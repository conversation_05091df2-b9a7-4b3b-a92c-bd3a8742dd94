# Python Compatibility Bridge for Ren'Py

## Overview

This compatibility bridge allows your Ren'Py project to work seamlessly with both the original Python 3.9.10 engine and newer Python versions (3.10+, 3.11+, 3.12+) without breaking existing functionality.

## What This Does

### 🔗 Bridges Old and New
- **Maintains** compatibility with your existing Python 3.9.10 Ren'Py engine
- **Enables** newer Python features when available
- **Preserves** all existing game functionality
- **Adds** enhanced capabilities for newer Python versions

### 🛡️ Safety First
- **No breaking changes** to your existing code
- **Backward compatible** with all Ren'Py versions
- **Graceful fallbacks** when newer features aren't available
- **Safe imports** that won't crash on missing modules

## Quick Start

### 1. Run the Configuration Tool

```bash
python "python configure/renpy python config.py"
```

This will:
- Detect your current Python version
- Create compatibility mappings
- Generate bridge configuration files
- Provide integration instructions

### 2. Add to Your Ren'Py Game

Add this to your `game/script.rpy` file:

```python
init -999 python:
    # Python Version Bridge Integration
    import sys
    import os
    
    # Add python configure to path
    python_config_path = os.path.join(config.basedir, "python configure")
    if python_config_path not in sys.path:
        sys.path.insert(0, python_config_path)
    
    try:
        from version_adapter import RenPyVersionAdapter
        from compatibility_bridge import compatibility_bridge
        
        # Initialize the adapter
        version_adapter = RenPyVersionAdapter(config.basedir)
        adapted_config = version_adapter.create_adapted_config()
        
        # Set up compatibility
        if adapted_config['compatibility_info']['is_newer_version']:
            print("Python Bridge: Newer Python detected, compatibility mode active")
        else:
            print("Python Bridge: Running on original engine Python")
        
        # Make available globally
        config.python_version_adapter = version_adapter
        config.python_compatibility = compatibility_bridge
        
    except ImportError as e:
        print(f"Warning: Could not load Python bridge: {e}")
```

## Features by Python Version

### Python 3.9.10 (Original Engine)
✅ **Fully Supported** - Your engine's native version
- All existing features work exactly as before
- No changes required to existing code
- Bridge provides enhanced module detection

### Python 3.10+
✅ **Enhanced Compatibility**
- Pattern matching (when available)
- Union type syntax (`int | str`)
- Better error messages
- All 3.9 features + new capabilities

### Python 3.11+
✅ **Performance Boost**
- Faster CPython execution
- Improved asyncio
- Exception groups
- Enhanced error reporting

### Python 3.12+
✅ **Latest Features**
- Improved error locations
- Per-interpreter GIL
- Enhanced pathlib
- All previous features

## Using the Bridge in Your Code

### Safe Module Imports

```python
# Instead of direct imports that might fail
from compatibility_bridge import safe_import

# Safe import with fallback
requests = safe_import('requests')
if requests:
    # Use requests module
    response = requests.get(url)
else:
    # Fallback behavior
    print("Requests not available")
```

### Version-Aware Features

```python
from compatibility_bridge import is_newer_python, get_features

if is_newer_python():
    print("Using enhanced Python features")
    # Use newer syntax when available
else:
    print("Using compatible Python features")
    # Use older syntax for compatibility

# Check specific features
features = get_features()
if features['pattern_matching']:
    # Use pattern matching
    pass
```

### Dictionary Operations

```python
from compatibility_bridge import merge_dicts

# Works on all Python versions
dict1 = {"a": 1, "b": 2}
dict2 = {"c": 3, "d": 4}
merged = merge_dicts(dict1, dict2)  # Uses | operator on 3.9+, .update() fallback
```

## Configuration Files

### `python_config_adapted.json`
Contains the complete compatibility mapping:
- Original engine configuration
- Current Python version info
- Feature availability matrix
- Module compatibility status
- Path adaptations

### `compatibility_bridge.py`
The main compatibility layer that:
- Detects available Python features
- Provides safe import functions
- Handles version differences
- Creates compatibility wrappers

### `version_adapter.py`
Specifically handles:
- Engine version bridging
- Configuration adaptation
- Feature flag management
- Integration code generation

## Troubleshooting

### Bridge Not Loading
```python
# Check if bridge is available
if hasattr(config, 'python_compatibility'):
    print("Bridge loaded successfully")
else:
    print("Bridge not available, using fallback")
```

### Module Import Issues
```python
# Use safe imports
from compatibility_bridge import safe_import
module = safe_import('problematic_module', fallback_module)
```

### Version Detection Problems
```python
# Manual version check
import sys
if sys.version_info >= (3, 10):
    # Newer Python code
    pass
else:
    # Compatible code
    pass
```

## Benefits

### For Developers
- **Write once, run anywhere** - Code works across Python versions
- **Future-proof** - Ready for newer Python versions
- **No migration needed** - Existing code continues to work
- **Enhanced features** - Access to newer Python capabilities

### For Users
- **Seamless experience** - Games work regardless of Python version
- **Better performance** - Automatic optimization for newer Python
- **Improved stability** - Graceful handling of version differences
- **Future compatibility** - Ready for Python updates

## Advanced Usage

### Custom Compatibility Checks
```python
from compatibility_bridge import compatibility_bridge

# Check specific Python features
if compatibility_bridge.features['pattern_matching']:
    # Use pattern matching syntax
    pass

# Get typing module for current version
typing_info = compatibility_bridge.get_typing_module()
if typing_info['supports_new_union']:
    # Use new union syntax
    pass
```

### Ren'Py Integration
```python
# Access adapter through config
if hasattr(config, 'python_version_adapter'):
    adapter = config.python_version_adapter
    report = adapter.generate_compatibility_report()
    print(report)
```

## Support

The compatibility bridge is designed to be:
- **Self-contained** - No external dependencies
- **Lightweight** - Minimal performance impact
- **Robust** - Handles edge cases gracefully
- **Maintainable** - Clear, documented code

For issues or questions, check the generated compatibility report or examine the configuration files for detailed information about your specific setup.
