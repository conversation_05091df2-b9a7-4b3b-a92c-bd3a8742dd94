## Distribution Source Tracker
## Creates tamper-proof files tracking where the game was downloaded from

init -90 python:
    import json
    import hashlib
    import time
    import os
    from datetime import datetime
    import base64

    # Distribution tracking state
    distribution_state = {
        'source_file_created': False,
        'source_verified': False,
        'distribution_hash': None,
        'creation_timestamp': None,
        'last_verification': None,
        'tamper_detected': False,
        'source_data': {}
    }

    # Distribution tracking configuration
    DISTRIBUTION_CONFIG = {
        'enabled': True,
        'create_source_file': True,
        'verify_on_startup': True,
        'encrypt_source_data': True,
        'use_hardware_binding': True,
        'source_file_name': '.distribution_source',
        'backup_file_name': '.distribution_backup',
        'verification_interval': 300,  # 5 minutes
        'tamper_protection': True
    }

    class DistributionTracker:
        def __init__(self):
            self.source_file_path = DISTRIBUTION_CONFIG['source_file_name']
            self.backup_file_path = DISTRIBUTION_CONFIG['backup_file_name']
            self.hardware_signature = None
            self.encryption_key = None
            
        def create_source_file(self, platform_data=None):
            """Create a tamper-proof source file tracking distribution origin"""
            try:
                if not DISTRIBUTION_CONFIG.get('enabled', True):
                    return False
                
                # Get platform data from detector if not provided
                if platform_data is None:
                    platform_data = platform_detector.create_detection_report()
                
                # Generate hardware signature for binding
                if DISTRIBUTION_CONFIG.get('use_hardware_binding', True):
                    self.hardware_signature = self._generate_hardware_signature()
                
                # Create comprehensive source data
                source_data = {
                    'distribution_info': {
                        'detected_platform': platform_data.get('primary_platform', 'unknown'),
                        'all_platforms': platform_data.get('detected_platforms', []),
                        'platform_details': platform_data.get('platform_details', {}),
                        'detection_timestamp': platform_data.get('detection_timestamp'),
                        'creation_timestamp': datetime.now().isoformat(),
                        'game_version': getattr(config, 'version', 'unknown'),
                        'game_name': getattr(config, 'name', 'unknown')
                    },
                    'system_info': {
                        'os': platform.system(),
                        'architecture': platform.machine(),
                        'python_version': platform.python_version(),
                        'renpy_version': getattr(renpy, 'version_string', 'unknown')
                    },
                    'security_info': {
                        'hardware_signature': self.hardware_signature,
                        'creation_hash': None,  # Will be filled after creation
                        'verification_count': 0,
                        'last_verification': None,
                        'tamper_attempts': 0
                    },
                    'metadata': {
                        'file_version': '1.0',
                        'encryption_enabled': DISTRIBUTION_CONFIG.get('encrypt_source_data', True),
                        'hardware_bound': DISTRIBUTION_CONFIG.get('use_hardware_binding', True),
                        'anti_cheat_integrated': True
                    }
                }
                
                # Generate creation hash
                source_data['security_info']['creation_hash'] = self._generate_data_hash(source_data)
                
                # Encrypt data if enabled
                if DISTRIBUTION_CONFIG.get('encrypt_source_data', True):
                    encrypted_data = self._encrypt_source_data(source_data)
                    file_content = {
                        'encrypted': True,
                        'data': encrypted_data,
                        'verification_hash': self._generate_data_hash(encrypted_data)
                    }
                else:
                    file_content = {
                        'encrypted': False,
                        'data': source_data,
                        'verification_hash': self._generate_data_hash(source_data)
                    }
                
                # Write source file
                with open(self.source_file_path, 'w') as f:
                    json.dump(file_content, f, indent=2)
                
                # Create backup file
                with open(self.backup_file_path, 'w') as f:
                    json.dump(file_content, f, indent=2)
                
                # Update distribution state
                distribution_state['source_file_created'] = True
                distribution_state['creation_timestamp'] = datetime.now().isoformat()
                distribution_state['distribution_hash'] = file_content['verification_hash']
                distribution_state['source_data'] = source_data
                
                renpy.log("Distribution source file created successfully")
                return True
                
            except Exception as e:
                renpy.log(f"Error creating distribution source file: {str(e)}")
                return False
        
        def verify_source_file(self):
            """Verify the integrity of the distribution source file"""
            try:
                if not os.path.exists(self.source_file_path):
                    renpy.log("Distribution source file not found")
                    distribution_state['source_verified'] = False
                    return False
                
                # Read source file
                with open(self.source_file_path, 'r') as f:
                    file_content = json.load(f)
                
                # Verify file structure
                if not all(key in file_content for key in ['encrypted', 'data', 'verification_hash']):
                    renpy.log("Distribution source file corrupted - missing keys")
                    distribution_state['tamper_detected'] = True
                    return False
                
                # Decrypt data if encrypted
                if file_content['encrypted']:
                    try:
                        source_data = self._decrypt_source_data(file_content['data'])
                    except Exception as e:
                        renpy.log(f"Failed to decrypt distribution source: {str(e)}")
                        distribution_state['tamper_detected'] = True
                        return False
                else:
                    source_data = file_content['data']
                
                # Verify data hash
                expected_hash = self._generate_data_hash(source_data if not file_content['encrypted'] else file_content['data'])
                if expected_hash != file_content['verification_hash']:
                    renpy.log("Distribution source file hash mismatch - tamper detected")
                    distribution_state['tamper_detected'] = True
                    return False
                
                # Verify hardware binding if enabled
                if DISTRIBUTION_CONFIG.get('use_hardware_binding', True):
                    stored_signature = source_data.get('security_info', {}).get('hardware_signature')
                    current_signature = self._generate_hardware_signature()
                    
                    if stored_signature != current_signature:
                        renpy.log("Hardware signature mismatch - possible unauthorized transfer")
                        distribution_state['tamper_detected'] = True
                        return False
                
                # Update verification info
                source_data['security_info']['verification_count'] += 1
                source_data['security_info']['last_verification'] = datetime.now().isoformat()
                
                # Update distribution state
                distribution_state['source_verified'] = True
                distribution_state['last_verification'] = datetime.now().isoformat()
                distribution_state['source_data'] = source_data
                
                renpy.log("Distribution source file verified successfully")
                return True
                
            except Exception as e:
                renpy.log(f"Error verifying distribution source file: {str(e)}")
                distribution_state['source_verified'] = False
                return False
        
        def get_distribution_info(self):
            """Get distribution information from the source file"""
            try:
                if not distribution_state.get('source_verified', False):
                    if not self.verify_source_file():
                        return None
                
                return distribution_state.get('source_data', {}).get('distribution_info', {})
                
            except Exception as e:
                renpy.log(f"Error getting distribution info: {str(e)}")
                return None
        
        def report_tamper_attempt(self, details="Unknown tamper attempt"):
            """Report a tamper attempt to the anti-cheat system"""
            try:
                # Update tamper count
                if 'source_data' in distribution_state:
                    security_info = distribution_state['source_data'].get('security_info', {})
                    security_info['tamper_attempts'] = security_info.get('tamper_attempts', 0) + 1
                
                # Report to anti-cheat system if available
                if 'anticheat_core' in globals():
                    violation_data = {
                        'type': 'DISTRIBUTION_TAMPERING',
                        'details': details,
                        'timestamp': datetime.now().isoformat(),
                        'severity': 'HIGH',
                        'source_file_status': distribution_state
                    }
                    
                    # This would integrate with your existing anti-cheat system
                    # anticheat_core.report_violation(violation_data)
                
                renpy.log(f"Distribution tamper attempt reported: {details}")
                
            except Exception as e:
                renpy.log(f"Error reporting tamper attempt: {str(e)}")
        
        def _generate_hardware_signature(self):
            """Generate a hardware-based signature for binding"""
            try:
                # Collect hardware identifiers
                hw_data = {
                    'system': platform.system(),
                    'machine': platform.machine(),
                    'processor': platform.processor(),
                    'node': platform.node()
                }
                
                # Add Windows-specific identifiers
                if platform.system() == 'Windows':
                    try:
                        import winreg
                        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                           r"SOFTWARE\Microsoft\Cryptography")
                        machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                        hw_data['machine_guid'] = machine_guid
                        winreg.CloseKey(key)
                    except:
                        pass
                
                # Create signature
                signature_string = json.dumps(hw_data, sort_keys=True)
                signature_hash = hashlib.sha256(signature_string.encode()).hexdigest()
                
                return signature_hash
                
            except Exception as e:
                renpy.log(f"Error generating hardware signature: {str(e)}")
                return "unknown_hardware"
        
        def _generate_data_hash(self, data):
            """Generate a hash for data integrity verification"""
            try:
                if isinstance(data, dict):
                    data_string = json.dumps(data, sort_keys=True)
                else:
                    data_string = str(data)
                
                return hashlib.sha256(data_string.encode()).hexdigest()
                
            except Exception as e:
                renpy.log(f"Error generating data hash: {str(e)}")
                return "hash_error"
        
        def _encrypt_source_data(self, data):
            """Encrypt source data for protection"""
            try:
                # Simple encryption using base64 and XOR (for demonstration)
                # In production, use proper encryption like AES
                data_string = json.dumps(data)
                
                # Generate a simple key based on hardware signature
                if not self.encryption_key:
                    key_base = self.hardware_signature or "default_key"
                    self.encryption_key = hashlib.md5(key_base.encode()).hexdigest()
                
                # XOR encryption (simple but effective for this use case)
                encrypted_bytes = []
                key_bytes = self.encryption_key.encode()
                
                for i, byte in enumerate(data_string.encode()):
                    encrypted_bytes.append(byte ^ key_bytes[i % len(key_bytes)])
                
                encrypted_data = base64.b64encode(bytes(encrypted_bytes)).decode()
                return encrypted_data
                
            except Exception as e:
                renpy.log(f"Error encrypting source data: {str(e)}")
                return str(data)
        
        def _decrypt_source_data(self, encrypted_data):
            """Decrypt source data"""
            try:
                # Generate the same key
                if not self.encryption_key:
                    key_base = self.hardware_signature or "default_key"
                    self.encryption_key = hashlib.md5(key_base.encode()).hexdigest()
                
                # Decode and decrypt
                encrypted_bytes = base64.b64decode(encrypted_data.encode())
                key_bytes = self.encryption_key.encode()
                
                decrypted_bytes = []
                for i, byte in enumerate(encrypted_bytes):
                    decrypted_bytes.append(byte ^ key_bytes[i % len(key_bytes)])
                
                decrypted_string = bytes(decrypted_bytes).decode()
                return json.loads(decrypted_string)
                
            except Exception as e:
                renpy.log(f"Error decrypting source data: {str(e)}")
                raise

    # Initialize distribution tracker
    distribution_tracker = DistributionTracker()

# Auto-create and verify source file on startup
init python:
    if DISTRIBUTION_CONFIG.get('enabled', True):
        try:
            # Create source file if it doesn't exist
            if DISTRIBUTION_CONFIG.get('create_source_file', True):
                if not os.path.exists(distribution_tracker.source_file_path):
                    distribution_tracker.create_source_file()
            
            # Verify source file on startup
            if DISTRIBUTION_CONFIG.get('verify_on_startup', True):
                distribution_tracker.verify_source_file()
                
        except Exception as e:
            renpy.log(f"Distribution tracker initialization failed: {str(e)}")

# Distribution tracking functions for use in game
define distribution_tracking_enabled = DISTRIBUTION_CONFIG.get('enabled', True)
define distribution_verified = distribution_state.get('source_verified', False)
define distribution_source = distribution_state.get('source_data', {}).get('distribution_info', {}).get('detected_platform', 'unknown')
