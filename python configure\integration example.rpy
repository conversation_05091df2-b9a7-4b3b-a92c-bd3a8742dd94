#!/usr/bin/env python3
"""
Ren'Py Python Configuration Tool
Configures Python environment for Ren'Py without modifying existing game files
"""

import os
import sys
import json
import platform
import subprocess
from pathlib import Path

class RenPyPythonConfigurator:
    """Handles Python configuration for Ren'Py projects"""
    
    def __init__(self, project_dir=None):
        self.project_dir = Path(project_dir) if project_dir else Path.cwd().parent
        self.game_dir = self.project_dir / "game"
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "python_config.json"
        
    def detect_renpy_environment(self):
        """Detect Ren'Py environment and Python version"""
        print("Detecting Ren'Py environment...")
        
        # Check if we're in a Ren'Py project
        if not self.game_dir.exists():
            print(f"Warning: Game directory not found at {self.game_dir}")
        
        # Try to detect Ren'Py version
        try:
            # Look for version info in common locations
            version_file = self.project_dir / "renpy" / "version.py"
            if version_file.exists():
                with open(version_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    version_match = re.search(r'version\s*=\s*[\'"](.+?)[\'"]', content)
                    if version_match:
                        return version_match.group(1)
            
            # Alternative method - check game/options.rpy
            options_file = self.game_dir / "options.rpy"
            if options_file.exists():
                with open(options_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Look for config.version
                    version_match = re.search(r'config\.version\s*=\s*[\'"](.+?)[\'"]', content)
                    if version_match:
                        return f"Ren'Py (config.version: {version_match.group(1)})"
            
            return "Ren'Py (version unknown)"
        except Exception as e:
            print(f"Error detecting Ren'Py version: {e}")
            return "Ren'Py (detection failed)"
    
    def save_configuration(self, config_data):
        """Save configuration to JSON file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)
            print(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def load_configuration(self):
        """Load configuration from JSON file"""
        if not self.config_file.exists():
            return self.create_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return self.create_default_config()
    
    def create_default_config(self):
        """Create default configuration"""
        return {
            "project_name": "netcode_protogen_game",
            "python_version": platform.python_version(),
            "renpy_detected": self.detect_renpy_environment(),
            "system_info": {
                "os": platform.system(),
                "platform": platform.platform(),
                "processor": platform.processor()
            },
            "python_paths": {
                "site_packages": self.get_site_packages_path(),
                "executable": sys.executable
            },
            "modules_available": self.check_required_modules(),
            "last_updated": str(Path().cwd())
        }
    
    def get_site_packages_path(self):
        """Get site-packages directory path"""
        for path in sys.path:
            if "site-packages" in path:
                return path
        return "Not found"
    
    def check_required_modules(self):
        """Check if required modules are available"""
        required_modules = [
            "cryptography", "pillow", "requests", 
            "numpy", "pygame", "future"
        ]
        
        available_modules = {}
        for module in required_modules:
            try:
                __import__(module)
                available_modules[module] = True
            except ImportError:
                available_modules[module] = False
        
        return available_modules
    
    def configure_python(self):
        """Configure Python for Ren'Py project"""
        print("Configuring Python for Ren'Py project...")
        
        # Load or create configuration
        config = self.load_configuration()
        
        # Update configuration with current info
        config["python_version"] = platform.python_version()
        config["renpy_detected"] = self.detect_renpy_environment()
        config["system_info"]["os"] = platform.system()
        config["system_info"]["platform"] = platform.platform()
        config["modules_available"] = self.check_required_modules()
        config["last_updated"] = str(Path().cwd())
        
        # Save updated configuration
        self.save_configuration(config)
        
        # Create Python path configuration file
        self.create_path_config()
        
        print("Python configuration complete!")
        return True
    
    def create_path_config(self):
        """Create Python path configuration file"""
        path_config = self.config_dir / "python_path.py"
        
        content = f"""# Python Path Configuration for Ren'Py
# Generated by RenPyPythonConfigurator

import sys
import os

# Add custom paths
custom_paths = [
    os.path.join(config.basedir, "python configure"),
    os.path.join(config.basedir, ".vscode"),
    os.path.join(config.basedir, "anti-cheat")
]

# Add paths to sys.path if they exist
for path in custom_paths:
    if os.path.exists(path) and path not in sys.path:
        sys.path.insert(0, path)
"""
        
        try:
            with open(path_config, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Python path configuration created: {path_config}")
            return True
        except Exception as e:
            print(f"Error creating path configuration: {e}")
            return False

def main():
    """Main function"""
    print("=" * 50)
    print("Ren'Py Python Configuration Tool")
    print("=" * 50)
    
    # Get project directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(f"Project directory: {project_dir}")
    
    # Create configurator
    configurator = RenPyPythonConfigurator(project_dir)
    
    # Configure Python
    configurator.configure_python()
    
    print("\nConfiguration complete!")
    print("=" * 50)
    print("To use this configuration in your Ren'Py game:")
    print("1. Add the following to your game/script.rpy file:")
    print("   init -999 python:")
    print("       import sys")
    print("       sys.path.insert(0, config.basedir + '/python configure')")
    print("       try:")
    print("           import python_path")
    print("       except ImportError:")
    print("           pass")
    print("=" * 50)

if __name__ == "__main__":
    main()
