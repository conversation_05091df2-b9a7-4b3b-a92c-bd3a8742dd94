#!/usr/bin/env python3
"""
Developer Setup Script for Legal Protection System
Generates developer computer signature for authentication
"""

import os
import sys
import json
import hashlib
import platform
import uuid

def generate_developer_signature():
    """Generate unique developer computer signature"""
    try:
        # Collect system information
        system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version()
        }
        
        # Get MAC address
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0,2*6,2)][::-1])
        system_info['mac_address'] = mac
        
        # Get CPU info (Windows)
        try:
            if platform.system() == 'Windows':
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                cpu_name = winreg.QueryValueEx(key, "ProcessorNameString")[0]
                system_info['cpu_name'] = cpu_name
                winreg.CloseKey(key)
        except:
            pass
        
        # Create signature
        signature_string = json.dumps(system_info, sort_keys=True)
        signature_hash = hashlib.sha256(signature_string.encode()).hexdigest()
        
        return signature_hash, system_info
        
    except Exception as e:
        print(f"Error generating signature: {e}")
        return None, None

def update_legal_protection_file(signature):
    """Update legal protection file with developer signature"""
    try:
        legal_file = "anti-cheat/legal_protection.rpy"
        
        if not os.path.exists(legal_file):
            print(f"Legal protection file not found: {legal_file}")
            return False
        
        # Read the file
        with open(legal_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the signature placeholder
        old_signature_line = '                "your_developer_signature_hash_here",'
        new_signature_line = f'                "{signature}",'
        
        if old_signature_line in content:
            content = content.replace(old_signature_line, new_signature_line)
            
            # Write back to file
            with open(legal_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Updated legal protection file with developer signature")
            return True
        else:
            print("Could not find signature placeholder in legal protection file")
            return False
            
    except Exception as e:
        print(f"Error updating legal protection file: {e}")
        return False

def create_developer_config(signature, system_info):
    """Create developer configuration file"""
    try:
        config = {
            'developer_signature': signature,
            'system_info': system_info,
            'created': platform.time.time() if hasattr(platform, 'time') else 0,
            'computer_name': platform.node(),
            'instructions': [
                "This file contains your developer computer signature",
                "Keep this file secure and do not share it",
                "The signature is used to authenticate your developer computer",
                "If you change hardware, you may need to regenerate the signature"
            ]
        }
        
        config_file = "anti-cheat/developer_config.json"
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        print(f"Created developer configuration: {config_file}")
        return True
        
    except Exception as e:
        print(f"Error creating developer config: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("DEVELOPER SETUP - LEGAL PROTECTION SYSTEM")
    print("=" * 60)
    print()
    
    print("Generating developer computer signature...")
    signature, system_info = generate_developer_signature()
    
    if not signature:
        print("Failed to generate developer signature!")
        sys.exit(1)
    
    print(f"Developer Signature: {signature}")
    print()
    
    print("System Information:")
    for key, value in system_info.items():
        print(f"  {key}: {value}")
    print()
    
    # Update legal protection file
    print("Updating legal protection file...")
    if update_legal_protection_file(signature):
        print("✓ Legal protection file updated successfully")
    else:
        print("✗ Failed to update legal protection file")
    
    # Create developer config
    print("Creating developer configuration...")
    if create_developer_config(signature, system_info):
        print("✓ Developer configuration created successfully")
    else:
        print("✗ Failed to create developer configuration")
    
    print()
    print("=" * 60)
    print("SETUP COMPLETE")
    print("=" * 60)
    print()
    print("IMPORTANT INSTRUCTIONS:")
    print("1. Your developer signature has been generated and saved")
    print("2. The legal protection system will now recognize this computer")
    print("3. Legal documents will be auto-created on this computer")
    print("4. Keep the developer_config.json file secure")
    print("5. Do not share your developer signature with others")
    print()
    print("If you add more developer computers, run this script on each one")
    print("and add their signatures to the legal_protection.rpy file.")
    print()
    
    # Show next steps
    print("NEXT STEPS:")
    print("1. Run your game to test the legal protection system")
    print("2. Check that legal documents are created in 'legal_documents/' folder")
    print("3. Verify that the anti-cheat system recognizes your computer")
    print("4. Test on a different computer to ensure protection works")
    print()

if __name__ == "__main__":
    main()
