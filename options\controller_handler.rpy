## Controller Input Handler
## Handles input from various controller types including Maple, Xbox, PlayStation, and Nintendo

init -5 python:
    import pygame
    
    # Controller state
    controller_state = {
        'connected': False,
        'controller_object': None,
        'controller_name': '',
        'button_states': {},
        'axis_states': {},
        'last_input_time': 0
    }
    
    # Input mapping for different controller types
    input_actions = {
        'confirm': [renpy.ui.interact, lambda: renpy.run(Return())],
        'cancel': [lambda: renpy.run(Return()), renpy.ui.interact],
        'menu': [lambda: renpy.show_menu('preferences')],
        'quick_save': [lambda: renpy.save('quick')],
        'previous_choice': [lambda: renpy.music.set_volume(max(0, renpy.music.get_volume() - 0.1))],
        'next_choice': [lambda: renpy.music.set_volume(min(1, renpy.music.get_volume() + 0.1))],
        'auto_mode': [lambda: renpy.set_autoreload(not renpy.get_autoreload())],
        'skip': [lambda: _preferences.skip_unseen],
        'pause_menu': [lambda: renpy.show_menu('save')],
        'history': [lambda: renpy.show_menu('history')]
    }

class ControllerHandler:
    """Handles controller input and mapping"""
    
    def __init__(self):
        self.enabled = True
        self.deadzone = 0.2
        self.button_repeat_delay = 0.3
        self.last_button_time = {}
        
    def initialize_controller(self):
        """Initialize controller support"""
        try:
            pygame.init()
            pygame.joystick.init()
            
            if pygame.joystick.get_count() > 0:
                controller_state['controller_object'] = pygame.joystick.Joystick(0)
                controller_state['controller_object'].init()
                controller_state['controller_name'] = controller_state['controller_object'].get_name()
                controller_state['connected'] = True
                
                renpy.log("Controller connected: {}".format(controller_state['controller_name']))
                return True
            else:
                renpy.log("No controllers detected")
                return False
                
        except Exception as e:
            renpy.log("Controller initialization failed: {}".format(str(e)))
            return False
    
    def detect_controller_type(self):
        """Detect controller type based on name"""
        if not controller_state['connected']:
            return 'keyboard'
        
        name = controller_state['controller_name'].lower()
        
        if 'xbox' in name or 'microsoft' in name:
            return 'xbox'
        elif 'playstation' in name or 'dualshock' in name or 'dualsense' in name:
            return 'playstation'
        elif 'nintendo' in name or 'switch' in name or 'pro controller' in name:
            return 'nintendo'
        elif 'maple' in name:
            return 'maple'
        else:
            return 'generic'
    
    def update_controller_state(self):
        """Update controller button and axis states"""
        if not controller_state['connected'] or not controller_state['controller_object']:
            return
        
        try:
            pygame.event.pump()
            
            controller = controller_state['controller_object']
            
            # Update button states
            for i in range(controller.get_numbuttons()):
                button_pressed = controller.get_button(i)
                controller_state['button_states'][i] = button_pressed
            
            # Update axis states
            for i in range(controller.get_numaxes()):
                axis_value = controller.get_axis(i)
                # Apply deadzone
                if abs(axis_value) < self.deadzone:
                    axis_value = 0
                controller_state['axis_states'][i] = axis_value
            
        except Exception as e:
            renpy.log("Controller state update failed: {}".format(str(e)))
    
    def handle_controller_input(self):
        """Handle controller input based on current mapping"""
        if not self.enabled or not controller_state['connected']:
            return
        
        current_time = time.time()
        controller_type = gaming_settings.get('controller_type', 'keyboard')
        
        if controller_type not in controller_mappings:
            return
        
        mapping = controller_mappings[controller_type]
        
        # Handle button presses
        for button_name, action_name in mapping['buttons'].items():
            if self._is_button_pressed(button_name, controller_type):
                # Check for button repeat delay
                if button_name in self.last_button_time:
                    if current_time - self.last_button_time[button_name] < self.button_repeat_delay:
                        continue
                
                self.last_button_time[button_name] = current_time
                self._execute_action(action_name)
    
    def _is_button_pressed(self, button_name, controller_type):
        """Check if a specific button is pressed"""
        try:
            if controller_type == 'keyboard' or controller_type == 'maple':
                # Handle keyboard input
                return self._check_keyboard_input(button_name)
            else:
                # Handle controller input
                return self._check_controller_button(button_name, controller_type)
        except:
            return False
    
    def _check_keyboard_input(self, key_name):
        """Check keyboard input"""
        # This would need to be integrated with Ren'Py's input system
        # For now, return False as keyboard input is handled by Ren'Py directly
        return False
    
    def _check_controller_button(self, button_name, controller_type):
        """Check controller button press"""
        if not controller_state['connected']:
            return False
        
        # Map button names to controller button indices
        button_mappings = {
            'xbox': {
                'a': 0, 'b': 1, 'x': 2, 'y': 3,
                'lb': 4, 'rb': 5, 'lt': 6, 'rt': 7,
                'select': 8, 'start': 9
            },
            'playstation': {
                'cross': 0, 'circle': 1, 'square': 2, 'triangle': 3,
                'l1': 4, 'r1': 5, 'l2': 6, 'r2': 7,
                'share': 8, 'options': 9
            },
            'nintendo': {
                'b': 0, 'a': 1, 'y': 2, 'x': 3,
                'l': 4, 'r': 5, 'zl': 6, 'zr': 7,
                'minus': 8, 'plus': 9
            }
        }
        
        if controller_type in button_mappings and button_name in button_mappings[controller_type]:
            button_index = button_mappings[controller_type][button_name]
            return controller_state['button_states'].get(button_index, False)
        
        return False
    
    def _execute_action(self, action_name):
        """Execute the mapped action"""
        try:
            if action_name in input_actions:
                action_func = input_actions[action_name][0]
                if callable(action_func):
                    action_func()
                    renpy.log("Executed controller action: {}".format(action_name))
        except Exception as e:
            renpy.log("Error executing controller action {}: {}".format(action_name, str(e)))
    
    def vibrate_controller(self, duration=0.1, strength=0.5):
        """Vibrate the controller if supported"""
        if not controller_state['connected'] or not gaming_settings.get('controller_vibration', True):
            return
        
        try:
            if hasattr(controller_state['controller_object'], 'rumble'):
                controller_state['controller_object'].rumble(strength, strength, int(duration * 1000))
        except Exception as e:
            renpy.log("Controller vibration failed: {}".format(str(e)))

# Initialize controller handler
controller_handler = ControllerHandler()

# Controller status screen
screen controller_status():
    
    if gaming_settings.get('controller_type', 'keyboard') != 'keyboard':
        frame:
            xpos 350
            ypos 20
            xsize 280
            ysize 100
            background "#000000dd"
            
            vbox:
                spacing 8
                
                hbox:
                    spacing 10
                    
                    if controller_state['connected']:
                        text "🎮" size 20 color "#00ff00"
                        text "CONTROLLER CONNECTED" size 12 color "#00ff00" bold True
                    else:
                        text "🎮" size 20 color "#ff6666"
                        text "CONTROLLER DISCONNECTED" size 12 color "#ff6666" bold True
                
                if controller_state['connected']:
                    text "Type: {}".format(gaming_settings.get('controller_type', 'unknown').title()) size 10 color "#ffffff"
                    text "Name: {}".format(controller_state['controller_name'][:20] + "..." if len(controller_state['controller_name']) > 20 else controller_state['controller_name']) size 9 color "#cccccc"
                else:
                    text "Please connect a controller" size 10 color "#888888"

# Controller test screen
screen controller_test():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 500
        
        vbox:
            spacing 20
            
            text "🎮 Controller Test" size 24 xalign 0.5
            
            if controller_state['connected']:
                vbox:
                    spacing 15
                    
                    text "Controller: {}".format(controller_state['controller_name']) size 16
                    
                    # Button states
                    frame:
                        background "#2e2e2e"
                        padding (15, 10)
                        
                        vbox:
                            spacing 10
                            
                            text "Button States:" size 14 color "#ffaa00"
                            
                            grid 4 3:
                                spacing 10
                                
                                for i in range(12):
                                    if i in controller_state['button_states']:
                                        if controller_state['button_states'][i]:
                                            text "Btn {}".format(i) size 12 color "#00ff00"
                                        else:
                                            text "Btn {}".format(i) size 12 color "#666666"
                                    else:
                                        text "---" size 12 color "#333333"
                    
                    # Axis states
                    frame:
                        background "#2e2e2e"
                        padding (15, 10)
                        
                        vbox:
                            spacing 10
                            
                            text "Axis States:" size 14 color "#ffaa00"
                            
                            for i in range(min(4, len(controller_state['axis_states']))):
                                hbox:
                                    spacing 10
                                    text "Axis {}:".format(i) size 12
                                    bar value StaticValue(controller_state['axis_states'].get(i, 0), 2.0, offset=-1.0) xsize 200
                    
                    # Test vibration
                    textbutton "Test Vibration" action Function(controller_handler.vibrate_controller) text_size 14
            
            else:
                text "No controller connected" size 16 color "#ff6666" xalign 0.5
                text "Please connect a controller and restart the game" size 12 color "#888888" xalign 0.5
            
            textbutton "Close" action Return() xalign 0.5

# Initialize controller on game start
label after_load:
    $ controller_handler.initialize_controller()
    $ controller_handler.update_controller_state()
    return

# Continuous controller monitoring
init python:
    def monitor_controller():
        """Monitor controller input continuously"""
        if gaming_settings.get('controller_type', 'keyboard') != 'keyboard':
            controller_handler.update_controller_state()
            controller_handler.handle_controller_input()
    
    # Add controller monitoring to the game loop
    config.periodic_callbacks.append(monitor_controller)

# Controller settings integration
init python:
    def refresh_controller():
        """Refresh controller connection"""
        controller_handler.initialize_controller()
        renpy.restart_interaction()
    
    def test_controller():
        """Open controller test screen"""
        renpy.call_screen("controller_test")

# Add controller refresh to gaming settings
screen enhanced_preferences():
    
    tag menu
    
    use game_menu(_("Gaming Settings"), scroll="viewport"):
        
        vbox:
            spacing 20
            
            # Settings tabs
            hbox:
                spacing 10
                xalign 0.5
                
                textbutton "Graphics" action SetScreenVariable("settings_tab", "graphics") text_size 16
                textbutton "Audio" action SetScreenVariable("settings_tab", "audio") text_size 16
                textbutton "Controls" action SetScreenVariable("settings_tab", "controls") text_size 16
                textbutton "Hardware" action SetScreenVariable("settings_tab", "hardware") text_size 16
                textbutton "Basic" action SetScreenVariable("settings_tab", "basic") text_size 16
            
            null height 20
            
            # Show controller status
            use controller_status
            
            # Settings panels (same as before)
            if settings_tab == "graphics":
                use graphics_settings_panel
            elif settings_tab == "audio":
                use audio_settings_panel
            elif settings_tab == "controls":
                use controls_settings_panel
                
                # Add controller test button
                null height 20
                hbox:
                    spacing 20
                    textbutton "🔄 Refresh Controller" action Function(refresh_controller) text_size 14
                    textbutton "🧪 Test Controller" action Function(test_controller) text_size 14
                
            elif settings_tab == "hardware":
                use hardware_info_panel
            else:
                use basic_settings_panel
