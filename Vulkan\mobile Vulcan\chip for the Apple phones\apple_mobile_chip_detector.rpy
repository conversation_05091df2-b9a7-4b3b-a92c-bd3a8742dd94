## Apple Mobile Chip Detection and Configuration System
## Detects Apple M1 and higher chips on iPhones and configures optimal settings
## Supports M1, M1 Pro, M1 Max, M1 Ultra, M2, M2 Pro, M2 Max, M2 Ultra, M3, M3 Pro, M3 Max

init python:
    import os
    import sys
    import platform
    import subprocess
    import re
    
    class AppleMobileChipDetector:
        """
        Comprehensive Apple mobile chip detection system
        Optimizes Ren'Py for M1 and higher Apple Silicon chips on iPhones
        """
        
        def __init__(self):
            self.detected_chip = None
            self.chip_generation = None
            self.chip_variant = None
            self.is_apple_device = False
            self.is_mobile = False
            self.chip_capabilities = {}
            self.recommended_settings = {}
            self.performance_tier = None
            
        def detect_apple_mobile_chip(self):
            """
            Main function to detect Apple mobile chips and configure settings
            """
            print("=== APPLE MOBILE CHIP DETECTION ===")
            
            try:
                # Check if running on Apple device
                if not self._is_apple_device():
                    print("❌ Not running on Apple device")
                    return False
                
                # Check if running on mobile (iPhone/iPad)
                if not self._is_mobile_device():
                    print("❌ Not running on mobile device")
                    return False
                
                # Detect specific chip model
                chip_info = self._detect_chip_model()
                
                if chip_info:
                    self.detected_chip = chip_info['chip']
                    self.chip_generation = chip_info['generation']
                    self.chip_variant = chip_info['variant']
                    
                    # Get chip capabilities
                    self._analyze_chip_capabilities()
                    
                    # Configure Ren'Py settings
                    self._configure_renpy_for_apple_chip()
                    
                    # Generate report
                    self._generate_apple_chip_report()
                    
                    return True
                else:
                    print("⚠️  Could not detect specific Apple chip model")
                    return False
                    
            except Exception as e:
                print(f"Error in Apple chip detection: {e}")
                return False
        
        def _is_apple_device(self):
            """Check if running on Apple device"""
            try:
                # Check platform
                if platform.system() == "Darwin":
                    self.is_apple_device = True
                    return True
                
                # Check for iOS/iPadOS indicators
                if hasattr(sys, 'platform'):
                    if 'ios' in sys.platform.lower() or 'darwin' in sys.platform.lower():
                        self.is_apple_device = True
                        return True
                
                # Check user agent or other mobile indicators
                if hasattr(renpy, 'variant'):
                    if any(variant in renpy.variant() for variant in ['ios', 'mobile', 'touch']):
                        self.is_apple_device = True
                        return True
                
                return False
                
            except Exception as e:
                print(f"Error checking Apple device: {e}")
                return False
        
        def _is_mobile_device(self):
            """Check if running on mobile device (iPhone/iPad)"""
            try:
                # Check Ren'Py variants
                if hasattr(renpy, 'variant'):
                    variants = renpy.variant()
                    if any(variant in variants for variant in ['ios', 'mobile', 'touch', 'small']):
                        self.is_mobile = True
                        print("✅ Mobile device detected")
                        return True
                
                # Check screen size indicators
                if hasattr(renpy, 'config'):
                    screen_width = getattr(renpy.config, 'screen_width', 0)
                    screen_height = getattr(renpy.config, 'screen_height', 0)
                    
                    # Typical mobile screen dimensions
                    if (screen_width <= 1920 and screen_height <= 1920) or (screen_width <= 1080):
                        self.is_mobile = True
                        print("✅ Mobile screen dimensions detected")
                        return True
                
                return False
                
            except Exception as e:
                print(f"Error checking mobile device: {e}")
                return False
        
        def _detect_chip_model(self):
            """Detect specific Apple chip model"""
            try:
                chip_info = None
                
                # Method 1: Try to get chip info from system
                if platform.system() == "Darwin":
                    try:
                        # Use sysctl to get chip information
                        result = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'], 
                                              capture_output=True, text=True, timeout=5)
                        cpu_brand = result.stdout.strip()
                        
                        if cpu_brand:
                            chip_info = self._parse_chip_from_brand(cpu_brand)
                            
                    except Exception as e:
                        print(f"Could not get chip info via sysctl: {e}")
                
                # Method 2: Try alternative detection methods
                if not chip_info:
                    chip_info = self._detect_chip_alternative_methods()
                
                # Method 3: Fallback to device model detection
                if not chip_info:
                    chip_info = self._detect_chip_from_device_model()
                
                return chip_info
                
            except Exception as e:
                print(f"Error detecting chip model: {e}")
                return None
        
        def _parse_chip_from_brand(self, cpu_brand):
            """Parse chip information from CPU brand string"""
            cpu_brand_lower = cpu_brand.lower()
            
            # M3 Series Detection
            if 'm3' in cpu_brand_lower:
                if 'max' in cpu_brand_lower:
                    return {'chip': 'M3 Max', 'generation': 'M3', 'variant': 'Max'}
                elif 'pro' in cpu_brand_lower:
                    return {'chip': 'M3 Pro', 'generation': 'M3', 'variant': 'Pro'}
                else:
                    return {'chip': 'M3', 'generation': 'M3', 'variant': 'Base'}
            
            # M2 Series Detection
            elif 'm2' in cpu_brand_lower:
                if 'ultra' in cpu_brand_lower:
                    return {'chip': 'M2 Ultra', 'generation': 'M2', 'variant': 'Ultra'}
                elif 'max' in cpu_brand_lower:
                    return {'chip': 'M2 Max', 'generation': 'M2', 'variant': 'Max'}
                elif 'pro' in cpu_brand_lower:
                    return {'chip': 'M2 Pro', 'generation': 'M2', 'variant': 'Pro'}
                else:
                    return {'chip': 'M2', 'generation': 'M2', 'variant': 'Base'}
            
            # M1 Series Detection
            elif 'm1' in cpu_brand_lower:
                if 'ultra' in cpu_brand_lower:
                    return {'chip': 'M1 Ultra', 'generation': 'M1', 'variant': 'Ultra'}
                elif 'max' in cpu_brand_lower:
                    return {'chip': 'M1 Max', 'generation': 'M1', 'variant': 'Max'}
                elif 'pro' in cpu_brand_lower:
                    return {'chip': 'M1 Pro', 'generation': 'M1', 'variant': 'Pro'}
                else:
                    return {'chip': 'M1', 'generation': 'M1', 'variant': 'Base'}
            
            return None
        
        def _detect_chip_alternative_methods(self):
            """Alternative methods to detect Apple chip"""
            try:
                # Check for Apple Silicon indicators in environment
                if os.environ.get('PROCESSOR_ARCHITECTURE') == 'arm64':
                    # Assume M1 or higher if ARM64 on Apple device
                    return {'chip': 'M1 or higher', 'generation': 'M1+', 'variant': 'Unknown'}
                
                # Check platform machine
                if platform.machine() == 'arm64':
                    return {'chip': 'Apple Silicon', 'generation': 'M1+', 'variant': 'Unknown'}
                
                return None
                
            except Exception as e:
                print(f"Error in alternative chip detection: {e}")
                return None
        
        def _detect_chip_from_device_model(self):
            """Detect chip based on device model (iPhone/iPad)"""
            try:
                # This would typically require device-specific APIs
                # For now, assume M1+ if on modern iOS device
                
                # Check iOS version or other indicators
                if hasattr(sys, 'version_info'):
                    # Modern iOS devices likely have M1+ chips
                    return {'chip': 'M1 or higher (Mobile)', 'generation': 'M1+', 'variant': 'Mobile'}
                
                return None
                
            except Exception as e:
                print(f"Error detecting chip from device model: {e}")
                return None
        
        def _analyze_chip_capabilities(self):
            """Analyze capabilities of detected Apple chip"""
            if not self.chip_generation:
                return
            
            # Base capabilities for all M1+ chips
            base_capabilities = {
                'architecture': 'Apple Silicon ARM64',
                'unified_memory': True,
                'neural_engine': True,
                'metal_support': True,
                'vulkan_support': False,  # Apple uses Metal instead
                'max_resolution': '4K+',
                'hdr_support': True
            }
            
            # Generation-specific capabilities
            if self.chip_generation == 'M3':
                self.chip_capabilities = {
                    **base_capabilities,
                    'process_node': '3nm',
                    'cpu_cores': '8-24',
                    'gpu_cores': '10-40',
                    'memory_bandwidth': 'Up to 800 GB/s',
                    'performance_tier': 'Ultra High',
                    'ray_tracing': True,
                    'av1_decode': True
                }
            elif self.chip_generation == 'M2':
                self.chip_capabilities = {
                    **base_capabilities,
                    'process_node': '5nm Enhanced',
                    'cpu_cores': '8-20',
                    'gpu_cores': '8-76',
                    'memory_bandwidth': 'Up to 800 GB/s',
                    'performance_tier': 'Very High',
                    'ray_tracing': False,
                    'av1_decode': True
                }
            elif self.chip_generation == 'M1':
                self.chip_capabilities = {
                    **base_capabilities,
                    'process_node': '5nm',
                    'cpu_cores': '8-20',
                    'gpu_cores': '7-64',
                    'memory_bandwidth': 'Up to 800 GB/s',
                    'performance_tier': 'High',
                    'ray_tracing': False,
                    'av1_decode': False
                }
            
            # Variant-specific adjustments
            if self.chip_variant == 'Ultra':
                self.performance_tier = 'Maximum'
            elif self.chip_variant == 'Max':
                self.performance_tier = 'Very High'
            elif self.chip_variant == 'Pro':
                self.performance_tier = 'High'
            else:
                self.performance_tier = 'Standard'

        def _configure_renpy_for_apple_chip(self):
            """Configure Ren'Py settings optimized for Apple M1+ chips"""
            print(f"Configuring Ren'Py for {self.detected_chip}...")

            try:
                # Base settings for Apple Silicon
                if self.chip_generation in ['M1', 'M2', 'M3']:
                    settings = self._get_apple_silicon_settings()
                else:
                    settings = self._get_fallback_mobile_settings()

                self._apply_renpy_settings(settings)
                self.recommended_settings = settings

            except Exception as e:
                print(f"Error configuring Ren'Py for Apple chip: {e}")

        def _get_apple_silicon_settings(self):
            """Get optimized settings for Apple Silicon chips"""
            base_settings = {
                'renderer': 'gles2',  # Use OpenGL ES for mobile
                'vsync': True,
                'texture_scaling': 'linear',
                'framerate_limit': 60,
                'quality_preset': 'mobile_optimized'
            }

            # Performance tier specific settings
            if self.performance_tier == 'Maximum':  # M1/M2/M3 Ultra
                settings = {
                    **base_settings,
                    'max_texture_size': 4096,
                    'anisotropic_filtering': True,
                    'framerate_limit': 120,  # High refresh rate support
                    'quality_preset': 'ultra_mobile'
                }
            elif self.performance_tier == 'Very High':  # M1/M2/M3 Max
                settings = {
                    **base_settings,
                    'max_texture_size': 2048,
                    'anisotropic_filtering': True,
                    'framerate_limit': 90,
                    'quality_preset': 'high_mobile'
                }
            elif self.performance_tier == 'High':  # M1/M2/M3 Pro
                settings = {
                    **base_settings,
                    'max_texture_size': 2048,
                    'anisotropic_filtering': True,
                    'framerate_limit': 60,
                    'quality_preset': 'high_mobile'
                }
            else:  # M1/M2/M3 Base
                settings = {
                    **base_settings,
                    'max_texture_size': 1024,
                    'anisotropic_filtering': False,
                    'framerate_limit': 60,
                    'quality_preset': 'standard_mobile'
                }

            return settings

        def _get_fallback_mobile_settings(self):
            """Fallback settings for unrecognized Apple devices"""
            return {
                'renderer': 'gles2',
                'vsync': True,
                'texture_scaling': 'nearest',
                'max_texture_size': 1024,
                'anisotropic_filtering': False,
                'framerate_limit': 30,
                'quality_preset': 'safe_mobile'
            }

        def _apply_renpy_settings(self, settings):
            """Apply settings to Ren'Py configuration"""
            try:
                # Core renderer settings
                renpy.config.renderer = settings.get('renderer', 'gles2')
                renpy.config.gl_vsync = settings.get('vsync', True)

                # Texture settings
                if settings.get('texture_scaling') == 'linear':
                    renpy.config.gl_texture_scaling = True
                else:
                    renpy.config.gl_texture_scaling = False

                renpy.config.gl_maximum_texture_size = settings.get('max_texture_size', 1024)

                # Mobile-specific optimizations
                renpy.config.gl_anisotropic = settings.get('anisotropic_filtering', False)
                renpy.config.gl_framerate = settings.get('framerate_limit', 60)

                # Apple-specific optimizations
                renpy.config.gl_resize = True
                renpy.config.gl_tearing = False
                renpy.config.gl_powersave = False  # Apple Silicon is efficient

                # Memory optimizations for unified memory architecture
                renpy.config.gl_depth_size = 16  # Reduce depth buffer for mobile
                renpy.config.image_cache_size = 8  # Optimize for unified memory

                print(f"✅ Applied {settings['quality_preset']} settings for Apple Silicon")

            except Exception as e:
                print(f"Error applying Ren'Py settings: {e}")

        def _generate_apple_chip_report(self):
            """Generate comprehensive Apple chip detection report"""
            print(f"\n{'='*60}")
            print("APPLE MOBILE CHIP DETECTION REPORT")
            print(f"{'='*60}")

            # Device Information
            print(f"Device Type: Apple Mobile Device")
            print(f"Detected Chip: {self.detected_chip}")
            print(f"Chip Generation: {self.chip_generation}")
            print(f"Chip Variant: {self.chip_variant}")
            print(f"Performance Tier: {self.performance_tier}")

            # Chip Capabilities
            if self.chip_capabilities:
                print(f"\nChip Capabilities:")
                caps = self.chip_capabilities
                print(f"  Architecture: {caps.get('architecture', 'Unknown')}")
                print(f"  Process Node: {caps.get('process_node', 'Unknown')}")
                print(f"  CPU Cores: {caps.get('cpu_cores', 'Unknown')}")
                print(f"  GPU Cores: {caps.get('gpu_cores', 'Unknown')}")
                print(f"  Memory Bandwidth: {caps.get('memory_bandwidth', 'Unknown')}")
                print(f"  Unified Memory: {'Yes' if caps.get('unified_memory') else 'No'}")
                print(f"  Neural Engine: {'Yes' if caps.get('neural_engine') else 'No'}")
                print(f"  Metal Support: {'Yes' if caps.get('metal_support') else 'No'}")
                print(f"  Ray Tracing: {'Yes' if caps.get('ray_tracing') else 'No'}")
                print(f"  AV1 Decode: {'Yes' if caps.get('av1_decode') else 'No'}")

            # Applied Settings
            if self.recommended_settings:
                print(f"\nApplied Ren'Py Settings:")
                for key, value in self.recommended_settings.items():
                    print(f"  {key}: {value}")

            # Performance Recommendations
            print(f"\nPerformance Recommendations:")
            if self.performance_tier == 'Maximum':
                print("  • Excellent performance expected")
                print("  • High refresh rate gaming supported")
                print("  • 4K textures and effects recommended")
            elif self.performance_tier == 'Very High':
                print("  • Very good performance expected")
                print("  • High quality settings recommended")
                print("  • 2K textures supported")
            elif self.performance_tier == 'High':
                print("  • Good performance expected")
                print("  • Balanced quality settings applied")
                print("  • Standard mobile optimization")
            else:
                print("  • Standard performance expected")
                print("  • Conservative settings for stability")
                print("  • Power-efficient configuration")

            print(f"{'='*60}")

    # Initialize Apple chip detector
    apple_chip_detector = AppleMobileChipDetector()

    def detect_and_configure_apple_chip():
        """Main function to detect and configure Apple mobile chips"""
        return apple_chip_detector.detect_apple_mobile_chip()

    def get_apple_chip_info():
        """Get current Apple chip information"""
        return {
            'detected_chip': apple_chip_detector.detected_chip,
            'chip_generation': apple_chip_detector.chip_generation,
            'chip_variant': apple_chip_detector.chip_variant,
            'performance_tier': apple_chip_detector.performance_tier,
            'is_apple_device': apple_chip_detector.is_apple_device,
            'is_mobile': apple_chip_detector.is_mobile,
            'chip_capabilities': apple_chip_detector.chip_capabilities,
            'recommended_settings': apple_chip_detector.recommended_settings
        }

# Automatically run Apple chip detection at startup (only on Apple devices)
init:
    python:
        try:
            if platform.system() == "Darwin" or (hasattr(renpy, 'variant') and 'ios' in renpy.variant()):
                print("Starting Apple mobile chip detection...")
                detect_and_configure_apple_chip()
        except Exception as e:
            print(f"Error in Apple chip detection: {e}")

# Manual Apple chip detection label for testing
label test_apple_chip_detection:
    "Testing Apple mobile chip detection..."

    python:
        detect_and_configure_apple_chip()

    "Check the console for detailed Apple chip detection results!"
    return

# Apple chip performance check label
label check_apple_chip_performance:
    "Checking Apple chip performance configuration..."

    python:
        chip_info = get_apple_chip_info()

        if chip_info['is_apple_device']:
            narrator(f"Apple Device: {'Yes' if chip_info['is_apple_device'] else 'No'}")
            narrator(f"Mobile Device: {'Yes' if chip_info['is_mobile'] else 'No'}")

            if chip_info['detected_chip']:
                narrator(f"Detected Chip: {chip_info['detected_chip']}")
                narrator(f"Generation: {chip_info['chip_generation']}")
                narrator(f"Variant: {chip_info['chip_variant']}")
                narrator(f"Performance Tier: {chip_info['performance_tier']}")

                if chip_info['recommended_settings']:
                    narrator("Applied Settings:")
                    for key, value in chip_info['recommended_settings'].items():
                        narrator(f"  {key}: {value}")
            else:
                narrator("No Apple chip detected or unsupported model")
        else:
            narrator("Not running on Apple device")

    "Apple chip performance check complete!"
    return
