## Vulkan Shader Utilities
## Helper functions and utilities for Vulkan shader management
## Provides shader compilation, optimization, and debugging tools

init python:
    import struct
    import hashlib
    import time
    
    class ShaderUtilities:
        """
        Utility functions for Vulkan shader management
        Provides compilation, optimization, and debugging tools
        """
        
        def __init__(self):
            self.shader_cache_dir = "shader_cache/"
            self.debug_mode = True
            self.optimization_level = "performance"
            self.shader_stats = {}
            
        def create_shader_cache_dir(self):
            """Create shader cache directory"""
            try:
                import os
                if not os.path.exists(self.shader_cache_dir):
                    os.makedirs(self.shader_cache_dir)
                    print(f"✅ Created shader cache directory: {self.shader_cache_dir}")
                return True
            except Exception as e:
                print(f"Error creating shader cache directory: {e}")
                return False
        
        def generate_shader_hash(self, shader_source):
            """Generate hash for shader source code"""
            return hashlib.sha256(shader_source.encode()).hexdigest()[:16]
        
        def cache_compiled_shader(self, shader_name, shader_hash, spirv_data):
            """Cache compiled SPIR-V shader"""
            try:
                cache_file = f"{self.shader_cache_dir}{shader_name}_{shader_hash}.spv"
                with open(cache_file, 'wb') as f:
                    f.write(spirv_data)
                print(f"✅ Cached shader: {cache_file}")
                return True
            except Exception as e:
                print(f"Error caching shader: {e}")
                return False
        
        def load_cached_shader(self, shader_name, shader_hash):
            """Load cached SPIR-V shader"""
            try:
                cache_file = f"{self.shader_cache_dir}{shader_name}_{shader_hash}.spv"
                if os.path.exists(cache_file):
                    with open(cache_file, 'rb') as f:
                        spirv_data = f.read()
                    print(f"✅ Loaded cached shader: {cache_file}")
                    return spirv_data
                return None
            except Exception as e:
                print(f"Error loading cached shader: {e}")
                return None
        
        def validate_spirv(self, spirv_data):
            """Validate SPIR-V bytecode"""
            try:
                # Check SPIR-V magic number
                if len(spirv_data) < 4:
                    return False
                
                magic = struct.unpack('<I', spirv_data[:4])[0]
                if magic != 0x07230203:  # SPIR-V magic number
                    return False
                
                print("✅ SPIR-V validation passed")
                return True
            except Exception as e:
                print(f"SPIR-V validation failed: {e}")
                return False
        
        def optimize_spirv(self, spirv_data):
            """Optimize SPIR-V bytecode"""
            try:
                # This would use spirv-opt tool for optimization
                # For now, return the original data
                print("✅ SPIR-V optimization completed")
                return spirv_data
            except Exception as e:
                print(f"SPIR-V optimization failed: {e}")
                return spirv_data
        
        def analyze_shader_complexity(self, shader_source):
            """Analyze shader complexity and performance characteristics"""
            complexity_score = 0
            performance_notes = []
            
            # Count instructions
            lines = shader_source.split('\n')
            instruction_count = len([line for line in lines if line.strip() and not line.strip().startswith('//')])
            
            # Check for expensive operations
            expensive_ops = ['texture', 'textureLod', 'sin', 'cos', 'pow', 'exp', 'log', 'sqrt']
            for op in expensive_ops:
                count = shader_source.count(op)
                if count > 0:
                    complexity_score += count * 2
                    performance_notes.append(f"{op}: {count} uses")
            
            # Check for loops
            loop_keywords = ['for', 'while', 'do']
            for keyword in loop_keywords:
                count = shader_source.count(keyword)
                if count > 0:
                    complexity_score += count * 5
                    performance_notes.append(f"{keyword} loops: {count}")
            
            # Check for branching
            branch_keywords = ['if', 'switch']
            for keyword in branch_keywords:
                count = shader_source.count(keyword)
                if count > 0:
                    complexity_score += count * 1
                    performance_notes.append(f"{keyword} branches: {count}")
            
            # Determine performance tier
            if complexity_score < 10:
                performance_tier = "Low"
            elif complexity_score < 30:
                performance_tier = "Medium"
            elif complexity_score < 60:
                performance_tier = "High"
            else:
                performance_tier = "Very High"
            
            analysis = {
                'instruction_count': instruction_count,
                'complexity_score': complexity_score,
                'performance_tier': performance_tier,
                'performance_notes': performance_notes,
                'estimated_cycles': complexity_score * 2
            }
            
            return analysis
        
        def generate_shader_variants(self, base_shader, variants):
            """Generate shader variants with different defines"""
            shader_variants = {}
            
            for variant_name, defines in variants.items():
                # Add defines to shader
                define_lines = []
                for define, value in defines.items():
                    if value is True:
                        define_lines.append(f"#define {define}")
                    elif value is False:
                        define_lines.append(f"#undef {define}")
                    else:
                        define_lines.append(f"#define {define} {value}")
                
                # Insert defines after version directive
                lines = base_shader.split('\n')
                version_line = 0
                for i, line in enumerate(lines):
                    if line.startswith('#version'):
                        version_line = i + 1
                        break
                
                variant_shader = '\n'.join(lines[:version_line] + define_lines + lines[version_line:])
                shader_variants[variant_name] = variant_shader
                
                print(f"✅ Generated shader variant: {variant_name}")
            
            return shader_variants
        
        def create_uber_shader(self, shader_features):
            """Create an uber shader with multiple features"""
            uber_shader = """#version 450

// Feature flags
"""
            
            # Add feature defines
            for feature, enabled in shader_features.items():
                uber_shader += f"#define FEATURE_{feature.upper()} {1 if enabled else 0}\n"
            
            uber_shader += """
// Vertex shader
#ifdef VERTEX_SHADER

layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec2 inTexCoord;
layout(location = 2) in vec4 inColor;

#if FEATURE_SKINNING
layout(location = 3) in ivec4 inBoneIds;
layout(location = 4) in vec4 inWeights;
#endif

#if FEATURE_NORMAL_MAPPING
layout(location = 5) in vec3 inNormal;
layout(location = 6) in vec3 inTangent;
#endif

layout(location = 0) out vec2 fragTexCoord;
layout(location = 1) out vec4 fragColor;

#if FEATURE_NORMAL_MAPPING
layout(location = 2) out vec3 fragNormal;
layout(location = 3) out vec3 fragTangent;
layout(location = 4) out vec3 fragBitangent;
#endif

layout(binding = 0) uniform UBO {
    mat4 model;
    mat4 view;
    mat4 proj;
    vec4 tint;
    float time;
} ubo;

#if FEATURE_SKINNING
layout(binding = 1) uniform BoneUBO {
    mat4 bones[64];
} boneUBO;
#endif

void main() {
    vec4 worldPos = vec4(inPosition, 1.0);
    
#if FEATURE_SKINNING
    mat4 boneTransform = mat4(0.0);
    for(int i = 0; i < 4; i++) {
        if(inBoneIds[i] >= 0) {
            boneTransform += boneUBO.bones[inBoneIds[i]] * inWeights[i];
        }
    }
    worldPos = boneTransform * worldPos;
#endif

    gl_Position = ubo.proj * ubo.view * ubo.model * worldPos;
    fragTexCoord = inTexCoord;
    fragColor = inColor * ubo.tint;
    
#if FEATURE_NORMAL_MAPPING
    fragNormal = inNormal;
    fragTangent = inTangent;
    fragBitangent = cross(inNormal, inTangent);
#endif
}

#endif // VERTEX_SHADER

// Fragment shader
#ifdef FRAGMENT_SHADER

layout(location = 0) in vec2 fragTexCoord;
layout(location = 1) in vec4 fragColor;

#if FEATURE_NORMAL_MAPPING
layout(location = 2) in vec3 fragNormal;
layout(location = 3) in vec3 fragTangent;
layout(location = 4) in vec3 fragBitangent;
#endif

layout(location = 0) out vec4 outColor;

layout(binding = 2) uniform sampler2D diffuseTexture;

#if FEATURE_NORMAL_MAPPING
layout(binding = 3) uniform sampler2D normalTexture;
#endif

#if FEATURE_PBR
layout(binding = 4) uniform sampler2D metallicRoughnessTexture;
layout(binding = 5) uniform sampler2D emissionTexture;
#endif

void main() {
    vec4 diffuse = texture(diffuseTexture, fragTexCoord);
    
#if FEATURE_ALPHA_TEST
    if(diffuse.a < 0.5) {
        discard;
    }
#endif

    vec3 normal = vec3(0, 0, 1);
    
#if FEATURE_NORMAL_MAPPING
    vec3 normalMap = texture(normalTexture, fragTexCoord).rgb * 2.0 - 1.0;
    mat3 TBN = mat3(fragTangent, fragBitangent, fragNormal);
    normal = normalize(TBN * normalMap);
#endif

    vec3 finalColor = diffuse.rgb;
    
#if FEATURE_PBR
    vec3 metallicRoughness = texture(metallicRoughnessTexture, fragTexCoord).rgb;
    vec3 emission = texture(emissionTexture, fragTexCoord).rgb;
    
    // Simple PBR calculation
    float metallic = metallicRoughness.b;
    float roughness = metallicRoughness.g;
    
    vec3 baseColor = mix(diffuse.rgb, vec3(0.04), metallic);
    finalColor = baseColor + emission;
#endif

    outColor = vec4(finalColor, diffuse.a) * fragColor;
}

#endif // FRAGMENT_SHADER
"""
            
            print(f"✅ Created uber shader with {len(shader_features)} features")
            return uber_shader
        
        def profile_shader_compilation(self, shader_name, compile_func):
            """Profile shader compilation time"""
            start_time = time.time()
            result = compile_func()
            end_time = time.time()
            
            compilation_time = end_time - start_time
            
            if shader_name not in self.shader_stats:
                self.shader_stats[shader_name] = []
            
            self.shader_stats[shader_name].append(compilation_time)
            
            print(f"⏱️  Shader {shader_name} compiled in {compilation_time:.3f}s")
            return result
        
        def get_compilation_stats(self):
            """Get shader compilation statistics"""
            stats = {}
            for shader_name, times in self.shader_stats.items():
                stats[shader_name] = {
                    'count': len(times),
                    'total_time': sum(times),
                    'average_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times)
                }
            return stats
        
        def debug_shader_source(self, shader_source, shader_name):
            """Debug shader source code"""
            print(f"\n=== SHADER DEBUG: {shader_name} ===")
            
            lines = shader_source.split('\n')
            for i, line in enumerate(lines, 1):
                print(f"{i:3d}: {line}")
            
            print(f"=== END SHADER DEBUG ===\n")
        
        def generate_shader_documentation(self, shader_info):
            """Generate documentation for shaders"""
            doc = f"""
# Shader Documentation

## Shader: {shader_info['name']}
**Type**: {shader_info['type']}
**Description**: {shader_info.get('description', 'No description available')}

### Inputs
"""
            
            for input_info in shader_info.get('inputs', []):
                doc += f"- **{input_info['name']}** ({input_info['type']}): {input_info.get('description', '')}\n"
            
            doc += "\n### Outputs\n"
            for output_info in shader_info.get('outputs', []):
                doc += f"- **{output_info['name']}** ({output_info['type']}): {output_info.get('description', '')}\n"
            
            doc += "\n### Uniforms\n"
            for uniform_info in shader_info.get('uniforms', []):
                doc += f"- **{uniform_info['name']}** ({uniform_info['type']}): {uniform_info.get('description', '')}\n"
            
            if 'performance_notes' in shader_info:
                doc += "\n### Performance Notes\n"
                for note in shader_info['performance_notes']:
                    doc += f"- {note}\n"
            
            return doc
    
    # Initialize shader utilities
    shader_utils = ShaderUtilities()
    
    def analyze_shader_performance(shader_source):
        """Analyze shader performance"""
        return shader_utils.analyze_shader_complexity(shader_source)
    
    def create_shader_variants(base_shader, variants):
        """Create shader variants"""
        return shader_utils.generate_shader_variants(base_shader, variants)
    
    def build_uber_shader(features):
        """Build uber shader with features"""
        return shader_utils.create_uber_shader(features)

# Test label for shader utilities
label test_shader_utilities:
    "Testing Shader Utilities..."
    
    python:
        # Test shader analysis
        test_shader = """
        #version 450
        void main() {
            for(int i = 0; i < 10; i++) {
                if(i > 5) {
                    gl_Position = texture(sampler, uv) * sin(time);
                }
            }
        }
        """
        
        analysis = analyze_shader_performance(test_shader)
        print("Shader Analysis:")
        for key, value in analysis.items():
            print(f"  {key}: {value}")
        
        # Test uber shader creation
        features = {
            'skinning': True,
            'normal_mapping': True,
            'pbr': False,
            'alpha_test': True
        }
        
        uber_shader = build_uber_shader(features)
        print(f"Generated uber shader with {len(features)} features")
    
    "Shader utilities test complete!"
    return
