{"platform_name": "itch", "display_name": "itch.io", "detection_config": {"processes": ["itch.exe", "itch", "butler.exe", "butler"], "environment_variables": ["ITCH_GAME_URL", "ITCH_API_KEY"], "installation_paths": ["~/.config/itch", "~/Library/Application Support/itch", "~/.itch", "%APPDATA%\\itch"], "config_files": [".itch.toml", "itch.toml", ".itch/config.json"]}, "features": {"api_integration": {"enabled": true, "config_required": true}, "purchase_verification": {"enabled": true, "api_key_required": true}, "analytics": {"enabled": true, "config_file": "itch_analytics.json"}, "updates": {"enabled": true, "butler_required": true}}, "terms_of_service": {"content_rating_required": false, "self_rated": true, "age_verification": {"required_for_adult": true, "minimum_age": 18}, "content_restrictions": {"adult_content_allowed": true, "proper_tagging_required": true, "no_illegal_content": true}}, "monetization": {"pay_what_you_want": true, "free_games_allowed": true, "donations_allowed": true, "revenue_sharing": {"default_rate": 0.1, "adjustable": true}}, "technical_requirements": {"butler_integration": false, "itch_api": false, "cross_platform": true}, "analytics": {"download_tracking": true, "user_feedback": true, "rating_system": true}, "security": {"api_validation": true, "purchase_verification": true, "violation_reporting": false}}