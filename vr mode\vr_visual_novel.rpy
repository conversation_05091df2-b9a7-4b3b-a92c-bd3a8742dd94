# VR Visual Novel Interaction System
# Enhanced visual novel experience for VR with 3D character interaction

# VR Character Positioning System
init python:
    class VRCharacter:
        def __init__(self, name, model_path=None):
            self.name = name
            self.position = [0.0, 0.0, -2.0]  # 2 meters in front of player
            self.rotation = [0.0, 0.0, 0.0, 1.0]
            self.scale = 1.0
            self.visible = False
            self.model_path = model_path or f"characters/{name}_vr_model.png"
            self.emotion = "neutral"
            self.speaking = False
            
        def set_position(self, x, y, z):
            """Set character position in VR space"""
            self.position = [x, y, z]
            
        def set_emotion(self, emotion):
            """Set character emotion/expression"""
            self.emotion = emotion
            
        def show(self):
            """Show character in VR space"""
            self.visible = True
            
        def hide(self):
            """Hide character from VR space"""
            self.visible = False
    
    # Initialize VR characters
    store.vr_characters = {}
    
    def create_vr_character(name, model_path=None):
        """Create a new VR character"""
        store.vr_characters[name] = VRCharacter(name, model_path)
        return store.vr_characters[name]
    
    def show_vr_character(name, position=None, emotion="neutral"):
        """Show character in VR space"""
        if name not in store.vr_characters:
            create_vr_character(name)
        
        char = store.vr_characters[name]
        if position:
            char.set_position(*position)
        char.set_emotion(emotion)
        char.show()
        
        # Update VR scene
        renpy.restart_interaction()
    
    def hide_vr_character(name):
        """Hide character from VR space"""
        if name in store.vr_characters:
            store.vr_characters[name].hide()
            renpy.restart_interaction()

# VR Dialogue System
init python:
    class VRDialogue:
        def __init__(self):
            self.current_speaker = None
            self.dialogue_panel_position = [0.0, 0.5, -1.5]  # Floating dialogue panel
            self.choice_panels = []
            self.dialogue_visible = False
            
        def show_dialogue(self, speaker, text, choices=None):
            """Show dialogue in VR space"""
            self.current_speaker = speaker
            self.dialogue_visible = True
            
            # Position dialogue panel
            if speaker in store.vr_characters:
                char = store.vr_characters[speaker]
                # Position dialogue near character
                self.dialogue_panel_position = [
                    char.position[0],
                    char.position[1] + 0.5,
                    char.position[2] + 0.5
                ]
            
            # Show dialogue screen
            renpy.show_screen("vr_dialogue", speaker=speaker, text=text, choices=choices)
            
        def hide_dialogue(self):
            """Hide dialogue from VR space"""
            self.dialogue_visible = False
            renpy.hide_screen("vr_dialogue")
    
    # Initialize VR dialogue system
    store.vr_dialogue = VRDialogue()

# VR Dialogue Screen
screen vr_dialogue(speaker, text, choices=None):
    # Floating dialogue panel in VR space
    frame at vr_dialogue_panel:
        xpos vr_dialogue.dialogue_panel_position[0] * 100 + 400
        ypos vr_dialogue.dialogue_panel_position[1] * 100 + 200
        xsize 600
        ysize 200
        background "#000033"
        padding (20, 20)
        
        vbox:
            spacing 10
            
            if speaker:
                text "[speaker]:" style "vr_speaker_name"
            
            text "[text]" style "vr_dialogue_text"
    
    # VR choice buttons
    if choices:
        vbox:
            xpos vr_dialogue.dialogue_panel_position[0] * 100 + 400
            ypos vr_dialogue.dialogue_panel_position[1] * 100 + 420
            spacing 15
            
            for i, choice in enumerate(choices):
                textbutton choice[0] style "vr_choice_button":
                    action choice[1]
                    at vr_choice_hover

# VR Character Display Screen
screen vr_characters():
    if steamvr_mode_active:
        for name, char in vr_characters.items():
            if char.visible:
                # Character model in VR space
                add char.model_path at vr_character_transform:
                    xpos char.position[0] * 100 + 400
                    ypos char.position[1] * 100 + 300
                    zoom char.scale
                    alpha 1.0
                
                # Character emotion indicator
                if char.speaking:
                    add "#ffff00" alpha 0.3:
                        xpos char.position[0] * 100 + 390
                        ypos char.position[1] * 100 + 290
                        xsize 120
                        ysize 200

# VR Environment System
init python:
    class VREnvironment:
        def __init__(self):
            self.current_background = None
            self.environment_objects = []
            self.lighting = "normal"
            self.atmosphere = "neutral"
            
        def set_background(self, bg_path):
            """Set VR environment background"""
            self.current_background = bg_path
            renpy.show_screen("vr_environment")
            
        def add_object(self, obj_name, position, model_path):
            """Add 3D object to VR environment"""
            obj = {
                'name': obj_name,
                'position': position,
                'model': model_path,
                'visible': True
            }
            self.environment_objects.append(obj)
            
        def set_lighting(self, lighting_type):
            """Set VR environment lighting"""
            self.lighting = lighting_type
    
    # Initialize VR environment
    store.vr_environment = VREnvironment()

# VR Environment Screen
screen vr_environment():
    if steamvr_mode_active and vr_environment.current_background:
        # 360-degree background
        add vr_environment.current_background:
            xpos 0
            ypos 0
            xsize config.screen_width
            ysize config.screen_height
            alpha 0.8
        
        # Environment objects
        for obj in vr_environment.environment_objects:
            if obj['visible']:
                add obj['model']:
                    xpos obj['position'][0] * 100 + 400
                    ypos obj['position'][1] * 100 + 300
                    alpha 0.9

# VR Transforms
transform vr_dialogue_panel:
    anchor (0.5, 0.5)
    zoom 0.9
    alpha 0.95
    easein 0.3 zoom 1.0 alpha 1.0

transform vr_character_transform:
    anchor (0.5, 1.0)
    zoom 1.0
    alpha 0.0
    easein 0.5 alpha 1.0

transform vr_choice_hover:
    zoom 1.0
    hover:
        easein 0.2 zoom 1.05
    idle:
        easeout 0.2 zoom 1.0

# VR Styles for Visual Novel
init python:
    if not renpy.variant("mobile"):
        style.vr_speaker_name = Style(style.default)
        style.vr_speaker_name.size = 28
        style.vr_speaker_name.color = "#00ffff"
        style.vr_speaker_name.bold = True
        style.vr_speaker_name.outlines = [(2, "#000000", 0, 0)]
        
        style.vr_dialogue_text = Style(style.default)
        style.vr_dialogue_text.size = 24
        style.vr_dialogue_text.color = "#ffffff"
        style.vr_dialogue_text.outlines = [(2, "#000000", 0, 0)]
        style.vr_dialogue_text.text_align = 0.0
        
        style.vr_choice_button = Style(style.button)
        style.vr_choice_button.minimum = (300, 60)
        style.vr_choice_button.padding = (20, 15, 20, 15)
        style.vr_choice_button.background = "#003366"
        style.vr_choice_button.hover_background = "#0066cc"
        
        style.vr_choice_button_text = Style(style.button_text)
        style.vr_choice_button_text.size = 20
        style.vr_choice_button_text.color = "#ffffff"
        style.vr_choice_button_text.hover_color = "#ffff00"

# VR Visual Novel Functions
init python:
    def vr_say(character, text, emotion="neutral"):
        """VR version of character dialogue"""
        if steamvr_mode_active:
            # Show character in VR if not already visible
            if character not in store.vr_characters:
                create_vr_character(character)
            
            char = store.vr_characters[character]
            char.set_emotion(emotion)
            char.speaking = True
            char.show()
            
            # Show VR dialogue
            store.vr_dialogue.show_dialogue(character, text)
            
            # Wait for player interaction
            renpy.pause()
            
            char.speaking = False
            store.vr_dialogue.hide_dialogue()
        else:
            # Fallback to normal dialogue
            renpy.say(character, text)
    
    def vr_menu(choices):
        """VR version of choice menu"""
        if steamvr_mode_active:
            # Show VR choice menu
            store.vr_dialogue.show_dialogue(None, "Choose an option:", choices)
            return renpy.display_menu(choices)
        else:
            # Fallback to normal menu
            return renpy.display_menu(choices)
    
    def vr_scene(background, characters=None):
        """Set up VR scene with background and characters"""
        if steamvr_mode_active:
            # Set VR environment
            store.vr_environment.set_background(background)
            
            # Position characters
            if characters:
                for i, char_name in enumerate(characters):
                    x_pos = (i - len(characters)/2) * 1.5  # Spread characters out
                    show_vr_character(char_name, position=[x_pos, 0.0, -2.0])
        else:
            # Fallback to normal scene
            renpy.scene(background)

# VR Interaction Labels
label vr_start_visual_novel:
    """Start VR visual novel mode"""
    
    if steamvr_mode_active:
        # Initialize VR visual novel
        show screen vr_characters
        show screen vr_environment
        
        # Welcome message
        call vr_welcome_message
    
    return

label vr_welcome_message:
    """VR welcome message"""
    
    scene black
    
    if steamvr_mode_active:
        # VR-specific welcome
        python:
            vr_scene("backgrounds/vr_space.jpg")
            vr_say("System", "Welcome to VR Visual Novel mode! Use your controllers to interact with characters and make choices.")
    else:
        "Welcome to the visual novel!"
    
    return

# VR Character Interaction
label vr_character_interaction(character_name):
    """Handle VR character interaction"""
    
    if steamvr_mode_active:
        python:
            # Bring character closer for interaction
            if character_name in store.vr_characters:
                char = store.vr_characters[character_name]
                char.set_position(0.0, 0.0, -1.0)  # Move closer
                
        # Character responds to player presence
        call expression f"vr_interact_{character_name}"
    
    return

# Initialize VR visual novel variables
default vr_visual_novel_active = False
default vr_current_scene = None
