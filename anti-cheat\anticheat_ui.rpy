## Anti-Cheat User Interface and Ban System

# Anti-cheat ban screen
screen anticheat_ban_screen(reason, days_remaining):
    
    modal True
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            # Warning icon and title
            hbox:
                xalign 0.5
                spacing 20
                
                text "⚠️" size 80 color "#FF0000"
                vbox:
                    text "ANTI-CHEAT VIOLATION" size 40 color "#FF0000" bold True
                    text "ACCESS DENIED" size 30 color "#FFFFFF"
            
            # Ban information
            frame:
                background "#1A1A1A"
                padding (30, 20)
                
                vbox:
                    spacing 15
                    
                    text "Your account has been suspended for violating our anti-cheat policy." size 20 color "#FFFFFF"
                    
                    text "Violation Reason:" size 16 color "#FF9800"
                    text "[reason]" size 14 color "#FFFFFF"
                    
                    text "Suspension Duration:" size 16 color "#FF9800"
                    text "[days_remaining] days remaining" size 14 color "#FFFFFF"
                    
                    null height 10
                    
                    text "Our anti-cheat system detected unauthorized modifications or cheating tools." size 14 color "#CCCCCC"
                    text "This suspension is automatic and cannot be appealed." size 14 color "#CCCCCC"
            
            # System information
            frame:
                background "#2E2E2E"
                padding (20, 15)
                
                vbox:
                    spacing 10
                    
                    text "Anti-Cheat System Information:" size 16 color "#2196F3"
                    text "• Real-time memory protection" size 12 color "#FFFFFF"
                    text "• Process monitoring and detection" size 12 color "#FFFFFF"
                    text "• File integrity verification" size 12 color "#FFFFFF"
                    text "• Behavioral analysis engine" size 12 color "#FFFFFF"
                    text "• Kernel-level security checks" size 12 color "#FFFFFF"
            
            # Exit button
            textbutton "Exit Game" action Quit() xalign 0.5 text_size 20

# Anti-cheat status screen for debugging (developer mode only)
screen anticheat_status():
    
    if config.developer:
        frame:
            xpos 10
            ypos 10
            xsize 400
            ysize 300
            background "#000000AA"
            
            vbox:
                spacing 5
                
                text "Anti-Cheat Status" size 16 color "#00FF00"
                
                text "Initialized: {}".format(anticheat_state.get('initialized', False)) size 12
                text "Violations: {}".format(anticheat_state.get('violations', 0)) size 12
                text "Behavioral Score: {}".format(anticheat_state.get('behavioral_score', 100)) size 12
                text "Session ID: {}".format(anticheat_state.get('session_id', 'None')[:8] + "...") size 12
                text "Last Check: {:.1f}s ago".format(time.time() - anticheat_state.get('last_check', 0)) size 12
                
                null height 5
                
                if anticheat_state.get('banned', False):
                    text "STATUS: BANNED" size 14 color "#FF0000"
                    text "Reason: {}".format(anticheat_state.get('ban_reason', 'Unknown')[:30] + "...") size 10
                else:
                    text "STATUS: ACTIVE" size 14 color "#00FF00"

# Anti-cheat warning screen for first-time violations
screen anticheat_warning(violation_type):
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#1A1A1A"
        
        vbox:
            spacing 20
            
            text "⚠️ ANTI-CHEAT WARNING" size 30 color "#FF9800" xalign 0.5
            
            frame:
                background "#2E2E2E"
                padding (20, 15)
                
                vbox:
                    spacing 10
                    
                    text "Suspicious activity detected:" size 16 color "#FFFFFF"
                    text "[violation_type]" size 14 color "#FF9800"
                    
                    null height 10
                    
                    text "This is a warning. Continued violations will result in:" size 14 color "#FFFFFF"
                    text "• Immediate account suspension" size 12 color "#FFFFFF"
                    text "• Loss of game progress" size 12 color "#FFFFFF"
                    text "• Permanent ban from the game" size 12 color "#FFFFFF"
            
            text "Please ensure you are not running any unauthorized software." size 14 color "#CCCCCC" text_align 0.5
            
            textbutton "I Understand" action Return() xalign 0.5

# Anti-cheat initialization screen
screen anticheat_init():
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            text "🛡️" size 100 color "#2196F3" xalign 0.5
            
            text "INITIALIZING SECURITY SYSTEM" size 24 color "#FFFFFF" xalign 0.5
            
            vbox:
                spacing 10
                xalign 0.5
                
                text "• Loading anti-cheat engine..." size 14 color "#CCCCCC"
                text "• Verifying file integrity..." size 14 color "#CCCCCC"
                text "• Initializing memory protection..." size 14 color "#CCCCCC"
                text "• Starting process monitoring..." size 14 color "#CCCCCC"
                text "• Activating behavioral analysis..." size 14 color "#CCCCCC"
            
            text "Please wait..." size 16 color "#2196F3" xalign 0.5

# Label to initialize anti-cheat system
label initialize_anticheat:
    
    # Show initialization screen
    show screen anticheat_init
    
    # Initialize anti-cheat in background
    python:
        import time
        time.sleep(2)  # Simulate initialization time
        
        success = anticheat_core.initialize()
        
        if not success:
            renpy.call_screen("anticheat_ban_screen", 
                reason="Failed to initialize security system",
                days_remaining=30)
    
    hide screen anticheat_init
    
    return

# Anti-cheat event handlers
init python:
    
    def on_user_click():
        """Called when user clicks"""
        if anticheat_state.get('initialized', False):
            current_time = time.time()
            last_click = getattr(renpy.store, '_last_click_time', 0)
            click_interval = current_time - last_click
            
            anticheat_core.record_user_action("click", click_interval)
            renpy.store._last_click_time = current_time
    
    def on_save_game():
        """Called when game is saved"""
        if anticheat_state.get('initialized', False):
            anticheat_core.record_user_action("save")
    
    def on_load_game():
        """Called when game is loaded"""
        if anticheat_state.get('initialized', False):
            anticheat_core.record_user_action("load")
    
    def on_skip_text():
        """Called when text is skipped"""
        if anticheat_state.get('initialized', False):
            anticheat_core.record_user_action("skip")
    
    # Hook into Ren'Py events
    config.interact_callbacks.append(on_user_click)

# Anti-cheat configuration screen (developer only)
screen anticheat_config():
    
    if config.developer:
        modal True
        
        frame:
            xalign 0.5
            yalign 0.5
            xsize 600
            ysize 500
            
            vbox:
                spacing 20
                
                text "Anti-Cheat Configuration" size 24 xalign 0.5
                
                vbox:
                    spacing 10
                    
                    hbox:
                        text "Enabled:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['enabled']) action ToggleDict(ANTICHEAT_CONFIG, 'enabled')
                    
                    hbox:
                        text "Strict Mode:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['strict_mode']) action ToggleDict(ANTICHEAT_CONFIG, 'strict_mode')
                    
                    hbox:
                        text "Memory Protection:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['memory_protection']) action ToggleDict(ANTICHEAT_CONFIG, 'memory_protection')
                    
                    hbox:
                        text "Process Monitoring:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['process_monitoring']) action ToggleDict(ANTICHEAT_CONFIG, 'process_monitoring')
                    
                    hbox:
                        text "File Integrity:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['file_integrity']) action ToggleDict(ANTICHEAT_CONFIG, 'file_integrity')
                    
                    hbox:
                        text "Behavioral Analysis:" size 16
                        textbutton "{}".format(ANTICHEAT_CONFIG['behavioral_analysis']) action ToggleDict(ANTICHEAT_CONFIG, 'behavioral_analysis')
                
                hbox:
                    spacing 20
                    xalign 0.5
                    
                    textbutton "Reset Violations" action SetDict(anticheat_state, 'violations', 0)
                    textbutton "Unban User" action [SetField(persistent, 'anticheat_banned', False), SetDict(anticheat_state, 'banned', False)]
                    textbutton "Close" action Return()

# Label to show anti-cheat config (developer only)
label anticheat_config:
    if config.developer:
        call screen anticheat_config
    return
