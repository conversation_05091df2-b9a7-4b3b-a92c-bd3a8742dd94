## Gaming Settings System
## Comprehensive hardware and gaming preferences

init -10 python:
    import platform
    import subprocess
    
    # Gaming settings configuration
    gaming_settings = {
        # Graphics Settings
        'resolution': '1920x1080',
        'texture_quality': 'high',
        'shadow_quality': 'high',
        'anti_aliasing': True,
        'v_sync': True,
        'field_of_view': 90,
        'fps_limit': 60,
        'window_mode': 'fullscreen',
        'colorblind_mode': False,
        
        # Input Controls
        'mouse_sensitivity': 50,
        'mouse_acceleration': False,
        'controller_type': 'none',
        'controller_vibration': True,
        
        # Audio Settings (extended)
        'master_volume': 100,
        'sfx_volume': 100,
        'voice_volume': 100,
        'music_volume': 100,
        'subtitles_enabled': True,
        'audio_quality': 'high',
        
        # Hardware Detection
        'detected_cpu': 'Unknown',
        'detected_gpu': 'Unknown',
        'detected_ram': 'Unknown',
        'detected_monitor': 'Unknown',
        'detected_controllers': []
    }
    
    # Controller mappings
    controller_mappings = {
        'xbox': {
            'name': 'Xbox Controller',
            'buttons': {
                'a': 'confirm',
                'b': 'cancel',
                'x': 'menu',
                'y': 'quick_save',
                'lb': 'previous_choice',
                'rb': 'next_choice',
                'lt': 'auto_mode',
                'rt': 'skip',
                'start': 'pause_menu',
                'select': 'history'
            }
        },
        'playstation': {
            'name': 'PlayStation Controller',
            'buttons': {
                'cross': 'confirm',
                'circle': 'cancel',
                'square': 'menu',
                'triangle': 'quick_save',
                'l1': 'previous_choice',
                'r1': 'next_choice',
                'l2': 'auto_mode',
                'r2': 'skip',
                'options': 'pause_menu',
                'share': 'history'
            }
        },
        'nintendo': {
            'name': 'Nintendo Switch Controller',
            'buttons': {
                'a': 'confirm',
                'b': 'cancel',
                'x': 'menu',
                'y': 'quick_save',
                'l': 'previous_choice',
                'r': 'next_choice',
                'zl': 'auto_mode',
                'zr': 'skip',
                'plus': 'pause_menu',
                'minus': 'history'
            }
        },
        'maple': {
            'name': 'Maple Keyboard/Controller',
            'buttons': {
                'enter': 'confirm',
                'escape': 'cancel',
                'tab': 'menu',
                'f5': 'quick_save',
                'left_arrow': 'previous_choice',
                'right_arrow': 'next_choice',
                'space': 'auto_mode',
                'ctrl': 'skip',
                'f1': 'pause_menu',
                'h': 'history'
            }
        }
    }
    
    def detect_hardware():
        """Detect system hardware"""
        try:
            # CPU Detection
            gaming_settings['detected_cpu'] = platform.processor() or "Unknown CPU"
            
            # GPU Detection (Windows)
            if platform.system() == "Windows":
                try:
                    result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'],
                                            capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:
                            if line.strip():
                                gaming_settings['detected_gpu'] = line.strip()
                                break
                except:
                    pass
            
            # RAM Detection
            try:
                import psutil
                ram_gb = round(psutil.virtual_memory().total / (1024**3))
                gaming_settings['detected_ram'] = f"{ram_gb} GB"
            except:
                gaming_settings['detected_ram'] = "Unknown"
            
            # Monitor Detection
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'path', 'Win32_DesktopMonitor', 'get', 'ScreenWidth,ScreenHeight'],
                                            capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:
                            if line.strip() and 'ScreenHeight' not in line:
                                parts = line.strip().split()
                                if len(parts) >= 2:
                                    gaming_settings['detected_monitor'] = f"{parts[1]}x{parts[0]}"
                                    break
            except:
                pass
            
        except Exception as e:
            renpy.log("Hardware detection failed: {}".format(str(e)))
    
    def apply_graphics_settings():
        """Apply graphics settings to the game"""
        try:
            # Apply resolution
            if gaming_settings['resolution'] != 'auto':
                width, height = map(int, gaming_settings['resolution'].split('x'))
                renpy.set_physical_size((width, height))
            
            # Apply window mode
            if gaming_settings['window_mode'] == 'fullscreen':
                _preferences.fullscreen = True
            else:
                _preferences.fullscreen = False
            
            # Apply V-Sync (if supported)
            if hasattr(config, 'gl_vsync'):
                config.gl_vsync = gaming_settings['v_sync']
            
            # Apply FPS limit
            if gaming_settings['fps_limit'] > 0:
                renpy.config.framerate = gaming_settings['fps_limit']
            
        except Exception as e:
            renpy.log("Error applying graphics settings: {}".format(str(e)))
    
    def apply_audio_settings():
        """Apply audio settings"""
        try:
            # Apply volume settings
            _preferences.set_volume('music', gaming_settings['music_volume'] / 100.0)
            _preferences.set_volume('sound', gaming_settings['sfx_volume'] / 100.0)
            _preferences.set_volume('voice', gaming_settings['voice_volume'] / 100.0)
            
        except Exception as e:
            renpy.log("Error applying audio settings: {}".format(str(e)))
    
    def save_gaming_settings():
        """Save gaming settings to persistent data"""
        persistent.gaming_settings = gaming_settings.copy()
    
    def load_gaming_settings():
        """Load gaming settings from persistent data"""
        if hasattr(persistent, 'gaming_settings') and persistent.gaming_settings:
            gaming_settings.update(persistent.gaming_settings)
    
    # Load settings on startup
    load_gaming_settings()
    detect_hardware()

# Default persistent values for gaming settings
default persistent.gaming_settings = None

# Enhanced preferences screen with gaming settings
screen enhanced_preferences():
    
    tag menu
    
    use game_menu(_("Gaming Settings"), scroll="viewport"):
        
        vbox:
            spacing 20
            
            # Settings tabs
            hbox:
                spacing 10
                xalign 0.5
                
                textbutton "Graphics" action SetScreenVariable("settings_tab", "graphics") text_size 16
                textbutton "Audio" action SetScreenVariable("settings_tab", "audio") text_size 16
                textbutton "Controls" action SetScreenVariable("settings_tab", "controls") text_size 16
                textbutton "Hardware" action SetScreenVariable("settings_tab", "hardware") text_size 16
                textbutton "Basic" action SetScreenVariable("settings_tab", "basic") text_size 16
            
            null height 20
            
            # Graphics Settings Tab
            if settings_tab == "graphics":
                use graphics_settings_panel
            
            # Audio Settings Tab
            elif settings_tab == "audio":
                use audio_settings_panel
            
            # Controls Settings Tab
            elif settings_tab == "controls":
                use controls_settings_panel
            
            # Hardware Info Tab
            elif settings_tab == "hardware":
                use hardware_info_panel
            
            # Basic Settings Tab (original preferences)
            else:
                use basic_settings_panel

# Graphics settings panel
screen graphics_settings_panel():
    
    vbox:
        spacing 15
        
        text "🎮 Graphics Settings" size 20 color "#00ff00" bold True
        
        # Resolution
        hbox:
            spacing 20
            
            vbox:
                style_prefix "radio"
                label "Resolution"
                textbutton "1920x1080" action SetDict(gaming_settings, 'resolution', '1920x1080')
                textbutton "2560x1440" action SetDict(gaming_settings, 'resolution', '2560x1440')
                textbutton "3840x2160" action SetDict(gaming_settings, 'resolution', '3840x2160')
                textbutton "Auto" action SetDict(gaming_settings, 'resolution', 'auto')
            
            vbox:
                style_prefix "radio"
                label "Window Mode"
                textbutton "Fullscreen" action [SetDict(gaming_settings, 'window_mode', 'fullscreen'), Function(apply_graphics_settings)]
                textbutton "Windowed" action [SetDict(gaming_settings, 'window_mode', 'windowed'), Function(apply_graphics_settings)]
                textbutton "Borderless" action [SetDict(gaming_settings, 'window_mode', 'borderless'), Function(apply_graphics_settings)]
        
        # Quality Settings
        hbox:
            spacing 20
            
            vbox:
                style_prefix "radio"
                label "Texture Quality"
                textbutton "Low" action SetDict(gaming_settings, 'texture_quality', 'low')
                textbutton "Medium" action SetDict(gaming_settings, 'texture_quality', 'medium')
                textbutton "High" action SetDict(gaming_settings, 'texture_quality', 'high')
                textbutton "Ultra" action SetDict(gaming_settings, 'texture_quality', 'ultra')
            
            vbox:
                style_prefix "radio"
                label "Shadow Quality"
                textbutton "Low" action SetDict(gaming_settings, 'shadow_quality', 'low')
                textbutton "Medium" action SetDict(gaming_settings, 'shadow_quality', 'medium')
                textbutton "High" action SetDict(gaming_settings, 'shadow_quality', 'high')
                textbutton "Ultra" action SetDict(gaming_settings, 'shadow_quality', 'ultra')
        
        # Advanced Graphics
        vbox:
            style_prefix "check"
            label "Advanced Graphics"
            textbutton "Anti-Aliasing" action ToggleDict(gaming_settings, 'anti_aliasing')
            textbutton "V-Sync" action [ToggleDict(gaming_settings, 'v_sync'), Function(apply_graphics_settings)]
            textbutton "Colorblind Mode" action ToggleDict(gaming_settings, 'colorblind_mode')
        
        # Performance
        hbox:
            spacing 20
            
            vbox:
                style_prefix "slider"
                label "Field of View: {}°".format(gaming_settings['field_of_view'])
                bar value DictValue(gaming_settings, 'field_of_view', 120, offset=60)
            
            vbox:
                style_prefix "slider"
                label "FPS Limit: {}".format(gaming_settings['fps_limit'] if gaming_settings['fps_limit'] > 0 else "Unlimited")
                bar value DictValue(gaming_settings, 'fps_limit', 144, offset=30)

# Audio settings panel
screen audio_settings_panel():
    
    vbox:
        spacing 15
        
        text "🔊 Audio Settings" size 20 color "#00ff00" bold True
        
        # Volume Controls
        hbox:
            spacing 20
            
            vbox:
                style_prefix "slider"
                
                label "Master Volume: {}%".format(gaming_settings['master_volume'])
                bar value DictValue(gaming_settings, 'master_volume', 100)
                
                label "Music Volume: {}%".format(gaming_settings['music_volume'])
                bar value [DictValue(gaming_settings, 'music_volume', 100), Function(apply_audio_settings)]
                
                label "Sound Effects: {}%".format(gaming_settings['sfx_volume'])
                bar value [DictValue(gaming_settings, 'sfx_volume', 100), Function(apply_audio_settings)]
                
                label "Voice Volume: {}%".format(gaming_settings['voice_volume'])
                bar value [DictValue(gaming_settings, 'voice_volume', 100), Function(apply_audio_settings)]
        
        # Audio Options
        vbox:
            style_prefix "check"
            label "Audio Options"
            textbutton "Subtitles" action ToggleDict(gaming_settings, 'subtitles_enabled')
        
        # Audio Quality
        vbox:
            style_prefix "radio"
            label "Audio Quality"
            textbutton "Low" action SetDict(gaming_settings, 'audio_quality', 'low')
            textbutton "Medium" action SetDict(gaming_settings, 'audio_quality', 'medium')
            textbutton "High" action SetDict(gaming_settings, 'audio_quality', 'high')

# Controls settings panel
screen controls_settings_panel():
    
    vbox:
        spacing 15
        
        text "🎮 Input Controls" size 20 color "#00ff00" bold True
        
        # Controller Selection
        vbox:
            style_prefix "radio"
            label "Controller Type"
            textbutton "Keyboard & Mouse" action SetDict(gaming_settings, 'controller_type', 'keyboard')
            textbutton "Xbox Controller" action SetDict(gaming_settings, 'controller_type', 'xbox')
            textbutton "PlayStation Controller" action SetDict(gaming_settings, 'controller_type', 'playstation')
            textbutton "Nintendo Switch Controller" action SetDict(gaming_settings, 'controller_type', 'nintendo')
            textbutton "Maple Controller/Keyboard" action SetDict(gaming_settings, 'controller_type', 'maple')
        
        # Mouse Settings
        if gaming_settings['controller_type'] in ['keyboard', 'maple']:
            vbox:
                style_prefix "slider"
                label "Mouse Sensitivity: {}%".format(gaming_settings['mouse_sensitivity'])
                bar value DictValue(gaming_settings, 'mouse_sensitivity', 100)
            
            vbox:
                style_prefix "check"
                label "Mouse Options"
                textbutton "Mouse Acceleration" action ToggleDict(gaming_settings, 'mouse_acceleration')
        
        # Controller Settings
        if gaming_settings['controller_type'] != 'keyboard':
            vbox:
                style_prefix "check"
                label "Controller Options"
                textbutton "Vibration" action ToggleDict(gaming_settings, 'controller_vibration')
        
        # Key Bindings Display
        if gaming_settings['controller_type'] in controller_mappings:
            vbox:
                spacing 10
                
                text "Current Key Bindings:" size 16 color "#ffaa00"
                
                $ current_mapping = controller_mappings[gaming_settings['controller_type']]
                
                grid 2 5:
                    spacing 10
                    
                    for button, action in current_mapping['buttons'].items():
                        text "{}:".format(button.upper()) size 12 color "#ffffff"
                        text action.replace('_', ' ').title() size 12 color "#cccccc"

# Hardware info panel
screen hardware_info_panel():

    vbox:
        spacing 15

        text "💻 Hardware Information" size 20 color "#00ff00" bold True

        # Core Hardware
        frame:
            background "#2e2e2e"
            padding (20, 15)

            vbox:
                spacing 10

                text "Core Hardware" size 16 color "#ffaa00" bold True

                hbox:
                    spacing 20

                    vbox:
                        spacing 8
                        text "CPU:" size 14 color "#ffffff"
                        text gaming_settings['detected_cpu'] size 12 color "#cccccc"

                        text "GPU:" size 14 color "#ffffff"
                        text gaming_settings['detected_gpu'] size 12 color "#cccccc"

                    vbox:
                        spacing 8
                        text "RAM:" size 14 color "#ffffff"
                        text gaming_settings['detected_ram'] size 12 color "#cccccc"

                        text "Monitor:" size 14 color "#ffffff"
                        text gaming_settings['detected_monitor'] size 12 color "#cccccc"

        # Performance Recommendations
        frame:
            background "#1a1a1a"
            padding (20, 15)

            vbox:
                spacing 10

                text "Performance Recommendations" size 16 color "#00aaff" bold True

                if graphics_info.get('is_integrated', False):
                    text "🔧 Integrated Graphics Detected" size 14 color "#ffaa00"
                    text "• Recommended: Low-Medium settings" size 12 color "#cccccc"
                    text "• Consider lowering resolution for better performance" size 12 color "#cccccc"
                elif graphics_info.get('is_discrete', False):
                    text "🎮 Discrete Graphics Detected" size 14 color "#00ff00"
                    text "• Recommended: High-Ultra settings" size 12 color "#cccccc"
                    text "• Full resolution and effects supported" size 12 color "#cccccc"
                else:
                    text "❓ Graphics Type Unknown" size 14 color "#888888"
                    text "• Start with Medium settings and adjust as needed" size 12 color "#cccccc"

        # Refresh Hardware Info
        textbutton "🔄 Refresh Hardware Info" action Function(detect_hardware) text_size 14

# Basic settings panel (original preferences)
screen basic_settings_panel():

    vbox:
        spacing 15

        text "⚙️ Basic Settings" size 20 color "#00ff00" bold True

        # Display Settings
        hbox:
            box_wrap True
            spacing 20

            if renpy.variant("pc") or renpy.variant("web"):
                vbox:
                    style_prefix "radio"
                    label _("Display")
                    textbutton _("Window") action Preference("display", "window")
                    textbutton _("Fullscreen") action Preference("display", "fullscreen")

            vbox:
                style_prefix "check"
                label _("Skip")
                textbutton _("Unseen Text") action Preference("skip", "toggle")
                textbutton _("After Choices") action Preference("after choices", "toggle")
                textbutton _("Transitions") action InvertSelected(Preference("transitions", "toggle"))

        null height 20

        # Audio and Speed Settings
        hbox:
            style_prefix "slider"
            box_wrap True
            spacing 20

            vbox:
                label _("Text Speed")
                bar value Preference("text speed")

                label _("Auto-Forward Time")
                bar value Preference("auto-forward time")

            vbox:
                if config.has_music:
                    label _("Music Volume")
                    hbox:
                        bar value Preference("music volume")

                if config.has_sound:
                    label _("Sound Volume")
                    hbox:
                        bar value Preference("sound volume")
                        if config.sample_sound:
                            textbutton _("Test") action Play("sound", config.sample_sound)

                if config.has_voice:
                    label _("Voice Volume")
                    hbox:
                        bar value Preference("voice volume")
                        if config.sample_voice:
                            textbutton _("Test") action Play("voice", config.sample_voice)

                if config.has_music or config.has_sound or config.has_voice:
                    null height gui.pref_spacing
                    textbutton _("Mute All"):
                        action Preference("all mute", "toggle")
                        style "mute_all_button"

# Initialize settings tab variable
default settings_tab = "graphics"

# Gaming settings screen (separate from basic preferences)
screen gaming_settings():

    tag menu

    # Use the enhanced preferences for gaming settings
    use enhanced_preferences

# Settings application functions
init python:

    def apply_all_settings():
        """Apply all gaming settings"""
        apply_graphics_settings()
        apply_audio_settings()
        save_gaming_settings()
        renpy.notify("Settings applied successfully!")

    def reset_to_defaults():
        """Reset all settings to defaults"""
        global gaming_settings
        gaming_settings = {
            'resolution': '1920x1080',
            'texture_quality': 'high',
            'shadow_quality': 'high',
            'anti_aliasing': True,
            'v_sync': True,
            'field_of_view': 90,
            'fps_limit': 60,
            'window_mode': 'fullscreen',
            'colorblind_mode': False,
            'mouse_sensitivity': 50,
            'mouse_acceleration': False,
            'controller_type': 'keyboard',
            'controller_vibration': True,
            'master_volume': 100,
            'sfx_volume': 100,
            'voice_volume': 100,
            'music_volume': 100,
            'subtitles_enabled': True,
            'audio_quality': 'high'
        }
        apply_all_settings()
        renpy.notify("Settings reset to defaults!")

    def optimize_for_performance():
        """Optimize settings for performance"""
        gaming_settings.update({
            'texture_quality': 'medium',
            'shadow_quality': 'low',
            'anti_aliasing': False,
            'v_sync': False,
            'fps_limit': 30
        })
        apply_all_settings()
        renpy.notify("Optimized for performance!")

    def optimize_for_quality():
        """Optimize settings for quality"""
        gaming_settings.update({
            'texture_quality': 'ultra',
            'shadow_quality': 'ultra',
            'anti_aliasing': True,
            'v_sync': True,
            'fps_limit': 60
        })
        apply_all_settings()
        renpy.notify("Optimized for quality!")

# Settings quick access screen
screen settings_quick_access():

    frame:
        xpos 1400
        ypos 20
        xsize 200
        ysize 300
        background "#000000dd"

        vbox:
            spacing 10

            text "⚙️ Quick Settings" size 14 color "#00ff00" bold True

            textbutton "Apply All" action Function(apply_all_settings) text_size 12
            textbutton "Reset Defaults" action Function(reset_to_defaults) text_size 12
            textbutton "Performance" action Function(optimize_for_performance) text_size 12
            textbutton "Quality" action Function(optimize_for_quality) text_size 12

            null height 10

            text "Controller:" size 12 color "#ffffff"
            text gaming_settings['controller_type'].title() size 10 color "#cccccc"

            text "Resolution:" size 12 color "#ffffff"
            text gaming_settings['resolution'] size 10 color "#cccccc"

# Auto-apply settings on startup
label after_load:
    $ apply_all_settings()
    return

# Save settings on game exit
init python:
    def save_settings_on_exit():
        save_gaming_settings()

    config.quit_callbacks.append(save_settings_on_exit)
