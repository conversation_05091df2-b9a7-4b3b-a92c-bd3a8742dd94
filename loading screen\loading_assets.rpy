# Loading Screen Assets and Configuration
# Additional assets and customization for the loading screen system

# Loading screen backgrounds for different games
image loading_bg_default = "#001122"
image loading_bg_netcode = "images/backgrounds/netcode_loading.png"
image loading_bg_lumetric = "images/backgrounds/lumetric_loading.png"
image loading_bg_bearwithus = "images/backgrounds/bearwithus_loading.png"
image loading_bg_hopkin = "images/backgrounds/hopkin_loading.png"

# Loading screen animations
image loading_spinner:
    "images/gui/loading_spinner.png"
    rotate 0
    linear 1.0 rotate 360
    repeat

image loading_dots:
    "●"
    pause 0.5
    "●●"
    pause 0.5
    "●●●"
    pause 0.5
    "●●●●"
    pause 0.5
    "●●●●●"
    pause 0.5
    repeat

# Game-specific loading configurations
init python:
    
    # Loading screen configurations for each game
    loading_configs = {
        "netcode": {
            "background": "loading_bg_netcode",
            "color_scheme": "#00b4d8",
            "loading_text": "Initializing Protogen systems...",
            "duration": 3.0
        },
        "lumetric": {
            "background": "loading_bg_lumetric",
            "color_scheme": "#ff69b4",
            "loading_text": "Loading streaming protocols...",
            "duration": 2.5
        },
        "bearwithus": {
            "background": "loading_bg_bearwithus",
            "color_scheme": "#8b4513",
            "loading_text": "Preparing arcade systems...",
            "duration": 2.0
        },
        "hopkin_girls_combat": {
            "background": "loading_bg_hopkin",
            "color_scheme": "#ffd700",
            "loading_text": "Assembling combat team...",
            "duration": 3.5
        },
        "hopkin_boys_combat": {
            "background": "loading_bg_hopkin",
            "color_scheme": "#4169e1",
            "loading_text": "Preparing warrior squad...",
            "duration": 3.5
        },
        "default": {
            "background": "loading_bg_default",
            "color_scheme": "#00b4d8",
            "loading_text": "Loading game...",
            "duration": 2.0
        }
    }
    
    def get_loading_config(game_key):
        """Get loading configuration for a specific game"""
        return loading_configs.get(game_key, loading_configs["default"])

# Enhanced loading screen with game-specific customization
screen enhanced_loading_screen(game_info):
    
    # Get game-specific configuration
    python:
        config = get_loading_config(game_info["key"])
    
    # Background
    add config["background"]
    
    # Semi-transparent overlay
    add "#000000" alpha 0.4
    
    # Main loading interface
    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 600
        background "#003366" alpha 0.95
        padding (50, 50)
        
        vbox:
            spacing 40
            xalign 0.5
            
            # Game logo and title section
            frame:
                xsize 800
                ysize 150
                background "#004488"
                padding (30, 30)
                
                hbox:
                    spacing 30
                    yalign 0.5
                    
                    # Game logo/icon
                    if game_info.get("image"):
                        add game_info["image"] size (100, 100) yalign 0.5
                    else:
                        text "🎮" size 80 color config["color_scheme"] yalign 0.5
                    
                    # Game information
                    vbox:
                        spacing 10
                        yalign 0.5
                        
                        text "[game_info['display_name']]" size 32 color "#caf0f8" text_align 0.0
                        text "[game_info.get('category', '').title()]" size 18 color config["color_scheme"] text_align 0.0
                        text "[config['loading_text']]" size 16 color "#90e0ef" text_align 0.0
            
            # Progress section with enhanced visuals
            frame:
                xsize 800
                ysize 250
                background "#002244"
                padding (40, 40)
                
                vbox:
                    spacing 30
                    xalign 0.5
                    
                    # Loading title with animation
                    hbox:
                        spacing 10
                        xalign 0.5
                        
                        text "Loading" size 28 color config["color_scheme"]
                        add "loading_dots" size 28 color config["color_scheme"]
                    
                    # Enhanced progress bar with glow effect
                    frame:
                        xsize 700
                        ysize 40
                        background "#001133"
                        padding (8, 8)
                        
                        # Progress bar container
                        frame:
                            xsize 684
                            ysize 24
                            background "#003366"
                            
                            # Animated progress fill
                            frame:
                                xsize int(684 * (loading_progress / 100.0))
                                ysize 24
                                background config["color_scheme"]
                                
                                # Glow effect
                                add config["color_scheme"] alpha 0.3 size (int(684 * (loading_progress / 100.0)) + 20, 28)
                    
                    # Progress information
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        text "[loading_progress]%" size 20 color "#90e0ef"
                        text "|" size 20 color "#666666"
                        
                        # Dynamic loading status
                        if loading_progress < 20:
                            text "Initializing..." size 16 color "#90e0ef"
                        elif loading_progress < 40:
                            text "Loading assets..." size 16 color "#90e0ef"
                        elif loading_progress < 60:
                            text "Preparing world..." size 16 color "#90e0ef"
                        elif loading_progress < 80:
                            text "Setting up systems..." size 16 color "#90e0ef"
                        elif loading_progress < 95:
                            text "Finalizing..." size 16 color "#90e0ef"
                        else:
                            text "Ready!" size 16 color "#00ff88"
            
            # Game description with scrolling
            frame:
                xsize 800
                ysize 120
                background "#001133"
                padding (25, 20)
                
                viewport:
                    xsize 750
                    ysize 80
                    scrollbars "vertical" if len(game_info.get('description full', '')) > 300 else None
                    
                    text "[game_info.get('description full', 'Preparing your gaming experience...')]" size 14 color "#90e0ef" text_align 0.0

# Quick loading screen for faster games
screen quick_loading_screen(game_info):
    
    # Simple background
    add "#001122"
    
    # Centered loading message
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 200
        background "#003366"
        padding (30, 30)
        
        vbox:
            spacing 20
            xalign 0.5
            
            # Game icon
            if game_info.get("image"):
                add game_info["image"] size (60, 60) xalign 0.5
            else:
                text "🎮" size 50 color "#00b4d8" xalign 0.5
            
            # Loading message
            text "Loading [game_info['display_name']]" size 20 color "#caf0f8" xalign 0.5
            
            # Simple progress indicator
            add "loading_spinner" size (30, 30) xalign 0.5

# Loading screen with tips and hints
screen loading_screen_with_tips(game_info):
    
    # Background
    add "#001122"
    
    # Main content
    frame:
        xalign 0.5
        yalign 0.5
        xsize 1000
        ysize 700
        background "#003366"
        padding (40, 40)
        
        hbox:
            spacing 40
            
            # Left side - loading progress
            vbox:
                spacing 30
                xsize 500
                
                # Game info (same as enhanced loading screen)
                frame:
                    xsize 500
                    ysize 120
                    background "#004488"
                    padding (20, 20)
                    
                    hbox:
                        spacing 20
                        yalign 0.5
                        
                        if game_info.get("image"):
                            add game_info["image"] size (80, 80) yalign 0.5
                        else:
                            text "🎮" size 60 color "#00b4d8" yalign 0.5
                        
                        vbox:
                            spacing 5
                            yalign 0.5
                            
                            text "[game_info['display_name']]" size 24 color "#caf0f8" text_align 0.0
                            text "[game_info.get('description title', '')]" size 14 color "#90e0ef" text_align 0.0
                
                # Progress section
                frame:
                    xsize 500
                    ysize 150
                    background "#002244"
                    padding (30, 30)
                    
                    vbox:
                        spacing 20
                        xalign 0.5
                        
                        text "Loading Progress" size 18 color "#00b4d8" xalign 0.5
                        
                        # Progress bar
                        frame:
                            xsize 440
                            ysize 30
                            background "#001133"
                            padding (5, 5)
                            
                            frame:
                                xsize int(430 * (loading_progress / 100.0))
                                ysize 20
                                background "#00b4d8"
                        
                        text "[loading_progress]%" size 16 color "#90e0ef" xalign 0.5
            
            # Right side - tips and information
            vbox:
                spacing 20
                xsize 420
                
                frame:
                    xsize 420
                    ysize 300
                    background "#002244"
                    padding (20, 20)
                    
                    vbox:
                        spacing 15
                        
                        text "Game Tips" size 18 color "#00b4d8" xalign 0.5
                        
                        # Game-specific tips
                        if "hopkin" in game_info["key"]:
                            text "• Each hopkin warrior has unique abilities" size 12 color "#90e0ef"
                            text "• Explore different character paths" size 12 color "#90e0ef"
                            text "• Team coordination is key to victory" size 12 color "#90e0ef"
                        elif "netcode" in game_info["key"]:
                            text "• Save frequently to preserve progress" size 12 color "#90e0ef"
                            text "• Explore all dialogue options" size 12 color "#90e0ef"
                            text "• Multiple endings available" size 12 color "#90e0ef"
                        else:
                            text "• Take your time to enjoy the story" size 12 color "#90e0ef"
                            text "• Experiment with different choices" size 12 color "#90e0ef"
                            text "• Check the settings for preferences" size 12 color "#90e0ef"
