## Ren'Py Encryption Integration
## Seamless integration with encrypted resources and code protection

init -200 python:
    import os
    import sys
    import time
    import threading
    import importlib.util
    
    # Add encryption modules to path
    encryption_path = os.path.join(config.basedir, '.vscode')
    if encryption_path not in sys.path:
        sys.path.insert(0, encryption_path)
    
    # Import encryption modules
    try:
        import encryption_core
        import resource_manager
        
        # Initialize resource manager
        project_resource_manager = resource_manager.initialize_resource_manager(config.basedir)
        
        # Encryption configuration
        ENCRYPTION_CONFIG = {
            'enabled': True,
            'password': None,  # Will be generated dynamically
            'auto_decrypt': True,
            'cache_resources': True,
            'secure_cleanup': True,
            'log_access': True
        }
        
        # Generate project-specific password
        project_key = encryption_core.encryption_engine.generate_project_key("netcode_protogen")
        ENCRYPTION_CONFIG['password'] = project_key
        
        renpy.log("Encryption system initialized successfully")
        
    except ImportError as e:
        renpy.log("Failed to import encryption modules: {}".format(str(e)))
        project_resource_manager = None

class EncryptedResourceLoader:
    """Custom resource loader for encrypted files"""
    
    def __init__(self):
        self.loaded_resources = {}
        self.access_count = {}
        self.last_access = {}
        
    def load_encrypted_image(self, image_path):
        """Load an encrypted image file"""
        try:
            if not project_resource_manager:
                return None
            
            # Check if it's an encrypted file
            encrypted_path = image_path + '.enc'
            
            if renpy.loadable(encrypted_path):
                # Load and decrypt the image
                temp_path = project_resource_manager.load_encrypted_resource(
                    encrypted_path, 
                    ENCRYPTION_CONFIG['password']
                )
                
                if temp_path:
                    # Load the decrypted image into Ren'Py
                    image_data = renpy.load_image(temp_path)
                    
                    # Track access
                    self.access_count[image_path] = self.access_count.get(image_path, 0) + 1
                    self.last_access[image_path] = time.time()
                    
                    return image_data
            
            # Fallback to normal loading
            if renpy.loadable(image_path):
                return renpy.load_image(image_path)
            
            return None
            
        except Exception as e:
            renpy.log("Error loading encrypted image {}: {}".format(image_path, str(e)))
            return None
    
    def load_encrypted_audio(self, audio_path):
        """Load an encrypted audio file"""
        try:
            if not project_resource_manager:
                return None
            
            # Check if it's an encrypted file
            encrypted_path = audio_path + '.enc'
            
            if renpy.loadable(encrypted_path):
                # Load and decrypt the audio
                temp_path = project_resource_manager.load_encrypted_resource(
                    encrypted_path,
                    ENCRYPTION_CONFIG['password']
                )
                
                if temp_path:
                    # Track access
                    self.access_count[audio_path] = self.access_count.get(audio_path, 0) + 1
                    self.last_access[audio_path] = time.time()
                    
                    return temp_path
            
            # Fallback to normal loading
            return audio_path if renpy.loadable(audio_path) else None
            
        except Exception as e:
            renpy.log("Error loading encrypted audio {}: {}".format(audio_path, str(e)))
            return None
    
    def load_encrypted_script(self, script_path):
        """Load and execute encrypted script"""
        try:
            if not project_resource_manager:
                return False
            
            # Check if it's an encrypted file
            encrypted_path = script_path + '.enc'
            
            if os.path.exists(encrypted_path):
                # Load and decrypt the script
                temp_path = project_resource_manager.load_encrypted_resource(
                    encrypted_path,
                    ENCRYPTION_CONFIG['password']
                )
                
                if temp_path:
                    # Execute the decrypted script
                    with open(temp_path, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    
                    # Compile and execute
                    compiled_code = compile(script_content, script_path, 'exec')
                    exec(compiled_code, globals())
                    
                    # Track access
                    self.access_count[script_path] = self.access_count.get(script_path, 0) + 1
                    self.last_access[script_path] = time.time()
                    
                    return True
            
            return False
            
        except Exception as e:
            renpy.log("Error loading encrypted script {}: {}".format(script_path, str(e)))
            return False

# Initialize encrypted resource loader
encrypted_loader = EncryptedResourceLoader()

# Custom image loading function
def load_encrypted_image(image_name):
    """Load image with encryption support"""
    return encrypted_loader.load_encrypted_image(image_name)

# Custom audio loading function  
def play_encrypted_audio(audio_name, channel="music"):
    """Play audio with encryption support"""
    audio_path = encrypted_loader.load_encrypted_audio(audio_name)
    if audio_path:
        renpy.music.play(audio_path, channel=channel)
        return True
    return False

# Enhanced show statement with encryption
def show_encrypted(image_name, **kwargs):
    """Enhanced show statement for encrypted images"""
    try:
        # Try to load encrypted version first
        encrypted_path = "images/" + image_name + ".enc"
        
        if renpy.loadable(encrypted_path):
            temp_path = project_resource_manager.load_encrypted_resource(
                encrypted_path,
                ENCRYPTION_CONFIG['password']
            )
            
            if temp_path:
                # Create temporary image definition
                temp_image_name = "temp_" + image_name.replace("/", "_")
                renpy.image(temp_image_name, temp_path)
                renpy.show(temp_image_name, **kwargs)
                return True
        
        # Fallback to normal show
        renpy.show(image_name, **kwargs)
        return True
        
    except Exception as e:
        renpy.log("Error showing encrypted image {}: {}".format(image_name, str(e)))
        return False

# Code obfuscation and protection
init -190 python:
    
    def obfuscate_code(code_string):
        """Obfuscate Python code"""
        try:
            import base64
            import zlib
            
            # Compress and encode
            compressed = zlib.compress(code_string.encode('utf-8'))
            encoded = base64.b64encode(compressed).decode('ascii')
            
            # Create obfuscated wrapper
            obfuscated = f"""
import base64, zlib
exec(zlib.decompress(base64.b64decode('{encoded}')).decode('utf-8'))
"""
            return obfuscated
            
        except Exception as e:
            renpy.log("Code obfuscation error: {}".format(str(e)))
            return code_string
    
    def protect_sensitive_code(func):
        """Decorator to protect sensitive functions"""
        def wrapper(*args, **kwargs):
            # Verify anti-cheat is active
            if hasattr(renpy.store, 'anticheat_state') and not anticheat_state.get('initialized', False):
                renpy.log("Attempted access to protected function without anti-cheat")
                return None
            
            # Execute original function
            return func(*args, **kwargs)
        
        return wrapper

# Encrypted save system
init python:
    
    def save_with_encryption(slot_name, extra_info=None):
        """Save game with encryption"""
        try:
            # Perform normal save first
            renpy.save(slot_name, extra_info)
            
            # Encrypt the save file
            save_path = renpy.config.savedir + "/" + slot_name + ".save"
            
            if os.path.exists(save_path):
                encrypted_path = encryption_core.encryption_engine.encrypt_file(
                    save_path,
                    ENCRYPTION_CONFIG['password'],
                    delete_original=True
                )
                
                if encrypted_path:
                    renpy.log("Save file encrypted: {}".format(slot_name))
                    return True
            
            return False
            
        except Exception as e:
            renpy.log("Encrypted save error: {}".format(str(e)))
            return False
    
    def load_with_decryption(slot_name):
        """Load game with decryption"""
        try:
            # Check for encrypted save
            save_path = renpy.config.savedir + "/" + slot_name + ".save"
            encrypted_save_path = save_path + ".enc"
            
            if os.path.exists(encrypted_save_path):
                # Decrypt the save file
                decrypted_path = encryption_core.encryption_engine.decrypt_file(
                    encrypted_save_path,
                    ENCRYPTION_CONFIG['password'],
                    save_path
                )
                
                if decrypted_path:
                    # Load the decrypted save
                    renpy.load(slot_name)
                    
                    # Re-encrypt after loading
                    if ENCRYPTION_CONFIG['secure_cleanup']:
                        encryption_core.encryption_engine.encrypt_file(
                            save_path,
                            ENCRYPTION_CONFIG['password'],
                            delete_original=True
                        )
                    
                    renpy.log("Encrypted save loaded: {}".format(slot_name))
                    return True
            else:
                # Try normal load
                renpy.load(slot_name)
                return True
            
            return False
            
        except Exception as e:
            renpy.log("Encrypted load error: {}".format(str(e)))
            return False

# Runtime encryption monitoring
init python:
    
    def monitor_file_access():
        """Monitor file access for security"""
        if not project_resource_manager:
            return
        
        # Get access log
        access_log = project_resource_manager.get_access_log(10)
        
        # Check for suspicious patterns
        recent_accesses = [entry for entry in access_log 
                          if time.time() - entry['timestamp'] < 60]
        
        if len(recent_accesses) > 20:  # Too many accesses
            if hasattr(renpy.store, 'anticheat_core'):
                anticheat_core._report_violation("EXCESSIVE_FILE_ACCESS", 
                    "Suspicious file access pattern detected")
    
    # Schedule monitoring
    def schedule_monitoring():
        if ENCRYPTION_CONFIG['log_access']:
            monitor_file_access()
        
        # Schedule next check
        renpy.timeout(30.0, schedule_monitoring)
    
    # Start monitoring
    if ENCRYPTION_CONFIG['enabled']:
        schedule_monitoring()

# Encryption status screen (developer mode)
screen encryption_status():
    
    if config.developer:
        frame:
            xpos 10
            ypos 520
            xsize 400
            ysize 150
            background "#000000AA"
            
            vbox:
                spacing 5
                
                text "Encryption Status" size 16 color "#00FFFF"
                
                if project_resource_manager:
                    text "Resource Manager: Active" size 12 color "#00FF00"
                    text "Active Resources: {}".format(len(project_resource_manager.list_active_resources())) size 12
                    text "Temp Directory: {}".format(project_resource_manager.temp_dir[-20:] + "..." if len(project_resource_manager.temp_dir) > 20 else project_resource_manager.temp_dir) size 10
                else:
                    text "Resource Manager: Inactive" size 12 color "#FF0000"
                
                text "Encryption: {}".format("Enabled" if ENCRYPTION_CONFIG['enabled'] else "Disabled") size 12
                text "Auto-Decrypt: {}".format("Yes" if ENCRYPTION_CONFIG['auto_decrypt'] else "No") size 12

# Cleanup on game exit
init python:
    
    def cleanup_encryption_on_exit():
        """Clean up encryption resources on game exit"""
        if project_resource_manager:
            project_resource_manager.shutdown()
        
        renpy.log("Encryption cleanup completed")
    
    # Register cleanup callback
    config.quit_callbacks.append(cleanup_encryption_on_exit)

# Enhanced character definition with encryption support
define narrator_encrypted = Character(None, 
    callback=lambda event, **kwargs: encrypted_loader.access_count.update({
        'narrator_access': encrypted_loader.access_count.get('narrator_access', 0) + 1
    }) if event == "show_done" else None
)

# Encryption utility functions for game scripts
init python:
    
    def encrypt_game_data(data, key=None):
        """Encrypt arbitrary game data"""
        if not key:
            key = ENCRYPTION_CONFIG['password']
        
        return encryption_core.encryption_engine.encrypt_data(data, key)
    
    def decrypt_game_data(encrypted_data, key=None):
        """Decrypt arbitrary game data"""
        if not key:
            key = ENCRYPTION_CONFIG['password']
        
        return encryption_core.encryption_engine.decrypt_data(encrypted_data, key)
    
    def secure_store_variable(var_name, value):
        """Securely store a variable with encryption"""
        encrypted_value = encrypt_game_data(str(value))
        setattr(persistent, "encrypted_" + var_name, encrypted_value)
    
    def secure_load_variable(var_name, default=None):
        """Securely load an encrypted variable"""
        encrypted_value = getattr(persistent, "encrypted_" + var_name, None)
        
        if encrypted_value:
            decrypted_data = decrypt_game_data(encrypted_value)
            if decrypted_data:
                return decrypted_data.decode('utf-8')
        
        return default

# Integration with anti-cheat system
init python:
    
    def report_encryption_violation(violation_type, details):
        """Report encryption-related violations to anti-cheat"""
        if hasattr(renpy.store, 'anticheat_core'):
            anticheat_core._report_violation(f"ENCRYPTION_{violation_type}", details)
    
    def verify_encryption_integrity():
        """Verify encryption system integrity"""
        try:
            # Test encryption/decryption
            test_data = "encryption_test_" + str(time.time())
            encrypted = encrypt_game_data(test_data)
            decrypted = decrypt_game_data(encrypted)
            
            if decrypted and decrypted.decode('utf-8') == test_data:
                return True
            else:
                report_encryption_violation("INTEGRITY_FAILURE", "Encryption test failed")
                return False
                
        except Exception as e:
            report_encryption_violation("SYSTEM_ERROR", str(e))
            return False
    
    # Verify integrity on startup
    if ENCRYPTION_CONFIG['enabled']:
        if not verify_encryption_integrity():
            renpy.log("Encryption integrity verification failed!")

# Show encryption status in developer mode
label start:
    if config.developer:
        show screen encryption_status
    
    # Continue with normal game start
    return
