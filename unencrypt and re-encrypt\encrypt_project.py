#!/usr/bin/env python3
"""
Project Encryption Script
Encrypts all game files and code with high-grade security
"""

import os
import sys
import json
import time
import shutil
import argparse
from pathlib import Path
from encryption_core import encryption_engine, encrypt_project_files
from resource_manager import SecureResourceManager

class ProjectEncryptor:
    """Handles complete project encryption"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.backup_path = self.project_path / '.backup'
        self.config_path = self.project_path / '.vscode' / 'encryption_config.json'
        
        # Encryption configuration
        self.config = {
            'project_name': 'netcode_protogen',
            'encryption_version': '2.0',
            'created': time.time(),
            'files_encrypted': [],
            'directories_processed': [],
            'backup_created': False,
            'password_hash': None
        }
        
        # File types to encrypt
        self.encrypt_extensions = {
            'code': ['.rpy', '.py', '.pyx', '.pyi'],
            'images': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp'],
            'audio': ['.ogg', '.mp3', '.wav', '.m4a', '.flac'],
            'data': ['.json', '.txt', '.csv', '.xml'],
            'docs': ['.md', '.rst', '.txt']
        }
        
        # Directories to skip
        self.skip_directories = {
            '.git', '__pycache__', '.vscode', 'cache', 'saves', 
            'tmp', 'temp', '.backup', 'log', 'logs'
        }
        
        # Files to skip
        self.skip_files = {
            'encryption_core.py', 'resource_manager.py', 'encrypt_project.py',
            'decrypt_project.py', 'encryption_config.json', '.gitignore',
            'README.md', 'LICENSE'
        }
    
    def create_backup(self):
        """Create backup of original project"""
        try:
            if self.backup_path.exists():
                shutil.rmtree(self.backup_path)
            
            print("Creating project backup...")
            
            # Create backup directory
            self.backup_path.mkdir(exist_ok=True)
            
            # Copy all files except skip directories
            for item in self.project_path.iterdir():
                if item.name not in self.skip_directories and item != self.backup_path:
                    if item.is_file():
                        shutil.copy2(item, self.backup_path / item.name)
                    elif item.is_dir():
                        shutil.copytree(item, self.backup_path / item.name, 
                                      ignore=shutil.ignore_patterns(*self.skip_directories))
            
            self.config['backup_created'] = True
            print(f"Backup created: {self.backup_path}")
            return True
            
        except Exception as e:
            print(f"Backup creation failed: {e}")
            return False
    
    def generate_project_password(self):
        """Generate secure project-specific password"""
        project_key = encryption_engine.generate_project_key(self.config['project_name'])
        
        # Store hash for verification
        import hashlib
        self.config['password_hash'] = hashlib.sha256(project_key.encode()).hexdigest()
        
        return project_key
    
    def encrypt_file_category(self, category, extensions, password):
        """Encrypt files of a specific category"""
        encrypted_files = []
        
        print(f"Encrypting {category} files...")
        
        for ext in extensions:
            for file_path in self.project_path.rglob(f'*{ext}'):
                # Skip if in skip directories
                if any(skip_dir in file_path.parts for skip_dir in self.skip_directories):
                    continue
                
                # Skip if in skip files
                if file_path.name in self.skip_files:
                    continue
                
                # Skip already encrypted files
                if file_path.suffix == '.enc':
                    continue
                
                try:
                    print(f"  Encrypting: {file_path.relative_to(self.project_path)}")
                    
                    encrypted_path = encryption_engine.encrypt_file(
                        str(file_path), 
                        password, 
                        delete_original=True
                    )
                    
                    if encrypted_path:
                        encrypted_files.append({
                            'original': str(file_path.relative_to(self.project_path)),
                            'encrypted': str(Path(encrypted_path).relative_to(self.project_path)),
                            'category': category,
                            'size': os.path.getsize(encrypted_path),
                            'timestamp': time.time()
                        })
                    
                except Exception as e:
                    print(f"    Error encrypting {file_path}: {e}")
        
        return encrypted_files
    
    def encrypt_project(self, create_backup=True):
        """Encrypt entire project"""
        try:
            print("Starting project encryption...")
            print(f"Project: {self.project_path}")
            
            # Create backup if requested
            if create_backup:
                if not self.create_backup():
                    print("Failed to create backup. Aborting encryption.")
                    return False
            
            # Generate password
            password = self.generate_project_password()
            print(f"Generated project password: {password[:8]}...")
            
            # Encrypt files by category
            all_encrypted_files = []
            
            for category, extensions in self.encrypt_extensions.items():
                encrypted_files = self.encrypt_file_category(category, extensions, password)
                all_encrypted_files.extend(encrypted_files)
                
                print(f"  {category}: {len(encrypted_files)} files encrypted")
            
            # Update configuration
            self.config['files_encrypted'] = all_encrypted_files
            self.config['total_files'] = len(all_encrypted_files)
            self.config['completed'] = time.time()
            
            # Save configuration
            self.save_config()
            
            # Create resource manifest
            rm = SecureResourceManager(str(self.project_path))
            manifest_path = rm.create_resource_manifest(str(self.project_path))
            rm.shutdown()
            
            print(f"\nEncryption completed successfully!")
            print(f"Files encrypted: {len(all_encrypted_files)}")
            print(f"Configuration saved: {self.config_path}")
            print(f"Resource manifest: {manifest_path}")
            print(f"Backup location: {self.backup_path}")
            
            return True
            
        except Exception as e:
            print(f"Project encryption failed: {e}")
            return False
    
    def save_config(self):
        """Save encryption configuration"""
        try:
            # Ensure .vscode directory exists
            self.config_path.parent.mkdir(exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            print(f"Configuration saved: {self.config_path}")
            
        except Exception as e:
            print(f"Failed to save configuration: {e}")
    
    def load_config(self):
        """Load encryption configuration"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                return True
            return False
            
        except Exception as e:
            print(f"Failed to load configuration: {e}")
            return False
    
    def verify_encryption(self):
        """Verify encryption was successful"""
        try:
            if not self.load_config():
                print("No encryption configuration found")
                return False
            
            print("Verifying encryption...")
            
            encrypted_files = self.config.get('files_encrypted', [])
            verified_count = 0
            missing_count = 0
            
            for file_info in encrypted_files:
                encrypted_path = self.project_path / file_info['encrypted']
                
                if encrypted_path.exists():
                    verified_count += 1
                else:
                    missing_count += 1
                    print(f"  Missing: {file_info['encrypted']}")
            
            print(f"Verification complete:")
            print(f"  Verified: {verified_count}")
            print(f"  Missing: {missing_count}")
            print(f"  Success rate: {(verified_count / len(encrypted_files) * 100):.1f}%")
            
            return missing_count == 0
            
        except Exception as e:
            print(f"Verification failed: {e}")
            return False
    
    def restore_backup(self):
        """Restore project from backup"""
        try:
            if not self.backup_path.exists():
                print("No backup found")
                return False
            
            print("Restoring from backup...")
            
            # Remove encrypted files
            for item in self.project_path.iterdir():
                if item != self.backup_path and item.name != '.vscode':
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item)
            
            # Restore from backup
            for item in self.backup_path.iterdir():
                if item.is_file():
                    shutil.copy2(item, self.project_path / item.name)
                elif item.is_dir():
                    shutil.copytree(item, self.project_path / item.name)
            
            print("Backup restored successfully")
            return True
            
        except Exception as e:
            print(f"Backup restoration failed: {e}")
            return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Encrypt Visual Novel Project')
    parser.add_argument('project_path', help='Path to project directory')
    parser.add_argument('--no-backup', action='store_true', help='Skip backup creation')
    parser.add_argument('--verify', action='store_true', help='Verify encryption')
    parser.add_argument('--restore', action='store_true', help='Restore from backup')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"Project path does not exist: {args.project_path}")
        sys.exit(1)
    
    encryptor = ProjectEncryptor(args.project_path)
    
    if args.restore:
        success = encryptor.restore_backup()
    elif args.verify:
        success = encryptor.verify_encryption()
    else:
        success = encryptor.encrypt_project(create_backup=not args.no_backup)
    
    if success:
        print("Operation completed successfully")
        sys.exit(0)
    else:
        print("Operation failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
