## Build Configuration for Anti-Cheat Integration
## Ensures proper compilation and distribution compatibility

init -100 python:
    import os
    import sys
    
    # Build-time configuration
    class BuildConfig:
        """Configuration for build system integration"""
        
        def __init__(self):
            self.build_mode = "release"
            self.include_anticheat = True
            self.include_debug_info = False
            self.compress_assets = True
            
        def configure_build(self):
            """Configure build settings"""
            try:
                # Set build directory name (ASCII only, no spaces)
                if hasattr(config, 'build'):
                    config.build.directory_name = "netcode_protogen_game"
                
                # Configure anti-cheat for build
                if self.include_anticheat:
                    self._configure_anticheat_build()
                
                # Configure asset compression
                if self.compress_assets:
                    self._configure_asset_compression()
                    
                return True
            except Exception as e:
                renpy.log("Build configuration failed: {}".format(str(e)))
                return False
        
        def _configure_anticheat_build(self):
            """Configure anti-cheat system for build"""
            # Ensure anti-cheat files are included
            if hasattr(build, 'classify'):
                # Include all anti-cheat files
                build.classify('anti-cheat/**', 'all')
                
                # Archive sensitive files
                build.classify('anti-cheat/anticheat_*.rpy', 'archive')
                build.classify('game/game_router.rpy', 'archive')
        
        def _configure_asset_compression(self):
            """Configure asset compression for build"""
            if hasattr(build, 'classify'):
                # Compress image assets
                build.classify('game/images/**.png', 'archive')
                build.classify('game/images/**.jpg', 'archive')
                build.classify('game/images/**.webp', 'archive')
                
                # Compress audio assets
                build.classify('game/audio/**.ogg', 'archive')
                build.classify('game/audio/**.mp3', 'archive')
                build.classify('game/audio/**.wav', 'archive')
        
        def validate_build_compatibility(self):
            """Validate build compatibility"""
            issues = []
            
            # Check build name
            if hasattr(config, 'name'):
                name = config.name
                if ' ' in str(name) or ':' in str(name) or ';' in str(name):
                    issues.append("config.name contains invalid characters for build")
            
            # Check build directory name
            if hasattr(build, 'name'):
                build_name = build.name
                if ' ' in str(build_name) or ':' in str(build_name) or ';' in str(build_name):
                    issues.append("build.name contains invalid characters")
            
            # Check version format
            if hasattr(config, 'version'):
                version = config.version
                if ':' in str(version) or ';' in str(version):
                    issues.append("config.version contains invalid characters")
            
            return issues
    
    # Initialize build configuration
    build_config = BuildConfig()

## Build validation label
label validate_build:
    python:
        # Validate build configuration
        issues = build_config.validate_build_compatibility()
        
        if issues:
            renpy.log("Build validation issues found:")
            for issue in issues:
                renpy.log("- {}".format(issue))
        else:
            renpy.log("Build validation passed")
            
        # Configure build
        if build_config.configure_build():
            renpy.log("Build configuration completed successfully")
        else:
            renpy.log("Build configuration failed")
    
    return

## Pre-build initialization
init python:
    # Auto-configure build on startup
    try:
        if build_config.configure_build():
            renpy.log("Auto-build configuration successful")
    except:
        pass  # Ignore errors during development
