# SteamVR Advanced Functions and Integration
# Functions for eye tracking, body tracking, and haptics

# Eye Tracking Functions
init python:
    def start_eye_calibration():
        """Start eye tracking calibration process"""
        store.eye_calibration_step = 0
        store.eye_calibration_points = [
            [0.1, 0.1], [0.5, 0.1], [0.9, 0.1],  # Top row
            [0.1, 0.5], [0.5, 0.5], [0.9, 0.5],  # Middle row
            [0.1, 0.9], [0.5, 0.9], [0.9, 0.9]   # Bottom row
        ]
        renpy.show_screen("eye_calibration_process")
    
    def complete_eye_calibration():
        """Complete eye tracking calibration"""
        store.eye_tracking_calibrated = True
        renpy.hide_screen("eye_calibration_process")
        renpy.notify("Eye tracking calibrated successfully!")
    
    def update_eye_tracking():
        """Update eye tracking data (called every frame)"""
        if eye_tracking_active and eye_tracking_calibrated:
            # Simulate eye tracking data (in real implementation, this would come from eye tracker APIs)
            import random
            
            # Add some realistic eye movement
            noise_x = (random.random() - 0.5) * 0.02  # 2% screen noise
            noise_y = (random.random() - 0.5) * 0.02
            
            # Update eye tracker with simulated data
            if hasattr(store, 'eye_tracker'):
                store.eye_tracker.update_gaze(
                    store.eye_tracker.gaze_point[0] + noise_x,
                    store.eye_tracker.gaze_point[1] + noise_y,
                    0.95  # High confidence
                )
    
    def enable_foveated_rendering():
        """Enable foveated rendering based on eye tracking"""
        if eye_tracking_active and eye_tracking_calibrated:
            # In real implementation, this would optimize rendering based on gaze
            store.foveated_rendering = True
            renpy.notify("Foveated rendering enabled")
    
    def gaze_select_ui_element():
        """Select UI element based on eye gaze"""
        if eye_tracking_active and hasattr(store, 'eye_tracker'):
            current_element = store.eye_tracker.get_gaze_ui_element()
            if current_element:
                # Provide haptic feedback for gaze selection
                if advanced_haptics_active:
                    store.haptics_controller.play_haptic_pattern('selection', ['hands'], 0.3)
                return current_element
        return None

# Body Tracking Functions
init python:
    def start_body_calibration():
        """Start full body tracking calibration"""
        store.body_calibration_active = True
        store.body_calibration_timer = 5.0  # 5 second calibration
        renpy.show_screen("body_calibration_process")
    
    def complete_body_calibration():
        """Complete body tracking calibration"""
        store.body_tracking_calibrated = True
        store.body_calibration_active = False
        renpy.hide_screen("body_calibration_process")
        renpy.notify("Body tracking calibrated successfully!")
    
    def reset_body_pose():
        """Reset body tracking to default pose"""
        store.body_tracking_points = {
            'head': [0.0, 1.7, 0.0],
            'chest': [0.0, 1.4, 0.0],
            'waist': [0.0, 1.0, 0.0],
            'left_shoulder': [-0.2, 1.5, 0.0],
            'right_shoulder': [0.2, 1.5, 0.0],
            'left_elbow': [-0.3, 1.2, 0.0],
            'right_elbow': [0.3, 1.2, 0.0],
            'left_hand': [-0.4, 1.0, 0.0],
            'right_hand': [0.4, 1.0, 0.0],
            'left_hip': [-0.1, 0.9, 0.0],
            'right_hip': [0.1, 0.9, 0.0],
            'left_knee': [-0.1, 0.5, 0.0],
            'right_knee': [0.1, 0.5, 0.0],
            'left_foot': [-0.1, 0.0, 0.0],
            'right_foot': [0.1, 0.0, 0.0]
        }
        renpy.restart_interaction()
    
    def update_body_tracking():
        """Update body tracking data (called every frame)"""
        if full_body_tracking_active and body_tracking_calibrated:
            # Simulate body tracking updates
            if hasattr(store, 'body_tracker'):
                current_gesture = store.body_tracker.detect_gesture()
                current_pose = store.body_tracker.get_pose_state()
                
                # Handle gesture-based interactions
                if current_gesture == "wave":
                    handle_wave_gesture()
                elif current_gesture == "point":
                    handle_point_gesture()
                elif current_gesture == "arms_crossed":
                    handle_arms_crossed_gesture()
    
    def handle_wave_gesture():
        """Handle wave gesture"""
        if advanced_haptics_active:
            store.haptics_controller.play_haptic_pattern('notification', ['hands'], 0.8)
        renpy.notify("Wave gesture detected!")
    
    def handle_point_gesture():
        """Handle pointing gesture"""
        # Point gesture can be used for UI selection
        if advanced_haptics_active:
            store.haptics_controller.play_haptic_pattern('selection', ['hands'], 0.5)
    
    def handle_arms_crossed_gesture():
        """Handle arms crossed gesture"""
        # Could be used to show character is thinking or defensive
        if advanced_haptics_active:
            store.haptics_controller.play_haptic_pattern('ambient', ['arms', 'chest'], 0.4)

# Advanced Haptics Functions
init python:
    def test_all_haptics():
        """Test all available haptic devices"""
        if advanced_haptics_active:
            # Test each haptic zone
            haptic_zones_list = ['hands', 'arms', 'chest', 'back', 'legs', 'feet']
            
            for i, zone in enumerate(haptic_zones_list):
                # Delay each test slightly
                renpy.call_in_new_context("haptic_test_sequence", zone, i * 0.5)
    
    def play_character_haptic(character_name, emotion, intensity=0.7):
        """Play haptic feedback based on character emotion"""
        if not advanced_haptics_active:
            return
        
        emotion_patterns = {
            'happy': 'heartbeat',
            'excited': 'notification',
            'sad': 'ambient',
            'angry': 'impact',
            'surprised': 'notification'
        }
        
        pattern = emotion_patterns.get(emotion, 'ambient')
        zones = ['chest', 'hands']  # Emotional feedback zones
        
        store.haptics_controller.play_haptic_pattern(pattern, zones, intensity)
    
    def play_environmental_haptic(environment_type):
        """Play environmental haptic effects"""
        if not advanced_haptics_active:
            return
        
        environment_effects = {
            'rain': ('ambient', ['back', 'arms'], 0.3),
            'wind': ('ambient', ['back', 'chest'], 0.4),
            'explosion': ('impact', ['chest', 'back'], 1.0),
            'heartbeat': ('heartbeat', ['chest'], 0.6),
            'footsteps': ('ambient', ['feet'], 0.5)
        }
        
        if environment_type in environment_effects:
            pattern, zones, intensity = environment_effects[environment_type]
            store.haptics_controller.play_haptic_pattern(pattern, zones, intensity)
    
    def play_interaction_haptic(interaction_type, position=None):
        """Play haptic feedback for interactions"""
        if not advanced_haptics_active:
            return
        
        interaction_effects = {
            'button_click': ('selection', ['hands'], 0.6),
            'menu_hover': ('ambient', ['hands'], 0.2),
            'dialogue_advance': ('notification', ['hands'], 0.4),
            'choice_select': ('selection', ['hands'], 0.8),
            'page_turn': ('ambient', ['hands'], 0.3)
        }
        
        if interaction_type in interaction_effects:
            pattern, zones, intensity = interaction_effects[interaction_type]
            
            if position and store.spatial_haptics:
                store.haptics_controller.play_spatial_haptic(position, intensity, zones)
            else:
                store.haptics_controller.play_haptic_pattern(pattern, zones, intensity)

# VR Visual Novel Integration Functions
init python:
    def vr_enhanced_say(character, text, emotion="neutral", haptic_feedback=True):
        """Enhanced VR say function with eye tracking and haptics"""
        # Standard VR say
        vr_say(character, text, emotion)
        
        # Add haptic feedback based on character emotion
        if haptic_feedback and advanced_haptics_active:
            play_character_haptic(character, emotion)
        
        # Eye tracking integration - highlight dialogue if user is looking
        if eye_tracking_active and hasattr(store, 'eye_tracker'):
            dialogue_bounds = [100, 400, 700, 600]  # Dialogue area bounds
            if store.eye_tracker.detect_fixation(dialogue_bounds):
                # User is reading dialogue, provide subtle haptic confirmation
                if advanced_haptics_active:
                    store.haptics_controller.play_haptic_pattern('ambient', ['hands'], 0.2)
    
    def vr_enhanced_menu(choices, haptic_feedback=True):
        """Enhanced VR menu with gaze selection and haptics"""
        # Add haptic feedback for menu appearance
        if haptic_feedback and advanced_haptics_active:
            play_interaction_haptic('menu_hover')
        
        # Eye tracking menu selection
        if eye_tracking_active and gaze_interaction_enabled:
            # Allow gaze-based selection after looking at choice for 2 seconds
            pass  # Implementation would track gaze time on each choice
        
        return vr_menu(choices)
    
    def vr_character_approach(character_name, distance=1.0):
        """Move character closer in VR space with haptic feedback"""
        if character_name in store.vr_characters:
            char = store.vr_characters[character_name]
            char.set_position(0.0, 0.0, -distance)
            
            # Haptic feedback for character approach
            if advanced_haptics_active:
                # Intensity based on proximity
                intensity = max(0.2, 1.0 - distance)
                store.haptics_controller.play_spatial_haptic(char.position, intensity, ['chest'])
    
    def vr_environment_effect(effect_type, intensity=0.5):
        """Add environmental VR effects with haptics"""
        # Set VR environment effect
        store.vr_environment.atmosphere = effect_type
        
        # Add corresponding haptic effect
        play_environmental_haptic(effect_type)

# Advanced VR Update Loop
label vr_advanced_update:
    """Update loop for advanced VR features"""
    
    python:
        if steamvr_mode_active:
            # Update eye tracking
            if eye_tracking_active:
                update_eye_tracking()
            
            # Update body tracking
            if full_body_tracking_active:
                update_body_tracking()
            
            # Update haptics (cleanup finished effects)
            if advanced_haptics_active and hasattr(store, 'haptics_controller'):
                # Remove finished haptic effects
                finished_effects = []
                for zone, effect in store.haptics_controller.active_effects.items():
                    effect['duration'] -= 0.1  # Assume 10fps update
                    if effect['duration'] <= 0:
                        finished_effects.append(zone)
                
                for zone in finished_effects:
                    del store.haptics_controller.active_effects[zone]
    
    return

# Calibration Process Screens
screen eye_calibration_process():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        background "#000000"
        
        if eye_calibration_step < len(eye_calibration_points):
            $ point = eye_calibration_points[eye_calibration_step]
            $ x_pos = point[0] * 700 + 50
            $ y_pos = point[1] * 500 + 50
            
            # Calibration point
            add "#ffff00":
                xpos x_pos
                ypos y_pos
                xsize 30
                ysize 30
                alpha 1.0
            
            # Instructions
            text "Look at the yellow dot" xalign 0.5 ypos 20 color "#ffffff" size 24
            
            timer 2.0 action [SetVariable("eye_calibration_step", eye_calibration_step + 1), Return()]
        else:
            text "Calibration Complete!" xalign 0.5 yalign 0.5 color "#00ff00" size 32
            timer 1.0 action [Function(complete_eye_calibration), Return()]

screen body_calibration_process():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#001122"
        padding (30, 30)
        
        vbox:
            spacing 20
            xalign 0.5
            
            text "Body Calibration in Progress..." style "steamvr_title" xalign 0.5
            text "Hold T-pose position" style "steamvr_text" xalign 0.5
            
            # Countdown timer
            text "Time remaining: [body_calibration_timer:.1f]s" style "steamvr_text" xalign 0.5 color "#ffff00"
            
            timer 0.1 repeat True action [
                SetVariable("body_calibration_timer", body_calibration_timer - 0.1),
                If(body_calibration_timer <= 0, [Function(complete_body_calibration), Return()])
            ]

# Haptic Test Sequence
label haptic_test_sequence(zone, delay):
    $ renpy.pause(delay)
    
    if advanced_haptics_active:
        python:
            store.haptics_controller.play_haptic_pattern('notification', [zone], 0.8)
        
        "[zone.title()] haptic test activated"
    
    return

# Initialize advanced VR variables
default eye_calibration_step = 0
default eye_calibration_points = []
default body_calibration_active = False
default body_calibration_timer = 0.0
default gaze_interaction_enabled = True
default foveated_rendering = False
default spatial_haptics = True
