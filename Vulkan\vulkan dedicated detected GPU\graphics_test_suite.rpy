## Comprehensive Graphics Test Suite
## Tests all graphics configurations across platforms and GPU series
## Integrated with dedicated GPU detection and CPU integrated graphics systems

# Test images for graphics testing
image test_gradient = Solid("#FF0000", xsize=100, ysize=100)
image test_pattern = "gui/overlay/game_menu.png"  # Use existing game asset

init python:
    def get_comprehensive_gpu_info():
        """Get comprehensive GPU information from all detection systems"""
        gpu_info = {}

        # Get dedicated GPU info
        if hasattr(store, 'gpu_detector') and gpu_detector.detected_gpu:
            gpu_info.update({
                'type': 'dedicated',
                'vendor': gpu_detector.gpu_vendor,
                'model': gpu_detector.gpu_model,
                'performance_tier': gpu_detector.performance_tier,
                'within_caps': gpu_detector.is_within_caps,
                'settings': gpu_detector.recommended_settings
            })

        # Get integrated GPU info
        elif hasattr(store, 'igpu_detector') and igpu_detector.detected_igpu:
            gpu_info.update({
                'type': 'integrated',
                'vendor': igpu_detector.detected_cpu,
                'model': igpu_detector.detected_igpu,
                'capabilities': igpu_detector.igpu_capabilities,
                'settings': igpu_detector.igpu_capabilities.get('recommended_settings', {})
            })

        return gpu_info

    def run_gpu_performance_test():
        """Run comprehensive GPU performance test"""
        gpu_info = get_comprehensive_gpu_info()

        if gpu_info.get('type') == 'dedicated':
            if gpu_info.get('performance_tier') == 'capped_high':
                renpy.notify("🔒 High-end GPU detected - Performance capped to GTX 1080 level")
            elif gpu_info.get('performance_tier') == 'below_minimum':
                renpy.notify("❌ GPU below minimum requirements")
            else:
                renpy.notify("✅ GPU performance acceptable")
        elif gpu_info.get('type') == 'integrated':
            vendor = gpu_info.get('vendor', 'Unknown')
            renpy.notify(f"🔧 {vendor} integrated graphics configured")
        else:
            renpy.notify("⚠️ No compatible GPU detected")

    def refresh_all_gpu_detection():
        """Refresh both dedicated and integrated GPU detection"""
        try:
            # Refresh dedicated GPU detection
            if hasattr(store, 'detect_and_configure_dedicated_gpu'):
                detect_and_configure_dedicated_gpu()

            # Refresh integrated GPU detection
            if hasattr(store, 'detect_and_configure_igpu'):
                detect_and_configure_igpu()

            renpy.notify("🔄 GPU detection refreshed")
        except Exception as e:
            renpy.notify(f"❌ Error refreshing GPU detection: {e}")

    def launch_with_renderer(renderer_type):
        """Launch game with specific renderer"""
        try:
            if renderer_type == "gl2":
                renpy.config.renderer = "gl2"
                renpy.notify("🎮 Launched with OpenGL 2.0 renderer")
            elif renderer_type == "gl":
                renpy.config.renderer = "gl"
                renpy.notify("🎮 Launched with OpenGL renderer")
            elif renderer_type == "angle2":
                renpy.config.renderer = "angle2"
                renpy.notify("🎮 Launched with ANGLE2 renderer")
            elif renderer_type == "gles2":
                renpy.config.renderer = "gles2"
                renpy.notify("🎮 Launched with OpenGL ES 2.0 renderer")
            else:
                renpy.notify("❌ Unknown renderer type")
        except Exception as e:
            renpy.notify(f"❌ Error setting renderer: {e}")

## Main graphics test suite screen
screen graphics_test_suite():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 1200
        ysize 800
        
        vbox:
            spacing 15
            
            text "Universal Graphics Test Suite" size 32 xalign 0.5 color "#4CAF50"
            
            hbox:
                spacing 30
                
                # System Information Panel
                frame:
                    xsize 350
                    ysize 300
                    background "#1E1E1E"
                    
                    vbox:
                        spacing 10
                        
                        text "System Information" size 20 color "#2196F3"
                        
                        if hasattr(store, 'system_info') and system_info:
                            text "OS: {}".format(system_info.get('os', 'Unknown').title()) size 14
                            text "Architecture: {}".format(system_info.get('architecture', 'Unknown')) size 14
                            text "Mobile: {}".format("Yes" if system_info.get('is_mobile', False) else "No") size 14
                            text "Recommended: {}".format(system_info.get('recommended_renderer', 'auto').upper()) size 14
                        else:
                            text "System detection not available" size 14
                        
                        null height 10
                        
                        if hasattr(store, 'mobile_info') and mobile_info and system_info.get('is_mobile', False):
                            text "Mobile Platform Details:" size 16 color "#FF9800"
                            text "Platform: {}".format(mobile_info.get('platform', 'Unknown').title()) size 12
                            text "Performance: {}".format(mobile_info.get('performance_class', 'Unknown').title()) size 12
                            text "Battery Opt: {}".format("Yes" if mobile_info.get('battery_optimization', True) else "No") size 12
                
                # GPU Information Panel
                frame:
                    xsize 350
                    ysize 300
                    background "#1E1E1E"

                    vbox:
                        spacing 10

                        text "GPU Information" size 20 color "#9C27B0"

                        python:
                            comprehensive_gpu_info = get_comprehensive_gpu_info()

                        if comprehensive_gpu_info:
                            text "Type: {}".format(comprehensive_gpu_info.get('type', 'Unknown').title()) size 14
                            text "Vendor: {}".format(comprehensive_gpu_info.get('vendor', 'Unknown')) size 14
                            text "Model: {}".format(comprehensive_gpu_info.get('model', 'Unknown')[:25] + "..." if len(comprehensive_gpu_info.get('model', '')) > 25 else comprehensive_gpu_info.get('model', 'Unknown')) size 12

                            if comprehensive_gpu_info.get('type') == 'dedicated':
                                text "Performance: {}".format(comprehensive_gpu_info.get('performance_tier', 'Unknown').title()) size 14
                                text "Within Caps: {}".format("Yes" if comprehensive_gpu_info.get('within_caps', False) else "No") size 14

                                null height 5

                                # Performance tier indicator for dedicated GPUs
                                if comprehensive_gpu_info.get('performance_tier') == 'capped_high':
                                    text "🔒 Performance Capped" size 14 color "#FF9800"
                                elif comprehensive_gpu_info.get('performance_tier') == 'acceptable':
                                    text "✅ Performance Good" size 14 color "#4CAF50"
                                elif comprehensive_gpu_info.get('performance_tier') == 'below_minimum':
                                    text "❌ Below Minimum" size 14 color "#F44336"
                                else:
                                    text "⚠️ Limited Performance" size 14 color "#FFC107"

                            elif comprehensive_gpu_info.get('type') == 'integrated':
                                caps = comprehensive_gpu_info.get('capabilities', {})
                                text "Generation: {}".format(caps.get('generation', 'Unknown')) size 14
                                text "Max Resolution: {}".format(caps.get('max_resolution', 'Unknown')) size 12
                                text "Vulkan: {}".format(caps.get('vulkan_support', 'Unknown')) size 12
                        else:
                            text "GPU detection not available" size 14
                            text "Run detection to get GPU info" size 12
                
                # Renderer Status Panel
                frame:
                    xsize 350
                    ysize 300
                    background "#1E1E1E"
                    
                    vbox:
                        spacing 10
                        
                        text "Renderer Status" size 20 color "#F44336"
                        
                        try:
                            $ renderer_info = renpy.get_renderer_info()
                            if renderer_info:
                                text "Active: {}".format(renderer_info.get('renderer', 'Unknown')) size 14
                                text "Vendor: {}".format(renderer_info.get('vendor', 'Unknown')) size 14
                                text "Version: {}".format(renderer_info.get('version', 'Unknown')[:30] + "..." if len(renderer_info.get('version', '')) > 30 else renderer_info.get('version', 'Unknown')) size 12
                            else:
                                text "Renderer info not available" size 14
                        except:
                            text "Renderer detection failed" size 14
                        
                        null height 10
                        
                        text "Vulkan Information:" size 16 color "#673AB7"
                        text "Ren'Py uses OpenGL-based rendering" size 12
                        text "Vulkan detection optimizes settings" size 12
                        text "for Vulkan-capable hardware" size 12
            
            null height 20
            
            # Test Controls
            frame:
                xfill True
                background "#2E2E2E"
                
                vbox:
                    spacing 15
                    
                    text "Graphics Tests" size 24 xalign 0.5 color "#4CAF50"
                    
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        vbox:
                            spacing 10
                            text "GPU Detection:" size 16
                            textbutton "Refresh All Detection" action Function(refresh_all_gpu_detection)
                            textbutton "GPU Performance Test" action Function(run_gpu_performance_test)
                            textbutton "Dedicated GPU Test" action Function(detect_and_configure_dedicated_gpu) if hasattr(store, 'detect_and_configure_dedicated_gpu') else NullAction()
                            textbutton "Integrated GPU Test" action Function(detect_and_configure_igpu) if hasattr(store, 'detect_and_configure_igpu') else NullAction()

                        vbox:
                            spacing 10
                            text "Renderer Tests:" size 16
                            textbutton "Launch with GL2" action Function(launch_with_renderer, "gl2")
                            textbutton "Launch with GL" action Function(launch_with_renderer, "gl")
                            textbutton "Launch with ANGLE2" action Function(launch_with_renderer, "angle2")
                            textbutton "Launch with GLES2" action Function(launch_with_renderer, "gles2")
                            textbutton "Benchmark Mode" action Jump("graphics_benchmark")

                        vbox:
                            spacing 10
                            text "Advanced Tests:" size 16
                            textbutton "Vulkan iGPU Config" action Function(configure_vulkan_igpu) if hasattr(store, 'configure_vulkan_igpu') else NullAction()
                            textbutton "Current Renderer Info" action Function(check_renderer_performance) if hasattr(store, 'check_renderer_performance') else NullAction()
                            textbutton "Switch Renderer (Shift+G)" action NullAction()
                            textbutton "Performance Report" action Jump("gpu_performance_report")
            
            null height 20
            
            hbox:
                spacing 30
                xalign 0.5
                
                textbutton "Close Test Suite" action Return()
                textbutton "Launch Game" action [Return(), Jump("main_story")]

## Graphics benchmark test
label graphics_benchmark:
    
    scene black
    
    "Starting graphics benchmark..."
    "This will test rendering performance across different scenarios."
    
    # Test 1: Simple rendering
    scene bg black
    show test_gradient at center
    "Test 1: Basic rendering - Simple solid color"
    
    # Test 2: Transition test
    scene bg black
    show test_gradient at center
    with dissolve
    "Test 2: Transition rendering - Dissolve effect"
    
    # Test 3: Multiple objects
    scene bg black
    show test_gradient at left
    show test_gradient at center
    show test_gradient at right
    "Test 3: Multiple objects - Stress test"
    
    # Test 4: Text rendering
    scene bg black
    "Test 4: Text rendering performance"
    "This tests the text rendering system with various effects and formatting."
    "Multiple lines of text to test performance under load."
    
    scene bg black
    "Benchmark complete!"
    "Check the performance notifications for results."
    
    $ check_renderer_performance()
    
    return

## GPU Performance Report
label gpu_performance_report:
    scene black

    "GPU Performance Report"
    "Generating comprehensive GPU analysis..."

    python:
        gpu_info = get_comprehensive_gpu_info()

        if gpu_info.get('type') == 'dedicated':
            narrator(f"Dedicated GPU Detected: {gpu_info.get('vendor')} {gpu_info.get('model')}")
            narrator(f"Performance Tier: {gpu_info.get('performance_tier')}")
            narrator(f"Within Performance Caps: {'Yes' if gpu_info.get('within_caps') else 'No'}")

            if gpu_info.get('vendor') == 'NVIDIA':
                narrator("NVIDIA Performance Cap: GTX 1080 maximum")
                if gpu_info.get('performance_tier') == 'capped_high':
                    narrator("🔒 Your GPU performance is limited to GTX 1080 level")
            elif gpu_info.get('vendor') == 'AMD':
                narrator("AMD Performance Requirement: RX 560 minimum")
                if not gpu_info.get('within_caps'):
                    narrator("❌ Your GPU does not meet RX 560 minimum requirement")
            elif gpu_info.get('vendor') == 'Intel':
                narrator("Intel Arc GPU: Generally acceptable performance")

            settings = gpu_info.get('settings', {})
            if settings:
                narrator("Applied Settings:")
                for key, value in settings.items():
                    narrator(f"  {key}: {value}")

        elif gpu_info.get('type') == 'integrated':
            narrator(f"Integrated Graphics Detected: {gpu_info.get('vendor')} {gpu_info.get('model')}")
            caps = gpu_info.get('capabilities', {})
            if caps:
                narrator(f"Generation: {caps.get('generation', 'Unknown')}")
                narrator(f"Architecture: {caps.get('architecture', 'Unknown')}")
                narrator(f"Max Resolution: {caps.get('max_resolution', 'Unknown')}")
                narrator(f"Vulkan Support: {caps.get('vulkan_support', 'Unknown')}")

        else:
            narrator("No compatible GPU detected")
            narrator("Using fallback graphics settings")

    "Performance report complete!"
    return

## Enhanced Graphics Benchmark
label graphics_benchmark:
    scene black

    "Starting Enhanced Graphics Benchmark..."
    "This will test rendering performance with your detected GPU configuration."

    python:
        gpu_info = get_comprehensive_gpu_info()
        if gpu_info:
            narrator(f"Testing with: {gpu_info.get('vendor')} {gpu_info.get('type')} graphics")
        else:
            narrator("Testing with: Unknown graphics configuration")

    # Test 1: Basic rendering with GPU info
    scene bg black
    show test_gradient at center
    "Test 1: Basic rendering - GPU compatibility test"

    # Test 2: Performance tier specific test
    python:
        if gpu_info.get('performance_tier') == 'capped_high':
            narrator("Running high-end GPU test (performance capped)")
        elif gpu_info.get('performance_tier') == 'below_minimum':
            narrator("Running minimum performance test")
        else:
            narrator("Running standard performance test")

    # Test 3: Transition test
    scene bg black
    show test_gradient at center
    with dissolve
    "Test 2: Transition rendering - Dissolve effect"

    # Test 4: Multiple objects stress test
    scene bg black
    show test_gradient at left
    show test_gradient at center
    show test_gradient at right
    "Test 3: Multiple objects - GPU stress test"

    # Test 5: Text rendering performance
    scene bg black
    "Test 4: Text rendering performance"
    "This tests the text rendering system with your GPU configuration."
    "Multiple lines of text to test performance under load."
    "GPU-optimized text rendering active."

    scene bg black
    "Enhanced benchmark complete!"

    python:
        run_gpu_performance_test()

    "Check the performance notifications and console for detailed results."
    return

## Label to launch the comprehensive test suite
label graphics_test_suite:
    call screen graphics_test_suite
    return
