## Legal Document Protection System
## Protects TOS, EULA, Policies, and Copyright information

init -95 python:
    import os
    import hashlib
    import time
    import json
    import platform
    import uuid
    import subprocess
    
    # Legal protection configuration
    LEGAL_CONFIG = {
        'enabled': True,
        'strict_mode': True,
        'developer_verification': True,
        'auto_create_missing': True,
        'violation_ban': True,
        'check_interval': 60,  # Check every minute
        'require_acceptance': True
    }
    
    # Legal protection state
    legal_state = {
        'documents_verified': False,
        'developer_authenticated': False,
        'violations_detected': 0,
        'last_check': 0,
        'missing_documents': [],
        'tampered_documents': [],
        'user_accepted_terms': False
    }

class LegalDocumentProtector:
    """Protects legal documents and detects violations"""
    
    def __init__(self):
        self.legal_directory = os.path.join(config.basedir, "legal_documents")
        self.developer_signature = self._generate_developer_signature()
        self.document_hashes = {}
        self.required_documents = [
            'terms_of_service.txt',
            'end_user_license_agreement.txt',
            'privacy_policy.txt',
            'content_policy.txt',
            'partnership_policy.txt',
            'copyright_notice.txt',
            'dmca_policy.txt',
            'community_guidelines.txt',
            'refund_policy.txt',
            'data_protection_policy.txt'
        ]
        
    def _generate_developer_signature(self):
        """Generate unique developer computer signature"""
        try:
            # Collect system information
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version()
            }
            
            # Get MAC address
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            system_info['mac_address'] = mac
            
            # Get CPU info (Windows)
            try:
                if platform.system() == 'Windows':
                    import winreg
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                       r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                    cpu_name = winreg.QueryValueEx(key, "ProcessorNameString")[0]
                    system_info['cpu_name'] = cpu_name
                    winreg.CloseKey(key)
            except:
                pass
            
            # Create signature
            signature_string = json.dumps(system_info, sort_keys=True)
            signature_hash = hashlib.sha256(signature_string.encode()).hexdigest()
            
            return signature_hash
            
        except Exception as e:
            renpy.log("Developer signature generation failed: {}".format(str(e)))
            return "unknown_developer"
    
    def _verify_developer_computer(self):
        """Verify this is the developer's computer"""
        try:
            # Expected developer signature (replace with your actual signature)
            expected_signatures = [
                # Add your developer computer signatures here
                "your_developer_signature_hash_here",
                # You can add multiple computers for development team
            ]
            
            current_signature = self._generate_developer_signature()
            
            if current_signature in expected_signatures:
                legal_state['developer_authenticated'] = True
                renpy.log("Developer computer authenticated")
                return True
            else:
                renpy.log("Unauthorized computer detected - Signature: {}".format(current_signature[:16] + "..."))
                return False
                
        except Exception as e:
            renpy.log("Developer verification failed: {}".format(str(e)))
            return False
    
    def _ensure_legal_directory(self):
        """Ensure legal documents directory exists"""
        if not os.path.exists(self.legal_directory):
            os.makedirs(self.legal_directory)
            renpy.log("Created legal documents directory: {}".format(self.legal_directory))
    
    def _create_terms_of_service(self):
        """Create comprehensive Terms of Service"""
        content = """TERMS OF SERVICE
"Netcode the Protogen and More" Visual Novel

Last Updated: {}

1. ACCEPTANCE OF TERMS
By downloading, installing, accessing, or using "Netcode the Protogen and More" (the "Game"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, do not use the Game.

2. LICENSE GRANT
Subject to your compliance with these Terms, we grant you a limited, non-exclusive, non-transferable, revocable license to use the Game for personal, non-commercial purposes.

3. RESTRICTIONS
You may NOT:
- Reverse engineer, decompile, disassemble, or attempt to derive source code
- Modify, adapt, alter, translate, or create derivative works
- Remove, alter, or obscure any copyright, trademark, or proprietary notices
- Use the Game for commercial purposes without written permission
- Distribute, sell, rent, lease, or sublicense the Game
- Use cheats, exploits, automation software, bots, hacks, mods, or unauthorized third-party software
- Attempt to circumvent any security measures or access controls

4. INTELLECTUAL PROPERTY
All content, including but not limited to text, graphics, images, audio, video, software, and other materials in the Game are owned by us or our licensors and are protected by copyright, trademark, and other intellectual property laws.

5. ANTI-CHEAT AND MONITORING
The Game includes anti-cheat technology that may:
- Monitor your system for unauthorized modifications
- Collect information about your hardware and software
- Automatically ban accounts for violations
- Report violations to our servers

6. PRIVACY AND DATA COLLECTION
Your privacy is important to us. Please review our Privacy Policy for information about how we collect, use, and protect your data.

7. PROHIBITED CONDUCT
You agree not to:
- Violate any applicable laws or regulations
- Infringe upon the rights of others
- Engage in harassment, abuse, or harmful behavior
- Attempt to gain unauthorized access to our systems
- Interfere with the Game's operation or other users' enjoyment

8. CONTENT POLICY
The Game may contain mature content. By using the Game, you confirm that you meet the minimum age requirements in your jurisdiction.

9. TERMINATION
We may terminate or suspend your access to the Game at any time, with or without cause, with or without notice.

10. DISCLAIMERS
THE GAME IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND. WE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.

11. LIMITATION OF LIABILITY
TO THE MAXIMUM EXTENT PERMITTED BY LAW, WE SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES.

12. GOVERNING LAW
These Terms are governed by the laws of [Your Jurisdiction], without regard to conflict of law principles.

13. CHANGES TO TERMS
We reserve the right to modify these Terms at any time. Continued use of the Game constitutes acceptance of modified Terms.

14. CONTACT INFORMATION
For questions about these Terms, contact us at: [Your Contact Information]

By using the Game, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.

© {} All Rights Reserved.
""".format(time.strftime("%Y-%m-%d"), time.strftime("%Y"))
        
        return content
    
    def _create_eula(self):
        """Create End User License Agreement"""
        content = """END USER LICENSE AGREEMENT (EULA)
"Netcode the Protogen and More" Visual Novel

IMPORTANT - READ CAREFULLY: This End User License Agreement ("EULA") is a legal agreement between you and the developer of "Netcode the Protogen and More" (the "Software").

1. GRANT OF LICENSE
This EULA grants you the following rights:
- Installation and Use: You may install and use one copy of the Software on a single computer.
- Backup Copy: You may make one backup copy of the Software for archival purposes.

2. DESCRIPTION OF OTHER RIGHTS AND LIMITATIONS
- Limitations on Reverse Engineering: You may not reverse engineer, decompile, or disassemble the Software.
- Separation of Components: The Software is licensed as a single product and may not be separated.
- Rental: You may not rent, lease, or lend the Software.
- Software Transfer: You may permanently transfer all rights under this EULA only as part of a permanent sale.

3. COPYRIGHT
All title and copyrights in and to the Software are owned by the developer. The Software is protected by copyright laws and international treaty provisions.

4. ANTI-CHEAT TECHNOLOGY
The Software includes anti-cheat technology that monitors for unauthorized modifications and may collect system information for security purposes.

5. NO WARRANTIES
The Software is provided "AS IS" without warranty of any kind.

6. LIMITATION OF LIABILITY
In no event shall the developer be liable for any damages whatsoever arising out of the use of or inability to use this Software.

7. TERMINATION
Without prejudice to any other rights, the developer may terminate this EULA if you fail to comply with its terms.

By installing or using the Software, you agree to be bound by the terms of this EULA.

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        
        return content
    
    def _create_privacy_policy(self):
        """Create Privacy Policy"""
        content = """PRIVACY POLICY
"Netcode the Protogen and More" Visual Novel

Last Updated: {}

1. INFORMATION WE COLLECT
We may collect the following types of information:
- System Information: Hardware specifications, operating system details
- Game Data: Save files, progress, settings, preferences
- Anti-Cheat Data: System monitoring for security purposes
- Usage Analytics: How you interact with the Game

2. HOW WE USE INFORMATION
We use collected information to:
- Provide and maintain the Game
- Detect and prevent cheating and unauthorized modifications
- Improve Game performance and user experience
- Comply with legal obligations

3. INFORMATION SHARING
We do not sell, trade, or rent your personal information to third parties.
We may share information in the following circumstances:
- With your consent
- To comply with legal requirements
- To protect our rights and safety

4. DATA SECURITY
We implement appropriate security measures to protect your information against unauthorized access, alteration, disclosure, or destruction.

5. DATA RETENTION
We retain information only as long as necessary for the purposes outlined in this policy or as required by law.

6. YOUR RIGHTS
You may have the right to:
- Access your personal information
- Correct inaccurate information
- Request deletion of your information
- Object to processing of your information

7. CHILDREN'S PRIVACY
The Game is not intended for children under 13. We do not knowingly collect personal information from children under 13.

8. CHANGES TO POLICY
We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy.

9. CONTACT US
If you have questions about this Privacy Policy, contact us at: [Your Contact Information]

© {} All Rights Reserved.
""".format(time.strftime("%Y-%m-%d"), time.strftime("%Y"))
        
        return content
    
    def _create_copyright_notice(self):
        """Create Copyright Notice"""
        content = """COPYRIGHT NOTICE
"Netcode the Protogen and More" Visual Novel

© {} All Rights Reserved.

OWNERSHIP
All content in "Netcode the Protogen and More" including but not limited to:
- Source code and scripts
- Artwork, graphics, and visual assets
- Audio, music, and sound effects
- Characters, storylines, and narrative content
- User interface and design elements
- Documentation and written materials

Is the exclusive property of the developer and is protected by:
- United States Copyright Law
- International copyright treaties
- Trademark laws
- Trade secret laws

UNAUTHORIZED USE PROHIBITED
Any unauthorized use, reproduction, distribution, modification, or creation of derivative works is strictly prohibited and may result in:
- Immediate termination of license
- Legal action for copyright infringement
- Monetary damages and attorney fees
- Criminal prosecution where applicable

FAIR USE
Limited fair use may be permitted for:
- Educational purposes (with proper attribution)
- Non-commercial criticism or review
- Parody (within legal bounds)
- News reporting

DMCA COMPLIANCE
We respect intellectual property rights and comply with the Digital Millennium Copyright Act (DMCA).

REPORTING INFRINGEMENT
To report copyright infringement, contact us at: [Your DMCA Contact]

TRADEMARK NOTICE
"Netcode the Protogen and More" and associated logos are trademarks of the developer.

THIRD-PARTY CONTENT
Some content may be licensed from third parties and is subject to their respective copyright and licensing terms.

This notice serves as formal notification of our copyright claims and legal rights.

© {} All Rights Reserved.
""".format(time.strftime("%Y"), time.strftime("%Y"))
        
        return content
    
    def _create_partnership_policy(self):
        """Create Partnership Policy"""
        content = """PARTNERSHIP POLICY
"Netcode the Protogen and More" Visual Novel

Last Updated: {}

1. CONTENT CREATION PARTNERSHIPS
We welcome partnerships with content creators, streamers, and influencers under the following terms:

PERMITTED ACTIVITIES:
- Streaming gameplay with commentary
- Creating review and critique content
- Educational content about visual novels
- Non-commercial fan art (with proper attribution)

REQUIREMENTS:
- Proper attribution and credit
- Compliance with content guidelines
- Age-appropriate presentation
- Respect for intellectual property

2. COMMERCIAL PARTNERSHIPS
Commercial partnerships require written agreement and may include:
- Sponsored content arrangements
- Affiliate marketing programs
- Distribution partnerships
- Licensing agreements

3. PROHIBITED PARTNERSHIPS
We do not permit partnerships involving:
- Piracy or unauthorized distribution
- Circumvention of anti-cheat measures
- Modification or reverse engineering
- Adult content using our characters without permission

4. REVENUE SHARING
Content creators may monetize their content featuring our Game under these conditions:
- Content adds substantial commentary or educational value
- Proper attribution is provided
- Content complies with platform guidelines
- No direct redistribution of game assets

5. TRADEMARK USAGE
Use of our trademarks in partnership content must:
- Be clearly identified as trademarks
- Not suggest official endorsement without agreement
- Comply with trademark usage guidelines
- Include proper disclaimers

6. TERMINATION
We reserve the right to terminate partnership arrangements for:
- Violation of these policies
- Inappropriate content or behavior
- Legal or safety concerns
- Business reasons

7. APPLICATION PROCESS
To apply for partnership:
1. Submit partnership proposal
2. Provide portfolio of previous work
3. Demonstrate audience engagement
4. Agree to partnership terms

8. CONTACT
For partnership inquiries: [Partnership Contact]

© {} All Rights Reserved.
""".format(time.strftime("%Y-%m-%d"), time.strftime("%Y"))
        
        return content
    
    def create_missing_documents(self):
        """Create any missing legal documents"""
        try:
            self._ensure_legal_directory()
            
            document_creators = {
                'terms_of_service.txt': self._create_terms_of_service,
                'end_user_license_agreement.txt': self._create_eula,
                'privacy_policy.txt': self._create_privacy_policy,
                'copyright_notice.txt': self._create_copyright_notice,
                'partnership_policy.txt': self._create_partnership_policy,
                'content_policy.txt': self._create_content_policy,
                'dmca_policy.txt': self._create_dmca_policy,
                'community_guidelines.txt': self._create_community_guidelines,
                'refund_policy.txt': self._create_refund_policy,
                'data_protection_policy.txt': self._create_data_protection_policy
            }
            
            created_documents = []
            
            for doc_name in self.required_documents:
                doc_path = os.path.join(self.legal_directory, doc_name)
                
                if not os.path.exists(doc_path):
                    if doc_name in document_creators:
                        content = document_creators[doc_name]()
                        
                        with open(doc_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        created_documents.append(doc_name)
                        renpy.log("Created legal document: {}".format(doc_name))
            
            if created_documents:
                renpy.log("Created {} missing legal documents".format(len(created_documents)))
                self._update_document_hashes()
            
            return created_documents
            
        except Exception as e:
            renpy.log("Error creating legal documents: {}".format(str(e)))
            return []
    
    def _create_content_policy(self):
        """Create Content Policy"""
        content = """CONTENT POLICY
"Netcode the Protogen and More" Visual Novel

This policy outlines acceptable content standards and restrictions.

1. AGE RATING AND CONTENT
The Game contains mature themes and is rated for adults 18+.

2. PROHIBITED CONTENT MODIFICATIONS
Users may not create, distribute, or use modifications that:
- Add explicit sexual content
- Include hate speech or discriminatory material
- Promote illegal activities
- Violate copyright or trademark rights

3. USER-GENERATED CONTENT
Any user-generated content must comply with:
- Community standards
- Legal requirements
- Platform guidelines
- Intellectual property laws

4. REPORTING VIOLATIONS
Report content violations to: [Content Report Contact]

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        return content
    
    def _create_dmca_policy(self):
        """Create DMCA Policy"""
        content = """DMCA POLICY
"Netcode the Protogen and More" Visual Novel

We respect intellectual property rights and comply with the Digital Millennium Copyright Act.

REPORTING INFRINGEMENT:
Email: [DMCA Contact]
Include:
- Description of copyrighted work
- Location of infringing material
- Contact information
- Good faith statement
- Signature

COUNTER-NOTIFICATION:
If you believe content was wrongly removed, you may file a counter-notification.

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        return content
    
    def _create_community_guidelines(self):
        """Create Community Guidelines"""
        content = """COMMUNITY GUIDELINES
"Netcode the Protogen and More" Visual Novel

1. RESPECTFUL BEHAVIOR
Treat all community members with respect and courtesy.

2. NO HARASSMENT
Harassment, bullying, or threatening behavior is prohibited.

3. APPROPRIATE CONTENT
Keep discussions appropriate and relevant to the Game.

4. NO CHEATING
Use of cheats, hacks, or exploits is prohibited.

5. INTELLECTUAL PROPERTY
Respect copyright and trademark rights.

6. REPORTING
Report violations to moderators or support.

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        return content
    
    def _create_refund_policy(self):
        """Create Refund Policy"""
        content = """REFUND POLICY
"Netcode the Protogen and More" Visual Novel

REFUND ELIGIBILITY:
- Technical issues preventing gameplay
- Accidental purchases
- Requests within 14 days of purchase

REFUND PROCESS:
1. Contact support with purchase details
2. Describe reason for refund request
3. Allow 5-7 business days for processing

CONTACT: [Support Contact]

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        return content
    
    def _create_data_protection_policy(self):
        """Create Data Protection Policy"""
        content = """DATA PROTECTION POLICY
"Netcode the Protogen and More" Visual Novel

GDPR COMPLIANCE:
We comply with the General Data Protection Regulation (GDPR) and other applicable data protection laws.

DATA RIGHTS:
- Right to access your data
- Right to rectification
- Right to erasure
- Right to data portability
- Right to object to processing

CONTACT: [Data Protection Contact]

© {} All Rights Reserved.
""".format(time.strftime("%Y"))
        return content

    def _update_document_hashes(self):
        """Update hashes for all legal documents"""
        try:
            self.document_hashes = {}

            for doc_name in self.required_documents:
                doc_path = os.path.join(self.legal_directory, doc_name)

                if os.path.exists(doc_path):
                    with open(doc_path, 'rb') as f:
                        content = f.read()
                        doc_hash = hashlib.sha256(content).hexdigest()
                        self.document_hashes[doc_name] = {
                            'hash': doc_hash,
                            'size': len(content),
                            'modified': os.path.getmtime(doc_path),
                            'verified': True
                        }

            # Save hashes to secure file
            hash_file = os.path.join(self.legal_directory, '.document_hashes.json')
            with open(hash_file, 'w') as f:
                json.dump(self.document_hashes, f, indent=2)

            renpy.log("Updated document hashes for {} files".format(len(self.document_hashes)))

        except Exception as e:
            renpy.log("Error updating document hashes: {}".format(str(e)))

    def verify_document_integrity(self):
        """Verify integrity of all legal documents"""
        try:
            violations = []
            missing_docs = []

            # Load stored hashes
            hash_file = os.path.join(self.legal_directory, '.document_hashes.json')
            stored_hashes = {}

            if os.path.exists(hash_file):
                with open(hash_file, 'r') as f:
                    stored_hashes = json.load(f)

            # Check each required document
            for doc_name in self.required_documents:
                doc_path = os.path.join(self.legal_directory, doc_name)

                if not os.path.exists(doc_path):
                    missing_docs.append(doc_name)
                    continue

                # Calculate current hash
                with open(doc_path, 'rb') as f:
                    content = f.read()
                    current_hash = hashlib.sha256(content).hexdigest()

                # Compare with stored hash
                if doc_name in stored_hashes:
                    stored_info = stored_hashes[doc_name]

                    if current_hash != stored_info['hash']:
                        violations.append({
                            'document': doc_name,
                            'type': 'MODIFIED',
                            'expected_hash': stored_info['hash'],
                            'actual_hash': current_hash,
                            'timestamp': time.time()
                        })

                        renpy.log("Document tampered: {}".format(doc_name))

            # Update state
            legal_state['missing_documents'] = missing_docs
            legal_state['tampered_documents'] = [v['document'] for v in violations]
            legal_state['violations_detected'] = len(violations) + len(missing_docs)

            if violations or missing_docs:
                self._handle_legal_violations(violations, missing_docs)
                return False
            else:
                legal_state['documents_verified'] = True
                return True

        except Exception as e:
            renpy.log("Document verification failed: {}".format(str(e)))
            return False

    def _handle_legal_violations(self, violations, missing_docs):
        """Handle legal document violations"""
        try:
            # Report to anti-cheat system
            if hasattr(renpy.store, 'anticheat_core'):
                for violation in violations:
                    anticheat_core._report_violation(
                        "LEGAL_DOCUMENT_TAMPERED",
                        "Legal document modified: {}".format(violation['document'])
                    )

                for missing_doc in missing_docs:
                    anticheat_core._report_violation(
                        "LEGAL_DOCUMENT_MISSING",
                        "Required legal document missing: {}".format(missing_doc)
                    )

            # Auto-create missing documents if enabled and developer authenticated
            if (LEGAL_CONFIG['auto_create_missing'] and
                legal_state['developer_authenticated'] and
                missing_docs):

                created_docs = self.create_missing_documents()
                if created_docs:
                    renpy.log("Auto-created missing legal documents: {}".format(created_docs))

            # Trigger violation response
            if LEGAL_CONFIG['violation_ban']:
                self._trigger_legal_violation_ban(violations, missing_docs)

        except Exception as e:
            renpy.log("Error handling legal violations: {}".format(str(e)))

    def _trigger_legal_violation_ban(self, violations, missing_docs):
        """Trigger ban for legal violations"""
        violation_details = []

        for violation in violations:
            violation_details.append("Modified: {}".format(violation['document']))

        for missing_doc in missing_docs:
            violation_details.append("Missing: {}".format(missing_doc))

        ban_reason = "Legal document violations detected: {}".format(", ".join(violation_details))

        # Trigger anti-cheat ban
        if hasattr(renpy.store, 'anticheat_core'):
            anticheat_core._trigger_ban("LEGAL_VIOLATION", ban_reason)

        renpy.log("Legal violation ban triggered: {}".format(ban_reason))

    def initialize_legal_protection(self):
        """Initialize legal document protection system"""
        try:
            renpy.log("Initializing legal document protection...")

            # Verify developer computer
            if LEGAL_CONFIG['developer_verification']:
                if not self._verify_developer_computer():
                    # Not developer computer - strict verification
                    if not self.verify_document_integrity():
                        renpy.log("Legal document verification failed on non-developer system")
                        return False
                else:
                    # Developer computer - auto-create missing documents
                    missing_docs = []
                    for doc_name in self.required_documents:
                        doc_path = os.path.join(self.legal_directory, doc_name)
                        if not os.path.exists(doc_path):
                            missing_docs.append(doc_name)

                    if missing_docs:
                        created_docs = self.create_missing_documents()
                        renpy.log("Developer system: Created {} missing documents".format(len(created_docs)))

                    # Update hashes for developer system
                    self._update_document_hashes()

            # Verify document integrity
            if not self.verify_document_integrity():
                renpy.log("Legal document integrity check failed")
                return False

            legal_state['last_check'] = time.time()
            renpy.log("Legal document protection initialized successfully")
            return True

        except Exception as e:
            renpy.log("Legal protection initialization failed: {}".format(str(e)))
            return False

    def monitor_legal_documents(self):
        """Continuously monitor legal documents"""
        current_time = time.time()

        if current_time - legal_state['last_check'] > LEGAL_CONFIG['check_interval']:
            if not self.verify_document_integrity():
                renpy.log("Legal document monitoring detected violations")

            legal_state['last_check'] = current_time

    def get_developer_signature(self):
        """Get current developer signature for setup"""
        return self._generate_developer_signature()

    def require_terms_acceptance(self):
        """Require user to accept terms"""
        if not legal_state['user_accepted_terms'] and LEGAL_CONFIG['require_acceptance']:
            return True
        return False

# Initialize legal document protector
legal_protector = LegalDocumentProtector()

# Integration with anti-cheat initialization
def initialize_legal_anticheat():
    """Initialize legal document protection"""
    if LEGAL_CONFIG['enabled']:
        success = legal_protector.initialize_legal_protection()
        if not success:
            renpy.log("Legal protection initialization failed")
            return False
    return True

# Add legal monitoring to continuous checks
def monitor_legal_compliance():
    """Monitor legal document compliance"""
    if LEGAL_CONFIG['enabled']:
        legal_protector.monitor_legal_documents()

# Schedule legal monitoring
def schedule_legal_monitoring():
    if LEGAL_CONFIG['enabled']:
        monitor_legal_compliance()

    # Schedule next check
    renpy.timeout(LEGAL_CONFIG['check_interval'], schedule_legal_monitoring)

# Start legal monitoring
if LEGAL_CONFIG['enabled']:
    schedule_legal_monitoring()

# Developer signature display (for setup)
init python:
    def show_developer_signature():
        """Show developer signature for setup"""
        if config.developer:
            signature = legal_protector.get_developer_signature()
            renpy.notify("Developer Signature: {}".format(signature[:16] + "..."))
            renpy.log("Full Developer Signature: {}".format(signature))
            return signature
        return None

# Legal document status screen (developer mode)
screen legal_status():

    if config.developer:
        frame:
            xpos 10
            ypos 680
            xsize 400
            ysize 120
            background "#000000AA"

            vbox:
                spacing 5

                text "Legal Protection Status" size 16 color "#FFD700"

                text "Enabled: {}".format(LEGAL_CONFIG.get('enabled', False)) size 12
                text "Developer Auth: {}".format(legal_state.get('developer_authenticated', False)) size 12
                text "Documents Verified: {}".format(legal_state.get('documents_verified', False)) size 12
                text "Violations: {}".format(legal_state.get('violations_detected', 0)) size 12
                text "Missing Docs: {}".format(len(legal_state.get('missing_documents', []))) size 12

                if legal_state.get('tampered_documents'):
                    text "Tampered: {}".format(len(legal_state.get('tampered_documents', []))) size 12 color "#FF0000"

# Terms acceptance screen
screen terms_acceptance():

    modal True

    frame:
        xfill True
        yfill True
        background "#000000"

        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30

            text "📋 LEGAL AGREEMENT REQUIRED" size 32 color "#FFD700" bold True xalign 0.5

            frame:
                background "#1A1A1A"
                padding (30, 20)
                xsize 900
                ysize 500

                viewport:
                    scrollbars "vertical"
                    mousewheel True

                    vbox:
                        spacing 15

                        text "By using 'Netcode the Protogen and More', you agree to:" size 18 color "#FFFFFF"

                        text "• Terms of Service" size 16 color "#CCCCCC"
                        text "• End User License Agreement (EULA)" size 16 color "#CCCCCC"
                        text "• Privacy Policy" size 16 color "#CCCCCC"
                        text "• Content Policy" size 16 color "#CCCCCC"
                        text "• Copyright Notice" size 16 color "#CCCCCC"
                        text "• Community Guidelines" size 16 color "#CCCCCC"

                        null height 20

                        text "These documents are located in the 'legal_documents' folder." size 14 color "#888888"
                        text "Please review all legal documents before proceeding." size 14 color="#888888"

                        null height 20

                        text "By clicking 'I Agree', you confirm that you:" size 16 color="#FFFFFF"
                        text "• Have read and understood all legal documents" size 14 color="#CCCCCC"
                        text "• Agree to be bound by all terms and conditions" size 14 color="#CCCCCC"
                        text "• Meet the minimum age requirements" size 14 color="#CCCCCC"
                        text "• Will comply with all policies and guidelines" size 14 color="#CCCCCC"

            hbox:
                spacing 30
                xalign 0.5

                textbutton "I Agree" action [
                    SetVariable("legal_state['user_accepted_terms']", True),
                    SetVariable("persistent.legal_terms_accepted", True),
                    SetVariable("persistent.legal_acceptance_timestamp", time.time()),
                    Return(True)
                ] text_size 20 text_color "#00FF00"

                textbutton "I Disagree" action [
                    Function(renpy.quit)
                ] text_size 20 text_color "#FF0000"

# Default persistent values for legal system
default persistent.legal_terms_accepted = False
default persistent.legal_acceptance_timestamp = None

# Legal document viewer screen
screen legal_document_viewer(document_name):

    modal True

    frame:
        xfill True
        yfill True
        background "#000000"

        vbox:
            spacing 20

            hbox:
                spacing 20

                text document_name.replace('_', ' ').title() size 24 color "#FFD700"

                textbutton "Close" action Return() xalign 1.0

            frame:
                background "#1A1A1A"
                padding (20, 15)
                xfill True
                yfill True

                viewport:
                    scrollbars "vertical"
                    mousewheel True

                    text _load_legal_document(document_name) size 14 color "#FFFFFF"

# Function to load legal document content
init python:
    def _load_legal_document(document_name):
        """Load legal document content for display"""
        try:
            doc_path = os.path.join(legal_protector.legal_directory, document_name)
            if os.path.exists(doc_path):
                with open(doc_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "Document not found: {}".format(document_name)
        except Exception as e:
            return "Error loading document: {}".format(str(e))

# Labels for legal system integration
label check_legal_compliance:
    """Check legal compliance and show terms if needed"""

    # Check if terms acceptance is required
    if legal_protector.require_terms_acceptance():
        call screen terms_acceptance

    # Check for legal violations (non-developer computers)
    if not legal_state.get('developer_authenticated', False):
        if not legal_state.get('documents_verified', False):
            "Legal document verification failed. Please contact support."
            return False

    return True

label show_legal_document(document_name):
    """Show a specific legal document"""
    call screen legal_document_viewer(document_name)
    return

label legal_violation_detected:
    """Handle legal violation detection"""

    "⚠️ LEGAL VIOLATION DETECTED"
    "One or more legal documents have been tampered with or are missing."
    "This is a serious violation of the terms of service."

    if legal_state.get('missing_documents'):
        "Missing documents: [legal_state['missing_documents']]"

    if legal_state.get('tampered_documents'):
        "Tampered documents: [legal_state['tampered_documents']]"

    "Please contact support for assistance."
    "The game will now exit."

    $ renpy.quit()

    return
