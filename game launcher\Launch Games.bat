@echo off
title Netcode the Protogen and More - Game Launcher
echo.
echo ========================================
echo  NETCODE THE PROTOGEN AND MORE
echo  Game Launcher Starting...
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Launch the game launcher
echo Starting Game Launcher...
python game_launcher.py

REM If launcher exits with error, show message
if %errorlevel% neq 0 (
    echo.
    echo Game Launcher encountered an error.
    echo Check the console output above for details.
    pause
)

exit /b 0
