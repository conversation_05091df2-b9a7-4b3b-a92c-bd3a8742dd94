# Image Definitions for Visual Novels
# Defines placeholder images and backgrounds for the games

# Background images
image bg bedroom = "#2E2E2E"
image bg forest = "#1B4332"
image bg hallway = "#3E3E3E"
image bg kitchen = "#4A4A4A"
image bg cabin = "#8B4513"
image bedroom = "#2E2E2E"
image Anime_Hall_Background_10 = "#3E3E3E"

# Character placeholder images
image Lumetric = Placeholder("Lumetric")
image Netcode = Placeholder("Netcode")
image Netcode blushing = Placeholder("Netcode Blushing")
image BearWithUs = Placeholder("BearWithUs")
image RahNight_Bluelight = Placeholder("RahNight_Bluelight")
image mystery_character = Placeholder("Mystery Character")

# Netcode game characters
image whiskers = Placeholder("Whiskers")
image magnus = Placeholder("Magnus")
image luna = Placeholder("Luna")
image cipher = Placeholder("Cipher")
image vertex = Placeholder("Vertex")
image meadow = Placeholder("Meadow")
image bramble = Placeholder("Bramble")
image talon = Placeholder("Talon")
image quantum = Placeholder("Quantum")
image nexus = Placeholder("Nexus")
image cipher_prime = Placeholder("Cipher Prime")
image vector = Placeholder("Vector")
image matrix = Placeholder("Matrix")
image pulse = Placeholder("Pulse")
image flux = Placeholder("Flux")
image nova = Placeholder("Nova")
image echo = Placeholder("Echo")
image prism = Placeholder("Prism")
image frost = Placeholder("Frost")
image aurora = Placeholder("Aurora")
image glacier = Placeholder("Glacier")
image crystal = Placeholder("Crystal")
image arctic = Placeholder("Arctic")
image blizzard = Placeholder("Blizzard")
image ice_shard = Placeholder("Ice Shard")
image winter = Placeholder("Winter")
image snow_drift = Placeholder("Snow Drift")
image polar = Placeholder("Polar")
image helix = Placeholder("Helix")
image genome = Placeholder("Genome")
image bio_flux = Placeholder("Bio-Flux")
image neural_web = Placeholder("Neural Web")
image symbiosis = Placeholder("Symbiosis")
image evolution = Placeholder("Evolution")
image adaptation = Placeholder("Adaptation")
image metamorphosis = Placeholder("Metamorphosis")
image synthesis = Placeholder("Synthesis")
image organic_prime = Placeholder("Organic Prime")

# Additional placeholder images for future use
image netcode = Placeholder("netcode")
image player = Placeholder("player")

# Scene transitions
define fade = Fade(0.5, 0.0, 0.5)
define dissolve = Dissolve(0.5)

# Character positioning
transform center:
    xalign 0.5
    yalign 1.0

transform left:
    xalign 0.2
    yalign 1.0

transform right:
    xalign 0.8
    yalign 1.0
