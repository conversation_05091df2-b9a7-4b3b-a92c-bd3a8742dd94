# CPU Integrated Graphics Detection and Configuration System

This comprehensive system automatically detects Intel or AMD integrated graphics and configures the Ren'Py engine for optimal performance with CPU-based graphics processing.

## Features

### 🔍 **Automatic Detection**
- **CPU Vendor Detection**: Automatically identifies Intel or AMD processors
- **Processor Model Recognition**: Detects specific processor models and generations
- **Integrated Graphics Identification**: Identifies the exact iGPU model and capabilities
- **Vulkan Support Detection**: Checks for Vulkan API support on integrated graphics

### 🎯 **Intelligent GPU Switching**
- **Dedicated to Integrated**: Automatically switches from dedicated GPU to integrated graphics when needed
- **Environment Configuration**: Sets up environment variables to prefer integrated graphics
- **Driver Optimization**: Configures drivers for optimal integrated graphics performance

### 📊 **Detailed Processor Support**

#### Intel Processors
- **13th Generation (Raptor Lake)**: i3-13100, i5-13400, i5-13600K, i7-13700K, i9-13900K
- **12th Generation (Alder Lake)**: i3-12100, i5-12400, i5-12600K, i7-12700K
- **11th Generation (Tiger Lake)**: i5-11400, i7-11700K
- **Integrated Graphics**: UHD Graphics 730/770, Iris Xe Graphics

#### AMD Processors
- **Ryzen 7000 Series (Zen 4)**: Ryzen 5 7600X, Ryzen 7 7700X, Ryzen 9 7900X
- **Ryzen 6000 Series (Zen 3+)**: Ryzen 5 6600H, Ryzen 7 6800H
- **Ryzen 5000 Series APUs (Zen 3)**: Ryzen 5 5600G, Ryzen 7 5700G
- **Integrated Graphics**: RDNA 2, Radeon 660M/680M, Vega 7/8

## System Components

### 1. **CPU Integrated Graphics Detector** (`cpu_integrated_graphics_detector.rpy`)
Main detection and configuration system that:
- Scans system hardware information
- Identifies CPU vendor and model
- Detects integrated graphics capabilities
- Configures Ren'Py engine settings automatically

### 2. **Processor Database** (`processor_database.rpy`)
Comprehensive database containing:
- Detailed specifications for Intel and AMD processors
- Clock speeds, core counts, and thread information
- Integrated graphics specifications and capabilities
- Memory support and PCIe lane information

### 3. **Vulkan Configuration** (`vulkan_igpu_config.rpy`)
Vulkan-specific optimizations including:
- Vulkan device enumeration and selection
- Integrated graphics device prioritization
- Vendor-specific Vulkan optimizations
- Environment variable configuration

## How It Works

### 🚀 **Automatic Startup Process**
1. **System Scan**: Detects CPU model and integrated graphics
2. **Capability Assessment**: Determines iGPU performance characteristics
3. **Ren'Py Configuration**: Applies optimal settings for detected hardware
4. **Vulkan Setup**: Configures Vulkan API for integrated graphics (if supported)
5. **Performance Optimization**: Applies vendor-specific optimizations

### ⚙️ **Configuration Examples**

#### Intel UHD Graphics 770 (13th Gen)
```
Renderer: gl2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 4096
Vulkan Version: 1.3
DirectX Support: DirectX 12
```

#### AMD Radeon 680M (Ryzen 6000)
```
Renderer: gl2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 4096
Vulkan Version: 1.3
RDNA Version: RDNA 2
```

### 📈 **Performance Optimizations**

#### Intel-Specific Optimizations
- **Memory Management**: Conservative allocation for stability
- **Anisotropic Filtering**: Disabled for better performance
- **Depth Buffer**: 16-bit for reduced memory usage
- **Power Saving**: Enabled for thermal management

#### AMD-Specific Optimizations
- **Memory Management**: Balanced allocation for performance
- **Anisotropic Filtering**: Enabled (AMD handles this well)
- **Depth Buffer**: 24-bit for better quality
- **Compute Units**: Optimized for Vega/RDNA architecture

## Usage

### 🔧 **Automatic Operation**
The system runs automatically when Ren'Py starts. No manual configuration required.

### 🧪 **Manual Testing**
```renpy
# Test CPU detection
call test_igpu_detection

# Test Vulkan configuration
call test_vulkan_igpu
```

### 📊 **Console Output Example**
```
=== CPU INTEGRATED GRAPHICS DETECTION ===
Detected CPU: Intel(R) Core(TM) i7-13700K CPU @ 3.40GHz
✅ Intel Processor Detected:
   Generation: 13th Gen
   Architecture: Raptor Lake
   Integrated Graphics: Intel UHD Graphics 770

=== CONFIGURING REN'PY FOR Intel iGPU ===
✅ Renderer set to: gl2
✅ VSync set to: True
✅ Texture scaling set to: linear
✅ Maximum texture size set to: 4096
✅ Applied Intel iGPU optimizations
✅ Ren'Py configuration complete for integrated graphics
```

## Supported Configurations

### 🖥️ **Operating Systems**
- **Windows 10/11**: Full support with WMI detection
- **Linux**: Support via /proc/cpuinfo parsing
- **macOS**: Basic support (limited detection)

### 🎮 **Graphics APIs**
- **OpenGL**: Full support with version detection
- **Vulkan**: Advanced support with device selection
- **DirectX**: Windows-specific optimizations

### 💾 **Memory Configurations**
- **DDR4**: Optimized settings for bandwidth limitations
- **DDR5**: Enhanced settings for improved performance
- **LPDDR5**: Mobile-optimized configurations

## Troubleshooting

### ❌ **Common Issues**

#### "Unknown CPU vendor detected"
- **Cause**: Unrecognized processor model
- **Solution**: System falls back to safe generic settings

#### "Vulkan not available on this system"
- **Cause**: Missing Vulkan drivers or old hardware
- **Solution**: System automatically uses OpenGL fallback

#### "No integrated graphics device found"
- **Cause**: Processor lacks integrated graphics or disabled in BIOS
- **Solution**: Check BIOS settings or use dedicated GPU

### 🔧 **Manual Configuration**
If automatic detection fails, you can manually configure:
```renpy
# Force specific renderer
$ renpy.config.renderer = "gl2"

# Set texture limits
$ renpy.config.gl_maximum_texture_size = 2048

# Enable power saving
$ renpy.config.gl_powersave = True
```

## Performance Expectations

### 📊 **Typical Performance**

#### Intel UHD Graphics 770
- **1080p Gaming**: 30-60 FPS in visual novels
- **4K Support**: Limited to UI scaling
- **Memory Usage**: 1-2GB shared system RAM

#### AMD Radeon 680M
- **1080p Gaming**: 60+ FPS in visual novels
- **1440p Support**: Good performance for 2D content
- **Memory Usage**: 2-4GB shared system RAM

### 🎯 **Optimization Tips**
1. **Ensure adequate RAM**: 16GB+ recommended for 4K textures
2. **Enable XMP/DOCP**: Faster memory improves iGPU performance
3. **Update drivers**: Latest drivers provide best compatibility
4. **Monitor temperatures**: Integrated graphics share CPU thermal limits

This system ensures optimal Ren'Py performance regardless of whether you're using Intel or AMD integrated graphics, with automatic detection and configuration for the best possible visual novel experience.
