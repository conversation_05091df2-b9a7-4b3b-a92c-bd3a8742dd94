## Ren'Py Engine Detector and File Loader
## Advanced anti-cheat system that detects Ren'Py engine and manages secure file loading

init -200 python:
    import sys
    import os
    import platform
    import subprocess
    import hashlib
    import json
    import time
    import threading
    from collections import defaultdict
    
    # Enhanced engine detection configuration
    engine_detector = {
        'renpy_detected': False,
        'engine_version': 'Unknown',
        'engine_path': '',
        'engine_integrity': False,
        'python_version': '',
        'platform_info': {},
        'security_level': 'unknown',
        'file_loader_active': False,
        'encryption_bridge_active': False,
        'communication_channel': None,
        'sdk_path': None,  # Will be auto-detected
        'sdk_detected': False,
        'sdk_version_detected': None,
        'project_path': r"D:\renpy_projects\netcode the protogen and more",
        'anticheat_path': r"D:\renpy_projects\netcode the protogen and more\anti-cheat",
        'encryption_path': r"D:\renpy_projects\netcode the protogen and more\unencrypt and re-encrypt",
        'sdk_search_paths': [],  # Will be dynamically populated
        'all_drives_scan': True,
        'deep_scan_enabled': True,
        'scan_external_drives': True,
        'scan_network_drives': False,
        'engine_communication_active': False,
        'file_system_hooked': False,
        'all_files_through_anticheat': False
    }
    
    # File loading system
    secure_file_loader = {
        'loaded_files': {},
        'file_cache': {},
        'access_log': [],
        'encryption_requests': [],
        'decryption_queue': [],
        'file_integrity_map': {},
        'loading_stats': defaultdict(int)
    }
    
    # Engine signatures for verification
    renpy_signatures = {
        'version_patterns': [
            r'Ren\'Py\s+(\d+\.\d+\.\d+)',
            r'renpy\.version_tuple\s*=\s*\((\d+),\s*(\d+),\s*(\d+)\)',
            r'version\s*=\s*["\'](\d+\.\d+\.\d+)["\']'
        ],
        'module_signatures': [
            'renpy.main',
            'renpy.game',
            'renpy.display',
            'renpy.audio',
            'renpy.text'
        ],
        'file_signatures': [
            'renpy/__init__.py',
            'renpy/main.py',
            'renpy/game.py',
            'lib/python3.9/renpy'
        ]
    }

class RenpyEngineDetector:
    """Advanced Ren'Py engine detection and integration"""
    
    def __init__(self):
        self.detection_complete = False
        self.security_verified = False
        self.file_bridge_established = False
        
    def detect_renpy_engine(self):
        """Comprehensive Ren'Py engine detection"""
        try:
            renpy.log("Starting Ren'Py engine detection...")
            
            # Basic Ren'Py module detection
            if self.detect_renpy_modules():
                engine_detector['renpy_detected'] = True
                renpy.log("✓ Ren'Py modules detected")
            
            # Version detection
            version = self.detect_renpy_version()
            if version:
                engine_detector['engine_version'] = version
                renpy.log("✓ Ren'Py version: {}".format(version))
            
            # Path detection
            engine_path = self.detect_engine_path()
            if engine_path:
                engine_detector['engine_path'] = engine_path
                renpy.log("✓ Engine path: {}".format(engine_path))
            
            # Platform information
            self.gather_platform_info()
            
            # Security verification
            self.verify_engine_integrity()
            
            # Enhanced SDK detection
            self.detect_renpy_sdk_installation()

            # Establish file loading bridge
            self.establish_file_bridge()

            # Setup engine communication
            self.setup_engine_communication()

            self.detection_complete = True
            renpy.log("Ren'Py engine detection completed successfully")

            return True
            
        except Exception as e:
            renpy.log("Engine detection failed: {}".format(str(e)))
            return False
    
    def detect_renpy_modules(self):
        """Detect Ren'Py modules in the system"""
        try:
            detected_modules = []
            
            for module_name in renpy_signatures['module_signatures']:
                try:
                    if module_name in sys.modules:
                        detected_modules.append(module_name)
                    else:
                        # Try to import
                        __import__(module_name)
                        detected_modules.append(module_name)
                except ImportError:
                    continue
            
            if len(detected_modules) >= 3:  # Require at least 3 core modules
                return True
            
            return False
            
        except Exception as e:
            renpy.log("Module detection error: {}".format(str(e)))
            return False
    
    def detect_renpy_version(self):
        """Detect Ren'Py version"""
        try:
            # Try direct version access
            if hasattr(renpy, 'version_tuple'):
                version_tuple = renpy.version_tuple
                return "{}.{}.{}".format(version_tuple[0], version_tuple[1], version_tuple[2])
            
            if hasattr(renpy, 'version'):
                return str(renpy.version)
            
            # Try config version
            if hasattr(config, 'version'):
                return str(config.version)
            
            # Search in sys.modules
            for module_name, module in sys.modules.items():
                if 'renpy' in module_name.lower() and hasattr(module, 'version'):
                    return str(module.version)
            
            return "Unknown"
            
        except Exception as e:
            renpy.log("Version detection error: {}".format(str(e)))
            return "Unknown"
    
    def detect_engine_path(self):
        """Detect Ren'Py engine installation path"""
        try:
            # Try to get from renpy module
            if hasattr(renpy, '__file__'):
                return os.path.dirname(os.path.dirname(renpy.__file__))
            
            # Try to get from config
            if hasattr(config, 'basedir'):
                return config.basedir
            
            # Try to get from sys.executable
            exe_path = sys.executable
            if 'renpy' in exe_path.lower():
                return os.path.dirname(exe_path)
            
            # Search common installation paths
            common_paths = [
                os.path.join(os.path.expanduser('~'), 'renpy'),
                os.path.join('C:', 'renpy'),
                '/usr/local/renpy',
                '/opt/renpy'
            ]
            
            for path in common_paths:
                if os.path.exists(path) and self.verify_renpy_installation(path):
                    return path
            
            return os.getcwd()  # Fallback to current directory
            
        except Exception as e:
            renpy.log("Engine path detection error: {}".format(str(e)))
            return ""
    
    def verify_renpy_installation(self, path):
        """Verify if a path contains a valid Ren'Py installation"""
        try:
            for signature_file in renpy_signatures['file_signatures']:
                full_path = os.path.join(path, signature_file)
                if os.path.exists(full_path):
                    return True
            return False
        except:
            return False
    
    def gather_platform_info(self):
        """Gather comprehensive platform information"""
        try:
            engine_detector['platform_info'] = {
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'python_implementation': platform.python_implementation(),
                'architecture': platform.architecture(),
                'node': platform.node()
            }
            
            engine_detector['python_version'] = platform.python_version()
            
        except Exception as e:
            renpy.log("Platform info gathering error: {}".format(str(e)))
    
    def verify_engine_integrity(self):
        """Verify Ren'Py engine integrity"""
        try:
            integrity_checks = []
            
            # Check core modules
            for module_name in renpy_signatures['module_signatures']:
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                    if hasattr(module, '__file__') and os.path.exists(module.__file__):
                        integrity_checks.append(True)
                    else:
                        integrity_checks.append(False)
                else:
                    integrity_checks.append(False)
            
            # Calculate integrity score
            integrity_score = sum(integrity_checks) / len(integrity_checks) if integrity_checks else 0
            
            if integrity_score >= 0.8:  # 80% of checks must pass
                engine_detector['engine_integrity'] = True
                engine_detector['security_level'] = 'high'
            elif integrity_score >= 0.6:
                engine_detector['engine_integrity'] = True
                engine_detector['security_level'] = 'medium'
            else:
                engine_detector['engine_integrity'] = False
                engine_detector['security_level'] = 'low'
            
            self.security_verified = True
            renpy.log("Engine integrity verified: {} (score: {:.2f})".format(
                engine_detector['security_level'], integrity_score))
            
        except Exception as e:
            renpy.log("Engine integrity verification error: {}".format(str(e)))
            engine_detector['security_level'] = 'unknown'
    
    def establish_file_bridge(self):
        """Establish secure file loading bridge"""
        try:
            # Initialize file loader
            secure_file_loader['file_cache'].clear()
            secure_file_loader['loaded_files'].clear()
            
            # Set up communication channel with encryption system
            self.setup_encryption_bridge()
            
            # Initialize file monitoring
            self.initialize_file_monitoring()
            
            engine_detector['file_loader_active'] = True
            self.file_bridge_established = True
            
            renpy.log("Secure file loading bridge established")
            
        except Exception as e:
            renpy.log("File bridge establishment error: {}".format(str(e)))
    
    def setup_encryption_bridge(self):
        """Set up communication bridge with encryption system"""
        try:
            # Check if encryption system is available
            encryption_path = os.path.join(config.basedir, 'unencrypt and re-encrypt')
            
            if os.path.exists(encryption_path):
                # Import encryption modules
                sys.path.insert(0, encryption_path)
                
                try:
                    import encryption_core
                    import resource_manager
                    
                    # Establish communication channel
                    engine_detector['communication_channel'] = {
                        'encryption_core': encryption_core,
                        'resource_manager': resource_manager,
                        'active': True,
                        'last_sync': time.time()
                    }
                    
                    engine_detector['encryption_bridge_active'] = True
                    renpy.log("✓ Encryption bridge established")
                    
                except ImportError as e:
                    renpy.log("Encryption modules not available: {}".format(str(e)))
                    
            else:
                renpy.log("Encryption system not found at: {}".format(encryption_path))
                
        except Exception as e:
            renpy.log("Encryption bridge setup error: {}".format(str(e)))
    
    def initialize_file_monitoring(self):
        """Initialize file access monitoring"""
        try:
            # Set up file access hooks
            original_open = __builtins__['open']
            
            def monitored_open(filename, mode='r', *args, **kwargs):
                # Log file access
                self.log_file_access(filename, mode)
                
                # Check if file needs decryption
                if self.needs_decryption(filename):
                    return self.decrypt_and_load(filename, mode, *args, **kwargs)
                
                return original_open(filename, mode, *args, **kwargs)
            
            # Replace built-in open function
            __builtins__['open'] = monitored_open
            
            renpy.log("File monitoring initialized")
            
        except Exception as e:
            renpy.log("File monitoring initialization error: {}".format(str(e)))
    
    def log_file_access(self, filename, mode):
        """Log file access for security monitoring"""
        try:
            access_entry = {
                'filename': filename,
                'mode': mode,
                'timestamp': time.time(),
                'thread': threading.current_thread().name
            }
            
            secure_file_loader['access_log'].append(access_entry)
            secure_file_loader['loading_stats'][os.path.splitext(filename)[1]] += 1
            
            # Keep log size manageable
            if len(secure_file_loader['access_log']) > 1000:
                secure_file_loader['access_log'] = secure_file_loader['access_log'][-500:]
                
        except Exception as e:
            renpy.log("File access logging error: {}".format(str(e)))
    
    def needs_decryption(self, filename):
        """Check if a file needs decryption"""
        try:
            if not engine_detector['encryption_bridge_active']:
                return False
            
            # Check file extension and location
            encrypted_extensions = ['.rpyc', '.rpa', '.encrypted']
            file_ext = os.path.splitext(filename)[1].lower()
            
            if file_ext in encrypted_extensions:
                return True
            
            # Check if file is in encrypted directory
            encrypted_dirs = ['game', 'images', 'audio', 'scripts']
            for enc_dir in encrypted_dirs:
                if enc_dir in filename:
                    return True
            
            return False
            
        except Exception as e:
            renpy.log("Decryption check error: {}".format(str(e)))
            return False
    
    def decrypt_and_load(self, filename, mode, *args, **kwargs):
        """Decrypt file and load securely"""
        try:
            if not engine_detector['encryption_bridge_active']:
                return open(filename, mode, *args, **kwargs)
            
            comm_channel = engine_detector['communication_channel']
            
            if comm_channel and comm_channel['active']:
                # Request decryption through communication channel
                decrypted_content = self.request_decryption(filename)
                
                if decrypted_content:
                    # Cache decrypted content
                    secure_file_loader['file_cache'][filename] = {
                        'content': decrypted_content,
                        'timestamp': time.time(),
                        'access_count': 1
                    }
                    
                    # Return file-like object with decrypted content
                    from io import StringIO, BytesIO
                    
                    if 'b' in mode:
                        return BytesIO(decrypted_content)
                    else:
                        return StringIO(decrypted_content.decode('utf-8') if isinstance(decrypted_content, bytes) else decrypted_content)
            
            # Fallback to normal file access
            return open(filename, mode, *args, **kwargs)
            
        except Exception as e:
            renpy.log("Decrypt and load error: {}".format(str(e)))
            return open(filename, mode, *args, **kwargs)
    
    def request_decryption(self, filename):
        """Request file decryption through communication channel"""
        try:
            comm_channel = engine_detector['communication_channel']
            
            if not comm_channel or not comm_channel['active']:
                return None
            
            # Use encryption core to decrypt
            encryption_core = comm_channel['encryption_core']
            
            if hasattr(encryption_core, 'decrypt_file'):
                return encryption_core.decrypt_file(filename)
            
            return None
            
        except Exception as e:
            renpy.log("Decryption request error: {}".format(str(e)))
            return None

    def detect_renpy_sdk_installation(self):
        """Enhanced SDK detection that searches ALL drives and storage devices"""
        try:
            renpy.log("🔍 Starting comprehensive Ren'Py SDK search across all drives...")

            detected_sdks = []

            # First, discover all available drives and storage devices
            all_search_paths = self.discover_all_storage_locations()

            renpy.log("📂 Scanning {} storage locations for Ren'Py SDK...".format(len(all_search_paths)))

            # Search all discovered paths
            for search_path in all_search_paths:
                if not os.path.exists(search_path):
                    continue

                try:
                    renpy.log("🔍 Scanning: {}".format(search_path))

                    # Look for directories matching SDK patterns
                    for item in os.listdir(search_path):
                        item_path = os.path.join(search_path, item)

                        if os.path.isdir(item_path) and self.is_renpy_sdk_directory(item, item_path):
                            sdk_info = self.validate_sdk_installation(item_path)
                            if sdk_info:
                                detected_sdks.append(sdk_info)
                                renpy.log("✅ Found SDK: {} v{}".format(item_path, sdk_info['version']))

                except Exception as e:
                    renpy.log("Error searching {}: {}".format(search_path, str(e)))

            if detected_sdks:
                # Use the most recent SDK version
                latest_sdk = max(detected_sdks, key=lambda x: x.get('version_tuple', (0, 0, 0)))
                engine_detector['sdk_path'] = latest_sdk['path']
                engine_detector['sdk_detected'] = True
                engine_detector['sdk_version_detected'] = latest_sdk['version']

                renpy.log("🎯 Using SDK: {} v{}".format(latest_sdk['path'], latest_sdk['version']))
                return True
            else:
                renpy.log("❌ No Ren'Py SDK installations found")
                return False

        except Exception as e:
            renpy.log("SDK detection failed: {}".format(str(e)))
            return False

    def is_renpy_sdk_directory(self, dir_name, dir_path):
        """Check if a directory is a Ren'Py SDK"""
        try:
            # Check directory name patterns
            name_lower = dir_name.lower()
            sdk_indicators = ['renpy', 'sdk']

            if all(indicator in name_lower for indicator in sdk_indicators):
                return True

            # Check for essential SDK files
            essential_files = ['renpy.py', 'launcher.py']
            for essential_file in essential_files:
                if os.path.exists(os.path.join(dir_path, essential_file)):
                    return True

            return False

        except Exception as e:
            return False

    def discover_all_storage_locations(self):
        """Discover all available storage devices and common locations"""
        try:
            all_paths = set()

            # Discover all available drives
            available_drives = self.get_all_available_drives()
            renpy.log("💾 Found {} available drives: {}".format(len(available_drives), ', '.join(available_drives)))

            # Add drive roots
            for drive in available_drives:
                all_paths.add(drive)

            # Add common user directories on each drive
            for drive in available_drives:
                common_dirs = [
                    'Downloads', 'Desktop', 'Documents', 'Users', 'Program Files',
                    'Program Files (x86)', 'Games', 'Software', 'Development',
                    'Tools', 'SDK', 'RenPy', 'Ren\'Py', 'GameDev', 'Projects'
                ]

                for common_dir in common_dirs:
                    potential_path = os.path.join(drive, common_dir)
                    if os.path.exists(potential_path):
                        all_paths.add(potential_path)

                # Add user-specific directories
                users_path = os.path.join(drive, 'Users')
                if os.path.exists(users_path):
                    try:
                        for user_dir in os.listdir(users_path):
                            user_path = os.path.join(users_path, user_dir)
                            if os.path.isdir(user_path):
                                user_subdirs = ['Downloads', 'Desktop', 'Documents', 'AppData\\Local', 'AppData\\Roaming']
                                for subdir in user_subdirs:
                                    user_subpath = os.path.join(user_path, subdir)
                                    if os.path.exists(user_subpath):
                                        all_paths.add(user_subpath)
                    except Exception as e:
                        renpy.log("Error scanning users directory {}: {}".format(users_path, str(e)))

            # Add current user's specific paths
            user_paths = [
                os.path.expanduser("~"),
                os.path.expanduser("~/Downloads"),
                os.path.expanduser("~/Desktop"),
                os.path.expanduser("~/Documents"),
                os.path.expanduser("~/AppData/Local"),
                os.path.expanduser("~/AppData/Roaming"),
                os.path.join(os.path.expanduser("~"), "Games"),
                os.path.join(os.path.expanduser("~"), "Development"),
                os.path.join(os.path.expanduser("~"), "Tools")
            ]

            for user_path in user_paths:
                if os.path.exists(user_path):
                    all_paths.add(user_path)

            # Add external and removable drives
            if engine_detector.get('scan_external_drives', True):
                external_drives = self.get_external_drives()
                for ext_drive in external_drives:
                    all_paths.add(ext_drive)
                    # Add common directories on external drives
                    for common_dir in ['Downloads', 'Software', 'Games', 'RenPy']:
                        ext_path = os.path.join(ext_drive, common_dir)
                        if os.path.exists(ext_path):
                            all_paths.add(ext_path)

            # Convert to sorted list for consistent scanning order
            sorted_paths = sorted(list(all_paths))

            # Store discovered paths for reference
            engine_detector['sdk_search_paths'] = sorted_paths

            renpy.log("📍 Discovered {} total search locations".format(len(sorted_paths)))

            return sorted_paths

        except Exception as e:
            renpy.log("Storage discovery failed: {}".format(str(e)))
            # Fallback to basic paths
            return [
                "C:\\",
                "D:\\",
                "E:\\",
                os.path.expanduser("~"),
                os.path.expanduser("~/Downloads")
            ]

    def get_all_available_drives(self):
        """Get all available drives on the system"""
        try:
            drives = []

            if platform.system() == "Windows":
                # Use Windows-specific drive detection
                import string

                # Check all possible drive letters
                for letter in string.ascii_uppercase:
                    drive_path = "{}:\\".format(letter)
                    if os.path.exists(drive_path):
                        try:
                            # Test if drive is accessible
                            os.listdir(drive_path)
                            drives.append(drive_path)
                            renpy.log("✓ Drive {} accessible".format(drive_path))
                        except Exception:
                            renpy.log("⚠️ Drive {} not accessible".format(drive_path))

                # Also try using wmic for additional drive info
                try:
                    result = subprocess.run(['wmic', 'logicaldisk', 'get', 'size,freespace,caption'],
                                            capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # Skip header
                            if line.strip():
                                parts = line.strip().split()
                                if len(parts) >= 1:
                                    caption = parts[-1]  # Drive letter is usually last
                                    if caption.endswith(':'):
                                        drive_path = caption + "\\"
                                        if drive_path not in drives and os.path.exists(drive_path):
                                            drives.append(drive_path)
                except Exception as e:
                    renpy.log("WMIC drive detection failed: {}".format(str(e)))

            else:
                # Unix-like systems
                drives = ['/']

                # Check common mount points
                mount_points = ['/mnt', '/media', '/Volumes']
                for mount_point in mount_points:
                    if os.path.exists(mount_point):
                        try:
                            for item in os.listdir(mount_point):
                                item_path = os.path.join(mount_point, item)
                                if os.path.isdir(item_path):
                                    drives.append(item_path)
                        except Exception:
                            pass

            return drives

        except Exception as e:
            renpy.log("Drive detection failed: {}".format(str(e)))
            return ["C:\\", "D:\\"]  # Fallback for Windows

    def get_external_drives(self):
        """Detect external/removable drives"""
        try:
            external_drives = []

            if platform.system() == "Windows":
                try:
                    # Use wmic to detect removable drives
                    result = subprocess.run(['wmic', 'logicaldisk', 'where', 'drivetype=2', 'get', 'caption'],
                                            capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # Skip header
                            if line.strip():
                                drive_letter = line.strip()
                                if drive_letter.endswith(':'):
                                    drive_path = drive_letter + "\\"
                                    if os.path.exists(drive_path):
                                        external_drives.append(drive_path)
                                        renpy.log("🔌 External drive detected: {}".format(drive_path))
                except Exception as e:
                    renpy.log("External drive detection failed: {}".format(str(e)))

            return external_drives

        except Exception as e:
            renpy.log("External drive detection error: {}".format(str(e)))
            return []

    def validate_sdk_installation(self, sdk_path):
        """Validate and extract information from SDK installation"""
        try:
            # Check for core SDK files
            required_files = [
                'renpy.py',
                'launcher.py',
                os.path.join('renpy', '__init__.py'),
                os.path.join('renpy', 'main.py')
            ]

            for required_file in required_files:
                if not os.path.exists(os.path.join(sdk_path, required_file)):
                    return None

            # Extract version information
            version_info = self.extract_sdk_version(sdk_path)
            if not version_info:
                return None

            # Check SDK integrity
            integrity_check = self.check_sdk_integrity(sdk_path)

            return {
                'path': sdk_path,
                'version': version_info['version'],
                'version_tuple': version_info['version_tuple'],
                'integrity': integrity_check,
                'validated': True
            }

        except Exception as e:
            renpy.log("SDK validation failed: {}".format(str(e)))
            return None

    def extract_sdk_version(self, sdk_path):
        """Extract version from SDK installation"""
        try:
            # Try to read from renpy/__init__.py
            init_file = os.path.join(sdk_path, 'renpy', '__init__.py')
            if os.path.exists(init_file):
                with open(init_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                import re

                # Look for version_tuple
                version_tuple_match = re.search(r'version_tuple\s*=\s*\(([^)]+)\)', content)
                if version_tuple_match:
                    version_str = version_tuple_match.group(1)
                    version_parts = [int(x.strip()) for x in version_str.split(',')]
                    version_tuple = tuple(version_parts)
                    version = '.'.join(map(str, version_parts))

                    return {
                        'version': version,
                        'version_tuple': version_tuple
                    }

                # Look for version string
                version_match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
                if version_match:
                    version = version_match.group(1)
                    version_tuple = tuple(map(int, version.split('.')))

                    return {
                        'version': version,
                        'version_tuple': version_tuple
                    }

            # Fallback: extract from directory name
            dir_name = os.path.basename(sdk_path)
            version_match = re.search(r'(\d+)\.(\d+)\.(\d+)', dir_name)
            if version_match:
                version_tuple = tuple(map(int, version_match.groups()))
                version = '.'.join(map(str, version_tuple))

                return {
                    'version': version,
                    'version_tuple': version_tuple
                }

            return None

        except Exception as e:
            renpy.log("Version extraction failed: {}".format(str(e)))
            return None

    def check_sdk_integrity(self, sdk_path):
        """Check SDK file integrity"""
        try:
            critical_files = [
                'renpy/__init__.py',
                'renpy/main.py',
                'renpy/loader.py',
                'renpy/game.py'
            ]

            for critical_file in critical_files:
                file_path = os.path.join(sdk_path, critical_file)
                if not os.path.exists(file_path):
                    return False

            return True

        except Exception as e:
            return False

    def setup_engine_communication(self):
        """Setup communication channel with detected engine"""
        try:
            if not engine_detector['sdk_detected']:
                return False

            sdk_path = engine_detector['sdk_path']

            # Setup communication channel
            engine_detector['communication_channel'] = {
                'active': True,
                'sdk_path': sdk_path,
                'project_path': engine_detector['project_path'],
                'anticheat_path': engine_detector['anticheat_path'],
                'encryption_path': engine_detector['encryption_path'],
                'file_loader': self,
                'encryption_core': None
            }

            # Initialize encryption communication
            if os.path.exists(engine_detector['encryption_path']):
                self.setup_encryption_bridge()

            engine_detector['engine_communication_active'] = True
            engine_detector['all_files_through_anticheat'] = True

            renpy.log("🔗 Engine communication established")
            return True

        except Exception as e:
            renpy.log("Engine communication setup failed: {}".format(str(e)))
            return False

    def setup_encryption_bridge(self):
        """Setup bridge to encryption/decryption system"""
        try:
            encryption_path = engine_detector['encryption_path']

            if encryption_path not in sys.path:
                sys.path.insert(0, encryption_path)

            # Try to import encryption modules
            try:
                import encryption_core
                import resource_manager

                # Setup encryption bridge
                engine_detector['communication_channel']['encryption_core'] = encryption_core
                engine_detector['encryption_bridge_active'] = True

                renpy.log("🔐 Encryption bridge established")
                return True

            except ImportError as e:
                renpy.log("Encryption modules not found: {}".format(str(e)))
                return False

        except Exception as e:
            renpy.log("Encryption bridge setup failed: {}".format(str(e)))
            return False

# Initialize engine detector
renpy_engine_detector = RenpyEngineDetector()

# Secure File Communication Bridge
class SecureFileCommunicator:
    """Handles secure communication between anti-cheat and encryption systems"""

    def __init__(self):
        self.encryption_interface = None
        self.communication_active = False
        self.message_queue = []
        self.response_cache = {}

    def initialize_communication(self):
        """Initialize secure communication with encryption system"""
        try:
            encryption_path = os.path.join(config.basedir, 'unencrypt and re-encrypt')

            if os.path.exists(encryption_path):
                # Add encryption path to Python path
                if encryption_path not in sys.path:
                    sys.path.insert(0, encryption_path)

                # Import and initialize encryption interface
                try:
                    import encryption_core
                    import resource_manager

                    self.encryption_interface = {
                        'core': encryption_core,
                        'resource_manager': resource_manager,
                        'initialized': True
                    }

                    self.communication_active = True
                    renpy.log("✓ Secure file communication initialized")
                    return True

                except ImportError as e:
                    renpy.log("Failed to import encryption modules: {}".format(str(e)))
                    return False
            else:
                renpy.log("Encryption system not found")
                return False

        except Exception as e:
            renpy.log("Communication initialization error: {}".format(str(e)))
            return False

    def request_file_decryption(self, file_path, priority='normal'):
        """Request file decryption through secure channel"""
        try:
            if not self.communication_active or not self.encryption_interface:
                return None

            # Check cache first
            cache_key = "decrypt_{}".format(file_path)
            if cache_key in self.response_cache:
                cached_response = self.response_cache[cache_key]
                if time.time() - cached_response['timestamp'] < 300:  # 5 minute cache
                    return cached_response['content']

            # Request decryption
            encryption_core = self.encryption_interface['core']

            if hasattr(encryption_core, 'decrypt_resource'):
                decrypted_content = encryption_core.decrypt_resource(file_path)

                # Cache the result
                self.response_cache[cache_key] = {
                    'content': decrypted_content,
                    'timestamp': time.time()
                }

                return decrypted_content

            return None

        except Exception as e:
            renpy.log("File decryption request error: {}".format(str(e)))
            return None

    def request_file_encryption(self, file_path, content, priority='normal'):
        """Request file encryption through secure channel"""
        try:
            if not self.communication_active or not self.encryption_interface:
                return False

            encryption_core = self.encryption_interface['core']

            if hasattr(encryption_core, 'encrypt_resource'):
                success = encryption_core.encrypt_resource(file_path, content)

                # Clear cache for this file
                cache_key = "decrypt_{}".format(file_path)
                if cache_key in self.response_cache:
                    del self.response_cache[cache_key]

                return success

            return False

        except Exception as e:
            renpy.log("File encryption request error: {}".format(str(e)))
            return False

    def get_file_list(self, directory='game', encrypted_only=False):
        """Get list of files through secure communication"""
        try:
            if not self.communication_active or not self.encryption_interface:
                return []

            resource_manager = self.encryption_interface['resource_manager']

            if hasattr(resource_manager, 'list_resources'):
                return resource_manager.list_resources(directory, encrypted_only)

            return []

        except Exception as e:
            renpy.log("File list request error: {}".format(str(e)))
            return []

    def verify_file_integrity(self, file_path):
        """Verify file integrity through secure communication"""
        try:
            if not self.communication_active or not self.encryption_interface:
                return False

            encryption_core = self.encryption_interface['core']

            if hasattr(encryption_core, 'verify_integrity'):
                return encryption_core.verify_integrity(file_path)

            return False

        except Exception as e:
            renpy.log("File integrity verification error: {}".format(str(e)))
            return False

    def send_security_message(self, message_type, data):
        """Send security message to encryption system"""
        try:
            message = {
                'type': message_type,
                'data': data,
                'timestamp': time.time(),
                'source': 'anticheat'
            }

            self.message_queue.append(message)

            # Process message if communication is active
            if self.communication_active:
                return self.process_security_message(message)

            return False

        except Exception as e:
            renpy.log("Security message send error: {}".format(str(e)))
            return False

    def process_security_message(self, message):
        """Process security message"""
        try:
            message_type = message.get('type', '')
            data = message.get('data', {})

            if message_type == 'file_access_violation':
                return self.handle_file_access_violation(data)
            elif message_type == 'encryption_request':
                return self.handle_encryption_request(data)
            elif message_type == 'integrity_check':
                return self.handle_integrity_check(data)

            return False

        except Exception as e:
            renpy.log("Security message processing error: {}".format(str(e)))
            return False

    def handle_file_access_violation(self, data):
        """Handle file access violation"""
        try:
            file_path = data.get('file_path', '')
            violation_type = data.get('violation_type', '')

            # Log violation
            renpy.log("File access violation: {} - {}".format(file_path, violation_type))

            # Take appropriate action based on violation type
            if violation_type == 'unauthorized_access':
                # Re-encrypt the file
                return self.request_file_encryption(file_path, None)
            elif violation_type == 'tampering_detected':
                # Restore from backup
                return self.restore_file_from_backup(file_path)

            return True

        except Exception as e:
            renpy.log("File access violation handling error: {}".format(str(e)))
            return False

    def handle_encryption_request(self, data):
        """Handle encryption request"""
        try:
            file_path = data.get('file_path', '')
            operation = data.get('operation', 'encrypt')

            if operation == 'encrypt':
                return self.request_file_encryption(file_path, None)
            elif operation == 'decrypt':
                content = self.request_file_decryption(file_path)
                return content is not None

            return False

        except Exception as e:
            renpy.log("Encryption request handling error: {}".format(str(e)))
            return False

    def handle_integrity_check(self, data):
        """Handle integrity check request"""
        try:
            file_path = data.get('file_path', '')
            return self.verify_file_integrity(file_path)

        except Exception as e:
            renpy.log("Integrity check handling error: {}".format(str(e)))
            return False

    def restore_file_from_backup(self, file_path):
        """Restore file from backup"""
        try:
            if not self.communication_active or not self.encryption_interface:
                return False

            resource_manager = self.encryption_interface['resource_manager']

            if hasattr(resource_manager, 'restore_from_backup'):
                return resource_manager.restore_from_backup(file_path)

            return False

        except Exception as e:
            renpy.log("File restoration error: {}".format(str(e)))
            return False

# Initialize secure file communicator
secure_file_communicator = SecureFileCommunicator()

# Enhanced file loader with encryption integration
class EnhancedFileLoader:
    """Enhanced file loader with encryption and anti-cheat integration"""

    def __init__(self):
        self.loaded_files = {}
        self.access_monitor = {}
        self.security_violations = []

    def load_file_securely(self, file_path, mode='r'):
        """Load file with security checks and encryption support"""
        try:
            # Security check
            if not self.security_check_file_access(file_path):
                self.log_security_violation(file_path, 'unauthorized_access')
                return None

            # Check if file is encrypted
            if self.is_encrypted_file(file_path):
                return self.load_encrypted_file(file_path, mode)
            else:
                return self.load_regular_file(file_path, mode)

        except Exception as e:
            renpy.log("Secure file loading error: {}".format(str(e)))
            return None

    def security_check_file_access(self, file_path):
        """Perform security checks before file access"""
        try:
            # Check if file is in allowed directories
            allowed_dirs = ['game', 'images', 'audio', 'scripts', 'fonts', 'videos']

            file_dir = os.path.dirname(file_path).split(os.sep)[0]
            if file_dir not in allowed_dirs and not file_path.startswith('game/'):
                return False

            # Check file extension
            dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.com']
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in dangerous_extensions:
                return False

            # Check access frequency (rate limiting)
            current_time = time.time()
            if file_path in self.access_monitor:
                last_access = self.access_monitor[file_path]['last_access']
                access_count = self.access_monitor[file_path]['count']

                if current_time - last_access < 1.0 and access_count > 10:  # Rate limiting
                    return False

            # Update access monitor
            if file_path not in self.access_monitor:
                self.access_monitor[file_path] = {'count': 0, 'last_access': 0}

            self.access_monitor[file_path]['count'] += 1
            self.access_monitor[file_path]['last_access'] = current_time

            return True

        except Exception as e:
            renpy.log("Security check error: {}".format(str(e)))
            return False

    def is_encrypted_file(self, file_path):
        """Check if file is encrypted"""
        try:
            # Check file extension
            encrypted_extensions = ['.encrypted', '.enc', '.secure']
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in encrypted_extensions:
                return True

            # Check file header for encryption markers
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(16)
                    if header.startswith(b'ENCRYPTED') or header.startswith(b'ENC_'):
                        return True
            except:
                pass

            return False

        except Exception as e:
            renpy.log("Encryption check error: {}".format(str(e)))
            return False

    def load_encrypted_file(self, file_path, mode):
        """Load encrypted file through secure communication"""
        try:
            # Request decryption through secure communicator
            decrypted_content = secure_file_communicator.request_file_decryption(file_path)

            if decrypted_content:
                # Cache the loaded file
                self.loaded_files[file_path] = {
                    'content': decrypted_content,
                    'timestamp': time.time(),
                    'encrypted': True
                }

                # Return file-like object
                from io import StringIO, BytesIO

                if 'b' in mode:
                    return BytesIO(decrypted_content if isinstance(decrypted_content, bytes) else decrypted_content.encode('utf-8'))
                else:
                    return StringIO(decrypted_content if isinstance(decrypted_content, str) else decrypted_content.decode('utf-8'))

            return None

        except Exception as e:
            renpy.log("Encrypted file loading error: {}".format(str(e)))
            return None

    def load_regular_file(self, file_path, mode):
        """Load regular file with monitoring"""
        try:
            with open(file_path, mode) as f:
                content = f.read()

            # Cache the loaded file
            self.loaded_files[file_path] = {
                'content': content,
                'timestamp': time.time(),
                'encrypted': False
            }

            return content

        except Exception as e:
            renpy.log("Regular file loading error: {}".format(str(e)))
            return None

    def log_security_violation(self, file_path, violation_type):
        """Log security violation"""
        try:
            violation = {
                'file_path': file_path,
                'violation_type': violation_type,
                'timestamp': time.time(),
                'thread': threading.current_thread().name
            }

            self.security_violations.append(violation)

            # Send violation message to encryption system
            secure_file_communicator.send_security_message('file_access_violation', violation)

            # Log to anti-cheat system
            if hasattr(renpy.store, 'anticheat_core'):
                anticheat_core.record_violation('file_access', violation_type, file_path)

        except Exception as e:
            renpy.log("Security violation logging error: {}".format(str(e)))

# Initialize enhanced file loader
enhanced_file_loader = EnhancedFileLoader()

# Auto-detect and initialize on startup
init python:
    if anticheat_state.get('initialized', False):
        # Initialize engine detection
        renpy_engine_detector.detect_renpy_engine()

        # Initialize secure communication
        secure_file_communicator.initialize_communication()

        # Log initialization status
        renpy.log("Anti-cheat engine integration initialized")
        renpy.log("Engine detected: {}".format(engine_detector['renpy_detected']))
        renpy.log("Encryption bridge: {}".format(engine_detector['encryption_bridge_active']))
        renpy.log("File loader: {}".format(engine_detector['file_loader_active']))
