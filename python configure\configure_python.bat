@echo off
echo ================================================================
echo Python Compatibility Bridge Configuration for Ren'Py
echo ================================================================
echo.
echo This will configure Python compatibility between your Ren'Py
echo engine (Python 3.9.10) and newer Python versions.
echo.

REM Try different Python executables
set PYTHON_CMD=

REM Try python
python --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python
    goto :found_python
)

REM Try python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python3
    goto :found_python
)

REM Try py launcher
py --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=py
    goto :found_python
)

REM Try py -3
py -3 --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=py -3
    goto :found_python
)

echo ERROR: Python not found!
echo Please install Python or ensure it's in your PATH.
echo.
echo You can download Python from: https://www.python.org/downloads/
echo.
pause
exit /b 1

:found_python
echo Found Python: %PYTHON_CMD%
%PYTHON_CMD% --version
echo.

echo Running compatibility test...
%PYTHON_CMD% "test_compatibility.py"
if %errorlevel% neq 0 (
    echo.
    echo WARNING: Compatibility test had issues.
    echo Continuing with configuration anyway...
    echo.
)

echo.
echo Running Python configuration...
%PYTHON_CMD% "renpy python config.py"

if %errorlevel% == 0 (
    echo.
    echo ================================================================
    echo CONFIGURATION COMPLETE!
    echo ================================================================
    echo.
    echo Your Python compatibility bridge is now configured.
    echo.
    echo NEXT STEPS:
    echo 1. Check the generated files:
    echo    - python_config_adapted.json
    echo    - COMPATIBILITY_GUIDE.md
    echo.
    echo 2. Add the integration code to your game/script.rpy file
    echo    (see the output above for the exact code)
    echo.
    echo 3. Your Ren'Py project will now work with both:
    echo    - Original Python 3.9.10 engine
    echo    - Newer Python versions (3.10+, 3.11+, 3.12+)
    echo.
    echo ================================================================
) else (
    echo.
    echo ERROR: Configuration failed!
    echo Please check the error messages above.
    echo.
)

echo.
pause
