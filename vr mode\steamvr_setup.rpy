# SteamVR Integration for PC VR Headsets
# Detects SteamVR and converts PC version to full VR experience with controller interaction

# SteamVR Detection and Configuration
init -20 python:
    import os
    import sys
    import subprocess
    import json
    
    # SteamVR detection
    def detect_steamvr():
        """Detect if SteamVR is installed and running"""
        try:
            # Method 1: Check for SteamVR installation
            steam_paths = [
                os.path.expanduser("~/.steam/steam/steamapps/common/SteamVR"),
                os.path.expanduser("~/.local/share/Steam/steamapps/common/SteamVR"),
                "C:\\Program Files (x86)\\Steam\\steamapps\\common\\SteamVR",
                "C:\\Program Files\\Steam\\steamapps\\common\\SteamVR"
            ]
            
            steamvr_installed = False
            for path in steam_paths:
                if os.path.exists(path):
                    steamvr_installed = True
                    store.steamvr_path = path
                    break
            
            if not steamvr_installed:
                return False
            
            # Method 2: Check if SteamVR is running
            try:
                if sys.platform.startswith('win'):
                    # Windows: Check for vrserver.exe process
                    result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq vrserver.exe'], 
                                          capture_output=True, text=True)
                    steamvr_running = 'vrserver.exe' in result.stdout
                else:
                    # Linux: Check for vrserver process
                    result = subprocess.run(['pgrep', '-f', 'vrserver'], 
                                          capture_output=True, text=True)
                    steamvr_running = bool(result.stdout.strip())
                
                store.steamvr_running = steamvr_running
                
            except:
                store.steamvr_running = False
            
            # Method 3: Check for VR headset connection
            try:
                # Check OpenVR status
                vr_headset_connected = check_vr_headset()
                store.vr_headset_connected = vr_headset_connected
            except:
                store.vr_headset_connected = False
            
            return steamvr_installed
            
        except Exception as e:
            return False
    
    def check_vr_headset():
        """Check if VR headset is connected"""
        try:
            # Check for common VR headset indicators
            if sys.platform.startswith('win'):
                # Windows: Check device manager for VR devices
                result = subprocess.run(['powershell', '-Command', 
                    'Get-PnpDevice | Where-Object {$_.FriendlyName -like "*VR*" -or $_.FriendlyName -like "*Oculus*" -or $_.FriendlyName -like "*Vive*" -or $_.FriendlyName -like "*Index*"}'], 
                    capture_output=True, text=True)
                return bool(result.stdout.strip())
            else:
                # Linux: Check for VR devices in /dev
                vr_devices = ['/dev/hidraw0', '/dev/hidraw1', '/dev/hidraw2']
                for device in vr_devices:
                    if os.path.exists(device):
                        return True
                return False
        except:
            return False
    
    # SteamVR initialization
    def init_steamvr():
        """Initialize SteamVR for Ren'Py"""
        try:
            # Set up VR environment
            os.environ['VR_MODE'] = '1'
            os.environ['STEAMVR_ACTIVE'] = '1'
            
            # Configure OpenGL for VR
            config.gl_enable = True
            config.gl_resize = True
            config.gl_powersave = False
            
            # VR display settings (typical VR headset resolution)
            config.screen_width = 2160   # Combined eye width
            config.screen_height = 1200  # VR headset height
            
            # VR performance settings
            config.image_cache_size = 64
            config.sound_cache_size = 32
            config.predict_statements = 100
            
            # VR interaction settings
            config.mouse_hide_time = 0
            config.hardware_cursor = True
            
            return True
        except:
            return False
    
    # Auto-enable SteamVR with advanced features
    def auto_enable_steamvr():
        """Automatically enable SteamVR if detected"""
        if detect_steamvr():
            if init_steamvr():
                store.steamvr_mode_active = True
                store.current_platform = "steamvr"

                # Initialize advanced VR features
                init_advanced_vr_features()

                return True
        return False

# SteamVR Controller Integration
init python:
    # Controller state tracking
    class VRController:
        def __init__(self, controller_id):
            self.id = controller_id
            self.position = [0.0, 0.0, 0.0]
            self.rotation = [0.0, 0.0, 0.0, 1.0]
            self.trigger_pressed = False
            self.grip_pressed = False
            self.touchpad_pressed = False
            self.touchpad_position = [0.0, 0.0]
            self.menu_pressed = False
            self.connected = False
    
    # Initialize controllers
    store.vr_controller_left = VRController(0)
    store.vr_controller_right = VRController(1)
    
    # VR interaction functions
    def vr_point_at_ui(controller):
        """Check if controller is pointing at UI element"""
        # Simplified ray-casting for UI interaction
        # In a full implementation, this would use OpenVR APIs
        return True
    
    def vr_trigger_click():
        """Handle VR trigger click"""
        # Simulate mouse click for VR interaction
        renpy.invoke_in_new_context(renpy.call_in_new_context, "vr_click_handler")
    
    def vr_grip_action():
        """Handle VR grip action"""
        # Grip can be used for grabbing/moving UI elements
        pass
    
    def vr_menu_action():
        """Handle VR menu button"""
        # Open VR-specific menu
        renpy.show_screen("vr_menu")

# VR Room/Box Setup
init python:
    # VR room boundaries
    class VRRoom:
        def __init__(self):
            self.width = 4.0   # 4 meters wide
            self.height = 3.0  # 3 meters high  
            self.depth = 4.0   # 4 meters deep
            self.center = [0.0, 1.5, 0.0]  # Room center
            self.boundaries_visible = True
            
        def get_boundary_points(self):
            """Get room boundary points for visualization"""
            half_w = self.width / 2
            half_d = self.depth / 2
            
            return [
                [-half_w, 0, -half_d],  # Front left
                [half_w, 0, -half_d],   # Front right
                [half_w, 0, half_d],    # Back right
                [-half_w, 0, half_d],   # Back left
            ]
    
    # Initialize VR room
    store.vr_room = VRRoom()

    # Player position in VR space
    store.vr_player_position = [0.0, 0.0, 0.0]
    store.vr_player_rotation = [0.0, 0.0, 0.0, 1.0]

    # Initialize VR controllers with safe defaults
    store.vr_controller_left = VRController(0)
    store.vr_controller_right = VRController(1)

# VR UI Positioning System
init python:
    # VR UI panel positioning
    def position_ui_in_vr(panel_name, distance=2.0, height=1.5, angle=0.0):
        """Position UI panels in 3D VR space"""
        import math
        
        # Calculate position based on player facing direction
        x = distance * math.sin(math.radians(angle))
        z = distance * math.cos(math.radians(angle))
        y = height
        
        return [x, y, z]
    
    # VR UI panels configuration
    store.vr_ui_panels = {
        "main_menu": position_ui_in_vr("main_menu", 2.5, 1.5, 0),
        "game_selection": position_ui_in_vr("game_selection", 2.0, 1.5, 0),
        "settings": position_ui_in_vr("settings", 1.8, 1.2, -30),
        "inventory": position_ui_in_vr("inventory", 1.8, 1.2, 30),
    }

# SteamVR Transforms and Effects
transform vr_ui_panel:
    # 3D positioning for VR UI panels
    anchor (0.5, 0.5)
    zoom 0.8
    alpha 0.95
    
transform vr_ui_hover:
    # VR hover effect
    zoom 0.8
    easein 0.2 zoom 0.85
    easeout 0.2 zoom 0.8

transform vr_controller_pointer:
    # VR controller laser pointer effect
    alpha 0.7
    zoom 1.0

transform vr_room_boundary:
    # VR room boundary visualization
    alpha 0.3
    zoom 1.0

# SteamVR Styles
init python:
    if not renpy.variant("mobile"):
        # VR-specific styles for PC VR
        style.steamvr_button = Style(style.button)
        style.steamvr_button.minimum = (200, 100)
        style.steamvr_button.padding = (30, 30, 30, 30)
        style.steamvr_button.margin = (15, 15, 15, 15)
        style.steamvr_button.background = "#001a4d"
        style.steamvr_button.hover_background = "#003d99"
        
        style.steamvr_text = Style(style.default)
        style.steamvr_text.size = 32
        style.steamvr_text.color = "#00ccff"
        style.steamvr_text.outlines = [(3, "#000000", 0, 0)]
        
        style.steamvr_title = Style(style.default)
        style.steamvr_title.size = 48
        style.steamvr_title.color = "#ffffff"
        style.steamvr_title.bold = True
        style.steamvr_title.outlines = [(4, "#000000", 0, 0)]

# VR Room Visualization Screen
screen vr_room():
    # VR room boundaries and environment
    if steamvr_mode_active:
        # Room floor grid
        for i in range(-4, 5):
            for j in range(-4, 5):
                add "#333333" alpha 0.2 xpos (400 + i * 50) ypos (300 + j * 50) xsize 2 ysize 50
                add "#333333" alpha 0.2 xpos (400 + i * 50) ypos (300 + j * 50) xsize 50 ysize 2

        # Room boundaries
        if hasattr(store, 'vr_room') and vr_room.boundaries_visible:
            # Front wall
            add "#0066cc" alpha 0.3 xpos 200 ypos 100 xsize 800 ysize 5
            # Back wall
            add "#0066cc" alpha 0.3 xpos 200 ypos 500 xsize 800 ysize 5
            # Left wall
            add "#0066cc" alpha 0.3 xpos 200 ypos 100 xsize 5 ysize 400
            # Right wall
            add "#0066cc" alpha 0.3 xpos 995 ypos 100 xsize 5 ysize 400

        # VR controllers visualization (placeholder)
        if hasattr(store, 'vr_controller_left') and vr_controller_left.connected:
            add "#00ff00" alpha 0.8 xpos 300 ypos 400 xsize 20 ysize 60  # Left controller placeholder

        if hasattr(store, 'vr_controller_right') and vr_controller_right.connected:
            add "#ff0000" alpha 0.8 xpos 500 ypos 400 xsize 20 ysize 60  # Right controller placeholder

# VR Controller Screens
screen vr_controller_left():
    if steamvr_mode_active and hasattr(store, 'vr_controller_left') and vr_controller_left.connected:
        # Left controller visualization
        add "#00ff00" alpha 0.8:
            xpos vr_controller_left.position[0] * 100 + 400
            ypos vr_controller_left.position[1] * 100 + 300
            xsize 15
            ysize 50

        # Left controller laser pointer
        if hasattr(store, 'vr_laser_visible') and vr_laser_visible and vr_controller_left.trigger_pressed:
            add "#ff0000" alpha 0.6:
                xpos vr_controller_left.position[0] * 100 + 400
                ypos vr_controller_left.position[1] * 100 + 300
                xsize 3
                ysize 200

screen vr_controller_right():
    if steamvr_mode_active and hasattr(store, 'vr_controller_right') and vr_controller_right.connected:
        # Right controller visualization
        add "#ff0000" alpha 0.8:
            xpos vr_controller_right.position[0] * 100 + 400
            ypos vr_controller_right.position[1] * 100 + 300
            xsize 15
            ysize 50

        # Right controller laser pointer
        if hasattr(store, 'vr_laser_visible') and vr_laser_visible and vr_controller_right.trigger_pressed:
            add "#00ff00" alpha 0.6:
                xpos vr_controller_right.position[0] * 100 + 400
                ypos vr_controller_right.position[1] * 100 + 300
                xsize 3
                ysize 200

# VR Menu Screen
screen vr_menu():
    modal True
    
    # VR menu positioned in 3D space
    frame at vr_ui_panel:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 500
        background "#001122"
        padding (40, 40)
        
        vbox:
            spacing 30
            xalign 0.5
            
            text "🥽 SteamVR Menu" style "steamvr_title" xalign 0.5
            
            vbox:
                spacing 20
                
                textbutton "Resume Game" style "steamvr_button":
                    action Hide("vr_menu")
                    at vr_ui_hover
                
                textbutton "VR Settings" style "steamvr_button":
                    action Show("vr_settings")
                    at vr_ui_hover
                
                textbutton "Recenter View" style "steamvr_button":
                    action Function(vr_recenter_view)
                    at vr_ui_hover
                
                textbutton "Room Setup" style "steamvr_button":
                    action Show("vr_room_setup")
                    at vr_ui_hover

                textbutton "Advanced Features" style "steamvr_button":
                    action Show("steamvr_advanced_features")
                    at vr_ui_hover

                textbutton "Exit VR" style "steamvr_button":
                    action [Function(disable_steamvr), Hide("vr_menu")]
                    at vr_ui_hover

# VR Settings Screen
screen vr_settings():
    modal True
    
    frame at vr_ui_panel:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 600
        background "#001122"
        padding (40, 40)
        
        vbox:
            spacing 25
            
            text "⚙️ VR Settings" style "steamvr_title" xalign 0.5
            
            hbox:
                spacing 20
                text "Room Boundaries:" style "steamvr_text"
                textbutton "Toggle" style "steamvr_button":
                    action ToggleVariable("vr_room.boundaries_visible")
            
            hbox:
                spacing 20
                text "Controller Haptics:" style "steamvr_text"
                textbutton "On/Off" style "steamvr_button":
                    action ToggleVariable("vr_haptics_enabled")
            
            hbox:
                spacing 20
                text "Comfort Settings:" style "steamvr_text"
                textbutton "Adjust" style "steamvr_button":
                    action Show("vr_comfort_settings")
            
            textbutton "Close" style "steamvr_button":
                action Hide("vr_settings")
                xalign 0.5

# VR Functions
init python:
    def vr_recenter_view():
        """Recenter the VR view"""
        store.vr_player_position = [0.0, 0.0, 0.0]
        store.vr_player_rotation = [0.0, 0.0, 0.0, 1.0]
        renpy.restart_interaction()
    
    def enable_steamvr():
        """Enable SteamVR mode"""
        if auto_enable_steamvr():
            renpy.show_screen("vr_room")
            return True
        return False
    
    def disable_steamvr():
        """Disable SteamVR mode"""
        store.steamvr_mode_active = False
        store.current_platform = "desktop"
        renpy.hide_screen("vr_room")
        renpy.restart_interaction()

# Initialize default VR variables
default steamvr_mode_active = False
default vr_haptics_enabled = True
default vr_comfort_mode = True
