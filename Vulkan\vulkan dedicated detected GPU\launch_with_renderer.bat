@echo off
title Enhanced Graphics Launcher - Netcode the Protogen and More
echo ================================================================
echo Enhanced Graphics Renderer Launcher
echo NVIDIA Cap: GTX 1080 Max | AMD Min: RX 560 | Intel Arc Support
echo ================================================================
echo.

REM Detect system information
echo Detecting system configuration...
for /f "tokens=2 delims==" %%i in ('wmic path win32_VideoController get name /value ^| find "="') do (
    echo GPU Detected: %%i
    set GPU_NAME=%%i
    goto :gpu_detected
)
:gpu_detected

echo OS: Windows (DirectX + OpenGL Support)
echo.

REM Enhanced GPU detection with performance caps
set GPU_TYPE=UNKNOWN
set IS_INTEGRATED=NO
set PERFORMANCE_CAP=NO
set BELOW_MINIMUM=NO
set GPU_VENDOR=UNKNOWN

if "%GPU_NAME%" == "" (
    echo GPU: Unknown - Using conservative settings
    set RECOMMENDED=GL2
    set PERFORMANCE_TIER=LOW
) else (
    echo GPU: %GPU_NAME%

    REM Check for integrated graphics indicators
    echo %GPU_NAME% | findstr /i "integrated UHD Iris Vega.*Graphics Radeon.*Graphics Intel.*Graphics" >nul
    if %errorlevel% == 0 (
        set IS_INTEGRATED=YES
        set GPU_TYPE=INTEGRATED
        echo Type: Integrated Graphics Detected
    )

    REM NVIDIA Detection with GTX 1080 Performance Cap
    echo %GPU_NAME% | findstr /i "nvidia geforce gtx rtx" >nul
    if %errorlevel% == 0 (
        set GPU_VENDOR=NVIDIA
        echo Vendor: NVIDIA Detected

        REM Check for GPUs exceeding GTX 1080 cap
        echo %GPU_NAME% | findstr /i "rtx 40 rtx 30 rtx 20 gtx 1080" >nul
        if %errorlevel% == 0 (
            echo %GPU_NAME% | findstr /i "gtx 1080" >nul
            if %errorlevel% neq 0 (
                echo WARNING: GPU performance will be CAPPED to GTX 1080 level
                set PERFORMANCE_CAP=YES
            )
        )

        if /i "%GPU_NAME:RTX 40=%" neq "%GPU_NAME%" (
            echo Series: RTX 40 Series - PERFORMANCE CAPPED to GTX 1080
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=CAPPED_HIGH
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RTX 30=%" neq "%GPU_NAME%" (
            echo Series: RTX 30 Series - PERFORMANCE CAPPED to GTX 1080
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=CAPPED_HIGH
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RTX 20=%" neq "%GPU_NAME%" (
            echo %GPU_NAME% | findstr /i "2080 ti" >nul
            if %errorlevel% == 0 (
                echo Series: RTX 2080 Ti - PERFORMANCE CAPPED to GTX 1080
                set PERFORMANCE_TIER=CAPPED_HIGH
            ) else (
                echo Series: RTX 20 Series - Standard Performance
                set PERFORMANCE_TIER=HIGH
            )
            set RECOMMENDED=GL2
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:GTX 1080=%" neq "%GPU_NAME%" (
            echo %GPU_NAME% | findstr /i "1080 ti" >nul
            if %errorlevel% == 0 (
                echo Series: GTX 1080 Ti - PERFORMANCE CAPPED to GTX 1080
                set PERFORMANCE_TIER=CAPPED_HIGH
            ) else (
                echo Series: GTX 1080 - REFERENCE PERFORMANCE LEVEL
                set PERFORMANCE_TIER=REFERENCE
            )
            set RECOMMENDED=GL2
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:GTX=%" neq "%GPU_NAME%" (
            echo Series: GTX Series - Standard Performance
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=MEDIUM
            set GPU_TYPE=DISCRETE
        ) else (
            echo Series: NVIDIA GPU - Standard Settings
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=MEDIUM
            set GPU_TYPE=DISCRETE
        )
    )

    REM Intel Detection
    ) else if /i "%GPU_NAME:Intel=%" neq "%GPU_NAME%" (
        echo Vendor: Intel Detected
        if /i "%GPU_NAME:Arc=%" neq "%GPU_NAME%" (
            echo Series: Intel Arc Detected - High Performance Mode
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=HIGH
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:Iris Xe=%" neq "%GPU_NAME%" (
            echo Series: Intel Iris Xe Detected - Medium Performance Mode ^(Integrated^)
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=MEDIUM
            set GPU_TYPE=INTEGRATED
            set IS_INTEGRATED=YES
        ) else if /i "%GPU_NAME:Iris=%" neq "%GPU_NAME%" (
            echo Series: Intel Iris Detected - Low Performance Mode ^(Integrated^)
            set RECOMMENDED=ANGLE2
            set PERFORMANCE_TIER=LOW
            set GPU_TYPE=INTEGRATED
            set IS_INTEGRATED=YES
        ) else if /i "%GPU_NAME:UHD=%" neq "%GPU_NAME%" (
            echo Series: Intel UHD Detected - Low Performance Mode ^(Integrated^)
            set RECOMMENDED=ANGLE2
            set PERFORMANCE_TIER=LOW
            set GPU_TYPE=INTEGRATED
            set IS_INTEGRATED=YES
        ) else (
            echo Series: Intel Integrated Graphics - Conservative Settings
            set RECOMMENDED=ANGLE2
            set PERFORMANCE_TIER=LOW
            set GPU_TYPE=INTEGRATED
            set IS_INTEGRATED=YES
        )

    REM AMD Detection with RX 560 Minimum Requirement
    echo %GPU_NAME% | findstr /i "amd radeon rx" >nul
    if %errorlevel% == 0 (
        set GPU_VENDOR=AMD
        echo Vendor: AMD Detected

        REM Check for GPUs below RX 560 minimum
        echo %GPU_NAME% | findstr /i "rx 550 rx 460 rx 450 r7 r9" >nul
        if %errorlevel% == 0 (
            echo WARNING: GPU does NOT meet RX 560 minimum requirement
            set BELOW_MINIMUM=YES
        )

        if /i "%GPU_NAME:RX 7=%" neq "%GPU_NAME%" (
            echo Series: AMD RX 7000 - Excellent Performance
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=ULTRA
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RX 6=%" neq "%GPU_NAME%" (
            echo %GPU_NAME% | findstr /i "6500 6400" >nul
            if %errorlevel% == 0 (
                echo Series: AMD RX 6000 Low-End - Below RX 560 minimum
                set BELOW_MINIMUM=YES
                set PERFORMANCE_TIER=BELOW_MIN
            ) else (
                echo Series: AMD RX 6000 - High Performance
                set PERFORMANCE_TIER=HIGH
            )
            set RECOMMENDED=GL2
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RX 5=%" neq "%GPU_NAME%" (
            echo Series: AMD RX 5000 - Good Performance
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=HIGH
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RX 560=%" neq "%GPU_NAME%" (
            echo Series: AMD RX 560 - MINIMUM REQUIREMENT MET
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=MINIMUM
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:RX 5=%" neq "%GPU_NAME%" (
            echo %GPU_NAME% | findstr /i "570 580 590" >nul
            if %errorlevel% == 0 (
                echo Series: AMD RX 500 Series - Good Performance
                set PERFORMANCE_TIER=MEDIUM
            ) else (
                echo Series: AMD RX 500 Series - Check minimum requirements
                set PERFORMANCE_TIER=LOW
            )
            set RECOMMENDED=GL2
            set GPU_TYPE=DISCRETE
        ) else if /i "%GPU_NAME:Vega=%" neq "%GPU_NAME%" (
            echo %GPU_NAME% | findstr /i "Radeon.*Graphics" >nul
            if %errorlevel% == 0 (
                echo Series: AMD Vega APU - Integrated Graphics
                set RECOMMENDED=GL2
                set PERFORMANCE_TIER=MEDIUM
                set GPU_TYPE=APU
                set IS_INTEGRATED=YES
            ) else (
                echo Series: AMD Vega - Discrete Graphics
                set RECOMMENDED=GL2
                set PERFORMANCE_TIER=MEDIUM
                set GPU_TYPE=DISCRETE
            )
        ) else (
            echo Series: AMD Graphics - Check compatibility
            set RECOMMENDED=GL2
            set PERFORMANCE_TIER=MEDIUM
            set GPU_TYPE=DISCRETE
        )
    )
    ) else (
        echo Unknown GPU - Using balanced settings
        set RECOMMENDED=GL2
        set PERFORMANCE_TIER=MEDIUM
        set GPU_TYPE=UNKNOWN
    )
)

echo.
echo Recommended Renderer: %RECOMMENDED%
echo Performance Tier: %PERFORMANCE_TIER%
echo GPU Type: %GPU_TYPE%
if "%IS_INTEGRATED%" == "YES" (
    echo.
    echo *** INTEGRATED GRAPHICS DETECTED ***
    echo Optimizations applied for shared memory systems
    echo Conservative settings enabled for better performance
)
echo.
echo ================================================================
echo Available Renderer Options:
echo ================================================================

echo 1. GL2 (Recommended for most systems) - %RECOMMENDED%
echo 2. ANGLE2 (DirectX-based, good for compatibility)
echo 3. GL (Legacy OpenGL, older systems)
echo 4. GLES2 (Mobile/embedded systems)
echo 5. Auto-detect (let Ren'Py choose)
echo 6. Launch with graphics configuration menu
echo 7. Performance benchmark mode
echo.

set /p choice="Select renderer option (1-7): "

if "%choice%"=="1" (
    echo.
    echo Launching with GL2 renderer (OpenGL 2.0+)...
    echo Optimized for: RTX 20/30/40 series, modern AMD/Intel
    set RENPY_RENDERER=gl2
) else if "%choice%"=="2" (
    echo.
    echo Launching with ANGLE2 renderer (DirectX backend)...
    echo Best for: Compatibility issues, older drivers
    set RENPY_RENDERER=angle2
) else if "%choice%"=="3" (
    echo.
    echo Launching with GL renderer (Legacy OpenGL)...
    echo Best for: Older systems, compatibility mode
    set RENPY_RENDERER=gl
) else if "%choice%"=="4" (
    echo.
    echo Launching with GLES2 renderer (OpenGL ES)...
    echo Best for: Mobile devices, embedded systems
    set RENPY_RENDERER=gles2
) else if "%choice%"=="5" (
    echo.
    echo Launching with auto-detection...
    echo Ren'Py will automatically choose the best renderer
) else if "%choice%"=="6" (
    echo.
    echo Launching with graphics configuration menu...
    echo Access the universal graphics test from the main menu
) else if "%choice%"=="7" (
    echo.
    echo Launching in performance benchmark mode...
    echo Monitor performance with the in-game tools
) else (
    echo.
    echo Invalid choice. Using recommended renderer: %RECOMMENDED%
    set RENPY_RENDERER=%RECOMMENDED%
)

echo.
echo Starting game...
echo Press Shift+G during gameplay to change renderer
echo.

REM Launch the game (adjust path if needed)
if exist "netcode_the_protogen_and_more.exe" (
    start "Netcode Game" "netcode_the_protogen_and_more.exe"
) else if exist "netcode the protogen and more.exe" (
    start "Netcode Game" "netcode the protogen and more.exe"
) else (
    echo Game executable not found. Please run from the game directory.
    echo Or launch the game manually and press Shift+G to change renderer.
)

pause
