# Test file for hashtag image references
# This file demonstrates various ways to reference images in comments

label test_hashtag_demo:
    
    # Example 1: Standard hashtag comment format
    # add a file (named either "netcode.png") to the images directory
    
    # Example 2: Background image reference
    # add a file (named either "bedroom.jpg") to the images directory to show it
    
    # Example 3: Character with expression
    # replace it by adding a file named "austin_happy.png" to the images directory
    
    """
    Example 4: Hashtag comments inside docstring
    # add a file (named either "player_smiling.gif") to the images directory
    # add a file (named either "bg_forest.png") to the images directory to show it
    """
    
    # Example 5: Scene background
    # add a file (named either "starry_sky.jpg") to the images directory

    # Example 6: Define character image statements (commented to avoid Ren'Py parsing errors)
    # define character image Netcode = "netcode.jpg"
    # define character image Austin = "austin_happy.png"
    # define character image Player = "player_smiling.gif"
    
    # Test showing the images if they exist
    "Testing hashtag-referenced images..."
    
    # Try to show the images referenced in comments
    scene bg bedroom
    "This should show the bedroom background if bedroom.jpg exists."
    
    show netcode
    "This should show Netcode if netcode.png exists."
    
    show austin happy
    "This should show <PERSON> happy if austin_happy.png exists."
    
    scene bg forest
    "This should show the forest background if bg_forest.png exists."
    
    scene starry sky
    "This should show the starry sky if starry_sky.jpg exists."

    show player
    "This should show the player if player_smiling.gif exists (from define character image)."

    "Hashtag demo complete!"
    return
