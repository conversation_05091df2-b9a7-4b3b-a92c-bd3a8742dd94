## Apple Silicon Chip Database
## Comprehensive specifications for M1, M2, and M3 series chips
## Optimized for iPhone and iPad mobile gaming performance

init python:
    
    # Apple Silicon Chip Database with detailed specifications
    APPLE_CHIP_DATABASE = {
        # M3 Series (3nm) - 2023-2024
        "M3_series": {
            "M3": {
                "process_node": "3nm (N3B)",
                "cpu_cores": "8 (4P + 4E)",
                "gpu_cores": "10",
                "neural_engine": "16-core",
                "memory_bandwidth": "100 GB/s",
                "max_memory": "24GB",
                "transistors": "25 billion",
                "performance_tier": "Standard",
                "ray_tracing": True,
                "av1_decode": True,
                "av1_encode": True,
                "display_support": "Up to 6K",
                "thunderbolt_ports": 2,
                "release_year": 2023,
                "devices": ["MacBook Air", "iMac", "iPad Pro"],
                "mobile_optimized": True
            },
            "M3 Pro": {
                "process_node": "3nm (N3B)",
                "cpu_cores": "11-12 (5-6P + 6E)",
                "gpu_cores": "14-18",
                "neural_engine": "16-core",
                "memory_bandwidth": "150 GB/s",
                "max_memory": "36GB",
                "transistors": "37 billion",
                "performance_tier": "High",
                "ray_tracing": True,
                "av1_decode": True,
                "av1_encode": True,
                "display_support": "Up to 6K + 4K",
                "thunderbolt_ports": 3,
                "release_year": 2023,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\""],
                "mobile_optimized": True
            },
            "M3 Max": {
                "process_node": "3nm (N3B)",
                "cpu_cores": "14-16 (10-12P + 4E)",
                "gpu_cores": "30-40",
                "neural_engine": "16-core",
                "memory_bandwidth": "300-400 GB/s",
                "max_memory": "128GB",
                "transistors": "92 billion",
                "performance_tier": "Very High",
                "ray_tracing": True,
                "av1_decode": True,
                "av1_encode": True,
                "display_support": "Up to 4x 6K + 1x 4K",
                "thunderbolt_ports": 3,
                "release_year": 2023,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\""],
                "mobile_optimized": True
            }
        },
        
        # M2 Series (5nm Enhanced) - 2022-2023
        "M2_series": {
            "M2": {
                "process_node": "5nm Enhanced (N5P)",
                "cpu_cores": "8 (4P + 4E)",
                "gpu_cores": "8-10",
                "neural_engine": "16-core",
                "memory_bandwidth": "100 GB/s",
                "max_memory": "24GB",
                "transistors": "20 billion",
                "performance_tier": "Standard",
                "ray_tracing": False,
                "av1_decode": True,
                "av1_encode": False,
                "display_support": "Up to 6K",
                "thunderbolt_ports": 2,
                "release_year": 2022,
                "devices": ["MacBook Air", "MacBook Pro 13\"", "iPad Pro", "Mac mini"],
                "mobile_optimized": True
            },
            "M2 Pro": {
                "process_node": "5nm Enhanced (N5P)",
                "cpu_cores": "10-12 (6-8P + 4E)",
                "gpu_cores": "16-19",
                "neural_engine": "16-core",
                "memory_bandwidth": "200 GB/s",
                "max_memory": "32GB",
                "transistors": "40 billion",
                "performance_tier": "High",
                "ray_tracing": False,
                "av1_decode": True,
                "av1_encode": False,
                "display_support": "Up to 6K + 4K",
                "thunderbolt_ports": 4,
                "release_year": 2023,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\"", "Mac mini"],
                "mobile_optimized": True
            },
            "M2 Max": {
                "process_node": "5nm Enhanced (N5P)",
                "cpu_cores": "12 (8P + 4E)",
                "gpu_cores": "30-38",
                "neural_engine": "16-core",
                "memory_bandwidth": "400 GB/s",
                "max_memory": "96GB",
                "transistors": "67 billion",
                "performance_tier": "Very High",
                "ray_tracing": False,
                "av1_decode": True,
                "av1_encode": False,
                "display_support": "Up to 4x 6K + 1x 4K",
                "thunderbolt_ports": 4,
                "release_year": 2023,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\""],
                "mobile_optimized": True
            },
            "M2 Ultra": {
                "process_node": "5nm Enhanced (N5P)",
                "cpu_cores": "24 (16P + 8E)",
                "gpu_cores": "60-76",
                "neural_engine": "32-core",
                "memory_bandwidth": "800 GB/s",
                "max_memory": "192GB",
                "transistors": "134 billion",
                "performance_tier": "Maximum",
                "ray_tracing": False,
                "av1_decode": True,
                "av1_encode": False,
                "display_support": "Up to 6x 6K",
                "thunderbolt_ports": 8,
                "release_year": 2023,
                "devices": ["Mac Studio", "Mac Pro"],
                "mobile_optimized": False
            }
        },
        
        # M1 Series (5nm) - 2020-2022
        "M1_series": {
            "M1": {
                "process_node": "5nm (N5)",
                "cpu_cores": "8 (4P + 4E)",
                "gpu_cores": "7-8",
                "neural_engine": "16-core",
                "memory_bandwidth": "68.25 GB/s",
                "max_memory": "16GB",
                "transistors": "16 billion",
                "performance_tier": "Standard",
                "ray_tracing": False,
                "av1_decode": False,
                "av1_encode": False,
                "display_support": "Up to 6K",
                "thunderbolt_ports": 2,
                "release_year": 2020,
                "devices": ["MacBook Air", "MacBook Pro 13\"", "iPad Pro", "iPad Air", "Mac mini", "iMac"],
                "mobile_optimized": True
            },
            "M1 Pro": {
                "process_node": "5nm (N5)",
                "cpu_cores": "8-10 (6-8P + 2E)",
                "gpu_cores": "14-16",
                "neural_engine": "16-core",
                "memory_bandwidth": "200 GB/s",
                "max_memory": "32GB",
                "transistors": "33.7 billion",
                "performance_tier": "High",
                "ray_tracing": False,
                "av1_decode": False,
                "av1_encode": False,
                "display_support": "Up to 6K + 4K",
                "thunderbolt_ports": 3,
                "release_year": 2021,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\""],
                "mobile_optimized": True
            },
            "M1 Max": {
                "process_node": "5nm (N5)",
                "cpu_cores": "10 (8P + 2E)",
                "gpu_cores": "24-32",
                "neural_engine": "16-core",
                "memory_bandwidth": "400 GB/s",
                "max_memory": "64GB",
                "transistors": "57 billion",
                "performance_tier": "Very High",
                "ray_tracing": False,
                "av1_decode": False,
                "av1_encode": False,
                "display_support": "Up to 4x 6K + 1x 4K",
                "thunderbolt_ports": 3,
                "release_year": 2021,
                "devices": ["MacBook Pro 14\"", "MacBook Pro 16\""],
                "mobile_optimized": True
            },
            "M1 Ultra": {
                "process_node": "5nm (N5)",
                "cpu_cores": "20 (16P + 4E)",
                "gpu_cores": "48-64",
                "neural_engine": "32-core",
                "memory_bandwidth": "800 GB/s",
                "max_memory": "128GB",
                "transistors": "114 billion",
                "performance_tier": "Maximum",
                "ray_tracing": False,
                "av1_decode": False,
                "av1_encode": False,
                "display_support": "Up to 4x 6K + 1x 4K",
                "thunderbolt_ports": 6,
                "release_year": 2022,
                "devices": ["Mac Studio"],
                "mobile_optimized": False
            }
        }
    }
    
    # iPhone and iPad Device Mapping to Chips
    APPLE_DEVICE_CHIP_MAPPING = {
        # iPhone Models
        "iPhone 15 Pro Max": "M3",
        "iPhone 15 Pro": "M3",
        "iPhone 15 Plus": "A17 Pro",
        "iPhone 15": "A16 Bionic",
        "iPhone 14 Pro Max": "A16 Bionic",
        "iPhone 14 Pro": "A16 Bionic",
        "iPhone 14 Plus": "A15 Bionic",
        "iPhone 14": "A15 Bionic",
        "iPhone 13 Pro Max": "A15 Bionic",
        "iPhone 13 Pro": "A15 Bionic",
        "iPhone 13": "A15 Bionic",
        "iPhone 13 mini": "A15 Bionic",
        
        # iPad Models with M-series chips
        "iPad Pro 12.9\" (6th gen)": "M2",
        "iPad Pro 11\" (4th gen)": "M2",
        "iPad Pro 12.9\" (5th gen)": "M1",
        "iPad Pro 11\" (3rd gen)": "M1",
        "iPad Air (5th gen)": "M1",
        "iPad Air (4th gen)": "A14 Bionic"
    }
    
    def get_detailed_chip_specs(chip_name):
        """Get detailed specifications for a specific Apple chip"""
        for series, chips in APPLE_CHIP_DATABASE.items():
            if chip_name in chips:
                return chips[chip_name]
        return None
    
    def get_mobile_optimized_chips():
        """Get list of chips optimized for mobile devices"""
        mobile_chips = []
        for series, chips in APPLE_CHIP_DATABASE.items():
            for chip_name, specs in chips.items():
                if specs.get('mobile_optimized', False):
                    mobile_chips.append(chip_name)
        return mobile_chips
    
    def get_chip_performance_comparison():
        """Get performance comparison of all Apple chips"""
        performance_tiers = {
            'Maximum': [],
            'Very High': [],
            'High': [],
            'Standard': []
        }
        
        for series, chips in APPLE_CHIP_DATABASE.items():
            for chip_name, specs in chips.items():
                tier = specs.get('performance_tier', 'Standard')
                if tier in performance_tiers:
                    performance_tiers[tier].append(chip_name)
        
        return performance_tiers
    
    def print_chip_database_summary():
        """Print summary of Apple chip database"""
        print("\n=== APPLE SILICON CHIP DATABASE SUMMARY ===")
        
        for series, chips in APPLE_CHIP_DATABASE.items():
            print(f"\n{series.replace('_', ' ').title()}:")
            for chip_name, specs in chips.items():
                mobile_status = "📱 Mobile" if specs.get('mobile_optimized') else "🖥️ Desktop"
                print(f"  {chip_name}: {specs['cpu_cores']} CPU, {specs['gpu_cores']} GPU, {specs['performance_tier']} {mobile_status}")
        
        print(f"\nTotal chips in database: {sum(len(chips) for chips in APPLE_CHIP_DATABASE.values())}")
        mobile_count = len(get_mobile_optimized_chips())
        print(f"Mobile-optimized chips: {mobile_count}")
        
        print("\n" + "="*50)
