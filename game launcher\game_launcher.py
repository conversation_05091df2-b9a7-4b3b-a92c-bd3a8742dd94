#!/usr/bin/env python3
"""
Universal Game Launcher for Netcode the Protogen and More
Automatically detects and launches separate games
"""

import os
import sys
import json
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, PhotoImage
from pathlib import Path
import threading
import time

class GameLauncher:
    """Main game launcher application"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Netcode the Protogen and More - Game Launcher")
        self.root.geometry("800x600")
        self.root.configure(bg="#1a1a1a")
        
        # Paths
        self.project_root = Path(__file__).parent
        self.games_folder = self.project_root / "separate games"
        self.main_game_path = self.project_root
        
        # Game data
        self.games = {}
        self.selected_game = None
        
        # UI Elements
        self.setup_ui()
        self.scan_for_games()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Configure styles
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 24, 'bold'), background="#1a1a1a", foreground="#00ff00")
        style.configure('Game.TLabel', font=('Arial', 14), background="#2e2e2e", foreground="#ffffff")
        style.configure('Desc.TLabel', font=('Arial', 10), background="#2e2e2e", foreground="#cccccc")
        style.configure('Launch.TButton', font=('Arial', 12, 'bold'))
        
        # Main title
        title_frame = tk.Frame(self.root, bg="#1a1a1a")
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        title_label = ttk.Label(title_frame, text="🎮 NETCODE THE PROTOGEN AND MORE", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="Game Launcher", 
                                 font=('Arial', 12), background="#1a1a1a", foreground="#888888")
        subtitle_label.pack()
        
        # Main content frame
        content_frame = tk.Frame(self.root, bg="#1a1a1a")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Games list frame
        games_frame = tk.LabelFrame(content_frame, text="Available Games", 
                                  font=('Arial', 14, 'bold'), bg="#2e2e2e", fg="#ffffff")
        games_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Games listbox with scrollbar
        listbox_frame = tk.Frame(games_frame, bg="#2e2e2e")
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.games_listbox = tk.Listbox(listbox_frame, font=('Arial', 12), 
                                       bg="#1a1a1a", fg="#ffffff", 
                                       selectbackground="#00ff00", selectforeground="#000000")
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.games_listbox.yview)
        self.games_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.games_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.games_listbox.bind('<<ListboxSelect>>', self.on_game_select)
        
        # Game details frame
        details_frame = tk.LabelFrame(content_frame, text="Game Details", 
                                    font=('Arial', 14, 'bold'), bg="#2e2e2e", fg="#ffffff")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game info display
        info_frame = tk.Frame(details_frame, bg="#2e2e2e")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.game_title_label = ttk.Label(info_frame, text="Select a game", 
                                        font=('Arial', 16, 'bold'), background="#2e2e2e", foreground="#ffffff")
        self.game_title_label.pack(anchor=tk.W, pady=(0, 10))
        
        self.game_desc_label = ttk.Label(info_frame, text="No game selected", 
                                       font=('Arial', 11), background="#2e2e2e", foreground="#cccccc", 
                                       wraplength=300, justify=tk.LEFT)
        self.game_desc_label.pack(anchor=tk.W, pady=(0, 10))
        
        self.game_path_label = ttk.Label(info_frame, text="", 
                                       font=('Arial', 9), background="#2e2e2e", foreground="#888888")
        self.game_path_label.pack(anchor=tk.W, pady=(0, 20))
        
        # Launch button
        self.launch_button = ttk.Button(info_frame, text="🚀 Launch Game", 
                                      command=self.launch_selected_game, style='Launch.TButton')
        self.launch_button.pack(pady=10)
        self.launch_button.configure(state='disabled')
        
        # Control buttons frame
        controls_frame = tk.Frame(self.root, bg="#1a1a1a")
        controls_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Refresh button
        refresh_button = ttk.Button(controls_frame, text="🔄 Refresh Games", command=self.scan_for_games)
        refresh_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Open games folder button
        folder_button = ttk.Button(controls_frame, text="📁 Open Games Folder", command=self.open_games_folder)
        folder_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Settings button
        settings_button = ttk.Button(controls_frame, text="⚙️ Settings", command=self.open_settings)
        settings_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Exit button
        exit_button = ttk.Button(controls_frame, text="❌ Exit", command=self.root.quit)
        exit_button.pack(side=tk.RIGHT)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                             font=('Arial', 9), background="#1a1a1a", foreground="#888888")
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=5)
    
    def scan_for_games(self):
        """Scan for available games"""
        self.status_var.set("Scanning for games...")
        self.games.clear()
        self.games_listbox.delete(0, tk.END)
        
        # Add main game
        main_game = {
            'name': 'Netcode the Protogen and More (Main)',
            'description': 'The main visual novel experience with all features and content.',
            'path': str(self.main_game_path),
            'script_file': 'game/script.rpy',
            'type': 'main',
            'executable': self.find_renpy_executable()
        }
        
        if self.validate_game(main_game):
            self.games['main'] = main_game
            self.games_listbox.insert(tk.END, main_game['name'])
        
        # Scan separate games folder
        if self.games_folder.exists():
            for item in self.games_folder.iterdir():
                if item.is_dir():
                    game_info = self.analyze_game_folder(item)
                    if game_info:
                        game_id = item.name.lower().replace(' ', '_')
                        self.games[game_id] = game_info
                        self.games_listbox.insert(tk.END, game_info['name'])
        
        self.status_var.set(f"Found {len(self.games)} games")
        
        # Auto-select first game
        if self.games_listbox.size() > 0:
            self.games_listbox.selection_set(0)
            self.on_game_select(None)
    
    def analyze_game_folder(self, folder_path):
        """Analyze a game folder to extract information"""
        try:
            # Look for .rpy files
            rpy_files = list(folder_path.glob("*.rpy"))
            
            if not rpy_files:
                return None
            
            # Use the main .rpy file (usually same name as folder or first one found)
            main_script = None
            folder_name = folder_path.name
            
            # Try to find script with same name as folder
            for rpy_file in rpy_files:
                if rpy_file.stem.lower() == folder_name.lower():
                    main_script = rpy_file
                    break
            
            # If not found, use the first .rpy file
            if not main_script:
                main_script = rpy_files[0]
            
            # Extract game information from the script
            game_info = self.extract_game_info(main_script)
            
            # Set default values
            game_info.update({
                'path': str(folder_path),
                'script_file': str(main_script.relative_to(self.project_root)),
                'type': 'separate',
                'executable': self.find_renpy_executable()
            })
            
            return game_info
            
        except Exception as e:
            print(f"Error analyzing game folder {folder_path}: {e}")
            return None
    
    def extract_game_info(self, script_file):
        """Extract game information from script file"""
        game_info = {
            'name': script_file.stem.replace('_', ' ').title(),
            'description': 'A visual novel experience.',
            'version': '1.0',
            'author': 'Unknown'
        }
        
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for common patterns
            lines = content.split('\n')
            for line in lines[:50]:  # Check first 50 lines
                line = line.strip()
                
                # Look for title/name
                if line.startswith('# ') and 'title' in line.lower():
                    game_info['name'] = line[2:].strip()
                elif line.startswith('## '):
                    game_info['name'] = line[3:].strip()
                
                # Look for description
                if 'description' in line.lower() and '=' in line:
                    desc = line.split('=', 1)[1].strip().strip('"\'')
                    if desc:
                        game_info['description'] = desc
                
                # Look for version
                if 'version' in line.lower() and '=' in line:
                    version = line.split('=', 1)[1].strip().strip('"\'')
                    if version:
                        game_info['version'] = version
                
                # Look for author
                if 'author' in line.lower() and '=' in line:
                    author = line.split('=', 1)[1].strip().strip('"\'')
                    if author:
                        game_info['author'] = author
        
        except Exception as e:
            print(f"Error reading script file {script_file}: {e}")
        
        return game_info
    
    def find_renpy_executable(self):
        """Find Ren'Py executable"""
        # Common Ren'Py executable locations
        possible_paths = [
            "renpy.exe",
            "renpy.py",
            "lib/windows-i686/renpy.exe",
            "lib/windows-x86_64/renpy.exe",
            "renpy/renpy.exe"
        ]
        
        for path in possible_paths:
            full_path = self.project_root / path
            if full_path.exists():
                return str(full_path)
        
        # If not found, assume Python can run Ren'Py
        return "python"
    
    def validate_game(self, game_info):
        """Validate that a game can be launched"""
        try:
            # Check if path exists
            if not os.path.exists(game_info['path']):
                return False
            
            # Check if script file exists
            script_path = Path(game_info['path']) / game_info['script_file']
            if game_info['type'] == 'main':
                return script_path.exists()
            else:
                # For separate games, the script file IS the path
                return Path(game_info['script_file']).exists()
            
        except Exception as e:
            print(f"Error validating game: {e}")
            return False
    
    def on_game_select(self, event):
        """Handle game selection"""
        selection = self.games_listbox.curselection()
        if not selection:
            return
        
        selected_index = selection[0]
        selected_name = self.games_listbox.get(selected_index)
        
        # Find the game by name
        for game_id, game_info in self.games.items():
            if game_info['name'] == selected_name:
                self.selected_game = game_id
                self.update_game_details(game_info)
                break
    
    def update_game_details(self, game_info):
        """Update the game details display"""
        self.game_title_label.config(text=game_info['name'])
        
        description = f"{game_info['description']}\n\n"
        description += f"Version: {game_info.get('version', 'Unknown')}\n"
        description += f"Author: {game_info.get('author', 'Unknown')}\n"
        description += f"Type: {game_info['type'].title()} Game"
        
        self.game_desc_label.config(text=description)
        self.game_path_label.config(text=f"Path: {game_info['path']}")
        
        self.launch_button.configure(state='normal')
    
    def launch_selected_game(self):
        """Launch the selected game"""
        if not self.selected_game:
            messagebox.showerror("Error", "No game selected")
            return
        
        game_info = self.games[self.selected_game]
        self.status_var.set(f"Launching {game_info['name']}...")
        
        # Check anti-cheat status before launching
        if not self.check_anticheat_status():
            messagebox.showwarning("Anti-Cheat Warning",
                                 "Anti-cheat system detected issues. Game may not function properly.")

        # Launch in separate thread to avoid blocking UI
        launch_thread = threading.Thread(target=self._launch_game_thread, args=(game_info,))
        launch_thread.daemon = True
        launch_thread.start()
    
    def _launch_game_thread(self, game_info):
        """Launch game in separate thread"""
        try:
            if game_info['type'] == 'main':
                # Launch main game
                if game_info['executable'].endswith('.exe'):
                    subprocess.Popen([game_info['executable']], cwd=game_info['path'])
                else:
                    subprocess.Popen([game_info['executable'], 'game'], cwd=game_info['path'])
            else:
                # Launch separate game
                script_path = Path(game_info['script_file'])
                
                # Create a temporary launcher script
                temp_launcher = self.create_temp_launcher(script_path)
                
                if game_info['executable'].endswith('.exe'):
                    subprocess.Popen([game_info['executable'], str(temp_launcher)], 
                                   cwd=str(self.project_root))
                else:
                    subprocess.Popen([game_info['executable'], str(temp_launcher)], 
                                   cwd=str(self.project_root))
            
            # Update status
            self.root.after(0, lambda: self.status_var.set(f"Launched {game_info['name']}"))
            
            # Optionally close launcher after launching game
            time.sleep(2)
            self.root.after(0, self.root.quit)
            
        except Exception as e:
            error_msg = f"Failed to launch {game_info['name']}: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("Launch Error", error_msg))
            self.root.after(0, lambda: self.status_var.set("Launch failed"))
    
    def create_temp_launcher(self, script_path):
        """Create a temporary launcher script for separate games"""
        temp_dir = self.project_root / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        temp_script = temp_dir / "temp_game.rpy"
        
        # Read the original script
        with open(script_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Create launcher content
        launcher_content = f'''# Temporary launcher for {script_path.name}
# Auto-generated by Game Launcher

# Include the original game content
{original_content}

# Auto-start the game
label start:
    # Initialize any required systems
    call expression "start" from _call_original_start
    return
'''
        
        # Write temporary script
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        return temp_script
    
    def open_games_folder(self):
        """Open the games folder in file explorer"""
        try:
            if self.games_folder.exists():
                os.startfile(str(self.games_folder))
            else:
                self.games_folder.mkdir(parents=True, exist_ok=True)
                os.startfile(str(self.games_folder))
        except Exception as e:
            messagebox.showerror("Error", f"Could not open games folder: {e}")
    
    def open_settings(self):
        """Open settings window"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Launcher Settings")
        settings_window.geometry("400x300")
        settings_window.configure(bg="#2e2e2e")
        
        # Settings content
        ttk.Label(settings_window, text="Launcher Settings", 
                 font=('Arial', 16, 'bold')).pack(pady=20)
        
        # Auto-close option
        auto_close_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_window, text="Auto-close launcher after starting game", 
                       variable=auto_close_var).pack(pady=10)
        
        # Close button
        ttk.Button(settings_window, text="Close",
                  command=settings_window.destroy).pack(pady=20)

    def check_anticheat_status(self):
        """Check anti-cheat system status"""
        try:
            # Check for anti-cheat files
            anticheat_files = [
                "anti-cheat/anticheat_core.rpy",
                "anti-cheat/legal_protection.rpy",
                ".vscode/encryption_core.py"
            ]

            missing_files = []
            for file_path in anticheat_files:
                if not (self.project_root / file_path).exists():
                    missing_files.append(file_path)

            if missing_files:
                print(f"Missing anti-cheat files: {missing_files}")
                return False

            # Check for developer authentication
            dev_config = self.project_root / "anti-cheat" / "master_developer_config.json"
            if dev_config.exists():
                try:
                    with open(dev_config, 'r') as f:
                        config = json.load(f)
                        if config.get('developer_access', {}).get('full_access_enabled', False):
                            print("Developer access detected - bypassing anti-cheat checks")
                            return True
                except:
                    pass

            return True

        except Exception as e:
            print(f"Error checking anti-cheat status: {e}")
            return False

    def create_game_config(self, game_info):
        """Create configuration file for separate games"""
        try:
            config_dir = Path(game_info['path']) / "game"
            config_dir.mkdir(exist_ok=True)

            config_file = config_dir / "launcher_config.json"

            config = {
                'game_name': game_info['name'],
                'launched_from_launcher': True,
                'launcher_version': '1.0',
                'launch_time': time.time(),
                'anticheat_enabled': self.check_anticheat_status(),
                'main_game_path': str(self.project_root)
            }

            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            return str(config_file)

        except Exception as e:
            print(f"Error creating game config: {e}")
            return None
    
    def run(self):
        """Run the launcher"""
        self.root.mainloop()

def main():
    """Main function"""
    try:
        launcher = GameLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
