# Loading Screen System

This directory contains the loading screen system for the Universal Game Router.

## Features

### 🎮 **Animated Loading Screen**
- Progress bar with smooth animation
- Game-specific information display
- Dynamic loading status messages
- Visual transitions and effects

### 🎨 **Customizable Appearance**
- Game-specific color schemes
- Custom backgrounds for different games
- Animated elements (spinners, dots)
- Responsive design for different screen sizes

### ⚡ **Smart Loading Logic**
- Realistic loading simulation
- Configurable loading duration per game
- Error handling and fallback options
- Smooth transitions to game content

## Files

### `loading_screen.rpy`
Main loading screen implementation with:
- Basic loading screen with progress bar
- Quick loading screen for faster transitions
- Custom loading screen with backgrounds
- Error handling screens

### `loading_assets.rpy`
Additional assets and configurations:
- Game-specific loading configurations
- Enhanced loading screens with tips
- Custom color schemes and backgrounds
- Loading animations and effects

## How It Works

1. **Game Selection**: User clicks a game's play button (▶)
2. **Loading Screen**: Animated loading screen appears immediately
3. **Progress Animation**: Progress bar fills from 0% to 100% with realistic timing
4. **Game Launch**: After loading completes, transitions directly to the selected game

## Game-Specific Configurations

Each game can have custom loading settings:

```python
"netcode": {
    "background": "loading_bg_netcode",
    "color_scheme": "#00b4d8",
    "loading_text": "Initializing Protogen systems...",
    "duration": 3.0
}
```

## Loading Stages

The loading process includes realistic stages:
1. **Initializing** (0-25%) - Setting up game systems
2. **Loading Assets** (25-50%) - Loading game files and resources
3. **Preparing World** (50-75%) - Setting up game environment
4. **Finalizing** (75-95%) - Final preparations
5. **Ready** (95-100%) - Game ready to play

## Customization

### Adding Custom Backgrounds
Place background images in `images/backgrounds/` and reference them in `loading_assets.rpy`:

```python
image loading_bg_mygame = "images/backgrounds/mygame_loading.png"
```

### Custom Loading Messages
Add game-specific loading text in the configuration:

```python
"loading_text": "Your custom loading message..."
```

### Adjusting Loading Duration
Modify the duration in seconds:

```python
"duration": 2.5  # 2.5 seconds loading time
```

## Integration

The loading screen is automatically triggered when users click any game's play button (▶) in the game selection interface. No additional setup required - it works seamlessly with the existing game router system.

## Technical Details

- Uses Ren'Py's screen system for UI
- Python-based progress animation
- Configurable timing and appearance
- Error handling for missing assets
- Compatible with all game types in the router

## Future Enhancements

Potential improvements:
- Real loading progress based on actual file loading
- More animation effects and transitions
- Sound effects during loading
- Loading screen mini-games
- Network-based loading for online content
