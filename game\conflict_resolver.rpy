# Conflict Resolver System
# This system creates unique wrapper labels that redirect to the correct game versions
# without modifying the original separate game files

init -10 python:
    # Game context tracking
    current_game_context = None
    
    def set_game_context(game_name):
        """Set the current game context to route labels correctly"""
        global current_game_context
        current_game_context = game_name
        renpy.log(f"Game context set to: {game_name}")
    
    def get_game_context():
        """Get the current game context"""
        return current_game_context

# Wrapper labels that redirect to the correct version based on context
# These override the conflicting labels and provide smart routing

label whisker_and_bean_scene:
    python:
        context = get_game_context()
        
        if context == "lumetric":
            # Route to Lumetric's version by calling the integrated version
            renpy.call("lumetric_whisker_and_bean_wrapper")
        elif context == "netcode":
            # Route to Netcode's version by calling the integrated version  
            renpy.call("netcode_whisker_and_bean_wrapper")
        else:
            # Default fallback - show selection menu
            renpy.call("whisker_and_bean_selection_menu")
    
    return

label howling_hearth_scene:
    python:
        context = get_game_context()
        
        if context == "lumetric":
            renpy.call("lumetric_howling_hearth_wrapper")
        elif context == "netcode":
            renpy.call("netcode_howling_hearth_wrapper")
        else:
            renpy.call("howling_hearth_selection_menu")
    
    return

label binary_bistro_scene:
    python:
        context = get_game_context()
        
        if context == "lumetric":
            renpy.call("lumetric_binary_bistro_wrapper")
        elif context == "netcode":
            renpy.call("netcode_binary_bistro_wrapper")
        else:
            renpy.call("binary_bistro_selection_menu")
    
    return

label act_4_conclusion:
    python:
        context = get_game_context()
        
        if context == "lumetric":
            renpy.call("lumetric_act_4_wrapper")
        elif context == "netcode":
            renpy.call("netcode_act_4_wrapper")
        else:
            renpy.call("act_4_selection_menu")
    
    return

# Wrapper labels for Lumetric versions
label lumetric_whisker_and_bean_wrapper:
    "🐱 Whisker and Bean Scene - Lumetric Version"
    "Loading Lumetric's version of the Whisker and Bean scene..."

    # Call the actual Lumetric scene
    call lumetric_whisker_and_bean_scene_original from _call_lumetric_whisker_and_bean_scene_original

    return

label lumetric_howling_hearth_wrapper:
    "🔥 Howling Hearth Scene - Lumetric Version"
    "Loading Lumetric's version of the Howling Hearth scene..."

    # Call the actual Lumetric scene
    call lumetric_howling_hearth_scene_original from _call_lumetric_howling_hearth_scene_original

    return

label lumetric_binary_bistro_wrapper:
    "💻 Binary Bistro Scene - Lumetric Version"
    "Loading Lumetric's version of the Binary Bistro scene..."

    # Call the actual Lumetric scene
    call lumetric_binary_bistro_scene_original from _call_lumetric_binary_bistro_scene_original

    return

label lumetric_act_4_wrapper:
    "🎬 Act 4 Conclusion - Lumetric Version"
    "Loading Lumetric's version of Act 4 conclusion..."

    # Call the actual Lumetric scene
    call lumetric_act_4_conclusion_original from _call_lumetric_act_4_conclusion_original

    return

# Wrapper labels for Netcode versions
label netcode_whisker_and_bean_wrapper:
    "🤖 Whisker and Bean Scene - Netcode Version"
    "Loading Netcode's version of the Whisker and Bean scene..."

    # Call the actual Netcode scene
    call netcode_whisker_and_bean_scene_original from _call_netcode_whisker_and_bean_scene_original

    return

label netcode_howling_hearth_wrapper:
    "🔥 Howling Hearth Scene - Netcode Version"
    "Loading Netcode's version of the Howling Hearth scene..."

    # Call the actual Netcode scene
    call netcode_howling_hearth_scene_original from _call_netcode_howling_hearth_scene_original

    return

label netcode_binary_bistro_wrapper:
    "💻 Binary Bistro Scene - Netcode Version"
    "Loading Netcode's version of the Binary Bistro scene..."

    # Call the actual Netcode scene
    call netcode_binary_bistro_scene_original from _call_netcode_binary_bistro_scene_original

    return

label netcode_act_4_wrapper:
    "🎬 Act 4 Conclusion - Netcode Version"
    "Loading Netcode's version of Act 4 conclusion..."

    # Call the actual Netcode scene
    call netcode_act_4_conclusion_original from _call_netcode_act_4_conclusion_original

    return

# Selection menus for when no context is set
label whisker_and_bean_selection_menu:
    "🎭 Whisker and Bean Scene - Version Selection"
    "Multiple versions of this scene are available. Which would you like to experience?"
    
    menu:
        "Choose a version:"
        
        "🐱 Lumetric Version":
            $ set_game_context("lumetric")
            call lumetric_whisker_and_bean_wrapper from _call_lumetric_whisker_and_bean_wrapper
            return
        
        "🤖 Netcode Version":
            $ set_game_context("netcode")
            call netcode_whisker_and_bean_wrapper from _call_netcode_whisker_and_bean_wrapper
            return
        
        "Return to game selection":
            jump safe_game_launcher

label howling_hearth_selection_menu:
    "🔥 Howling Hearth Scene - Version Selection"
    
    menu:
        "Choose a version:"
        
        "🐱 Lumetric Version":
            $ set_game_context("lumetric")
            call lumetric_howling_hearth_wrapper from _call_lumetric_howling_hearth_wrapper
            return
        
        "🤖 Netcode Version":
            $ set_game_context("netcode")
            call netcode_howling_hearth_wrapper from _call_netcode_howling_hearth_wrapper
            return
        
        "Return to game selection":
            jump safe_game_launcher

label binary_bistro_selection_menu:
    "💻 Binary Bistro Scene - Version Selection"
    
    menu:
        "Choose a version:"
        
        "🐱 Lumetric Version":
            $ set_game_context("lumetric")
            call lumetric_binary_bistro_wrapper from _call_lumetric_binary_bistro_wrapper
            return
        
        "🤖 Netcode Version":
            $ set_game_context("netcode")
            call netcode_binary_bistro_wrapper from _call_netcode_binary_bistro_wrapper
            return
        
        "Return to game selection":
            jump safe_game_launcher

label act_4_selection_menu:
    "🎬 Act 4 Conclusion - Version Selection"
    
    menu:
        "Choose a version:"
        
        "🐱 Lumetric Version":
            $ set_game_context("lumetric")
            call lumetric_act_4_wrapper from _call_lumetric_act_4_wrapper
            return
        
        "🤖 Netcode Version":
            $ set_game_context("netcode")
            call netcode_act_4_wrapper from _call_netcode_act_4_wrapper
            return
        
        "Return to game selection":
            jump safe_game_launcher



# Test the conflict resolution system
label test_conflict_resolution:
    "🧪 Testing Conflict Resolution System"
    ""
    
    menu:
        "Which version would you like to test?"
        
        "Test Lumetric versions":
            $ set_game_context("lumetric")
            "Testing Lumetric context..."
            call whisker_and_bean_scene from _call_whisker_and_bean_scene
            call howling_hearth_scene from _call_howling_hearth_scene
            call binary_bistro_scene from _call_binary_bistro_scene
            call act_4_conclusion from _call_act_4_conclusion
            jump test_conflict_resolution
        
        "Test Netcode versions":
            $ set_game_context("netcode")
            "Testing Netcode context..."
            call whisker_and_bean_scene from _call_whisker_and_bean_scene_1
            call howling_hearth_scene from _call_howling_hearth_scene_1
            call binary_bistro_scene from _call_binary_bistro_scene_1
            call act_4_conclusion from _call_act_4_conclusion_1
            jump test_conflict_resolution
        
        "Test without context (selection menus)":
            $ set_game_context(None)
            "Testing without context - should show selection menus..."
            call whisker_and_bean_scene from _call_whisker_and_bean_scene_2
            jump test_conflict_resolution
        
        "Back to launcher":
            jump safe_game_launcher
