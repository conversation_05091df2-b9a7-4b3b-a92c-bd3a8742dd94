#!/usr/bin/env python3
"""
Test script for Python Compatibility Bridge
Verifies that the bridge works correctly across Python versions
"""

import sys
import os
import json
from pathlib import Path

def test_compatibility_bridge():
    """Test the compatibility bridge functionality"""
    print("=" * 60)
    print("Python Compatibility Bridge Test")
    print("=" * 60)
    
    print(f"Python Version: {sys.version}")
    print(f"Version Info: {sys.version_info}")
    
    # Test 1: Import compatibility modules
    print("\n1. Testing module imports...")
    try:
        from compatibility_bridge import (
            compatibility_bridge, 
            is_newer_python, 
            get_features, 
            safe_import,
            merge_dicts
        )
        print("   ✅ Compatibility bridge imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import compatibility bridge: {e}")
        return False
    
    try:
        from version_adapter import RenPyVersionAdapter
        print("   ✅ Version adapter imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import version adapter: {e}")
        return False
    
    # Test 2: Check Python version detection
    print("\n2. Testing version detection...")
    is_newer = is_newer_python()
    print(f"   Is newer Python (3.10+): {is_newer}")
    
    features = get_features()
    print("   Available features:")
    for feature, available in features.items():
        status = "✅" if available else "❌"
        print(f"     {feature}: {status}")
    
    # Test 3: Test safe imports
    print("\n3. Testing safe imports...")
    test_modules = ['json', 'os', 'sys', 'pathlib', 'typing']
    for module_name in test_modules:
        module = safe_import(module_name)
        status = "✅" if module else "❌"
        print(f"   {module_name}: {status}")
    
    # Test 4: Test dictionary merging
    print("\n4. Testing dictionary operations...")
    dict1 = {"a": 1, "b": 2}
    dict2 = {"c": 3, "d": 4}
    merged = merge_dicts(dict1, dict2)
    expected = {"a": 1, "b": 2, "c": 3, "d": 4}
    
    if merged == expected:
        print("   ✅ Dictionary merging works correctly")
    else:
        print(f"   ❌ Dictionary merging failed: {merged} != {expected}")
    
    # Test 5: Test version adapter
    print("\n5. Testing version adapter...")
    try:
        adapter = RenPyVersionAdapter()
        config = adapter.create_adapted_config()
        
        print("   ✅ Version adapter created configuration")
        print(f"   Engine Python: {config['compatibility_info']['engine_python_version']}")
        print(f"   Current Python: {config['compatibility_info']['current_python_version']}")
        print(f"   Compatibility mode: {config['compatibility_info']['compatibility_mode']}")
        
    except Exception as e:
        print(f"   ❌ Version adapter test failed: {e}")
    
    # Test 6: Test feature flags
    print("\n6. Testing feature flags...")
    try:
        feature_flags = config['feature_flags']
        important_features = [
            'dict_union_operator',
            'type_hints_support',
            'pathlib_support',
            'renpy_engine_compatible'
        ]
        
        for feature in important_features:
            if feature in feature_flags:
                status = "✅" if feature_flags[feature] else "❌"
                print(f"   {feature}: {status}")
            else:
                print(f"   {feature}: ❓ (not found)")
                
    except Exception as e:
        print(f"   ❌ Feature flags test failed: {e}")
    
    # Test 7: Test module compatibility
    print("\n7. Testing module compatibility...")
    try:
        module_compat = config['module_compatibility']
        for module_name, info in module_compat.items():
            if info['available']:
                version = info.get('version', 'unknown')
                print(f"   ✅ {module_name} (v{version})")
            else:
                print(f"   ❌ {module_name} (not available)")
                
    except Exception as e:
        print(f"   ❌ Module compatibility test failed: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("=" * 60)
    
    return True

def test_renpy_integration():
    """Test Ren'Py integration code"""
    print("\n" + "=" * 60)
    print("Ren'Py Integration Test")
    print("=" * 60)
    
    try:
        from version_adapter import RenPyVersionAdapter
        adapter = RenPyVersionAdapter()
        
        # Generate integration code
        integration_code = adapter.create_bridge_integration()
        print("✅ Integration code generated successfully")
        
        # Test the integration code syntax
        try:
            compile(integration_code, '<integration>', 'exec')
            print("✅ Integration code syntax is valid")
        except SyntaxError as e:
            print(f"❌ Integration code syntax error: {e}")
            return False
        
        # Generate compatibility report
        report = adapter.generate_compatibility_report()
        print("✅ Compatibility report generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Ren'Py integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Starting Python Compatibility Bridge Tests...")
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run tests
    bridge_test = test_compatibility_bridge()
    integration_test = test_renpy_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if bridge_test and integration_test:
        print("🎉 All tests passed! Compatibility bridge is working correctly.")
        print("\nNext steps:")
        print("1. Run 'python \"renpy python config.py\"' to configure your project")
        print("2. Add the integration code to your game/script.rpy file")
        print("3. Your Ren'Py project will work with both old and new Python versions")
        return 0
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit(main())
