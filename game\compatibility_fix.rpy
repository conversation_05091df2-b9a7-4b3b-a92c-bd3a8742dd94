# Compatibility Fix for Save File Rollback Issues
# This file handles transitions between different script versions

init -100 python:
    import traceback
    
    # Compatibility configuration
    compatibility_config = {
        'version': '**********.123v',
        'rollback_fix_enabled': True,
        'clear_rollback_on_error': True,
        'safe_mode': True
    }
    
    def safe_rollback_handler():
        """Handle rollback errors gracefully"""
        try:
            # Clear problematic rollback data
            if hasattr(renpy.game, 'log'):
                # Clear the rollback log if it exists
                renpy.game.log.rollback_limit = 0
                renpy.game.log.rollback_limit = 128
            
            # Reset rollback state
            if hasattr(renpy, 'rollback'):
                renpy.rollback.rollback_limit = 128
                
        except Exception as e:
            # If rollback fix fails, continue anyway
            pass
    
    def compatibility_after_load():
        """Called after loading a save file"""
        try:
            safe_rollback_handler()
        except:
            pass
    
    def compatibility_before_save():
        """Called before saving"""
        try:
            # Ensure clean state before saving
            safe_rollback_handler()
        except:
            pass

    # Register compatibility callbacks
    if compatibility_config['rollback_fix_enabled']:
        config.after_load_callbacks.append(compatibility_after_load)
        # Note: before_save_callbacks doesn't exist in this Ren'Py version

# Simple compatibility handler - no engine overrides
init -99 python:

    def handle_rollback_error():
        """Simple error handler for rollback issues"""
        try:
            # Just clear the rollback limit and reset
            if hasattr(renpy.game, 'log'):
                renpy.game.log.rollback_limit = 128
        except:
            pass

# Label to handle compatibility issues
label compatibility_check:
    
    # This label can be called to ensure compatibility
    python:
        try:
            safe_rollback_handler()
        except:
            pass
    
    return

# Emergency compatibility reset
label emergency_compatibility_reset:

    "Applying compatibility fixes..."

    python:
        try:
            # Simple rollback limit reset
            handle_rollback_error()
        except:
            pass

    "Compatibility fixes applied."

    return

# Auto-fix on startup
init python:

    def startup_compatibility_check():
        """Check and fix compatibility issues on startup"""
        try:
            safe_rollback_handler()
        except:
            pass

    # Run compatibility check on startup - using valid callback
    # Note: init_callbacks doesn't exist, so we'll just run it directly
    startup_compatibility_check()
