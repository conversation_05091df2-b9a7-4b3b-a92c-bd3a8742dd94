# SteamVR Advanced Features: Eye Tracking, Full Body Tracking, and Haptics
# Enhanced VR experience with professional VR hardware support

# Advanced VR Hardware Detection
init -19 python:
    import os
    import sys
    import subprocess
    import json
    
    # Eye tracking detection
    def detect_eye_tracking():
        """Detect eye tracking hardware (Tobii, Varjo, Vive Pro Eye, etc.)"""
        try:
            eye_tracking_devices = []
            
            # Check for Tobii Eye Tracker
            tobii_paths = [
                "C:\\Program Files\\Tobii\\Tobii Eye Tracking",
                "C:\\Program Files (x86)\\Tobii\\Tobii Eye Tracking"
            ]
            
            for path in tobii_paths:
                if os.path.exists(path):
                    eye_tracking_devices.append("Tobii")
                    break
            
            # Check for Varjo Aero eye tracking
            varjo_paths = [
                "C:\\Program Files\\Varjo\\varjo-openxr",
                "C:\\Program Files\\Varjo\\VarjoBase"
            ]
            
            for path in varjo_paths:
                if os.path.exists(path):
                    eye_tracking_devices.append("Varjo")
                    break
            
            # Check for Vive Pro Eye
            if sys.platform.startswith('win'):
                try:
                    result = subprocess.run(['reg', 'query', 'HKLM\\SOFTWARE\\HTC Corporation'], 
                                          capture_output=True, text=True)
                    if 'Vive' in result.stdout:
                        eye_tracking_devices.append("Vive Pro Eye")
                except:
                    pass
            
            # Check for Pico 4 Enterprise eye tracking
            pico_paths = [
                "C:\\Program Files\\Pico\\PicoConnect",
                "C:\\Program Files\\ByteDance\\PicoVR"
            ]
            
            for path in pico_paths:
                if os.path.exists(path):
                    eye_tracking_devices.append("Pico 4 Enterprise")
                    break
            
            store.detected_eye_trackers = eye_tracking_devices
            return len(eye_tracking_devices) > 0
            
        except Exception as e:
            store.detected_eye_trackers = []
            return False
    
    # Full body tracking detection
    def detect_full_body_tracking():
        """Detect full body tracking systems (Vive Trackers, SlimeVR, etc.)"""
        try:
            body_tracking_systems = []
            
            # Check for HTC Vive Trackers
            if detect_steamvr():
                try:
                    # Check SteamVR config for trackers
                    steamvr_config_paths = [
                        os.path.expanduser("~/.steam/steam/config/steamvr.vrsettings"),
                        "C:\\Program Files (x86)\\Steam\\config\\steamvr.vrsettings"
                    ]
                    
                    for config_path in steamvr_config_paths:
                        if os.path.exists(config_path):
                            with open(config_path, 'r') as f:
                                config_data = f.read()
                                if 'tracker' in config_data.lower():
                                    body_tracking_systems.append("HTC Vive Trackers")
                                    break
                except:
                    pass
            
            # Check for SlimeVR
            slimevr_paths = [
                os.path.expanduser("~/SlimeVR"),
                "C:\\Program Files\\SlimeVR",
                "C:\\Users\\<USER>\\AppData\\Local\\SlimeVR"
            ]
            
            for path in slimevr_paths:
                if os.path.exists(path):
                    body_tracking_systems.append("SlimeVR")
                    break
            
            # Check for Kinect body tracking
            kinect_paths = [
                "C:\\Program Files\\Microsoft SDKs\\Kinect",
                "C:\\Program Files (x86)\\Microsoft SDKs\\Kinect"
            ]
            
            for path in kinect_paths:
                if os.path.exists(path):
                    body_tracking_systems.append("Kinect")
                    break
            
            # Check for Mocopi (Sony)
            mocopi_paths = [
                "C:\\Program Files\\Sony\\mocopi",
                os.path.expanduser("~/mocopi")
            ]
            
            for path in mocopi_paths:
                if os.path.exists(path):
                    body_tracking_systems.append("Sony mocopi")
                    break
            
            store.detected_body_trackers = body_tracking_systems
            return len(body_tracking_systems) > 0
            
        except Exception as e:
            store.detected_body_trackers = []
            return False
    
    # Advanced haptics detection
    def detect_advanced_haptics():
        """Detect advanced haptic devices (Ultraleap, bHaptics, etc.)"""
        try:
            haptic_devices = []
            
            # Check for Ultraleap Hand Tracking
            ultraleap_paths = [
                "C:\\Program Files\\Ultraleap\\LeapSDK",
                "C:\\Program Files\\Leap Motion\\Core Assets"
            ]
            
            for path in ultraleap_paths:
                if os.path.exists(path):
                    haptic_devices.append("Ultraleap Hand Tracking")
                    break
            
            # Check for bHaptics
            bhaptics_paths = [
                "C:\\Program Files\\bHaptics\\bHaptics Player",
                os.path.expanduser("~/bHaptics")
            ]
            
            for path in bhaptics_paths:
                if os.path.exists(path):
                    haptic_devices.append("bHaptics Suit")
                    break
            
            # Check for HaptX
            haptx_paths = [
                "C:\\Program Files\\HaptX",
                "C:\\Program Files\\HaptX\\HaptX SDK"
            ]
            
            for path in haptx_paths:
                if os.path.exists(path):
                    haptic_devices.append("HaptX Gloves")
                    break
            
            # Check for SenseGlove
            senseglove_paths = [
                "C:\\Program Files\\SenseGlove",
                "C:\\Program Files\\SenseGlove\\SenseGlove SDK"
            ]
            
            for path in senseglove_paths:
                if os.path.exists(path):
                    haptic_devices.append("SenseGlove")
                    break
            
            store.detected_haptic_devices = haptic_devices
            return len(haptic_devices) > 0
            
        except Exception as e:
            store.detected_haptic_devices = []
            return False

# Advanced VR Features Initialization
init -18 python:
    def init_advanced_vr_features():
        """Initialize advanced VR features if hardware detected"""
        try:
            # Initialize eye tracking
            if detect_eye_tracking():
                store.eye_tracking_active = True
                store.eye_tracking_calibrated = False
                init_eye_tracking()
            
            # Initialize full body tracking
            if detect_full_body_tracking():
                store.full_body_tracking_active = True
                store.body_tracking_calibrated = False
                init_full_body_tracking()
            
            # Initialize advanced haptics
            if detect_advanced_haptics():
                store.advanced_haptics_active = True
                init_advanced_haptics()
            
            return True
        except:
            return False
    
    def init_eye_tracking():
        """Initialize eye tracking system"""
        try:
            # Set up eye tracking variables
            store.eye_position = [0.0, 0.0]  # Normalized screen coordinates
            store.eye_gaze_direction = [0.0, 0.0, -1.0]  # 3D gaze vector
            store.pupil_diameter = [3.0, 3.0]  # Left and right pupil size
            store.blink_state = [False, False]  # Left and right eye blink
            store.eye_tracking_confidence = 0.0  # Tracking quality
            
            # Eye tracking settings
            store.eye_tracking_smoothing = 0.8
            store.gaze_interaction_enabled = True
            store.foveated_rendering = True
            
            return True
        except:
            return False
    
    def init_full_body_tracking():
        """Initialize full body tracking system"""
        try:
            # Body tracking points (standard VR tracking points)
            store.body_tracking_points = {
                'head': [0.0, 1.7, 0.0],
                'chest': [0.0, 1.4, 0.0],
                'waist': [0.0, 1.0, 0.0],
                'left_shoulder': [-0.2, 1.5, 0.0],
                'right_shoulder': [0.2, 1.5, 0.0],
                'left_elbow': [-0.3, 1.2, 0.0],
                'right_elbow': [0.3, 1.2, 0.0],
                'left_hand': [-0.4, 1.0, 0.0],
                'right_hand': [0.4, 1.0, 0.0],
                'left_hip': [-0.1, 0.9, 0.0],
                'right_hip': [0.1, 0.9, 0.0],
                'left_knee': [-0.1, 0.5, 0.0],
                'right_knee': [0.1, 0.5, 0.0],
                'left_foot': [-0.1, 0.0, 0.0],
                'right_foot': [0.1, 0.0, 0.0]
            }
            
            # Body tracking settings
            store.body_tracking_smoothing = 0.7
            store.full_body_avatar_enabled = True
            store.gesture_recognition_enabled = True
            
            return True
        except:
            return False
    
    def init_advanced_haptics():
        """Initialize advanced haptic systems"""
        try:
            # Haptic zones for different devices
            store.haptic_zones = {
                'hands': {'left': False, 'right': False},
                'arms': {'left': False, 'right': False},
                'chest': False,
                'back': False,
                'legs': {'left': False, 'right': False},
                'feet': {'left': False, 'right': False}
            }
            
            # Haptic settings
            store.haptic_intensity = 0.7
            store.haptic_enabled = True
            store.spatial_haptics = True
            
            return True
        except:
            return False

# Eye Tracking System
init python:
    class EyeTracker:
        def __init__(self):
            self.gaze_point = [0.5, 0.5]  # Center of screen
            self.gaze_history = []
            self.fixation_threshold = 0.05  # 5% of screen
            self.fixation_time = 0.0
            self.current_fixation = None
            
        def update_gaze(self, x, y, confidence):
            """Update gaze position"""
            if confidence > 0.7:  # Only use high-confidence data
                # Apply smoothing
                smooth_factor = store.eye_tracking_smoothing
                self.gaze_point[0] = (self.gaze_point[0] * smooth_factor + x * (1 - smooth_factor))
                self.gaze_point[1] = (self.gaze_point[1] * smooth_factor + y * (1 - smooth_factor))
                
                # Add to history
                self.gaze_history.append([x, y, confidence])
                if len(self.gaze_history) > 30:  # Keep last 30 frames
                    self.gaze_history.pop(0)
        
        def detect_fixation(self, ui_element_bounds):
            """Detect if user is looking at UI element"""
            gx, gy = self.gaze_point
            x1, y1, x2, y2 = ui_element_bounds
            
            # Convert to screen coordinates
            screen_x = gx * config.screen_width
            screen_y = gy * config.screen_height
            
            return x1 <= screen_x <= x2 and y1 <= screen_y <= y2
        
        def get_gaze_ui_element(self):
            """Get UI element currently being looked at"""
            # Check common UI areas
            gx, gy = self.gaze_point
            
            if 0.1 <= gx <= 0.9 and 0.1 <= gy <= 0.3:
                return "top_menu"
            elif 0.1 <= gx <= 0.4 and 0.3 <= gy <= 0.9:
                return "left_panel"
            elif 0.4 <= gx <= 0.6 and 0.3 <= gy <= 0.9:
                return "center_panel"
            elif 0.6 <= gx <= 0.9 and 0.3 <= gy <= 0.9:
                return "right_panel"
            
            return None
    
    # Initialize eye tracker
    if steamvr_mode_active and eye_tracking_active:
        store.eye_tracker = EyeTracker()

# Full Body Tracking System
init python:
    class BodyTracker:
        def __init__(self):
            self.tracking_confidence = {}
            self.gesture_buffer = []
            self.current_pose = "standing"
            
        def update_tracking_point(self, point_name, position, confidence):
            """Update body tracking point"""
            if point_name in store.body_tracking_points:
                if confidence > 0.6:  # Only use reliable tracking
                    store.body_tracking_points[point_name] = position
                    self.tracking_confidence[point_name] = confidence
        
        def detect_gesture(self):
            """Detect body gestures"""
            # Simple gesture detection based on hand positions
            left_hand = store.body_tracking_points.get('left_hand', [0, 0, 0])
            right_hand = store.body_tracking_points.get('right_hand', [0, 0, 0])
            head = store.body_tracking_points.get('head', [0, 1.7, 0])
            
            # Wave gesture (hand above head)
            if left_hand[1] > head[1] + 0.2 or right_hand[1] > head[1] + 0.2:
                return "wave"
            
            # Pointing gesture (one hand extended forward)
            if left_hand[2] < -0.3 or right_hand[2] < -0.3:
                return "point"
            
            # Arms crossed
            if abs(left_hand[0] - right_hand[0]) < 0.2 and left_hand[1] > 1.2:
                return "arms_crossed"
            
            return "neutral"
        
        def get_pose_state(self):
            """Get current body pose"""
            head_y = store.body_tracking_points.get('head', [0, 1.7, 0])[1]
            
            if head_y < 1.2:
                return "sitting"
            elif head_y < 0.8:
                return "crouching"
            else:
                return "standing"
    
    # Initialize body tracker
    if steamvr_mode_active and full_body_tracking_active:
        store.body_tracker = BodyTracker()

# Advanced Haptics System
init python:
    class HapticsController:
        def __init__(self):
            self.active_effects = {}
            self.haptic_patterns = {
                'heartbeat': [0.8, 0.0, 0.8, 0.0, 0.3, 0.0],
                'notification': [1.0, 0.0, 0.5, 0.0],
                'selection': [0.6],
                'impact': [1.0, 0.8, 0.6, 0.4, 0.2],
                'ambient': [0.2, 0.3, 0.2, 0.3]
            }
        
        def play_haptic_pattern(self, pattern_name, zones=None, intensity=1.0):
            """Play haptic pattern on specified body zones"""
            if not store.haptic_enabled:
                return
            
            if zones is None:
                zones = ['hands']
            
            pattern = self.haptic_patterns.get(pattern_name, [0.5])
            
            # Apply to specified zones
            for zone in zones:
                if zone in store.haptic_zones:
                    # Simulate haptic effect (in real implementation, this would call haptic APIs)
                    self.active_effects[zone] = {
                        'pattern': pattern,
                        'intensity': intensity * store.haptic_intensity,
                        'duration': len(pattern) * 0.1
                    }
        
        def play_spatial_haptic(self, position, intensity, zones=None):
            """Play haptic effect based on 3D position"""
            if not store.spatial_haptics:
                return
            
            # Calculate which body parts should feel the effect based on position
            player_pos = [0.0, 1.0, 0.0]  # Player center
            distance = ((position[0] - player_pos[0])**2 + 
                       (position[1] - player_pos[1])**2 + 
                       (position[2] - player_pos[2])**2)**0.5
            
            # Reduce intensity based on distance
            spatial_intensity = max(0.0, intensity * (2.0 - distance) / 2.0)
            
            if spatial_intensity > 0.1:
                self.play_haptic_pattern('ambient', zones, spatial_intensity)
        
        def stop_all_haptics(self):
            """Stop all haptic effects"""
            self.active_effects.clear()
    
    # Initialize haptics controller
    if steamvr_mode_active and advanced_haptics_active:
        store.haptics_controller = HapticsController()

# Initialize default advanced VR variables
default eye_tracking_active = False
default full_body_tracking_active = False
default advanced_haptics_active = False
default eye_tracking_calibrated = False
default body_tracking_calibrated = False
default detected_eye_trackers = []
default detected_body_trackers = []
default detected_haptic_devices = []

# Advanced VR Features Screen
screen steamvr_advanced_features():
    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 700
        background "#001122"
        padding (40, 40)

        vbox:
            spacing 25

            text "🔬 Advanced VR Features" style "steamvr_title" xalign 0.5

            # Eye Tracking Section
            frame:
                xsize 820
                background "#002244"
                padding (20, 20)

                vbox:
                    spacing 15

                    text "👁️ Eye Tracking" style "steamvr_text" color "#00ffff"

                    if eye_tracking_active:
                        text "✅ Eye Tracking: ACTIVE" style "steamvr_text" color "#00ff88"
                        text "Detected Devices: [', '.join(detected_eye_trackers)]" style "steamvr_text" size 16

                        hbox:
                            spacing 20
                            textbutton "Calibrate Eyes" style "steamvr_button":
                                action Show("eye_tracking_calibration")
                            textbutton "Gaze Settings" style "steamvr_button":
                                action Show("gaze_settings")
                    else:
                        text "❌ Eye Tracking: NOT DETECTED" style "steamvr_text" color "#ff6666"
                        text "Supported: Tobii, Varjo Aero, Vive Pro Eye, Pico 4 Enterprise" style "steamvr_text" size 14

            # Full Body Tracking Section
            frame:
                xsize 820
                background "#002244"
                padding (20, 20)

                vbox:
                    spacing 15

                    text "🏃 Full Body Tracking" style "steamvr_text" color "#00ffff"

                    if full_body_tracking_active:
                        text "✅ Body Tracking: ACTIVE" style "steamvr_text" color "#00ff88"
                        text "Detected Systems: [', '.join(detected_body_trackers)]" style "steamvr_text" size 16

                        hbox:
                            spacing 20
                            textbutton "Calibrate Body" style "steamvr_button":
                                action Show("body_tracking_calibration")
                            textbutton "Avatar Settings" style "steamvr_button":
                                action Show("avatar_settings")
                    else:
                        text "❌ Body Tracking: NOT DETECTED" style "steamvr_text" color "#ff6666"
                        text "Supported: Vive Trackers, SlimeVR, Kinect, Sony mocopi" style "steamvr_text" size 14

            # Advanced Haptics Section
            frame:
                xsize 820
                background "#002244"
                padding (20, 20)

                vbox:
                    spacing 15

                    text "🤲 Advanced Haptics" style "steamvr_text" color "#00ffff"

                    if advanced_haptics_active:
                        text "✅ Advanced Haptics: ACTIVE" style "steamvr_text" color "#00ff88"
                        text "Detected Devices: [', '.join(detected_haptic_devices)]" style "steamvr_text" size 16

                        hbox:
                            spacing 20
                            textbutton "Test Haptics" style "steamvr_button":
                                action Function(test_all_haptics)
                            textbutton "Haptic Settings" style "steamvr_button":
                                action Show("haptic_settings")
                    else:
                        text "❌ Advanced Haptics: NOT DETECTED" style "steamvr_text" color "#ff6666"
                        text "Supported: Ultraleap, bHaptics, HaptX, SenseGlove" style "steamvr_text" size 14

            textbutton "Close" style "steamvr_button":
                action Hide("steamvr_advanced_features")
                xalign 0.5

# Eye Tracking Calibration Screen
screen eye_tracking_calibration():
    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        background "#001122"
        padding (30, 30)

        vbox:
            spacing 30
            xalign 0.5

            text "👁️ Eye Tracking Calibration" style "steamvr_title" xalign 0.5

            if not eye_tracking_calibrated:
                text "Follow the calibration points with your eyes:" style "steamvr_text"

                # Calibration points
                for i in range(9):
                    $ x_pos = (i % 3) * 250 + 150
                    $ y_pos = (i // 3) * 150 + 150

                    add "#ffff00" alpha 0.8:
                        xpos x_pos
                        ypos y_pos
                        xsize 20
                        ysize 20

                textbutton "Start Calibration" style "steamvr_button":
                    action Function(start_eye_calibration)
                    xalign 0.5
            else:
                text "✅ Eye tracking calibrated successfully!" style "steamvr_text" color "#00ff88"

                # Show current gaze position
                if eye_tracking_active:
                    text "Current gaze: ([eye_tracker.gaze_point[0]:.2f], [eye_tracker.gaze_point[1]:.2f])" style "steamvr_text"

                textbutton "Recalibrate" style "steamvr_button":
                    action Function(start_eye_calibration)
                    xalign 0.5

            textbutton "Close" style "steamvr_button":
                action Hide("eye_tracking_calibration")
                xalign 0.5

# Body Tracking Calibration Screen
screen body_tracking_calibration():
    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        background "#001122"
        padding (30, 30)

        vbox:
            spacing 25

            text "🏃 Full Body Tracking Calibration" style "steamvr_title" xalign 0.5

            text "Stand in T-pose for calibration:" style "steamvr_text"

            # Body tracking visualization
            frame:
                xsize 740
                ysize 300
                background "#002244"
                padding (20, 20)

                # Simple stick figure representation
                add "#ffffff" alpha 0.8 xpos 370 ypos 50 xsize 2 ysize 60   # Head to chest
                add "#ffffff" alpha 0.8 xpos 370 ypos 110 xsize 2 ysize 80  # Chest to waist
                add "#ffffff" alpha 0.8 xpos 320 ypos 80 xsize 100 ysize 2  # Arms
                add "#ffffff" alpha 0.8 xpos 370 ypos 190 xsize 2 ysize 80  # Waist to legs
                add "#ffffff" alpha 0.8 xpos 350 ypos 270 xsize 40 ysize 2  # Left leg
                add "#ffffff" alpha 0.8 xpos 370 ypos 270 xsize 40 ysize 2  # Right leg

                # Tracking points
                for point_name, position in body_tracking_points.items():
                    $ screen_x = position[0] * 100 + 370
                    $ screen_y = 280 - position[1] * 100

                    if point_name in body_tracker.tracking_confidence:
                        $ confidence = body_tracker.tracking_confidence[point_name]
                        $ color = "#00ff00" if confidence > 0.8 else "#ffff00" if confidence > 0.6 else "#ff0000"
                        add color alpha 0.8 xpos screen_x-3 ypos screen_y-3 xsize 6 ysize 6

            hbox:
                spacing 30
                xalign 0.5

                textbutton "Start Calibration" style "steamvr_button":
                    action Function(start_body_calibration)

                textbutton "Reset Pose" style "steamvr_button":
                    action Function(reset_body_pose)

            textbutton "Close" style "steamvr_button":
                action Hide("body_tracking_calibration")
                xalign 0.5

# Haptic Settings Screen
screen haptic_settings():
    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        background "#001122"
        padding (30, 30)

        vbox:
            spacing 25

            text "🤲 Haptic Settings" style "steamvr_title" xalign 0.5

            # Haptic intensity slider
            hbox:
                spacing 20
                text "Haptic Intensity:" style "steamvr_text"
                bar value VariableValue("haptic_intensity", 1.0) xsize 200
                text "[haptic_intensity:.1f]" style "steamvr_text"

            # Haptic zones
            text "Active Haptic Zones:" style "steamvr_text"

            grid 3 3:
                spacing 10

                textbutton "Left Hand" style "steamvr_button":
                    action ToggleDict(haptic_zones['hands'], 'left')
                    text_color "#00ff00" if haptic_zones['hands']['left'] else "#666666"

                textbutton "Chest" style "steamvr_button":
                    action ToggleVariable("haptic_zones['chest']")
                    text_color "#00ff00" if haptic_zones['chest'] else "#666666"

                textbutton "Right Hand" style "steamvr_button":
                    action ToggleDict(haptic_zones['hands'], 'right')
                    text_color "#00ff00" if haptic_zones['hands']['right'] else "#666666"

                textbutton "Left Arm" style "steamvr_button":
                    action ToggleDict(haptic_zones['arms'], 'left')
                    text_color "#00ff00" if haptic_zones['arms']['left'] else "#666666"

                textbutton "Back" style "steamvr_button":
                    action ToggleVariable("haptic_zones['back']")
                    text_color "#00ff00" if haptic_zones['back'] else "#666666"

                textbutton "Right Arm" style "steamvr_button":
                    action ToggleDict(haptic_zones['arms'], 'right')
                    text_color "#00ff00" if haptic_zones['arms']['right'] else "#666666"

                textbutton "Left Leg" style "steamvr_button":
                    action ToggleDict(haptic_zones['legs'], 'left')
                    text_color "#00ff00" if haptic_zones['legs']['left'] else "#666666"

                textbutton "Feet" style "steamvr_button":
                    action ToggleDict(haptic_zones['feet'], 'left')
                    text_color "#00ff00" if haptic_zones['feet']['left'] else "#666666"

                textbutton "Right Leg" style "steamvr_button":
                    action ToggleDict(haptic_zones['legs'], 'right')
                    text_color "#00ff00" if haptic_zones['legs']['right'] else "#666666"

            textbutton "Close" style "steamvr_button":
                action Hide("haptic_settings")
                xalign 0.5
