#!/usr/bin/env python3
"""
Python Compatibility Bridge for Ren'Py
Bridges Python 3.9.10 (current engine) with newer Python versions (3.10+, 3.11+, 3.12+)
Maintains backward compatibility while enabling newer features when available
"""

import sys
import os
import importlib
import warnings
from typing import Any, Dict, List, Optional, Union

class PythonCompatibilityBridge:
    """
    Compatibility bridge that allows old Python 3.9.10 code to work with newer versions
    and enables newer Python features when available
    """
    
    def __init__(self):
        self.python_version = sys.version_info
        self.is_legacy = self.python_version < (3, 10)
        self.features = self._detect_features()
        
    def _detect_features(self) -> Dict[str, bool]:
        """Detect available Python features across versions"""
        features = {
            # Python 3.9 features (baseline)
            'dict_union': self.python_version >= (3, 9),
            'type_hints': self.python_version >= (3, 9),
            'pathlib': True,
            'asyncio': True,
            
            # Python 3.10+ features
            'pattern_matching': self.python_version >= (3, 10),
            'union_types': self.python_version >= (3, 10),
            'better_error_messages': self.python_version >= (3, 10),
            
            # Python 3.11+ features
            'exception_groups': self.python_version >= (3, 11),
            'improved_asyncio': self.python_version >= (3, 11),
            'faster_cpython': self.python_version >= (3, 11),
            
            # Python 3.12+ features
            'improved_error_locations': self.python_version >= (3, 12),
            'per_interpreter_gil': self.python_version >= (3, 12),
            'improved_pathlib': self.python_version >= (3, 12),
        }
        return features
    
    def safe_import(self, module_name: str, fallback=None):
        """Safely import modules with fallback for older versions"""
        try:
            return importlib.import_module(module_name)
        except ImportError:
            if fallback:
                return fallback
            return None
    
    def get_typing_module(self):
        """Get appropriate typing module for the Python version"""
        if self.python_version >= (3, 10):
            # Use newer typing features
            try:
                from typing import Union, Optional, List, Dict, Any
                # Python 3.10+ union syntax available
                return {
                    'Union': Union,
                    'Optional': Optional,
                    'List': List,
                    'Dict': Dict,
                    'Any': Any,
                    'supports_new_union': True
                }
            except ImportError:
                pass
        
        # Fallback to Python 3.9 typing
        from typing import Union, Optional, List, Dict, Any
        return {
            'Union': Union,
            'Optional': Optional,
            'List': List,
            'Dict': Dict,
            'Any': Any,
            'supports_new_union': False
        }
    
    def dict_merge(self, dict1: dict, dict2: dict) -> dict:
        """Merge dictionaries using the best available method"""
        if self.features['dict_union']:
            # Python 3.9+ dict union operator
            return dict1 | dict2
        else:
            # Fallback method
            result = dict1.copy()
            result.update(dict2)
            return result
    
    def safe_pathlib_operations(self, path_str: str):
        """Safe pathlib operations across Python versions"""
        from pathlib import Path
        
        path = Path(path_str)
        
        # Enhanced operations for newer versions
        if self.features['improved_pathlib']:
            # Python 3.12+ improvements
            return {
                'path': path,
                'exists': path.exists(),
                'is_file': path.is_file(),
                'is_dir': path.is_dir(),
                'resolve': path.resolve(),
                'enhanced_features': True
            }
        else:
            # Standard pathlib for older versions
            return {
                'path': path,
                'exists': path.exists(),
                'is_file': path.is_file(),
                'is_dir': path.is_dir(),
                'resolve': path.resolve(),
                'enhanced_features': False
            }
    
    def create_compatibility_wrapper(self, old_function, new_function=None):
        """Create a wrapper that uses new function if available, falls back to old"""
        def wrapper(*args, **kwargs):
            if new_function and self.python_version >= (3, 10):
                try:
                    return new_function(*args, **kwargs)
                except Exception:
                    pass
            return old_function(*args, **kwargs)
        return wrapper
    
    def get_json_module(self):
        """Get JSON module with enhanced features for newer Python versions"""
        import json
        
        if self.python_version >= (3, 11):
            # Enhanced JSON features in Python 3.11+
            return {
                'module': json,
                'enhanced_performance': True,
                'better_error_messages': True
            }
        else:
            return {
                'module': json,
                'enhanced_performance': False,
                'better_error_messages': False
            }
    
    def setup_warnings_compatibility(self):
        """Setup warnings to handle version differences"""
        if self.is_legacy:
            # Suppress warnings about newer Python features
            warnings.filterwarnings('ignore', category=DeprecationWarning)
            warnings.filterwarnings('ignore', message='.*typing.*')
        
        # Custom warning for compatibility issues
        def compatibility_warning(message):
            warnings.warn(f"Python Compatibility: {message}", UserWarning, stacklevel=2)
        
        return compatibility_warning
    
    def get_asyncio_module(self):
        """Get asyncio with version-appropriate features"""
        import asyncio
        
        if self.features['improved_asyncio']:
            # Python 3.11+ asyncio improvements
            return {
                'module': asyncio,
                'task_groups': True,
                'improved_performance': True
            }
        else:
            return {
                'module': asyncio,
                'task_groups': False,
                'improved_performance': False
            }
    
    def create_renpy_integration(self):
        """Create Ren'Py-specific compatibility integration"""
        integration = {
            'python_version': f"{self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}",
            'is_legacy_engine': self.is_legacy,
            'available_features': self.features,
            'compatibility_mode': True
        }
        
        # Add Ren'Py-specific compatibility functions
        def safe_renpy_call(func_name, *args, **kwargs):
            """Safely call Ren'Py functions with compatibility handling"""
            try:
                if 'renpy' in globals():
                    func = getattr(renpy, func_name, None)
                    if func:
                        return func(*args, **kwargs)
            except Exception as e:
                print(f"Ren'Py compatibility warning: {func_name} failed: {e}")
            return None
        
        integration['safe_renpy_call'] = safe_renpy_call
        return integration

# Global compatibility bridge instance
compatibility_bridge = PythonCompatibilityBridge()

# Convenience functions for easy access
def is_newer_python():
    """Check if running on Python 3.10+"""
    return compatibility_bridge.python_version >= (3, 10)

def get_features():
    """Get available Python features"""
    return compatibility_bridge.features

def safe_import(module_name, fallback=None):
    """Safely import with fallback"""
    return compatibility_bridge.safe_import(module_name, fallback)

def merge_dicts(dict1, dict2):
    """Merge dictionaries using best available method"""
    return compatibility_bridge.dict_merge(dict1, dict2)

# Setup compatibility warnings
compatibility_warning = compatibility_bridge.setup_warnings_compatibility()

# Export compatibility information
__all__ = [
    'PythonCompatibilityBridge',
    'compatibility_bridge',
    'is_newer_python',
    'get_features',
    'safe_import',
    'merge_dicts',
    'compatibility_warning'
]
