# Qualcomm Snapdragon Mobile Detection System

This comprehensive system automatically detects Qualcomm Snapdragon 835 and higher chipsets on Android devices, configuring Ren'Py for optimal mobile gaming performance with Vulkan support.

## Features

### 📱 **Snapdragon Chipset Support**
- **8 Gen Series**: Snapdragon 8 Gen 3, 8 Gen 2, 8 Gen 1 (Flagship)
- **800 Series**: Snapdragon 888, 865, 855, 845, 835 (High-End to Upper Mid-Range)
- **700 Series**: Snapdragon 7 Gen 3, 780G, 778G (Mid-Range)
- **Minimum Requirement**: Snapdragon 835 and higher

### 🎮 **Vulkan API Support**
- **Vulkan 1.3**: Latest 8 Gen series chipsets
- **Vulkan 1.1**: Most 800 and 700 series chipsets
- **Vulkan 1.0**: Snapdragon 835 (minimum support)
- **Adreno GPU**: Optimized for Adreno 540 and higher

### ⚡ **Performance Tiers**

#### **Flagship Performance** (8 Gen Series)
```
Process: 4nm-5nm
CPU: 8 cores (1+3+4 or 1+5+2)
GPU: Adreno 730-750
Vulkan: 1.1-1.3
Settings: 2K textures, 90fps, Ray Tracing
```

#### **High-End Performance** (888, 865)
```
Process: 5nm-7nm
CPU: 8 cores (1+3+4)
GPU: Adreno 650-660
Vulkan: 1.1
Settings: 2K textures, 60fps, Advanced Effects
```

#### **Upper Mid-Range** (855, 845, 835)
```
Process: 7nm-10nm
CPU: 8 cores (4+4)
GPU: Adreno 540-640
Vulkan: 1.0-1.1
Settings: 1K textures, 60fps, Standard Effects
```

#### **Mid-Range** (700 Series)
```
Process: 4nm-8nm
CPU: 8 cores (1+3+4)
GPU: Adreno 642-720
Vulkan: 1.1
Settings: 1K textures, 30-60fps, Balanced
```

## System Components

### 1. **Snapdragon Mobile Detector** (`snapdragon_mobile_detector.rpy`)
Main detection and configuration system:
- **Android Detection**: Identifies Android devices and mobile status
- **Chipset Recognition**: Detects specific Snapdragon models via /proc/cpuinfo and system properties
- **Performance Analysis**: Determines optimal settings based on chipset capabilities
- **Vulkan Configuration**: Enables Vulkan optimizations when supported

### 2. **Snapdragon Chipset Database** (`snapdragon_chipset_database.rpy`)
Comprehensive chipset specifications:
- **Technical Specs**: CPU/GPU cores, process node, memory bandwidth
- **Performance Metrics**: AnTuTu scores, gaming performance ratings
- **Vulkan Support**: Version compatibility and feature support
- **Device Mapping**: Links popular Android devices to their chipsets

## How It Works

### 🔍 **Detection Process**
1. **Platform Check**: Verifies running on Android/Linux platform
2. **Mobile Detection**: Confirms mobile device via Ren'Py variants
3. **Chipset Identification**: Reads /proc/cpuinfo and system properties
4. **Minimum Check**: Ensures chipset meets Snapdragon 835 requirement
5. **Capability Analysis**: Looks up chipset specifications in database
6. **Settings Application**: Applies optimal Ren'Py configuration

### ⚙️ **Configuration Examples**

#### **Snapdragon 8 Gen 3 (Flagship)**
```
Renderer: gles2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 2048
Anisotropic Filtering: Enabled
Framerate Limit: 90 FPS
Vulkan: Enabled (1.3)
Quality Preset: flagship_android
```

#### **Snapdragon 865 (High-End)**
```
Renderer: gles2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 2048
Anisotropic Filtering: Enabled
Framerate Limit: 60 FPS
Vulkan: Enabled (1.1)
Quality Preset: high_android
```

#### **Snapdragon 835 (Upper Mid-Range)**
```
Renderer: gles2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 1024
Anisotropic Filtering: Disabled
Framerate Limit: 60 FPS
Vulkan: Enabled (1.0)
Quality Preset: standard_android
```

### 🎯 **Android Optimizations**
- **Power Management**: Battery-optimized settings for mobile gaming
- **Thermal Throttling**: Performance scaling to prevent overheating
- **Memory Management**: Efficient texture streaming for limited RAM
- **Vulkan Integration**: Hardware-accelerated graphics when supported

## Supported Devices

### 📱 **Popular Android Devices**

#### **Samsung Galaxy Series**
- **Galaxy S24 Ultra/+/Standard**: Snapdragon 8 Gen 3
- **Galaxy S23 Ultra/+/Standard**: Snapdragon 8 Gen 2
- **Galaxy S22 Ultra**: Snapdragon 8 Gen 1
- **Galaxy S21 Ultra**: Snapdragon 888
- **Galaxy S20 Ultra**: Snapdragon 865

#### **OnePlus Series**
- **OnePlus 12**: Snapdragon 8 Gen 3
- **OnePlus 11**: Snapdragon 8 Gen 2
- **OnePlus 10 Pro**: Snapdragon 8 Gen 1
- **OnePlus 9 Pro**: Snapdragon 888
- **OnePlus 8 Pro**: Snapdragon 865

#### **Xiaomi Series**
- **Xiaomi 14 Ultra**: Snapdragon 8 Gen 3
- **Xiaomi 13 Ultra**: Snapdragon 8 Gen 2
- **Xiaomi 12 Ultra**: Snapdragon 8 Gen 1
- **Xiaomi Mi 11 Ultra**: Snapdragon 888

#### **Google Pixel Series**
- **Pixel 7a/6a**: Snapdragon 778G (some variants)

## Usage

### 🚀 **Automatic Operation**
The system runs automatically when Ren'Py starts on Android devices:
```renpy
# Automatic detection at startup
init:
    python:
        if platform.system() == "Linux" and 'android' in renpy.variant():
            detect_and_configure_snapdragon()
```

### 🧪 **Manual Testing**
```renpy
# Test Snapdragon detection
call test_snapdragon_detection

# Check current chipset performance
call check_snapdragon_performance
```

### 📊 **Console Output Example**
```
=== QUALCOMM SNAPDRAGON DETECTION ===
✅ Android mobile device detected
✅ Snapdragon 8 Gen 2 detected

QUALCOMM SNAPDRAGON DETECTION REPORT
================================================================
Device Type: Android Mobile Device
Detected Chipset: Snapdragon 8 Gen 2
Chipset Series: 8 Gen
Chipset Generation: Gen 2
Adreno GPU: Adreno 740
Performance Tier: Flagship
Vulkan Support: Yes

Chipset Capabilities:
  Architecture: ARM64
  Process Node: 4nm (TSMC N4)
  CPU Cores: 8 (1+4+3)
  CPU Architecture: Cortex-X3/A715/A510
  Max CPU Frequency: 3.2 GHz
  Vulkan Version: 1.3
  OpenGL ES: 3.2
  Memory Type: LPDDR5X
  Ray Tracing: Yes
  Variable Rate Shading: Yes

Applied Ren'Py Settings:
  renderer: gles2
  max_texture_size: 2048
  anisotropic_filtering: True
  framerate_limit: 90
  vulkan_enabled: True
  quality_preset: flagship_android
```

## Technical Specifications

### 🔧 **8 Gen Series (4nm-5nm)**
- **Process**: TSMC N4/N4P (4nm)
- **Release**: 2021-2024
- **Features**: Ray tracing, Vulkan 1.3, mesh shaders
- **Performance**: 1.5M+ AnTuTu score

### 🔧 **800 Series (5nm-10nm)**
- **Process**: TSMC N7/Samsung 5LPE (5nm-10nm)
- **Release**: 2016-2021
- **Features**: Vulkan 1.1, variable rate shading
- **Performance**: 200K-800K AnTuTu score

### 🔧 **700 Series (4nm-8nm)**
- **Process**: TSMC N4/N6 (4nm-8nm)
- **Release**: 2020-2023
- **Features**: Vulkan 1.1, power efficiency
- **Performance**: 500K-700K AnTuTu score

## Performance Expectations

### 📈 **Gaming Performance**

#### **Visual Novel Performance**
- **8 Gen 3**: 2K@90fps, Ultra settings, Ray tracing
- **8 Gen 2**: 2K@90fps, Ultra settings
- **888/865**: 2K@60fps, High settings
- **855/845**: 1080p@60fps, Standard settings
- **835**: 1080p@60fps, Conservative settings

#### **Vulkan Benefits**
- **Reduced CPU Overhead**: Better performance on multi-core CPUs
- **Improved GPU Utilization**: More efficient graphics rendering
- **Advanced Features**: Ray tracing, variable rate shading (newer chipsets)
- **Better Battery Life**: More efficient rendering pipeline

### 🔋 **Power Efficiency**
- **Thermal Management**: Automatic performance scaling
- **Battery Optimization**: Balanced performance and battery life
- **Background Processing**: Efficient when app is backgrounded
- **Adaptive Performance**: Dynamic quality adjustment

## Troubleshooting

### ❌ **Common Issues**

#### "Not running on Android device"
- **Cause**: Running on non-Android platform
- **Solution**: System only works on Android devices

#### "Chipset does not meet Snapdragon 835 minimum requirement"
- **Cause**: Older Snapdragon chipset (820, 810, etc.)
- **Solution**: Upgrade device or accept limited performance

#### "Could not detect Snapdragon chipset"
- **Cause**: Non-Snapdragon chipset (MediaTek, Exynos, etc.)
- **Solution**: Falls back to generic Android settings

### 🔧 **Manual Override**
```renpy
# Force specific Android settings
$ renpy.config.renderer = "gles2"
$ renpy.config.gl_maximum_texture_size = 1024
$ renpy.config.gl_framerate = 60
```

## Future Support

### 🔮 **Upcoming Features**
- **Snapdragon 8 Gen 4**: Ready for next-generation chipsets
- **Enhanced Ray Tracing**: 8 Gen 3+ optimization
- **Variable Refresh Rate**: Adaptive sync support
- **AI Acceleration**: Hexagon DSP integration

### 📱 **Chipset Expansion**
- **MediaTek Support**: Dimensity series detection
- **Exynos Support**: Samsung Exynos chipsets
- **Tensor Support**: Google Tensor chips

This system ensures optimal Ren'Py performance on Android devices with Snapdragon 835 and higher chipsets, providing the best possible visual novel experience with Vulkan API acceleration and mobile-specific optimizations.
