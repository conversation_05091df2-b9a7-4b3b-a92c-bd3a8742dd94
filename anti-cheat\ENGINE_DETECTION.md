# Ren'Py Engine Detection and Communication System

## Overview
The Engine Detection System is an advanced anti-cheat component that automatically detects Ren'Py SDK installations, establishes secure communication with the engine, and manages all file loading through the anti-cheat system with encryption/decryption capabilities.

## 🎯 **Key Features**

### **Automatic SDK Detection**
- **Smart Search**: Automatically searches common installation paths
- **Version Detection**: Identifies SDK version from directory names and files
- **Validation**: Verifies SDK integrity and completeness
- **Multi-Version Support**: Detects any SDK version (8.3.7, 8.4.x, etc.)

### **Engine Communication**
- **Direct Integration**: Establishes communication channel with Ren'Py engine
- **File System Hook**: Routes all file operations through anti-cheat
- **Real-time Monitoring**: Continuously monitors engine integrity
- **Secure Bridge**: Encrypted communication between components

### **File Management**
- **Unified Loading**: All files loaded through anti-cheat system
- **Encryption Integration**: Seamless encrypt/decrypt operations
- **Access Logging**: Complete audit trail of file access
- **Cache Management**: Intelligent caching of decrypted content

## 🔍 **SDK Detection Process**

### **Search Locations**
The system automatically searches these locations for Ren'Py SDK:

```
C:\Users\<USER>\Downloads\
C:\Users\<USER>\Desktop\
C:\Users\<USER>\Documents\
C:\Program Files\
C:\Program Files (x86)\
D:\
E:\
~/Downloads/
~/Desktop/
~/Documents/
```

### **Detection Patterns**
Looks for directories matching these patterns:
- `renpy-*-sdk` (e.g., renpy-8.3.7-sdk)
- `renpy-sdk-*` (e.g., renpy-sdk-8.3.7)
- `renpy_sdk_*` (e.g., renpy_sdk_8_3_7)
- `RenPy-*-SDK` (case variations)

### **Validation Criteria**
For a directory to be recognized as a valid SDK:
- ✅ Must contain `renpy.py`
- ✅ Must contain `launcher.py`
- ✅ Must have `renpy/__init__.py`
- ✅ Must have `renpy/main.py`
- ✅ Version must be extractable

## 🔗 **Communication Architecture**

### **Communication Channel**
```
Anti-Cheat System
       ↕️
Engine Communication Bridge
       ↕️
Ren'Py SDK (C:\Users\<USER>\Downloads\renpy-8.3.7-sdk)
       ↕️
Project Files (D:\renpy_projects\netcode the protogen and more)
       ↕️
Encryption System (unencrypt and re-encrypt folder)
```

### **File Loading Flow**
1. **Request**: Game requests file access
2. **Intercept**: Anti-cheat intercepts the request
3. **Validate**: Check file integrity and permissions
4. **Decrypt**: If encrypted, decrypt through encryption bridge
5. **Load**: Provide decrypted content to game
6. **Cache**: Store in secure cache for performance
7. **Re-encrypt**: Queue for re-encryption after use

## 📁 **File System Integration**

### **Monitored Directories**
- `game/` - All game content files
- `images/` - Image assets
- `audio/` - Sound and music files
- `scripts/` - Ren'Py script files
- `fonts/` - Font files
- `videos/` - Video files

### **File Types Handled**
- **Scripts**: `.rpy`, `.rpyc` files
- **Images**: `.png`, `.jpg`, `.gif`, `.webp`
- **Audio**: `.ogg`, `.mp3`, `.wav`
- **Data**: `.json`, `.txt`, `.dat`
- **Encrypted**: `.enc`, `.encrypted`

### **Security Features**
- **Integrity Checking**: SHA-256 checksums for all files
- **Access Control**: Permission-based file access
- **Audit Logging**: Complete access history
- **Tamper Detection**: Immediate detection of file modifications

## 🔐 **Encryption Integration**

### **Encryption Bridge**
The system establishes a secure bridge to the encryption system:

```
Location: D:\renpy_projects\netcode the protogen and more\unencrypt and re-encrypt\
Modules: encryption_core, resource_manager
```

### **Encryption Process**
1. **Detection**: Identify encrypted files by extension or header
2. **Request**: Send decryption request to encryption core
3. **Decrypt**: Receive decrypted content
4. **Cache**: Store decrypted content securely
5. **Access**: Provide content to game engine
6. **Cleanup**: Re-encrypt and clear cache after use

### **File Encryption Indicators**
- **Extensions**: `.enc`, `.encrypted`
- **Headers**: Files starting with `ENCRYPTED_`
- **Directory**: Files in encrypted directories
- **Metadata**: Files marked in encryption manifest

## 🖥️ **User Interface**

### **Status Display**
Real-time status overlay showing:
- **SDK Detection**: Found/Not Found with version
- **Communication**: Active/Inactive status
- **File Loading**: Through anti-cheat or standard
- **Encryption**: Bridge active/inactive

### **Detailed Information**
Comprehensive details screen with:
- **SDK Information**: Path, version, validation status
- **Communication Status**: All bridge statuses
- **File Statistics**: Loading counts, cache status
- **Search Paths**: Which paths were checked

### **Testing Tools**
- **Communication Test**: Verify all bridges work
- **Encryption Test**: Test encrypt/decrypt functionality
- **File Loading Test**: Verify file routing
- **Integrity Check**: Validate all components

## ⚙️ **Configuration**

### **Detection Settings**
```python
engine_detector = {
    'sdk_search_paths': [...],  # Paths to search
    'sdk_detected': False,      # Detection status
    'sdk_version_detected': None, # Found version
    'engine_communication_active': False, # Communication status
    'all_files_through_anticheat': False  # File routing
}
```

### **File Loading Settings**
```python
secure_file_loader = {
    'loaded_files': {},         # Currently loaded files
    'file_cache': {},          # Decrypted file cache
    'access_log': [],          # File access history
    'encryption_requests': [], # Pending encryption
    'decryption_queue': []     # Pending decryption
}
```

## 🔧 **API Reference**

### **Core Methods**

**`detect_renpy_sdk_installation()`**
- Searches for and validates SDK installations
- Returns: Boolean success status

**`setup_engine_communication()`**
- Establishes communication with detected SDK
- Returns: Boolean success status

**`load_file_through_anticheat(filename)`**
- Loads file through anti-cheat with decryption
- Returns: File content or file-like object

**`request_decryption(filename)`**
- Requests file decryption through encryption bridge
- Returns: Decrypted content or None

### **Status Methods**

**`is_sdk_detected()`**
- Returns: Boolean SDK detection status

**`get_communication_status()`**
- Returns: Dictionary with all communication statuses

**`get_file_loading_stats()`**
- Returns: Dictionary with file loading statistics

## 🚨 **Troubleshooting**

### **Common Issues**

**"SDK Not Detected"**
- Verify SDK is installed in a searched location
- Check directory name matches expected patterns
- Ensure all required SDK files are present
- Try manual rescan

**"Communication Failed"**
- Check SDK integrity and completeness
- Verify anti-cheat system is active
- Ensure no file permission issues
- Check system logs for errors

**"Encryption Bridge Inactive"**
- Verify encryption folder exists
- Check encryption modules are present
- Ensure Python path includes encryption directory
- Test encryption system independently

**"File Loading Issues"**
- Check file exists and is accessible
- Verify file is not corrupted
- Check encryption status if applicable
- Review access logs for clues

### **Diagnostic Commands**
- **Rescan SDK**: Force re-detection of SDK
- **Test Communication**: Verify all bridges work
- **Check File Access**: Test file loading system
- **Validate Integrity**: Check all component integrity

## 📊 **Monitoring and Logging**

### **Real-time Monitoring**
- **SDK Status**: Continuous monitoring of SDK availability
- **Communication Health**: Bridge status monitoring
- **File Access**: Real-time file operation tracking
- **Performance**: Loading times and cache efficiency

### **Logging System**
- **Detection Events**: SDK discovery and validation
- **Communication Events**: Bridge establishment and failures
- **File Operations**: All file access and encryption operations
- **Security Events**: Integrity violations and security issues

## 🎯 **Best Practices**

### **For Developers**
- **Keep SDK Updated**: Use latest stable SDK version
- **Monitor Status**: Check communication status regularly
- **Test Thoroughly**: Use built-in testing tools
- **Review Logs**: Check logs for any issues

### **For Security**
- **Verify Integrity**: Regular integrity checks
- **Monitor Access**: Review file access patterns
- **Update Encryption**: Keep encryption system current
- **Backup Configuration**: Maintain configuration backups

---

**The Engine Detection System provides seamless integration between your anti-cheat system and Ren'Py engine, ensuring all file operations are secure, monitored, and properly encrypted while maintaining full compatibility with the game engine.**
