# Universal Graphics Setup for "Netcode the Protogen and More"

## Overview
This visual novel features a universal graphics configuration system that automatically detects and optimizes for:
- **GPU Series**: RTX 20/30/40 series, GTX series, AMD RDNA2/3, Intel Arc
- **Operating Systems**: Windows, Linux, macOS, Android, iOS
- **Rendering APIs**: OpenGL, DirectX (via ANGLE), OpenGL ES
- **Vulkan Optimization**: Hardware detection for Vulkan-capable GPUs

## Universal Configuration System

### Automatic Detection
The system automatically detects:
- Operating system and architecture
- GPU vendor, series, and capabilities
- Vulkan support availability
- Optimal renderer and performance settings
- Memory tier classification (Low/Medium/High/Ultra)

## Platform-Specific Configurations

### Windows
- **Primary Renderer**: GL2 (OpenGL 2.0+)
- **Alternative**: ANGLE2 (DirectX 9/11 backend)
- **Vulkan Detection**: Optimizes settings for Vulkan-capable hardware
- **GPU Support**: Full RTX 20/30/40 series optimization

### Linux
- **Primary Renderer**: GL2 (OpenGL 2.0+)
- **Alternative**: GL (Legacy compatibility)
- **Driver Support**: Mesa, NVIDIA proprietary, AMD AMDGPU
- **Performance**: Optimized for open-source drivers

### macOS
- **Primary Renderer**: GL2 (OpenGL 2.0+)
- **Compatibility**: Works with OpenGL deprecation
- **Metal Support**: Prepared for future Metal backend
- **Conservative Settings**: Optimized for macOS limitations

### Android
- **Primary Renderer**: GLES2 (OpenGL ES 2.0)
- **Mobile Optimization**: Battery-conscious settings
- **Vulkan Ready**: Detects Vulkan-capable Android devices
- **Performance Scaling**: Adaptive quality based on hardware

### iOS
- **Primary Renderer**: GLES2 (OpenGL ES 2.0)
- **Metal Preparation**: Ready for Metal backend transition
- **Power Efficiency**: Optimized for iOS power management
- **Device Detection**: Automatic iPhone/iPad optimization

## GPU Series Support

### NVIDIA RTX 40 Series (Ultra Tier)
- **Memory**: 2GB+ image cache
- **Quality**: Maximum settings, sharp textures
- **Prediction**: 128 statements
- **Vulkan**: Full optimization for Vulkan-capable hardware

### NVIDIA RTX 30 Series (High Tier)
- **Memory**: 1.5GB image cache
- **Quality**: High settings, enhanced textures
- **Prediction**: 64 statements
- **Vulkan**: Optimized for Vulkan capabilities

### NVIDIA RTX 20 Series (High Tier)
- **Memory**: 1GB image cache
- **Quality**: High settings, sharp textures
- **Prediction**: 64 statements
- **Vulkan**: Vulkan-optimized settings

### NVIDIA GTX Series (Medium Tier)
- **Memory**: 512MB image cache
- **Quality**: Balanced settings
- **Prediction**: 32 statements
- **Compatibility**: OpenGL-focused optimization

### AMD RDNA3 (RX 7000) (Ultra Tier)
- **Memory**: 2GB+ image cache
- **Quality**: Maximum settings
- **Prediction**: 128 statements
- **Vulkan**: Full RDNA3 optimization

### AMD RDNA2 (RX 6000) (High Tier)
- **Memory**: 1.5GB image cache
- **Quality**: High settings
- **Prediction**: 64 statements
- **Vulkan**: RDNA2-optimized settings

### Intel Arc (High Tier)
- **Memory**: 1GB image cache
- **Quality**: High settings with Intel optimizations
- **Prediction**: 64 statements
- **Vulkan**: Intel Arc Vulkan support

## Integrated Graphics Support

### Intel Integrated Graphics

#### Intel Iris Xe (Medium Tier)
- **Memory**: 384MB image cache
- **Quality**: Balanced settings for modern integrated
- **Prediction**: 16 statements
- **Optimizations**: Shared memory aware, modern Intel features

#### Intel Iris/Iris Plus (Low Tier)
- **Memory**: 192-256MB image cache
- **Quality**: Conservative settings
- **Prediction**: 8-12 statements
- **Optimizations**: Legacy compatibility, power efficient

#### Intel UHD Graphics (Low Tier)
- **Memory**: 128MB image cache
- **Quality**: Performance-focused settings
- **Prediction**: 8 statements
- **Optimizations**: Minimal GPU load, CPU assistance

#### Intel HD Graphics (Low Tier)
- **Memory**: 96MB image cache
- **Quality**: Very conservative settings
- **Prediction**: 4 statements
- **Optimizations**: Maximum compatibility mode

### AMD Integrated Graphics (APU)

#### AMD Vega APU (Medium Tier)
- **Memory**: 256MB image cache
- **Quality**: Balanced settings for APU
- **Prediction**: 12 statements
- **Optimizations**: Shared memory optimization, Vulkan support

### Integrated Graphics Optimizations

#### Automatic Detection
- **Shared Memory Detection**: Identifies systems using shared system RAM
- **Conservative Settings**: Reduces GPU load for better performance
- **Power Efficiency**: Optimized for battery life on laptops
- **Thermal Management**: Prevents overheating on compact systems

#### Performance Optimizations
- **Reduced Cache**: Smaller image cache to preserve system RAM
- **Simplified Rendering**: Nearest neighbor filtering for performance
- **Minimal Effects**: Disabled mipmapping and complex transitions
- **CPU Assistance**: Offloads work to CPU when beneficial

## How to Change Renderer

### Method 1: Launcher Scripts
- **Windows**: Run `launch_with_renderer.bat`
- **Linux/macOS**: Run `launch_with_renderer.sh`
- **Automatic Detection**: Shows optimal settings for your hardware

### Method 2: In-Game
1. **During Startup**: Hold `Shift` while launching
2. **During Gameplay**: Press `Shift + G`
3. **Graphics Menu**: Access from main menu

### Method 3: Environment Variables
```bash
# Windows
set RENPY_RENDERER=gl2

# Linux/macOS
export RENPY_RENDERER=gl2
```

## Performance Tips

### For Your RTX 4080:
- Current settings are optimized for your hardware
- GL2 renderer provides the best performance and features
- 1GB image cache utilizes your GPU's VRAM effectively

### If You Experience Issues:
1. Update NVIDIA drivers to latest version
2. Try ANGLE2 renderer if GL2 has problems
3. Check Windows GPU scheduling is enabled
4. Ensure game is using dedicated GPU (not integrated)

## Testing Graphics

The game includes a graphics test menu accessible from the start screen:
- View current renderer information
- Test performance
- Monitor graphics settings

## Technical Details

### Ren'Py Renderer Hierarchy:
1. **GL2** (Model-based, Ren'Py 7.4+) ← You're using this
2. **ANGLE2** (DirectX-based alternative)
3. **GLES2** (OpenGL ES, mobile-focused)

### Why Not Vulkan?
- Ren'Py uses OpenGL-based rendering
- GL2 renderer provides excellent performance on modern hardware
- Vulkan support would require engine-level changes
- Current setup maximizes your RTX 4080's capabilities within Ren'Py

## Files Modified
- `game/options.rpy` - Core graphics configuration
- `game/graphics_config.rpy` - Advanced graphics settings
- `game/script.rpy` - Graphics test integration

## Troubleshooting

### Common Issues:
- **Black screen**: Try ANGLE2 renderer
- **Poor performance**: Ensure using dedicated GPU
- **Texture issues**: Update graphics drivers

### Performance Monitoring:
- Check `log.txt` for renderer information
- Use in-game graphics test for real-time info
- Monitor GPU usage with MSI Afterburner or similar

## Future Enhancements

Potential improvements for your setup:
- Custom shaders for enhanced visual effects
- High refresh rate optimizations (120Hz/144Hz)
- HDR support (when available in Ren'Py)
- Advanced post-processing effects

---

**Note**: This setup maximizes visual quality and performance for your NVIDIA RTX 4080 within Ren'Py's capabilities. The GL2 renderer with these optimizations provides the best possible experience for your hardware.
