## Game Launcher Integration
## Detects and handles games launched from the universal launcher

init -100 python:
    import os
    import json
    import time
    
    # Launcher integration configuration
    LAUNCHER_CONFIG = {
        'enabled': True,
        'check_launcher_config': True,
        'show_launcher_info': True,
        'integrate_with_anticheat': True
    }
    
    # Launcher state
    launcher_state = {
        'launched_from_launcher': False,
        'launcher_detected': False,
        'game_config': None,
        'separate_game_mode': False,
        'main_game_path': None
    }

class LauncherIntegration:
    """Handles integration with the game launcher"""
    
    def __init__(self):
        self.config_file = None
        self.launcher_config = {}
        
    def detect_launcher(self):
        """Detect if game was launched from the launcher"""
        try:
            # Check for launcher config file
            config_paths = [
                os.path.join(config.gamedir, "launcher_config.json"),
                os.path.join(config.basedir, "game", "launcher_config.json"),
                os.path.join(config.basedir, "launcher_config.json")
            ]
            
            for config_path in config_paths:
                if os.path.exists(config_path):
                    self.config_file = config_path
                    break
            
            if self.config_file:
                with open(self.config_file, 'r') as f:
                    self.launcher_config = json.load(f)
                
                launcher_state['launched_from_launcher'] = self.launcher_config.get('launched_from_launcher', False)
                launcher_state['launcher_detected'] = True
                launcher_state['game_config'] = self.launcher_config
                launcher_state['main_game_path'] = self.launcher_config.get('main_game_path')
                
                renpy.log("Launcher detected: {}".format(self.launcher_config.get('game_name', 'Unknown')))
                return True
            
            # Check if this is a separate game by looking at the file structure
            if self.detect_separate_game():
                launcher_state['separate_game_mode'] = True
                launcher_state['launcher_detected'] = True
                renpy.log("Separate game mode detected")
                return True
            
            return False
            
        except Exception as e:
            renpy.log("Error detecting launcher: {}".format(str(e)))
            return False
    
    def detect_separate_game(self):
        """Detect if this is a separate game"""
        try:
            # Check if we're in a separate games folder
            current_path = os.path.abspath(config.basedir)
            
            # Look for "separate games" in the path
            if "separate games" in current_path.lower():
                return True
            
            # Check if the main game files are missing (indicating separate game)
            main_game_files = [
                "anti-cheat/anticheat_core.rpy",
                "game/script.rpy",
                ".vscode/encryption_core.py"
            ]
            
            missing_count = 0
            for file_path in main_game_files:
                if not os.path.exists(os.path.join(config.basedir, file_path)):
                    missing_count += 1
            
            # If most main game files are missing, this is likely a separate game
            return missing_count >= 2
            
        except Exception as e:
            renpy.log("Error detecting separate game: {}".format(str(e)))
            return False
    
    def get_launcher_info(self):
        """Get launcher information"""
        if launcher_state['launcher_detected']:
            return {
                'launched_from_launcher': launcher_state['launched_from_launcher'],
                'game_name': self.launcher_config.get('game_name', 'Unknown'),
                'launcher_version': self.launcher_config.get('launcher_version', 'Unknown'),
                'launch_time': self.launcher_config.get('launch_time', 0),
                'separate_game_mode': launcher_state['separate_game_mode']
            }
        return None
    
    def integrate_with_anticheat(self):
        """Integrate launcher info with anti-cheat system"""
        try:
            if LAUNCHER_CONFIG['integrate_with_anticheat'] and hasattr(renpy.store, 'anticheat_state'):
                # Add launcher info to anti-cheat state
                anticheat_state['launcher_info'] = self.get_launcher_info()
                anticheat_state['separate_game_mode'] = launcher_state['separate_game_mode']
                
                # If this is a separate game, modify anti-cheat behavior
                if launcher_state['separate_game_mode']:
                    # Reduce anti-cheat strictness for separate games
                    if hasattr(renpy.store, 'ANTICHEAT_CONFIG'):
                        ANTICHEAT_CONFIG['strict_mode'] = False
                        ANTICHEAT_CONFIG['violation_threshold'] = 10  # Higher threshold
                    
                    renpy.log("Anti-cheat configured for separate game mode")
                
                return True
            
            return False
            
        except Exception as e:
            renpy.log("Error integrating with anti-cheat: {}".format(str(e)))
            return False
    
    def cleanup_config(self):
        """Clean up launcher config file"""
        try:
            if self.config_file and os.path.exists(self.config_file):
                os.remove(self.config_file)
                renpy.log("Launcher config cleaned up")
        except Exception as e:
            renpy.log("Error cleaning up launcher config: {}".format(str(e)))

# Initialize launcher integration
launcher_integration = LauncherIntegration()

# Detect launcher on startup
init python:
    if LAUNCHER_CONFIG['enabled']:
        launcher_integration.detect_launcher()
        launcher_integration.integrate_with_anticheat()

# Launcher info display screen
screen launcher_info():
    
    if LAUNCHER_CONFIG['show_launcher_info'] and launcher_state['launcher_detected']:
        frame:
            xpos 10
            ypos 10
            xsize 300
            ysize 100
            background "#000000AA"
            
            vbox:
                spacing 5
                
                text "🚀 Launcher Info" size 14 color "#00FF00"
                
                if launcher_state['launched_from_launcher']:
                    text "✓ Launched from Game Launcher" size 10 color "#FFFFFF"
                    
                    launcher_info = launcher_integration.get_launcher_info()
                    if launcher_info:
                        text "Game: {}".format(launcher_info['game_name'][:20] + "..." if len(launcher_info['game_name']) > 20 else launcher_info['game_name']) size 9 color "#CCCCCC"
                        text "Version: {}".format(launcher_info['launcher_version']) size 9 color "#CCCCCC"
                
                if launcher_state['separate_game_mode']:
                    text "📁 Separate Game Mode" size 10 color "#FFD700"

# Return to launcher function
init python:
    def return_to_launcher():
        """Return to the game launcher"""
        try:
            # Clean up config
            launcher_integration.cleanup_config()
            
            # Try to launch the launcher
            launcher_path = os.path.join(launcher_state.get('main_game_path', config.basedir), "game_launcher.py")
            
            if os.path.exists(launcher_path):
                import subprocess
                subprocess.Popen(["python", launcher_path], cwd=launcher_state.get('main_game_path', config.basedir))
                renpy.quit()
            else:
                renpy.notify("Launcher not found")
                
        except Exception as e:
            renpy.log("Error returning to launcher: {}".format(str(e)))
            renpy.notify("Could not return to launcher")

# Enhanced main menu with launcher integration
screen main_menu_launcher():
    
    tag menu
    
    # Show launcher info if detected
    if launcher_state['launcher_detected']:
        use launcher_info
    
    # Add return to launcher button if launched from launcher
    if launcher_state['launched_from_launcher']:
        textbutton "🔙 Return to Launcher" action Function(return_to_launcher) xpos 10 ypos 120

# Launcher integration status for developer mode
screen launcher_status():
    
    if config.developer:
        frame:
            xpos 10
            ypos 800
            xsize 400
            ysize 80
            background "#000000AA"
            
            vbox:
                spacing 5
                
                text "Launcher Integration Status" size 12 color "#9C27B0"
                
                text "Detected: {}".format("Yes" if launcher_state['launcher_detected'] else "No") size 10
                text "From Launcher: {}".format("Yes" if launcher_state['launched_from_launcher'] else "No") size 10
                text "Separate Game: {}".format("Yes" if launcher_state['separate_game_mode'] else "No") size 10
                
                if launcher_state['game_config']:
                    text "Config: {}".format(launcher_state['game_config'].get('game_name', 'Unknown')[:15] + "...") size 10

# Auto-show launcher info on game start
label start:
    
    # Show launcher info if enabled
    if LAUNCHER_CONFIG['show_launcher_info'] and launcher_state['launcher_detected']:
        if launcher_state['launched_from_launcher']:
            "Welcome! This game was launched from the Game Launcher."
        elif launcher_state['separate_game_mode']:
            "Welcome! You're playing a separate game experience."
    
    # Continue with normal game start
    return

# Cleanup on game exit
init python:
    def cleanup_launcher_on_exit():
        """Clean up launcher integration on game exit"""
        launcher_integration.cleanup_config()
    
    # Register cleanup callback
    config.quit_callbacks.append(cleanup_launcher_on_exit)

# Developer functions for launcher integration
init python:
    
    def show_launcher_debug_info():
        """Show launcher debug information"""
        if config.developer:
            info = launcher_integration.get_launcher_info()
            if info:
                debug_text = "Launcher Debug Info:\n"
                for key, value in info.items():
                    debug_text += f"{key}: {value}\n"
                renpy.notify(debug_text)
            else:
                renpy.notify("No launcher info available")
    
    def toggle_launcher_integration():
        """Toggle launcher integration"""
        if config.developer:
            LAUNCHER_CONFIG['enabled'] = not LAUNCHER_CONFIG['enabled']
            renpy.notify("Launcher integration: {}".format("ON" if LAUNCHER_CONFIG['enabled'] else "OFF"))
    
    def force_launcher_detection():
        """Force launcher detection"""
        if config.developer:
            success = launcher_integration.detect_launcher()
            renpy.notify("Launcher detection: {}".format("SUCCESS" if success else "FAILED"))

# Add launcher controls to developer panel if available
init python:
    if hasattr(renpy.store, 'anticheat_state'):
        # Add launcher info to anti-cheat state for monitoring
        anticheat_state['launcher_integration'] = launcher_state
