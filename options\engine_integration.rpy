## Game Engine Integration
## Complete integration of anti-cheat, notifications, and game selection

# Enhanced start label with full integration
label start:
    
    # Initialize all systems first
    call initialize_all_systems
    
    # Show anti-cheat engagement screen
    call screen anticheat_engagement
    
    # Check ban status
    if getattr(persistent, 'anticheat_banned', False):
        $ ban_time_remaining = int((getattr(persistent, 'anticheat_ban_timestamp', 0) + ANTICHEAT_CONFIG['ban_duration'] - time.time()) / 86400)
        call screen anticheat_ban_screen(reason=getattr(persistent, 'anticheat_ban_reason', 'Unknown violation'), days_remaining=ban_time_remaining)
        return
    
    # Check legal compliance
    if hasattr(renpy.store, 'legal_protector'):
        call check_legal_compliance
    
    # Show main game engine interface
    jump main_game_engine

# Initialize all systems
label initialize_all_systems:
    
    # Initialize anti-cheat core
    if hasattr(renpy.store, 'anticheat_core'):
        $ anticheat_core.initialize()
        $ renpy.notify("🛡️ Anti-cheat system initialized")
    
    # Initialize legal protection
    if hasattr(renpy.store, 'legal_protector'):
        $ legal_protector.initialize_legal_protection()
        $ renpy.notify("📋 Legal protection active")
    
    # Initialize encryption system
    if hasattr(renpy.store, 'encryption_engine'):
        $ renpy.notify("🔐 Encryption system ready")
    
    # Initialize NSFW detection
    if hasattr(renpy.store, 'nsfw_detector'):
        $ renpy.notify("🔞 Content detection active")
    
    # Detect graphics card
    $ detect_graphics_card()
    $ renpy.notify("🎮 Graphics system detected")
    
    return

# Main game engine interface
label main_game_engine:
    
    scene bg black
    
    # Show persistent notification overlay
    show screen notification_overlay
    
    # Main game engine screen
    call screen game_engine_main_interface
    
    # Should not reach here
    return

# Enhanced game engine main interface
screen game_engine_main_interface():
    
    tag menu
    
    # Background
    add "#0a0a0a"
    
    # Main title with glow effect
    frame:
        xalign 0.5
        ypos 80
        background None
        
        vbox:
            spacing 10
            xalign 0.5
            
            text "NETCODE THE PROTOGEN AND MORE" size 36 color "#00ff00" bold True xalign 0.5 outlines [(2, "#003300", 0, 0)]
            text "Game Engine v2.0" size 16 color "#888888" xalign 0.5
    
    # Central game selection panel
    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 500
        background Frame("gui/overlay/game_menu.png", 24, 24, 24, 24)
        
        vbox:
            spacing 30
            xalign 0.5
            yalign 0.5
            
            # Section title
            text "🎮 SELECT VISUAL NOVEL EXPERIENCE" size 24 color "#ffffff" xalign 0.5 bold True
            
            # Game selection grid (line 56 equivalent area)
            grid 1 3:
                spacing 20
                xalign 0.5
                
                # Netcode the Protogen
                frame:
                    background "#2e2e2e"
                    padding (20, 15)
                    xsize 700
                    
                    hbox:
                        spacing 20
                        
                        # Game icon/preview
                        frame:
                            background "#1a1a1a"
                            xsize 100
                            ysize 80
                            
                            text "🤖" size 40 color "#00ff00" xalign 0.5 yalign 0.5
                        
                        # Game info
                        vbox:
                            spacing 8
                            
                            text "Visual Novel: Netcode the Protogen" size 18 color "#ffffff" bold True
                            text "The main story featuring Netcode and friends in their adventures." size 12 color "#cccccc"
                            text "Status: Available | Rating: All Ages" size 10 color "#888888"
                        
                        # Launch button
                        textbutton "▶ PLAY" action [
                            Function(log_game_selection, "netcode_protogen"),
                            Jump("Netcode_the_protogen_bedroom_1")
                        ] text_size 16 xsize 100 ysize 50
                
                # Lumetric
                frame:
                    background "#2e2e2e"
                    padding (20, 15)
                    xsize 700
                    
                    hbox:
                        spacing 20
                        
                        # Game icon/preview
                        frame:
                            background "#1a1a1a"
                            xsize 100
                            ysize 80
                            
                            text "💎" size 40 color "#00aaff" xalign 0.5 yalign 0.5
                        
                        # Game info
                        vbox:
                            spacing 8
                            
                            text "Visual Novel: Lumetric" size 18 color "#ffffff" bold True
                            text "A mysterious adventure in a world of light and shadow." size 12 color "#cccccc"
                            text "Status: Available | Rating: Teen" size 10 color "#888888"
                        
                        # Launch button
                        textbutton "▶ PLAY" action [
                            Function(log_game_selection, "lumetric"),
                            Jump("Lumetric_1")
                        ] text_size 16 xsize 100 ysize 50
                
                # BearWithUs
                frame:
                    background "#2e2e2e"
                    padding (20, 15)
                    xsize 700
                    
                    hbox:
                        spacing 20
                        
                        # Game icon/preview
                        frame:
                            background "#1a1a1a"
                            xsize 100
                            ysize 80
                            
                            text "🐻" size 40 color "#8B4513" xalign 0.5 yalign 0.5
                        
                        # Game info
                        vbox:
                            spacing 8
                            
                            text "Visual Novel: BearWithUs" size 18 color "#ffffff" bold True
                            text "A heartwarming story about friendship and adventure." size 12 color "#cccccc"
                            text "Status: Available | Rating: All Ages" size 10 color "#888888"
                        
                        # Launch button
                        textbutton "▶ PLAY" action [
                            Function(log_game_selection, "bearwithus"),
                            Jump("BearWithUs_1")
                        ] text_size 16 xsize 100 ysize 50
    
    # Bottom control panel
    frame:
        xalign 0.5
        ypos 650
        background "#1a1a1a"
        padding (20, 10)
        
        hbox:
            spacing 30
            xalign 0.5
            
            textbutton "💾 Load Game" action ShowMenu("load") text_size 14
            textbutton "⚙️ Preferences" action ShowMenu("preferences") text_size 14
            textbutton "📖 About" action ShowMenu("about") text_size 14
            
            if anticheat_state.get('developer_authenticated', False):
                textbutton "🔧 Developer Tools" action Call("open_developer_panel") text_size 14
            
            textbutton "❌ Exit" action Quit() text_size 14

# Game selection logging function
init python:
    def log_game_selection(game_name):
        """Log game selection with anti-cheat system"""
        try:
            if hasattr(renpy.store, 'anticheat_core'):
                anticheat_core.record_user_action("game_selection", game_name)
                renpy.log("Game selected: {}".format(game_name))
            
            # Update persistent data
            if not hasattr(persistent, 'games_played'):
                persistent.games_played = []
            
            if game_name not in persistent.games_played:
                persistent.games_played.append(game_name)
            
            # Show selection notification
            renpy.notify("🎮 Starting {}".format(game_name.replace('_', ' ').title()))
            
        except Exception as e:
            renpy.log("Error logging game selection: {}".format(str(e)))

# Enhanced main menu that shows the game engine - DISABLED
# Using main menu from screens.rpy instead
# screen main_menu():
#
#     tag menu
#
#     # Use the game engine interface
#     use game_engine_main_interface
#
#     # Show notification overlay
#     use notification_overlay

# System status screen for developer mode
screen system_status_overlay():
    
    if config.developer:
        frame:
            xpos 1200
            ypos 20
            xsize 300
            ysize 400
            background "#000000dd"
            
            vbox:
                spacing 10
                
                text "🔧 SYSTEM STATUS" size 14 color "#ffd700" bold True
                
                # Anti-cheat status
                vbox:
                    spacing 5
                    
                    text "Anti-Cheat:" size 12 color "#ffffff"
                    text "  Initialized: {}".format("✓" if anticheat_state.get('initialized', False) else "✗") size 10
                    text "  Violations: {}".format(anticheat_state.get('violations', 0)) size 10
                    text "  Developer: {}".format("✓" if anticheat_state.get('developer_authenticated', False) else "✗") size 10
                
                # Legal protection status
                if hasattr(renpy.store, 'legal_state'):
                    vbox:
                        spacing 5
                        
                        text "Legal Protection:" size 12 color "#ffffff"
                        text "  Documents: {}".format("✓" if legal_state.get('documents_verified', False) else "✗") size 10
                        text "  Violations: {}".format(legal_state.get('violations_detected', 0)) size 10
                
                # Encryption status
                if hasattr(renpy.store, 'encryption_engine'):
                    vbox:
                        spacing 5
                        
                        text "Encryption:" size 12 color "#ffffff"
                        text "  Available: ✓" size 10
                        text "  Active Resources: {}".format(len(resource_manager.list_active_resources()) if hasattr(renpy.store, 'resource_manager') else 0) size 10
                
                # Graphics status
                vbox:
                    spacing 5
                    
                    text "Graphics:" size 12 color "#ffffff"
                    text "  Type: {}".format(graphics_info.get('gpu_type', 'unknown')) size 10
                    text "  Integrated: {}".format("✓" if graphics_info.get('is_integrated', False) else "✗") size 10

# Show system status in developer mode - DISABLED
# Using main menu from screens.rpy instead
# screen main_menu():
#
#     tag menu
#
#     # Use the game engine interface
#     use game_engine_main_interface
#
#     # Show notification overlay
#     use notification_overlay
#
#     # Show system status in developer mode
#     if config.developer:
#         use system_status_overlay

# Enhanced game start with full system integration
label enhanced_start:
    
    # This is the new main entry point
    # Initialize all systems
    call initialize_all_systems
    
    # Show anti-cheat engagement
    call screen anticheat_engagement
    
    # Check for any blocking conditions
    if getattr(persistent, 'anticheat_banned', False):
        $ ban_time_remaining = int((getattr(persistent, 'anticheat_ban_timestamp', 0) + ANTICHEAT_CONFIG['ban_duration'] - time.time()) / 86400)
        call screen anticheat_ban_screen(reason=getattr(persistent, 'anticheat_ban_reason', 'Unknown violation'), days_remaining=ban_time_remaining)
        return
    
    # Check legal compliance
    if hasattr(renpy.store, 'legal_protector') and legal_protector.require_terms_acceptance():
        call screen terms_acceptance
    
    # Show main game engine
    jump main_game_engine

# Placeholder game labels (to prevent errors)
label Netcode_the_protogen_bedroom_1:
    scene bg black
    "🤖 Welcome to Netcode the Protogen!"
    "This is where the main Netcode story would begin."
    "The anti-cheat system is monitoring this session."
    
    menu:
        "What would you like to do?"
        
        "Continue the story":
            "The story continues..."
        
        "Return to game engine":
            jump main_game_engine
    
    return

label Lumetric_1:
    scene bg black
    "💎 Welcome to Lumetric!"
    "This is where the Lumetric story would begin."
    "A world of light and shadow awaits you."
    
    menu:
        "What would you like to do?"
        
        "Continue the story":
            "The story continues..."
        
        "Return to game engine":
            jump main_game_engine
    
    return

label BearWithUs_1:
    scene bg black
    "🐻 Welcome to BearWithUs!"
    "This is where the BearWithUs story would begin."
    "A heartwarming adventure starts here."
    
    menu:
        "What would you like to do?"
        
        "Continue the story":
            "The story continues..."
        
        "Return to game engine":
            jump main_game_engine
    
    return
