## Platform Detection System - Main Integration
## Central hub for all platform detection and management features

init -50 python:
    import json
    import os
    from datetime import datetime

    # Main platform system state
    platform_system_state = {
        'initialized': False,
        'detection_complete': False,
        'integration_active': False,
        'compliance_checked': False,
        'anticheat_integrated': False,
        'distribution_tracked': False,
        'system_secure': False,
        'last_full_check': None,
        'errors': [],
        'warnings': []
    }

    # Main platform system configuration
    PLATFORM_SYSTEM_CONFIG = {
        'enabled': True,
        'auto_initialize': True,
        'full_integration': True,
        'strict_security': True,
        'comprehensive_logging': True,
        'periodic_checks': True,
        'check_interval': 1800,  # 30 minutes
        'error_tolerance': 3,
        'warning_tolerance': 10
    }

    class PlatformSystemManager:
        def __init__(self):
            self.components = {
                'detector': None,
                'tracker': None,
                'integrations': None,
                'compliance': None,
                'anticheat': None
            }
            self.system_status = {}
            self.initialization_order = [
                'detector',
                'tracker', 
                'integrations',
                'compliance',
                'anticheat'
            ]
            
        def initialize_complete_system(self):
            """Initialize the complete platform detection and management system"""
            try:
                renpy.log("Initializing complete platform detection system...")
                
                if not PLATFORM_SYSTEM_CONFIG.get('enabled', True):
                    renpy.log("Platform system disabled in configuration")
                    return False
                
                # Initialize components in order
                success_count = 0
                
                # 1. Initialize Platform Detector
                if self._initialize_detector():
                    success_count += 1
                    platform_system_state['detection_complete'] = True
                
                # 2. Initialize Distribution Tracker
                if self._initialize_tracker():
                    success_count += 1
                    platform_system_state['distribution_tracked'] = True
                
                # 3. Initialize Platform Integrations
                if self._initialize_integrations():
                    success_count += 1
                    platform_system_state['integration_active'] = True
                
                # 4. Initialize Terms Compliance
                if self._initialize_compliance():
                    success_count += 1
                    platform_system_state['compliance_checked'] = True
                
                # 5. Initialize Anti-Cheat Integration
                if self._initialize_anticheat():
                    success_count += 1
                    platform_system_state['anticheat_integrated'] = True
                
                # Check overall system status
                total_components = len(self.initialization_order)
                success_rate = success_count / total_components
                
                if success_rate >= 0.8:  # 80% success rate
                    platform_system_state['initialized'] = True
                    platform_system_state['system_secure'] = success_rate == 1.0
                    platform_system_state['last_full_check'] = datetime.now().isoformat()
                    
                    renpy.log(f"Platform system initialized successfully ({success_count}/{total_components} components)")
                    
                    # Create system status report
                    self._create_system_status_report()
                    
                    return True
                else:
                    renpy.log(f"Platform system initialization failed ({success_count}/{total_components} components)")
                    return False
                
            except Exception as e:
                renpy.log(f"Platform system initialization error: {str(e)}")
                platform_system_state['errors'].append(f"Initialization error: {str(e)}")
                return False
        
        def _initialize_detector(self):
            """Initialize the platform detector component"""
            try:
                if 'platform_detector' in globals():
                    self.components['detector'] = platform_detector
                    
                    # Perform detection
                    detected_platforms = platform_detector.detect_all_platforms()
                    
                    if detected_platforms or platform_detector.get_primary_platform():
                        renpy.log("Platform detector initialized successfully")
                        return True
                    else:
                        renpy.log("Platform detector found no platforms")
                        return True  # This is still valid for direct downloads
                else:
                    renpy.log("Platform detector not available")
                    return False
                    
            except Exception as e:
                renpy.log(f"Platform detector initialization error: {str(e)}")
                return False
        
        def _initialize_tracker(self):
            """Initialize the distribution tracker component"""
            try:
                if 'distribution_tracker' in globals():
                    self.components['tracker'] = distribution_tracker
                    
                    # Verify or create distribution source file
                    if not distribution_tracker.verify_source_file():
                        if distribution_tracker.create_source_file():
                            renpy.log("Distribution tracker created new source file")
                        else:
                            renpy.log("Distribution tracker failed to create source file")
                            return False
                    
                    renpy.log("Distribution tracker initialized successfully")
                    return True
                else:
                    renpy.log("Distribution tracker not available")
                    return False
                    
            except Exception as e:
                renpy.log(f"Distribution tracker initialization error: {str(e)}")
                return False
        
        def _initialize_integrations(self):
            """Initialize the platform integrations component"""
            try:
                if 'platform_integrations' in globals():
                    self.components['integrations'] = platform_integrations
                    
                    # Initialize platform-specific features
                    if platform_integrations.initialize_platform_features():
                        renpy.log("Platform integrations initialized successfully")
                        return True
                    else:
                        renpy.log("Platform integrations initialization failed")
                        return False
                else:
                    renpy.log("Platform integrations not available")
                    return False
                    
            except Exception as e:
                renpy.log(f"Platform integrations initialization error: {str(e)}")
                return False
        
        def _initialize_compliance(self):
            """Initialize the terms compliance component"""
            try:
                if 'terms_compliance' in globals():
                    self.components['compliance'] = terms_compliance
                    
                    # Check platform compliance
                    if terms_compliance.check_platform_compliance():
                        renpy.log("Terms compliance check passed")
                        return True
                    else:
                        violations = terms_compliance.get_violations()
                        warnings = terms_compliance.get_warnings()
                        
                        if violations:
                            renpy.log(f"Terms compliance violations found: {len(violations)}")
                            platform_system_state['errors'].extend(violations)
                        
                        if warnings:
                            renpy.log(f"Terms compliance warnings: {len(warnings)}")
                            platform_system_state['warnings'].extend(warnings)
                        
                        # Return True if only warnings, False if violations
                        return len(violations) == 0
                else:
                    renpy.log("Terms compliance not available")
                    return False
                    
            except Exception as e:
                renpy.log(f"Terms compliance initialization error: {str(e)}")
                return False
        
        def _initialize_anticheat(self):
            """Initialize the anti-cheat integration component"""
            try:
                if 'platform_anticheat' in globals():
                    self.components['anticheat'] = platform_anticheat
                    
                    # Initialize platform security
                    if platform_anticheat.initialize_platform_security():
                        renpy.log("Platform anti-cheat integration initialized successfully")
                        return True
                    else:
                        renpy.log("Platform anti-cheat integration failed")
                        return False
                else:
                    renpy.log("Platform anti-cheat integration not available")
                    return False
                    
            except Exception as e:
                renpy.log(f"Platform anti-cheat initialization error: {str(e)}")
                return False
        
        def _create_system_status_report(self):
            """Create a comprehensive system status report"""
            try:
                self.system_status = {
                    'initialization': {
                        'timestamp': platform_system_state.get('last_full_check'),
                        'components_initialized': sum(1 for comp in self.components.values() if comp is not None),
                        'total_components': len(self.components),
                        'success_rate': sum(1 for comp in self.components.values() if comp is not None) / len(self.components)
                    },
                    'platform_detection': {
                        'primary_platform': platform_detector.get_primary_platform() if self.components['detector'] else 'unknown',
                        'all_platforms': platform_detector.detected_platforms if self.components['detector'] else [],
                        'platform_verified': platform_state.get('platform_verified', False)
                    },
                    'distribution_tracking': {
                        'source_verified': distribution_state.get('source_verified', False),
                        'tamper_detected': distribution_state.get('tamper_detected', False),
                        'creation_timestamp': distribution_state.get('creation_timestamp')
                    },
                    'platform_integrations': {
                        'active_integrations': integration_state.get('active_integrations', []),
                        'features_available': len(integration_state.get('platform_features', {}))
                    },
                    'compliance_status': {
                        'violations': len(compliance_state.get('violations', [])),
                        'warnings': len(compliance_state.get('warnings', [])),
                        'compliant': len(compliance_state.get('violations', [])) == 0
                    },
                    'security_status': {
                        'platform_violations': platform_anticheat.get_violation_count() if self.components['anticheat'] else 0,
                        'platform_secure': platform_anticheat.is_platform_secure() if self.components['anticheat'] else True
                    },
                    'overall_status': {
                        'system_operational': platform_system_state.get('initialized', False),
                        'security_level': self._calculate_security_level(),
                        'errors': len(platform_system_state.get('errors', [])),
                        'warnings': len(platform_system_state.get('warnings', []))
                    }
                }
                
                renpy.log("System status report created")
                
            except Exception as e:
                renpy.log(f"System status report creation error: {str(e)}")
        
        def _calculate_security_level(self):
            """Calculate overall security level"""
            try:
                security_score = 0
                max_score = 5
                
                # Platform detection security
                if platform_state.get('platform_verified', False):
                    security_score += 1
                
                # Distribution tracking security
                if distribution_state.get('source_verified', False) and not distribution_state.get('tamper_detected', False):
                    security_score += 1
                
                # Compliance security
                if len(compliance_state.get('violations', [])) == 0:
                    security_score += 1
                
                # Anti-cheat security
                if self.components['anticheat'] and platform_anticheat.is_platform_secure():
                    security_score += 1
                
                # Overall system integrity
                if platform_system_state.get('initialized', False) and len(platform_system_state.get('errors', [])) == 0:
                    security_score += 1
                
                # Convert to percentage
                security_percentage = (security_score / max_score) * 100
                
                if security_percentage >= 90:
                    return 'HIGH'
                elif security_percentage >= 70:
                    return 'MEDIUM'
                elif security_percentage >= 50:
                    return 'LOW'
                else:
                    return 'CRITICAL'
                    
            except Exception as e:
                renpy.log(f"Security level calculation error: {str(e)}")
                return 'UNKNOWN'
        
        def perform_system_check(self):
            """Perform a comprehensive system check"""
            try:
                renpy.log("Performing comprehensive platform system check...")
                
                check_results = {
                    'detector_status': self._check_detector(),
                    'tracker_status': self._check_tracker(),
                    'integrations_status': self._check_integrations(),
                    'compliance_status': self._check_compliance(),
                    'anticheat_status': self._check_anticheat()
                }
                
                # Update system status
                self._create_system_status_report()
                
                # Check if periodic maintenance is needed
                if PLATFORM_SYSTEM_CONFIG.get('periodic_checks', True):
                    self._schedule_next_check()
                
                renpy.log("System check completed")
                return check_results
                
            except Exception as e:
                renpy.log(f"System check error: {str(e)}")
                return {}
        
        def _check_detector(self):
            """Check platform detector status"""
            if self.components['detector']:
                return {
                    'operational': True,
                    'primary_platform': platform_detector.get_primary_platform(),
                    'platforms_detected': len(platform_detector.detected_platforms)
                }
            return {'operational': False}
        
        def _check_tracker(self):
            """Check distribution tracker status"""
            if self.components['tracker']:
                return {
                    'operational': True,
                    'source_verified': distribution_tracker.verify_source_file(),
                    'tamper_detected': distribution_state.get('tamper_detected', False)
                }
            return {'operational': False}
        
        def _check_integrations(self):
            """Check platform integrations status"""
            if self.components['integrations']:
                return {
                    'operational': True,
                    'active_integrations': len(integration_state.get('active_integrations', [])),
                    'features_available': len(integration_state.get('platform_features', {}))
                }
            return {'operational': False}
        
        def _check_compliance(self):
            """Check terms compliance status"""
            if self.components['compliance']:
                return {
                    'operational': True,
                    'compliant': terms_compliance.is_compliant(),
                    'violations': len(terms_compliance.get_violations()),
                    'warnings': len(terms_compliance.get_warnings())
                }
            return {'operational': False}
        
        def _check_anticheat(self):
            """Check anti-cheat integration status"""
            if self.components['anticheat']:
                return {
                    'operational': True,
                    'secure': platform_anticheat.is_platform_secure(),
                    'violations': platform_anticheat.get_violation_count()
                }
            return {'operational': False}
        
        def _schedule_next_check(self):
            """Schedule the next periodic check"""
            # This would be implemented with a timer or scheduled callback
            # For now, we'll just log the intent
            check_interval = PLATFORM_SYSTEM_CONFIG.get('check_interval', 1800)
            renpy.log(f"Next system check scheduled in {check_interval} seconds")
        
        def get_system_status(self):
            """Get current system status"""
            return self.system_status
        
        def get_component_status(self, component_name):
            """Get status of a specific component"""
            return self.components.get(component_name) is not None
        
        def is_system_operational(self):
            """Check if the system is operational"""
            return platform_system_state.get('initialized', False)
        
        def get_security_summary(self):
            """Get a security summary"""
            return {
                'security_level': self._calculate_security_level(),
                'platform_secure': platform_system_state.get('system_secure', False),
                'total_errors': len(platform_system_state.get('errors', [])),
                'total_warnings': len(platform_system_state.get('warnings', [])),
                'last_check': platform_system_state.get('last_full_check')
            }

    # Initialize the main platform system manager
    platform_system = PlatformSystemManager()

# Auto-initialize the complete system on startup
init python:
    if PLATFORM_SYSTEM_CONFIG.get('enabled', True) and PLATFORM_SYSTEM_CONFIG.get('auto_initialize', True):
        try:
            platform_system.initialize_complete_system()
        except Exception as e:
            renpy.log(f"Platform system auto-initialization failed: {str(e)}")

# Platform system functions for use in game
define platform_system_enabled = PLATFORM_SYSTEM_CONFIG.get('enabled', True)
define platform_system_operational = platform_system.is_system_operational() if platform_system else False
define platform_system_secure = platform_system_state.get('system_secure', False)
define detected_distribution_platform = platform_detector.get_primary_platform() if 'platform_detector' in globals() else 'unknown'
