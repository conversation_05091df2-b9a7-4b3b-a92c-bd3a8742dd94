# Game Launchers - Simple connection between play buttons and games
# This file creates the missing labels that the play buttons are trying to call

# Netcode the Protogen launcher
label launch_netcode_game:
    # Call loading screen first
    call show_loading_screen({"key": "netcode", "display_name": "Netcode the Protogen", "description": "Complete protogen visual novel with branching storylines"})

    # After loading, jump to the actual game in separate games directory
    jump netcode

# Lumetric launcher
label launch_lumetric_game:
    # Call loading screen first
    call show_loading_screen({"key": "lumetric", "display_name": "Lumetric", "description": "Streaming adventure visual novel"})

    # After loading, jump to the actual game in separate games directory
    jump Lumetric_1

# BearWithUs launcher
label launch_bearwithus_game:
    # Call loading screen first
    call show_loading_screen({"key": "bearwithus", "display_name": "BearWithUs", "description": "Cozy bear adventure story"})

    # After loading, jump to the actual game in separate games directory
    jump BearWithUs_1

# Demo Game launcher
label launch_demo_game:
    # Call loading screen first
    call show_loading_screen({"key": "demo", "display_name": "Demo Game", "description": "Sample visual novel experience"})

    # After loading, jump to the actual game in separate games directory
    jump demo_game_start

# Hopkin Girls Combat Team launcher
label launch_hopkin_girls_game:
    # Call loading screen first
    call show_loading_screen({"key": "hopkin_girls_combat", "display_name": "Hopkin Girls Combat Team", "description": "Epic visual novel featuring 20 hopkin girls with extensive dialogue and character development"})

    # After loading, jump to the actual game in separate games directory
    jump hopkin_girls_combat_start

# Adventures of Atlyss launcher
label launch_atlyss_game:
    # Call loading screen first
    call show_loading_screen({"key": "atlyss", "display_name": "Adventures of Atlyss", "description": "A romantic adventure visual novel based on the Steam game Atlyss, featuring character selection and dating mechanics"})

    # After loading, jump to the actual game in separate games directory
    jump adventures_of_atlyss_start

# Placeholder launchers for other games
label hopkin_boys_placeholder:
    call show_loading_screen({"key": "hopkin_boys_combat", "display_name": "Hopkin Boys Combat Team", "description": "Coming soon!"})
    
    scene black
    with fade
    "Hopkin Boys Combat Team"
    "This game is coming soon!"
    "Returning to game selection..."
    return

label nardodragon_placeholder:
    call show_loading_screen({"key": "nardodragon_combat", "display_name": "Nardodragon Combat Team", "description": "Coming soon!"})
    
    scene black
    with fade
    "Nardodragon Combat Team"
    "This game is coming soon!"
    "Returning to game selection..."
    return

label avali_placeholder:
    call show_loading_screen({"key": "avali_combat", "display_name": "Avali Combat Team", "description": "Coming soon!"})
    
    scene black
    with fade
    "Avali Combat Team"
    "This game is coming soon!"
    "Returning to game selection..."
    return

label rexouium_placeholder:
    call show_loading_screen({"key": "rexouium_combat", "display_name": "Rexouium Combat Team", "description": "Coming soon!"})
    
    scene black
    with fade
    "Rexouium Combat Team"
    "This game is coming soon!"
    "Returning to game selection..."
    return

# Austin the Wickerbeast launcher
label launch_austin_game:
    # Call loading screen first
    call show_loading_screen({"key": "austin_wickerbeast", "display_name": "Austin the Wickerbeast", "description": "A peaceful visual novel featuring just you and Austin the Wickerbeast in a magical forest"})

    # After loading, jump to the actual game in separate games directory
    jump austin_wickerbeast_start

label mamanightscale_placeholder:
    call show_loading_screen({"key": "mamanightscale_dragons", "display_name": "Mamanightscale of the Dead & The Last Dragon", "description": "Coming soon!"})
    
    scene black
    with fade
    "Mamanightscale of the Dead & The Last Dragon"
    "This game is coming soon!"
    "Returning to game selection..."
    return
