## Anti-Aliasing Shader Library
## Comprehensive collection of anti-aliasing shaders for Vulkan Ren'Py
## Includes FXAA, SMAA, TAA, and MSAA shader implementations

init python:
    
    class AAShaderLibrary:
        """
        Library of anti-aliasing shaders for Vulkan rendering
        Provides GLSL shader source code for different AA techniques
        """
        
        def __init__(self):
            self.shader_sources = {}
            self._initialize_shader_library()
        
        def _initialize_shader_library(self):
            """Initialize the anti-aliasing shader library"""
            print("Initializing AA shader library...")
            
            # FXAA shaders
            self._create_fxaa_shaders()
            
            # SMAA shaders
            self._create_smaa_shaders()
            
            # TAA shaders
            self._create_taa_shaders()
            
            # Utility shaders
            self._create_utility_shaders()
            
            print(f"✅ Initialized {len(self.shader_sources)} AA shaders")
        
        def _create_fxaa_shaders(self):
            """Create FXAA shader implementations"""
            
            # FXAA Compute Shader
            fxaa_compute = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rgba8) uniform writeonly image2D outputImage;

layout(binding = 2) uniform FXAAParams {
    float edge_threshold;
    float edge_threshold_min;
    float console_edge_sharpness;
    float console_edge_threshold;
    vec2 screen_size;
    vec2 texel_size;
} fxaa_params;

vec3 fxaa_luma_weights = vec3(0.299, 0.587, 0.114);

float fxaa_luma(vec3 rgb) {
    return dot(rgb, fxaa_luma_weights);
}

vec4 fxaa_pixel(ivec2 coord) {
    vec2 uv = (vec2(coord) + 0.5) / fxaa_params.screen_size;
    
    // Sample center and neighbors
    vec3 rgbN = imageLoad(inputImage, coord + ivec2(0, -1)).rgb;
    vec3 rgbW = imageLoad(inputImage, coord + ivec2(-1, 0)).rgb;
    vec3 rgbM = imageLoad(inputImage, coord).rgb;
    vec3 rgbE = imageLoad(inputImage, coord + ivec2(1, 0)).rgb;
    vec3 rgbS = imageLoad(inputImage, coord + ivec2(0, 1)).rgb;
    
    float lumaN = fxaa_luma(rgbN);
    float lumaW = fxaa_luma(rgbW);
    float lumaM = fxaa_luma(rgbM);
    float lumaE = fxaa_luma(rgbE);
    float lumaS = fxaa_luma(rgbS);
    
    float rangeMin = min(lumaM, min(min(lumaN, lumaW), min(lumaE, lumaS)));
    float rangeMax = max(lumaM, max(max(lumaN, lumaW), max(lumaE, lumaS)));
    float range = rangeMax - rangeMin;
    
    // Early exit if no edge detected
    if(range < max(fxaa_params.edge_threshold_min, rangeMax * fxaa_params.edge_threshold)) {
        return vec4(rgbM, 1.0);
    }
    
    // Sample diagonal neighbors
    vec3 rgbNW = imageLoad(inputImage, coord + ivec2(-1, -1)).rgb;
    vec3 rgbNE = imageLoad(inputImage, coord + ivec2(1, -1)).rgb;
    vec3 rgbSW = imageLoad(inputImage, coord + ivec2(-1, 1)).rgb;
    vec3 rgbSE = imageLoad(inputImage, coord + ivec2(1, 1)).rgb;
    
    float lumaNW = fxaa_luma(rgbNW);
    float lumaNE = fxaa_luma(rgbNE);
    float lumaSW = fxaa_luma(rgbSW);
    float lumaSE = fxaa_luma(rgbSE);
    
    float lumaL = (lumaN + lumaW + lumaE + lumaS) * 0.25;
    float rangeL = abs(lumaL - lumaM);
    float blendL = max(0.0, (rangeL / range) - fxaa_params.console_edge_threshold) * fxaa_params.console_edge_sharpness;
    blendL = min(0.75, blendL);
    
    // Determine blend direction
    float edgeVert = abs(lumaN + lumaS - 2.0 * lumaM) * 2.0 + abs(lumaNE + lumaSE - 2.0 * lumaE) + abs(lumaNW + lumaSW - 2.0 * lumaW);
    float edgeHorz = abs(lumaW + lumaE - 2.0 * lumaM) * 2.0 + abs(lumaNW + lumaNE - 2.0 * lumaN) + abs(lumaSW + lumaSE - 2.0 * lumaS);
    bool horzSpan = edgeHorz >= edgeVert;
    
    // Calculate blend offset
    float lengthSign = horzSpan ? -fxaa_params.texel_size.y : -fxaa_params.texel_size.x;
    if(!horzSpan) {
        lumaN = lumaW;
        lumaS = lumaE;
    }
    
    float gradientN = abs(lumaN - lumaM);
    float gradientS = abs(lumaS - lumaM);
    lumaN = (lumaN + lumaM) * 0.5;
    lumaS = (lumaS + lumaM) * 0.5;
    
    if(gradientN < gradientS) {
        lumaN = lumaS;
        lengthSign *= -1.0;
    }
    
    vec2 posN = uv;
    if(horzSpan) {
        posN.y += lengthSign * 0.5;
    } else {
        posN.x += lengthSign * 0.5;
    }
    
    // Sample along edge
    vec2 offNP = horzSpan ? vec2(fxaa_params.texel_size.x, 0.0) : vec2(0.0, fxaa_params.texel_size.y);
    vec2 posP = posN - offNP;
    vec2 posN2 = posN + offNP;
    
    vec3 rgbA = 0.5 * (imageLoad(inputImage, ivec2(posP * fxaa_params.screen_size)).rgb + 
                       imageLoad(inputImage, ivec2(posN2 * fxaa_params.screen_size)).rgb);
    vec3 rgbB = rgbA * 0.5 + 0.25 * (imageLoad(inputImage, ivec2(posP * fxaa_params.screen_size - offNP)).rgb + 
                                     imageLoad(inputImage, ivec2(posN2 * fxaa_params.screen_size + offNP)).rgb);
    
    float lumaB = fxaa_luma(rgbB);
    if((lumaB < rangeMin) || (lumaB > rangeMax)) {
        return vec4(rgbA, 1.0);
    } else {
        return vec4(rgbB, 1.0);
    }
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(fxaa_params.screen_size.x) || coord.y >= int(fxaa_params.screen_size.y)) return;
    
    vec4 result = fxaa_pixel(coord);
    imageStore(outputImage, coord, result);
}
"""
            
            self.shader_sources['fxaa_compute'] = fxaa_compute
        
        def _create_smaa_shaders(self):
            """Create SMAA shader implementations"""
            
            # SMAA Edge Detection Compute Shader
            smaa_edge_detection = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rg8) uniform writeonly image2D edgeImage;

layout(binding = 2) uniform SMAAParams {
    float threshold;
    float local_contrast_adaptation_factor;
    vec2 screen_size;
    vec2 texel_size;
} smaa_params;

vec3 smaa_luma_weights = vec3(0.2126, 0.7152, 0.0722);

float smaa_luma(vec3 color) {
    return dot(color, smaa_luma_weights);
}

vec2 smaa_edge_detection_luma(ivec2 coord) {
    vec2 threshold = vec2(smaa_params.threshold);
    
    // Sample center and neighbors
    float L = smaa_luma(imageLoad(inputImage, coord).rgb);
    float Lleft = smaa_luma(imageLoad(inputImage, coord + ivec2(-1, 0)).rgb);
    float Ltop = smaa_luma(imageLoad(inputImage, coord + ivec2(0, -1)).rgb);
    
    // Calculate deltas
    vec4 delta;
    delta.x = abs(L - Lleft);
    delta.y = abs(L - Ltop);
    
    vec2 edges = step(threshold, delta.xy);
    
    if(dot(edges, vec2(1.0)) == 0.0) {
        return vec2(0.0);
    }
    
    // Sample additional neighbors for better edge detection
    float Lright = smaa_luma(imageLoad(inputImage, coord + ivec2(1, 0)).rgb);
    float Lbottom = smaa_luma(imageLoad(inputImage, coord + ivec2(0, 1)).rgb);
    
    delta.z = abs(L - Lright);
    delta.w = abs(L - Lbottom);
    
    vec2 maxDelta = max(delta.xy, delta.zw);
    
    // Local contrast adaptation
    float left_right = max(delta.x, delta.z);
    float top_bottom = max(delta.y, delta.w);
    
    edges.x *= step(smaa_params.local_contrast_adaptation_factor * left_right, maxDelta.x);
    edges.y *= step(smaa_params.local_contrast_adaptation_factor * top_bottom, maxDelta.y);
    
    return edges;
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(smaa_params.screen_size.x) || coord.y >= int(smaa_params.screen_size.y)) return;
    
    vec2 edges = smaa_edge_detection_luma(coord);
    imageStore(edgeImage, coord, vec4(edges, 0.0, 1.0));
}
"""
            
            # SMAA Blending Weight Calculation
            smaa_blending_weight = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rg8) uniform readonly image2D edgeImage;
layout(binding = 1, rgba8) uniform writeonly image2D blendImage;
layout(binding = 2) uniform sampler2D areaTex;
layout(binding = 3) uniform sampler2D searchTex;

layout(binding = 4) uniform SMAABlendParams {
    vec2 screen_size;
    vec2 texel_size;
    int max_search_steps;
    float corner_rounding;
} smaa_blend_params;

#define SMAA_MAX_SEARCH_STEPS 16
#define SMAA_AREATEX_MAX_DISTANCE 16
#define SMAA_AREATEX_PIXEL_SIZE (1.0 / vec2(160.0, 560.0))
#define SMAA_AREATEX_SUBTEX_SIZE (1.0 / 7.0)

vec4 smaa_blending_weight_calculation(ivec2 coord) {
    vec2 uv = (vec2(coord) + 0.5) / smaa_blend_params.screen_size;
    vec4 weights = vec4(0.0);
    
    vec2 e = imageLoad(edgeImage, coord).rg;
    
    if(e.g > 0.0) { // Top edge
        // Search for horizontal edge endpoints
        vec2 d = vec2(0.0);
        
        // Search left
        for(int i = 1; i <= SMAA_MAX_SEARCH_STEPS; i++) {
            ivec2 searchCoord = coord + ivec2(-i, 0);
            if(searchCoord.x < 0) break;
            
            vec2 searchEdge = imageLoad(edgeImage, searchCoord).rg;
            if(searchEdge.r > 0.0 || searchEdge.g == 0.0) {
                d.x = float(i - 1);
                break;
            }
        }
        
        // Search right
        for(int i = 1; i <= SMAA_MAX_SEARCH_STEPS; i++) {
            ivec2 searchCoord = coord + ivec2(i, 0);
            if(searchCoord.x >= int(smaa_blend_params.screen_size.x)) break;
            
            vec2 searchEdge = imageLoad(edgeImage, searchCoord).rg;
            if(searchEdge.r > 0.0 || searchEdge.g == 0.0) {
                d.y = float(i - 1);
                break;
            }
        }
        
        // Calculate area
        vec2 area = texture(areaTex, (d + vec2(1.0)) * SMAA_AREATEX_PIXEL_SIZE).rg;
        weights.rg = area;
    }
    
    if(e.r > 0.0) { // Left edge
        // Search for vertical edge endpoints
        vec2 d = vec2(0.0);
        
        // Search up
        for(int i = 1; i <= SMAA_MAX_SEARCH_STEPS; i++) {
            ivec2 searchCoord = coord + ivec2(0, -i);
            if(searchCoord.y < 0) break;
            
            vec2 searchEdge = imageLoad(edgeImage, searchCoord).rg;
            if(searchEdge.g > 0.0 || searchEdge.r == 0.0) {
                d.x = float(i - 1);
                break;
            }
        }
        
        // Search down
        for(int i = 1; i <= SMAA_MAX_SEARCH_STEPS; i++) {
            ivec2 searchCoord = coord + ivec2(0, i);
            if(searchCoord.y >= int(smaa_blend_params.screen_size.y)) break;
            
            vec2 searchEdge = imageLoad(edgeImage, searchCoord).rg;
            if(searchEdge.g > 0.0 || searchEdge.r == 0.0) {
                d.y = float(i - 1);
                break;
            }
        }
        
        // Calculate area
        vec2 area = texture(areaTex, (d + vec2(1.0)) * SMAA_AREATEX_PIXEL_SIZE + vec2(0.0, SMAA_AREATEX_SUBTEX_SIZE)).rg;
        weights.ba = area;
    }
    
    return weights;
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(smaa_blend_params.screen_size.x) || coord.y >= int(smaa_blend_params.screen_size.y)) return;
    
    vec4 weights = smaa_blending_weight_calculation(coord);
    imageStore(blendImage, coord, weights);
}
"""
            
            # SMAA Neighborhood Blending
            smaa_neighborhood_blending = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rgba8) uniform readonly image2D blendImage;
layout(binding = 2, rgba8) uniform writeonly image2D outputImage;

layout(binding = 3) uniform SMAANeighborhoodParams {
    vec2 screen_size;
    vec2 texel_size;
} smaa_neighborhood_params;

vec4 smaa_neighborhood_blending(ivec2 coord) {
    vec4 color = imageLoad(inputImage, coord);
    vec4 weights = imageLoad(blendImage, coord);
    
    if(dot(weights, vec4(1.0)) < 1e-5) {
        return color;
    }
    
    vec4 result = color;
    
    // Horizontal blending
    if(weights.r > 0.0) {
        vec4 left = imageLoad(inputImage, coord + ivec2(-1, 0));
        result = mix(result, left, weights.r);
    }
    
    if(weights.g > 0.0) {
        vec4 right = imageLoad(inputImage, coord + ivec2(1, 0));
        result = mix(result, right, weights.g);
    }
    
    // Vertical blending
    if(weights.b > 0.0) {
        vec4 top = imageLoad(inputImage, coord + ivec2(0, -1));
        result = mix(result, top, weights.b);
    }
    
    if(weights.a > 0.0) {
        vec4 bottom = imageLoad(inputImage, coord + ivec2(0, 1));
        result = mix(result, bottom, weights.a);
    }
    
    return result;
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(smaa_neighborhood_params.screen_size.x) || coord.y >= int(smaa_neighborhood_params.screen_size.y)) return;
    
    vec4 result = smaa_neighborhood_blending(coord);
    imageStore(outputImage, coord, result);
}
"""
            
            self.shader_sources['smaa_edge_detection'] = smaa_edge_detection
            self.shader_sources['smaa_blending_weight'] = smaa_blending_weight
            self.shader_sources['smaa_neighborhood_blending'] = smaa_neighborhood_blending

        def _create_taa_shaders(self):
            """Create TAA shader implementations"""

            # TAA Resolve Compute Shader
            taa_resolve = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D currentFrame;
layout(binding = 1, rgba8) uniform readonly image2D historyBuffer;
layout(binding = 2, rg16f) uniform readonly image2D velocityBuffer;
layout(binding = 3, rgba8) uniform writeonly image2D outputImage;

layout(binding = 4) uniform TAAParams {
    float feedback_factor;
    float motion_blur_scale;
    bool variance_clipping;
    float history_rejection_threshold;
    vec2 screen_size;
    vec2 texel_size;
    mat4 current_view_proj;
    mat4 previous_view_proj;
    vec2 jitter_offset;
} taa_params;

vec3 rgb_to_ycocg(vec3 rgb) {
    float Y = dot(rgb, vec3(0.25, 0.5, 0.25));
    float Co = dot(rgb, vec3(0.5, 0.0, -0.5));
    float Cg = dot(rgb, vec3(-0.25, 0.5, -0.25));
    return vec3(Y, Co, Cg);
}

vec3 ycocg_to_rgb(vec3 ycocg) {
    float Y = ycocg.x;
    float Co = ycocg.y;
    float Cg = ycocg.z;

    float tmp = Y - Cg;
    float r = tmp + Co;
    float g = Y + Cg;
    float b = tmp - Co;

    return vec3(r, g, b);
}

vec3 clip_aabb(vec3 aabb_min, vec3 aabb_max, vec3 p, vec3 q) {
    vec3 r = q - p;
    vec3 rmax = aabb_max - p;
    vec3 rmin = aabb_min - p;

    if(r.x > rmax.x + 1e-7) r *= (rmax.x / r.x);
    if(r.y > rmax.y + 1e-7) r *= (rmax.y / r.y);
    if(r.z > rmax.z + 1e-7) r *= (rmax.z / r.z);

    if(r.x < rmin.x - 1e-7) r *= (rmin.x / r.x);
    if(r.y < rmin.y - 1e-7) r *= (rmin.y / r.y);
    if(r.z < rmin.z - 1e-7) r *= (rmin.z / r.z);

    return p + r;
}

vec4 taa_resolve_pixel(ivec2 coord) {
    vec2 uv = (vec2(coord) + 0.5) / taa_params.screen_size;

    // Sample current frame
    vec4 current_color = imageLoad(currentFrame, coord);

    // Sample velocity
    vec2 velocity = imageLoad(velocityBuffer, coord).xy;

    // Calculate history UV
    vec2 history_uv = uv - velocity;

    // Check if history sample is valid
    if(history_uv.x < 0.0 || history_uv.x > 1.0 || history_uv.y < 0.0 || history_uv.y > 1.0) {
        return current_color;
    }

    // Sample history with bilinear filtering
    ivec2 history_coord = ivec2(history_uv * taa_params.screen_size);
    vec4 history_color = imageLoad(historyBuffer, history_coord);

    // Convert to YCoCg for better temporal stability
    vec3 current_ycocg = rgb_to_ycocg(current_color.rgb);
    vec3 history_ycocg = rgb_to_ycocg(history_color.rgb);

    // Variance clipping
    if(taa_params.variance_clipping) {
        // Sample neighborhood for variance calculation
        vec3 m1 = vec3(0.0);
        vec3 m2 = vec3(0.0);

        for(int x = -1; x <= 1; x++) {
            for(int y = -1; y <= 1; y++) {
                ivec2 sample_coord = coord + ivec2(x, y);
                sample_coord = clamp(sample_coord, ivec2(0), ivec2(taa_params.screen_size) - 1);

                vec3 sample_color = rgb_to_ycocg(imageLoad(currentFrame, sample_coord).rgb);
                m1 += sample_color;
                m2 += sample_color * sample_color;
            }
        }

        m1 /= 9.0;
        m2 /= 9.0;

        vec3 variance = m2 - m1 * m1;
        vec3 sigma = sqrt(max(variance, vec3(0.0)));

        vec3 aabb_min = m1 - sigma;
        vec3 aabb_max = m1 + sigma;

        history_ycocg = clip_aabb(aabb_min, aabb_max, current_ycocg, history_ycocg);
    }

    // Calculate blend factor based on motion
    float motion_length = length(velocity * taa_params.screen_size);
    float motion_factor = clamp(motion_length / 10.0, 0.0, 1.0);

    // Reduce history contribution for fast motion
    float blend_factor = taa_params.feedback_factor * (1.0 - motion_factor * 0.5);

    // History rejection based on color difference
    float color_diff = length(current_ycocg - history_ycocg);
    if(color_diff > taa_params.history_rejection_threshold) {
        blend_factor *= 0.5;
    }

    // Blend current and history
    vec3 result_ycocg = mix(current_ycocg, history_ycocg, blend_factor);
    vec3 result_rgb = ycocg_to_rgb(result_ycocg);

    return vec4(result_rgb, current_color.a);
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(taa_params.screen_size.x) || coord.y >= int(taa_params.screen_size.y)) return;

    vec4 result = taa_resolve_pixel(coord);
    imageStore(outputImage, coord, result);
}
"""

            # Velocity Buffer Generation Vertex Shader
            velocity_vertex = """
#version 450

layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec2 inTexCoord;

layout(location = 0) out vec2 fragTexCoord;
layout(location = 1) out vec4 currentPos;
layout(location = 2) out vec4 previousPos;

layout(binding = 0) uniform VelocityUBO {
    mat4 current_mvp;
    mat4 previous_mvp;
    mat4 model;
} velocity_ubo;

void main() {
    vec4 worldPos = velocity_ubo.model * vec4(inPosition, 1.0);

    currentPos = velocity_ubo.current_mvp * worldPos;
    previousPos = velocity_ubo.previous_mvp * worldPos;

    gl_Position = currentPos;
    fragTexCoord = inTexCoord;
}
"""

            # Velocity Buffer Generation Fragment Shader
            velocity_fragment = """
#version 450

layout(location = 0) in vec2 fragTexCoord;
layout(location = 1) in vec4 currentPos;
layout(location = 2) in vec4 previousPos;

layout(location = 0) out vec2 velocity;

void main() {
    // Convert to NDC
    vec2 currentNDC = currentPos.xy / currentPos.w;
    vec2 previousNDC = previousPos.xy / previousPos.w;

    // Convert to screen space [0,1]
    vec2 currentScreen = currentNDC * 0.5 + 0.5;
    vec2 previousScreen = previousNDC * 0.5 + 0.5;

    // Calculate velocity
    velocity = currentScreen - previousScreen;
}
"""

            self.shader_sources['taa_resolve'] = taa_resolve
            self.shader_sources['velocity_vertex'] = velocity_vertex
            self.shader_sources['velocity_fragment'] = velocity_fragment

        def _create_utility_shaders(self):
            """Create utility shaders for anti-aliasing"""

            # Simple copy shader for fallback
            copy_shader = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rgba8) uniform writeonly image2D outputImage;

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    vec4 color = imageLoad(inputImage, coord);
    imageStore(outputImage, coord, color);
}
"""

            # Downsample shader for performance scaling
            downsample_shader = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rgba8) uniform writeonly image2D outputImage;

layout(binding = 2) uniform DownsampleParams {
    vec2 input_size;
    vec2 output_size;
    float filter_radius;
} downsample_params;

vec4 downsample_pixel(ivec2 coord) {
    vec2 uv = (vec2(coord) + 0.5) / downsample_params.output_size;
    vec2 input_coord = uv * downsample_params.input_size;

    // Simple box filter
    vec4 result = vec4(0.0);
    float weight_sum = 0.0;

    int radius = int(downsample_params.filter_radius);
    for(int x = -radius; x <= radius; x++) {
        for(int y = -radius; y <= radius; y++) {
            ivec2 sample_coord = ivec2(input_coord) + ivec2(x, y);
            sample_coord = clamp(sample_coord, ivec2(0), ivec2(downsample_params.input_size) - 1);

            vec4 sample_color = imageLoad(inputImage, sample_coord);
            float weight = 1.0;

            result += sample_color * weight;
            weight_sum += weight;
        }
    }

    return result / weight_sum;
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(downsample_params.output_size.x) || coord.y >= int(downsample_params.output_size.y)) return;

    vec4 result = downsample_pixel(coord);
    imageStore(outputImage, coord, result);
}
"""

            # Upsample shader
            upsample_shader = """
#version 450

layout(local_size_x = 16, local_size_y = 16, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform readonly image2D inputImage;
layout(binding = 1, rgba8) uniform writeonly image2D outputImage;

layout(binding = 2) uniform UpsampleParams {
    vec2 input_size;
    vec2 output_size;
    float sharpness;
} upsample_params;

vec4 upsample_pixel(ivec2 coord) {
    vec2 uv = (vec2(coord) + 0.5) / upsample_params.output_size;
    vec2 input_coord = uv * upsample_params.input_size - 0.5;

    ivec2 base_coord = ivec2(floor(input_coord));
    vec2 frac = input_coord - vec2(base_coord);

    // Bilinear interpolation with sharpening
    vec4 c00 = imageLoad(inputImage, clamp(base_coord + ivec2(0, 0), ivec2(0), ivec2(upsample_params.input_size) - 1));
    vec4 c10 = imageLoad(inputImage, clamp(base_coord + ivec2(1, 0), ivec2(0), ivec2(upsample_params.input_size) - 1));
    vec4 c01 = imageLoad(inputImage, clamp(base_coord + ivec2(0, 1), ivec2(0), ivec2(upsample_params.input_size) - 1));
    vec4 c11 = imageLoad(inputImage, clamp(base_coord + ivec2(1, 1), ivec2(0), ivec2(upsample_params.input_size) - 1));

    vec4 c0 = mix(c00, c10, frac.x);
    vec4 c1 = mix(c01, c11, frac.x);
    vec4 result = mix(c0, c1, frac.y);

    // Apply sharpening
    if(upsample_params.sharpness > 0.0) {
        vec4 center = result;
        vec4 average = (c00 + c10 + c01 + c11) * 0.25;
        result = mix(result, center + (center - average) * upsample_params.sharpness, 0.5);
    }

    return result;
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    if(coord.x >= int(upsample_params.output_size.x) || coord.y >= int(upsample_params.output_size.y)) return;

    vec4 result = upsample_pixel(coord);
    imageStore(outputImage, coord, result);
}
"""

            self.shader_sources['copy_shader'] = copy_shader
            self.shader_sources['downsample_shader'] = downsample_shader
            self.shader_sources['upsample_shader'] = upsample_shader

        def get_shader_source(self, shader_name):
            """Get shader source code by name"""
            return self.shader_sources.get(shader_name, None)

        def get_all_shaders(self):
            """Get all available shader names"""
            return list(self.shader_sources.keys())

        def compile_shader_for_vulkan(self, shader_name):
            """Compile shader for Vulkan (placeholder for actual compilation)"""
            source = self.get_shader_source(shader_name)
            if source:
                # This would compile GLSL to SPIR-V
                print(f"✅ Compiled shader: {shader_name}")
                return True
            return False

    # Initialize AA shader library
    aa_shader_library = AAShaderLibrary()

    def get_aa_shader(shader_name):
        """Get anti-aliasing shader source"""
        return aa_shader_library.get_shader_source(shader_name)

    def compile_aa_shaders():
        """Compile all anti-aliasing shaders"""
        shaders = aa_shader_library.get_all_shaders()
        compiled_count = 0

        for shader_name in shaders:
            if aa_shader_library.compile_shader_for_vulkan(shader_name):
                compiled_count += 1

        print(f"✅ Compiled {compiled_count}/{len(shaders)} AA shaders")
        return compiled_count == len(shaders)
