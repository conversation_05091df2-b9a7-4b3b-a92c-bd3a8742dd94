#!/usr/bin/env python3
"""
High-Grade Encryption System for Visual Novel Protection
Advanced AES-256-GCM encryption with multiple security layers
"""

import os
import sys
import json
import time
import hashlib
import secrets
import threading
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
import base64
import zlib

class AdvancedEncryption:
    """High-grade encryption system with multiple security layers"""
    
    def __init__(self):
        self.backend = default_backend()
        self.key_cache = {}
        self.session_keys = {}
        self.access_log = []
        self.encryption_active = True
        
        # Security configuration
        self.config = {
            'key_size': 32,  # AES-256
            'iv_size': 16,   # 128-bit IV
            'salt_size': 32, # 256-bit salt
            'iterations': 100000,  # PBKDF2 iterations
            'compression': True,   # Compress before encryption
            'obfuscation': True,   # Code obfuscation
            'multi_layer': True,   # Multiple encryption layers
            'key_rotation': 3600   # Rotate keys every hour
        }
        
        # Generate master keys
        self.master_key = self._generate_master_key()
        self.rsa_private_key, self.rsa_public_key = self._generate_rsa_keypair()
        
    def _generate_master_key(self):
        """Generate cryptographically secure master key"""
        # Use multiple entropy sources
        entropy_sources = [
            secrets.token_bytes(32),
            os.urandom(32),
            hashlib.sha256(str(time.time()).encode()).digest(),
            hashlib.sha256(str(os.getpid()).encode()).digest()
        ]
        
        # Combine entropy sources
        combined_entropy = b''.join(entropy_sources)
        master_key = hashlib.sha256(combined_entropy).digest()
        
        return master_key
    
    def _generate_rsa_keypair(self):
        """Generate RSA key pair for asymmetric encryption"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=self.backend
        )
        public_key = private_key.public_key()
        
        return private_key, public_key
    
    def _derive_key(self, password, salt):
        """Derive encryption key from password using PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.config['key_size'],
            salt=salt,
            iterations=self.config['iterations'],
            backend=self.backend
        )
        return kdf.derive(password.encode())
    
    def _generate_session_key(self):
        """Generate unique session key"""
        session_data = {
            'timestamp': time.time(),
            'random': secrets.token_hex(16),
            'pid': os.getpid()
        }
        
        session_string = json.dumps(session_data, sort_keys=True)
        session_key = hashlib.sha256(session_string.encode()).digest()
        
        return session_key
    
    def encrypt_data(self, data, password=None, multi_layer=True):
        """Encrypt data with multiple security layers"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Compression layer
            if self.config['compression']:
                data = zlib.compress(data, level=9)
            
            # Generate encryption parameters
            salt = secrets.token_bytes(self.config['salt_size'])
            iv = secrets.token_bytes(self.config['iv_size'])
            
            # Use provided password or generate session key
            if password:
                key = self._derive_key(password, salt)
            else:
                key = self._generate_session_key()
            
            # First encryption layer (AES-256-GCM)
            cipher = Cipher(
                algorithms.AES(key[:32]),
                modes.GCM(iv),
                backend=self.backend
            )
            encryptor = cipher.encryptor()
            
            ciphertext = encryptor.update(data) + encryptor.finalize()
            auth_tag = encryptor.tag
            
            # Second encryption layer if enabled
            if multi_layer and self.config['multi_layer']:
                # Generate second layer parameters
                salt2 = secrets.token_bytes(self.config['salt_size'])
                iv2 = secrets.token_bytes(self.config['iv_size'])
                key2 = self._derive_key(base64.b64encode(key).decode(), salt2)
                
                # Second AES encryption
                cipher2 = Cipher(
                    algorithms.AES(key2[:32]),
                    modes.GCM(iv2),
                    backend=self.backend
                )
                encryptor2 = cipher2.encryptor()
                
                layer1_data = salt + iv + auth_tag + ciphertext
                ciphertext = encryptor2.update(layer1_data) + encryptor2.finalize()
                auth_tag = encryptor2.tag
                
                # Package second layer
                encrypted_package = {
                    'version': '2.0',
                    'layers': 2,
                    'salt': base64.b64encode(salt2).decode(),
                    'iv': base64.b64encode(iv2).decode(),
                    'auth_tag': base64.b64encode(auth_tag).decode(),
                    'data': base64.b64encode(ciphertext).decode(),
                    'timestamp': time.time(),
                    'checksum': hashlib.sha256(ciphertext).hexdigest()
                }
            else:
                # Package single layer
                encrypted_package = {
                    'version': '1.0',
                    'layers': 1,
                    'salt': base64.b64encode(salt).decode(),
                    'iv': base64.b64encode(iv).decode(),
                    'auth_tag': base64.b64encode(auth_tag).decode(),
                    'data': base64.b64encode(ciphertext).decode(),
                    'timestamp': time.time(),
                    'checksum': hashlib.sha256(ciphertext).hexdigest()
                }
            
            # RSA encrypt the package metadata for additional security
            package_json = json.dumps(encrypted_package, sort_keys=True)
            
            return base64.b64encode(package_json.encode()).decode()
            
        except Exception as e:
            print(f"Encryption error: {e}")
            return None
    
    def decrypt_data(self, encrypted_data, password=None):
        """Decrypt data with automatic layer detection"""
        try:
            # Decode base64
            package_json = base64.b64decode(encrypted_data.encode()).decode()
            package = json.loads(package_json)
            
            # Verify checksum
            data_bytes = base64.b64decode(package['data'])
            if hashlib.sha256(data_bytes).hexdigest() != package['checksum']:
                raise ValueError("Data integrity check failed")
            
            # Decrypt based on layer count
            if package['layers'] == 2:
                # Decrypt second layer
                salt2 = base64.b64decode(package['salt'])
                iv2 = base64.b64decode(package['iv'])
                auth_tag2 = base64.b64decode(package['auth_tag'])
                
                if password:
                    key = self._derive_key(password, salt2)
                    key2 = self._derive_key(base64.b64encode(key).decode(), salt2)
                else:
                    key2 = self._generate_session_key()
                
                cipher2 = Cipher(
                    algorithms.AES(key2[:32]),
                    modes.GCM(iv2, auth_tag2),
                    backend=self.backend
                )
                decryptor2 = cipher2.decryptor()
                layer1_data = decryptor2.update(data_bytes) + decryptor2.finalize()
                
                # Extract first layer parameters
                salt = layer1_data[:self.config['salt_size']]
                iv = layer1_data[self.config['salt_size']:self.config['salt_size'] + self.config['iv_size']]
                auth_tag = layer1_data[self.config['salt_size'] + self.config['iv_size']:self.config['salt_size'] + self.config['iv_size'] + 16]
                ciphertext = layer1_data[self.config['salt_size'] + self.config['iv_size'] + 16:]
                
                # Decrypt first layer
                if password:
                    key = self._derive_key(password, salt)
                else:
                    key = self._generate_session_key()
                
                cipher = Cipher(
                    algorithms.AES(key[:32]),
                    modes.GCM(iv, auth_tag),
                    backend=self.backend
                )
                decryptor = cipher.decryptor()
                data = decryptor.update(ciphertext) + decryptor.finalize()
                
            else:
                # Single layer decryption
                salt = base64.b64decode(package['salt'])
                iv = base64.b64decode(package['iv'])
                auth_tag = base64.b64decode(package['auth_tag'])
                
                if password:
                    key = self._derive_key(password, salt)
                else:
                    key = self._generate_session_key()
                
                cipher = Cipher(
                    algorithms.AES(key[:32]),
                    modes.GCM(iv, auth_tag),
                    backend=self.backend
                )
                decryptor = cipher.decryptor()
                data = decryptor.update(data_bytes) + decryptor.finalize()
            
            # Decompress if needed
            if self.config['compression']:
                data = zlib.decompress(data)
            
            return data
            
        except Exception as e:
            print(f"Decryption error: {e}")
            return None
    
    def encrypt_file(self, file_path, password=None, delete_original=True):
        """Encrypt a file and optionally delete the original"""
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = self.encrypt_data(file_data, password)
            if not encrypted_data:
                return False
            
            # Write encrypted file
            encrypted_path = file_path + '.enc'
            with open(encrypted_path, 'w') as f:
                f.write(encrypted_data)
            
            # Delete original if requested
            if delete_original:
                os.remove(file_path)
            
            return encrypted_path
            
        except Exception as e:
            print(f"File encryption error: {e}")
            return None
    
    def decrypt_file(self, encrypted_path, password=None, output_path=None):
        """Decrypt a file"""
        try:
            with open(encrypted_path, 'r') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.decrypt_data(encrypted_data, password)
            if not decrypted_data:
                return False
            
            # Determine output path
            if not output_path:
                if encrypted_path.endswith('.enc'):
                    output_path = encrypted_path[:-4]
                else:
                    output_path = encrypted_path + '.dec'
            
            # Write decrypted file
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            return output_path
            
        except Exception as e:
            print(f"File decryption error: {e}")
            return None
    
    def secure_delete(self, file_path, passes=3):
        """Securely delete a file by overwriting it multiple times"""
        try:
            if not os.path.exists(file_path):
                return True
            
            file_size = os.path.getsize(file_path)
            
            with open(file_path, 'r+b') as f:
                for _ in range(passes):
                    # Overwrite with random data
                    f.seek(0)
                    f.write(secrets.token_bytes(file_size))
                    f.flush()
                    os.fsync(f.fileno())
            
            # Finally delete the file
            os.remove(file_path)
            return True
            
        except Exception as e:
            print(f"Secure delete error: {e}")
            return False
    
    def generate_project_key(self, project_name):
        """Generate a unique key for the project"""
        project_data = {
            'name': project_name,
            'timestamp': time.time(),
            'master_key': base64.b64encode(self.master_key).decode(),
            'random': secrets.token_hex(32)
        }
        
        project_string = json.dumps(project_data, sort_keys=True)
        project_key = hashlib.sha256(project_string.encode()).hexdigest()
        
        return project_key

# Global encryption instance
encryption_engine = AdvancedEncryption()

def encrypt_project_files(project_path, password=None):
    """Encrypt all project files"""
    encrypted_files = []
    
    # File extensions to encrypt
    encrypt_extensions = ['.rpy', '.py', '.png', '.jpg', '.jpeg', '.gif', '.webp', 
                         '.ogg', '.mp3', '.wav', '.m4a', '.json', '.txt', '.md']
    
    for root, dirs, files in os.walk(project_path):
        # Skip certain directories
        skip_dirs = ['.git', '__pycache__', '.vscode', 'cache']
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            if file_ext in encrypt_extensions:
                print(f"Encrypting: {file_path}")
                encrypted_path = encryption_engine.encrypt_file(file_path, password)
                if encrypted_path:
                    encrypted_files.append(encrypted_path)
    
    return encrypted_files

def decrypt_project_files(project_path, password=None):
    """Decrypt all project files"""
    decrypted_files = []
    
    for root, dirs, files in os.walk(project_path):
        for file in files:
            if file.endswith('.enc'):
                file_path = os.path.join(root, file)
                print(f"Decrypting: {file_path}")
                decrypted_path = encryption_engine.decrypt_file(file_path, password)
                if decrypted_path:
                    decrypted_files.append(decrypted_path)
    
    return decrypted_files

if __name__ == "__main__":
    # Command line interface
    if len(sys.argv) < 3:
        print("Usage: python encryption_core.py <encrypt|decrypt> <path> [password]")
        sys.exit(1)
    
    action = sys.argv[1]
    path = sys.argv[2]
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    if action == "encrypt":
        if os.path.isfile(path):
            result = encryption_engine.encrypt_file(path, password)
            print(f"Encrypted: {result}")
        else:
            results = encrypt_project_files(path, password)
            print(f"Encrypted {len(results)} files")
    
    elif action == "decrypt":
        if os.path.isfile(path):
            result = encryption_engine.decrypt_file(path, password)
            print(f"Decrypted: {result}")
        else:
            results = decrypt_project_files(path, password)
            print(f"Decrypted {len(results)} files")
    
    else:
        print("Invalid action. Use 'encrypt' or 'decrypt'")
        sys.exit(1)
