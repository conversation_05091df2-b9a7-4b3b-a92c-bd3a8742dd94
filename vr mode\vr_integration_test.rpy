# VR Integration Test - Ensures VR system works with Ren'Py engine
# Safe testing and initialization of all VR features

# VR System Test Label
label vr_system_test:
    """Test VR system integration"""
    
    "Testing VR system integration..."
    
    python:
        # Test basic VR detection
        vr_systems_detected = []
        
        # Test SteamVR detection
        try:
            if detect_steamvr():
                vr_systems_detected.append("SteamVR")
        except:
            pass
        
        # Test Quest VR detection  
        try:
            if detect_quest_vr():
                vr_systems_detected.append("Quest VR")
        except:
            pass
        
        # Test advanced features
        advanced_features_detected = []
        
        try:
            if detect_eye_tracking():
                advanced_features_detected.append("Eye Tracking")
        except:
            pass
        
        try:
            if detect_full_body_tracking():
                advanced_features_detected.append("Body Tracking")
        except:
            pass
        
        try:
            if detect_advanced_haptics():
                advanced_features_detected.append("Advanced Haptics")
        except:
            pass
    
    if vr_systems_detected:
        "VR Systems Detected: [', '.join(vr_systems_detected)]"
    else:
        "No VR systems detected - running in standard mode"
    
    if advanced_features_detected:
        "Advanced VR Features: [', '.join(advanced_features_detected)]"
    else:
        "No advanced VR features detected"
    
    "VR system test complete!"
    
    return

# Safe VR Function Wrappers
init python:
    def safe_vr_function_call(func_name, *args, **kwargs):
        """Safely call VR functions with error handling"""
        try:
            if func_name in globals():
                return globals()[func_name](*args, **kwargs)
            else:
                return None
        except Exception as e:
            return None
    
    def test_vr_compatibility():
        """Test VR system compatibility with Ren'Py"""
        compatibility_report = {
            'steamvr_detection': False,
            'quest_detection': False,
            'eye_tracking': False,
            'body_tracking': False,
            'haptics': False,
            'screens_available': False,
            'functions_available': False
        }
        
        # Test SteamVR detection
        try:
            compatibility_report['steamvr_detection'] = safe_vr_function_call('detect_steamvr') or False
        except:
            pass
        
        # Test Quest detection
        try:
            compatibility_report['quest_detection'] = safe_vr_function_call('detect_quest_vr') or False
        except:
            pass
        
        # Test advanced features
        try:
            compatibility_report['eye_tracking'] = safe_vr_function_call('detect_eye_tracking') or False
        except:
            pass
        
        try:
            compatibility_report['body_tracking'] = safe_vr_function_call('detect_full_body_tracking') or False
        except:
            pass
        
        try:
            compatibility_report['haptics'] = safe_vr_function_call('detect_advanced_haptics') or False
        except:
            pass
        
        # Test screen availability
        vr_screens = ['vr_room', 'vr_controller_left', 'vr_controller_right', 'steamvr_advanced_features']
        screens_available = 0
        for screen_name in vr_screens:
            if renpy.has_screen(screen_name):
                screens_available += 1
        
        compatibility_report['screens_available'] = screens_available > 0
        
        # Test function availability
        vr_functions = ['enable_steamvr', 'init_advanced_vr_features', 'auto_enable_steamvr']
        functions_available = 0
        for func_name in vr_functions:
            if func_name in globals():
                functions_available += 1
        
        compatibility_report['functions_available'] = functions_available > 0
        
        return compatibility_report
    
    def initialize_vr_safely():
        """Safely initialize VR systems"""
        try:
            # Initialize basic VR variables if not already set
            if not hasattr(store, 'steamvr_mode_active'):
                store.steamvr_mode_active = False
            
            if not hasattr(store, 'vr_mode_active'):
                store.vr_mode_active = False
            
            if not hasattr(store, 'eye_tracking_active'):
                store.eye_tracking_active = False
            
            if not hasattr(store, 'full_body_tracking_active'):
                store.full_body_tracking_active = False
            
            if not hasattr(store, 'advanced_haptics_active'):
                store.advanced_haptics_active = False
            
            # Try to auto-detect and enable VR if available
            if 'auto_enable_steamvr' in globals():
                auto_enable_steamvr()
            
            if 'auto_enable_vr' in globals():
                auto_enable_vr()
            
            return True
        except:
            return False

# VR Status Screen
screen vr_status_display():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        background "#001122"
        padding (30, 30)
        
        vbox:
            spacing 20
            
            text "🥽 VR System Status" size 24 xalign 0.5 color "#00ffff"
            
            python:
                compatibility = test_vr_compatibility()
            
            # VR Systems
            text "VR Systems:" size 18 color "#caf0f8"
            if compatibility['steamvr_detection']:
                text "✅ SteamVR: Detected" size 14 color "#00ff88"
            else:
                text "❌ SteamVR: Not detected" size 14 color "#ff6666"
            
            if compatibility['quest_detection']:
                text "✅ Quest VR: Detected" size 14 color "#00ff88"
            else:
                text "❌ Quest VR: Not detected" size 14 color "#ff6666"
            
            # Advanced Features
            text "Advanced Features:" size 18 color "#caf0f8"
            if compatibility['eye_tracking']:
                text "✅ Eye Tracking: Available" size 14 color "#00ff88"
            else:
                text "❌ Eye Tracking: Not available" size 14 color "#ff6666"
            
            if compatibility['body_tracking']:
                text "✅ Body Tracking: Available" size 14 color "#00ff88"
            else:
                text "❌ Body Tracking: Not available" size 14 color "#ff6666"
            
            if compatibility['haptics']:
                text "✅ Advanced Haptics: Available" size 14 color "#00ff88"
            else:
                text "❌ Advanced Haptics: Not available" size 14 color "#ff6666"
            
            # System Status
            text "System Status:" size 18 color "#caf0f8"
            if compatibility['screens_available']:
                text "✅ VR Screens: Loaded" size 14 color "#00ff88"
            else:
                text "❌ VR Screens: Missing" size 14 color "#ff6666"
            
            if compatibility['functions_available']:
                text "✅ VR Functions: Available" size 14 color "#00ff88"
            else:
                text "❌ VR Functions: Missing" size 14 color "#ff6666"
            
            # Current Mode
            text "Current Mode:" size 18 color "#caf0f8"
            if steamvr_mode_active:
                text "🥽 SteamVR Mode Active" size 14 color "#00ff88"
            elif vr_mode_active:
                text "🎮 Quest VR Mode Active" size 14 color "#00ff88"
            else:
                text "🖥️ Desktop Mode" size 14 color "#90e0ef"
            
            textbutton "Close" action Hide("vr_status_display") xalign 0.5 xsize 150 ysize 40

# VR Initialization on Startup
label vr_startup_init:
    """Initialize VR systems on game startup"""
    
    python:
        # Safe VR initialization
        vr_init_success = initialize_vr_safely()
        
        if vr_init_success:
            # Check what VR systems are available
            vr_status = test_vr_compatibility()
            
            # Notify user of VR capabilities
            if vr_status['steamvr_detection'] or vr_status['quest_detection']:
                renpy.notify("VR systems detected!")
            
            if any([vr_status['eye_tracking'], vr_status['body_tracking'], vr_status['haptics']]):
                renpy.notify("Advanced VR features available!")
    
    return

# Add VR status to main menu (optional)
init python:
    def show_vr_status():
        """Show VR status screen"""
        renpy.show_screen("vr_status_display")

# Safe VR variable defaults
default vr_init_complete = False
default vr_compatibility_checked = False
