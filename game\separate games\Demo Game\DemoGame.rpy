# Demo Game - Sample Separate Game
# This is a demonstration of how separate games work with the launcher

## Game Information
# title: Demo Adventure
# description: A short demonstration game showing the launcher system in action. Follow a simple adventure story with multiple choices and outcomes.
# version: 1.0
# author: Netcode Development Team

# Character definitions
define narrator = Character(None, color="#ffffff")
define player = Character("You", color="#00ff00")
define guide = Character("Guide", color="#ffaa00")
define mysterious_voice = Character("???", color="#ff0066")

# Game variables
default demo_choice = ""
default demo_score = 0
default demo_ending = ""

# Background and image definitions (using simple colored backgrounds)
image bg forest = "#2d5016"
image bg cave = "#1a1a1a"
image bg treasure = "#ffd700"
image bg village = "#8b4513"

# Start of the demo game
label demo_game_start:
    
    # Show launcher integration info if available
    if hasattr(renpy.store, 'launcher_state') and launcher_state.get('launcher_detected', False):
        "Welcome to the Demo Game!"
        "This game was launched from the Universal Game Launcher."
        ""
    
    scene bg village
    
    narrator "Welcome to the Demo Adventure!"
    narrator "This is a sample game that demonstrates the separate game system."
    
    guide "Hello there, adventurer! I'm your guide for this demonstration."
    guide "This game shows how the launcher can detect and run separate games."
    
    player "This is pretty cool! How does it work?"
    
    guide "The launcher scans the 'separate games' folder and finds .rpy files like this one."
    guide "Each separate game can have its own story, characters, and gameplay."
    guide "But they all share the same engine and anti-cheat protection!"
    
    menu:
        "What would you like to do?"
        
        "Learn about the launcher system":
            jump learn_launcher
        
        "Go on an adventure":
            jump adventure_start
        
        "Test game features":
            jump test_features

label learn_launcher:
    
    guide "Great choice! Let me explain how this all works."
    
    guide "The Game Launcher is a Python application that:"
    guide "• Scans for .rpy files in the 'separate games' folder"
    guide "• Extracts game information from comments in the files"
    guide "• Creates a nice interface to select and launch games"
    guide "• Integrates with the anti-cheat and encryption systems"
    
    guide "Each separate game can be completely independent, with its own:"
    guide "• Story and characters"
    guide "• Images and sounds"
    guide "• Game mechanics"
    guide "• Save files"
    
    guide "But they all benefit from the main game's security features!"
    
    $ demo_score += 10
    
    menu:
        "What else would you like to know?"
        
        "How do I create my own separate game?":
            jump create_game_info
        
        "What about anti-cheat integration?":
            jump anticheat_info
        
        "Let's try the adventure instead":
            jump adventure_start

label create_game_info:
    
    guide "Creating your own separate game is easy!"
    
    guide "Just follow these steps:"
    guide "1. Create a new folder in 'separate games'"
    guide "2. Create a .rpy file with your game content"
    guide "3. Add game information in comments at the top"
    guide "4. The launcher will automatically detect it!"
    
    guide "Here's the format for game information:"
    guide "# title: Your Game Name"
    guide "# description: A description of your game"
    guide "# version: 1.0"
    guide "# author: Your Name"
    
    guide "The launcher reads these comments to display game details."
    
    $ demo_score += 15
    
    jump adventure_start

label anticheat_info:
    
    guide "The anti-cheat system is fully integrated with separate games!"
    
    guide "When you launch a game through the launcher:"
    guide "• It checks the anti-cheat system status"
    guide "• Configures appropriate security settings"
    guide "• Monitors for any violations"
    guide "• Protects against cheating and tampering"
    
    guide "Separate games get slightly relaxed anti-cheat settings since they're smaller experiences."
    guide "But they're still protected against major violations!"
    
    $ demo_score += 20
    
    jump adventure_start

label adventure_start:
    
    scene bg forest
    
    narrator "You find yourself at the edge of a mysterious forest."
    narrator "The trees seem to whisper secrets, and strange lights flicker in the distance."
    
    guide "This is where your adventure begins!"
    guide "Make choices and see how they affect your journey."
    
    menu:
        "Which path do you choose?"
        
        "Follow the bright lights deeper into the forest":
            $ demo_choice = "lights"
            jump forest_lights
        
        "Take the dark path that seems safer":
            $ demo_choice = "dark"
            jump forest_dark
        
        "Climb a tree to get a better view":
            $ demo_choice = "tree"
            jump forest_tree

label forest_lights:
    
    narrator "You follow the dancing lights deeper into the forest."
    narrator "They lead you to a hidden clearing where ancient stones form a circle."
    
    mysterious_voice "Welcome, brave one..."
    mysterious_voice "You have found the Circle of Choices."
    mysterious_voice "Here, your decisions shape reality itself."
    
    $ demo_score += 25
    
    menu:
        "The voice offers you a choice:"
        
        "Accept the mysterious power":
            $ demo_ending = "magic"
            jump ending_magic
        
        "Politely decline and leave":
            $ demo_ending = "wise"
            jump ending_wise

label forest_dark:
    
    narrator "You take the darker, seemingly safer path."
    narrator "It leads you to the entrance of a cave."
    
    scene bg cave
    
    narrator "Inside the cave, you discover ancient treasures!"
    narrator "Gold coins, precious gems, and mysterious artifacts fill the chamber."
    
    $ demo_score += 30
    
    menu:
        "What do you do with the treasure?"
        
        "Take some treasure for yourself":
            $ demo_ending = "treasure"
            jump ending_treasure
        
        "Leave the treasure untouched":
            $ demo_ending = "noble"
            jump ending_noble

label forest_tree:
    
    narrator "You climb the tallest tree you can find."
    narrator "From the top, you can see the entire forest spread out below you."
    
    narrator "You notice both the lights and the cave, plus a hidden village in the distance!"
    
    $ demo_score += 35
    
    menu:
        "With this new knowledge, where do you go?"
        
        "Visit the hidden village":
            $ demo_ending = "village"
            jump ending_village
        
        "Now go to the lights":
            jump forest_lights
        
        "Now go to the cave":
            jump forest_dark

label test_features:
    
    narrator "Let's test some game features!"
    
    guide "This section demonstrates various Ren'Py features working in a separate game."
    
    # Test variables
    $ test_var = "This is a test variable"
    guide "Variables work: [test_var]"
    
    # Test images
    scene bg treasure
    guide "Background images work!"
    
    # Test choices with conditions
    menu:
        "Choose a test:"
        
        "Test save/load" if True:
            guide "Save and load functionality works normally in separate games!"
            guide "Each separate game has its own save files."
        
        "Test preferences" if True:
            guide "Preferences and settings are shared with the main game."
            guide "This ensures a consistent user experience."
        
        "Test anti-cheat integration" if hasattr(renpy.store, 'anticheat_state'):
            guide "Anti-cheat system is active and monitoring!"
            guide "Separate games are protected just like the main game."
        
        "Continue to adventure":
            jump adventure_start

# Ending labels
label ending_magic:
    
    scene bg treasure
    
    mysterious_voice "You have chosen wisely. The power flows through you!"
    
    narrator "You feel a surge of magical energy."
    narrator "With your new abilities, you become a guardian of the forest."
    
    $ demo_score += 50
    
    jump final_score

label ending_wise:
    
    scene bg village
    
    narrator "Your wisdom in declining unknown power serves you well."
    narrator "You find your way to a peaceful village where you're welcomed as a hero."
    
    $ demo_score += 40
    
    jump final_score

label ending_treasure:
    
    scene bg treasure
    
    narrator "You take some of the treasure and become wealthy beyond your dreams."
    narrator "But you always wonder what other paths you might have taken."
    
    $ demo_score += 35
    
    jump final_score

label ending_noble:
    
    scene bg village
    
    narrator "Your noble choice to leave the treasure untouched is rewarded."
    narrator "The cave's guardian appears and grants you a different kind of wealth - wisdom."
    
    $ demo_score += 45
    
    jump final_score

label ending_village:
    
    scene bg village
    
    narrator "You discover the hidden village and are welcomed as a friend."
    narrator "The villagers share their knowledge and you become a respected member of their community."
    
    $ demo_score += 55
    
    jump final_score

label final_score:
    
    scene bg village
    
    guide "Congratulations! You've completed the Demo Adventure!"
    
    guide "Your final score: [demo_score] points"
    
    if demo_score >= 50:
        guide "Excellent! You made great choices throughout your adventure."
    elif demo_score >= 30:
        guide "Good job! You navigated the adventure well."
    else:
        guide "Not bad! Every adventure is a learning experience."
    
    guide "This demonstrates how separate games can have:"
    guide "• Multiple branching storylines"
    guide "• Character development"
    guide "• Score tracking"
    guide "• Multiple endings"
    guide "• Full Ren'Py feature support"
    
    menu:
        "What would you like to do now?"
        
        "Play again with different choices":
            $ demo_score = 0
            $ demo_choice = ""
            $ demo_ending = ""
            jump start
        
        "Return to the launcher" if hasattr(renpy.store, 'return_to_launcher'):
            call expression "return_to_launcher" from _call_return_to_launcher
        
        "Exit game":
            "Thanks for playing the Demo Adventure!"
            "This shows how the launcher system can support multiple independent games."
            return

# Default return if launcher function not available
label demo_game_end:
    "Thanks for playing the Demo Adventure!"
    "This demonstrates the separate game system in action."
    return
