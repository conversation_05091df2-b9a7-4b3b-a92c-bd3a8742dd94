#!/bin/bash

# Enhanced Graphics Launcher for Linux/macOS
# NVIDIA Cap: GTX 1080 Max | AMD Min: RX 560 | Intel Arc Support

echo "================================================================"
echo "Enhanced Graphics Renderer Launcher (Linux/macOS)"
echo "NVIDIA Cap: GTX 1080 Max | AMD Min: RX 560 | Intel Arc"
echo "================================================================"
echo

# Detect operating system
OS=$(uname -s)
ARCH=$(uname -m)

echo "Detecting system configuration..."
echo "OS: $OS"
echo "Architecture: $ARCH"

# Enhanced GPU detection with performance caps
GPU_INFO=""
RECOMMENDED="gl2"
PERFORMANCE_TIER="MEDIUM"
GPU_TYPE="UNKNOWN"
IS_INTEGRATED="NO"
GPU_VENDOR="UNKNOWN"
PERFORMANCE_CAP="false"
BELOW_MINIMUM="false"

if command -v lspci &> /dev/null; then
    # Linux GPU detection
    GPU_INFO=$(lspci | grep -i vga | head -1)
    echo "GPU: $GPU_INFO"

    # Check for integrated graphics indicators
    if [[ $GPU_INFO == *"Integrated"* ]] || [[ $GPU_INFO == *"UHD"* ]] || [[ $GPU_INFO == *"Iris"* ]] || [[ $GPU_INFO == *"Vega"*"Graphics"* ]]; then
        IS_INTEGRATED="YES"
        echo "Type: Integrated Graphics Detected"
    fi

    # NVIDIA Detection with GTX 1080 Performance Cap
    if [[ $GPU_INFO == *"NVIDIA"* ]] || [[ $GPU_INFO == *"GeForce"* ]] || [[ $GPU_INFO == *"GTX"* ]] || [[ $GPU_INFO == *"RTX"* ]]; then
        GPU_VENDOR="NVIDIA"
        echo "Vendor: NVIDIA Detected"

        # Check for GPUs exceeding GTX 1080 cap
        if [[ $GPU_INFO == *"RTX 40"* ]] || [[ $GPU_INFO == *"RTX4"* ]]; then
            echo "Series: RTX 40 Series - PERFORMANCE CAPPED to GTX 1080"
            PERFORMANCE_CAP="true"
            PERFORMANCE_TIER="CAPPED_HIGH"
        elif [[ $GPU_INFO == *"RTX 30"* ]] || [[ $GPU_INFO == *"RTX3"* ]]; then
            echo "Series: RTX 30 Series - PERFORMANCE CAPPED to GTX 1080"
            PERFORMANCE_CAP="true"
            PERFORMANCE_TIER="CAPPED_HIGH"
        elif [[ $GPU_INFO == *"RTX 20"* ]] || [[ $GPU_INFO == *"RTX2"* ]]; then
            if [[ $GPU_INFO == *"2080 Ti"* ]]; then
                echo "Series: RTX 2080 Ti - PERFORMANCE CAPPED to GTX 1080"
                PERFORMANCE_CAP="true"
                PERFORMANCE_TIER="CAPPED_HIGH"
            else
                echo "Series: RTX 20 Series - Standard Performance"
                PERFORMANCE_TIER="HIGH"
            fi
        elif [[ $GPU_INFO == *"GTX 1080"* ]]; then
            if [[ $GPU_INFO == *"1080 Ti"* ]]; then
                echo "Series: GTX 1080 Ti - PERFORMANCE CAPPED to GTX 1080"
                PERFORMANCE_CAP="true"
                PERFORMANCE_TIER="CAPPED_HIGH"
            else
                echo "Series: GTX 1080 - REFERENCE PERFORMANCE LEVEL"
                PERFORMANCE_TIER="REFERENCE"
            fi
        elif [[ $GPU_INFO == *"GTX"* ]]; then
            echo "Series: GTX Series - Standard Performance"
            PERFORMANCE_TIER="MEDIUM"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="HIGH"
        GPU_TYPE="DISCRETE"
    elif [[ $GPU_INFO == *"GTX"* ]]; then
        echo "Series: GTX Series Detected - Medium Performance Mode"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="MEDIUM"
        GPU_TYPE="DISCRETE"

    # Intel Detection
    elif [[ $GPU_INFO == *"Intel"* ]]; then
        echo "Vendor: Intel Detected"
        if [[ $GPU_INFO == *"Arc"* ]]; then
            echo "Series: Intel Arc Detected - High Performance Mode"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="HIGH"
            GPU_TYPE="DISCRETE"
        elif [[ $GPU_INFO == *"Iris Xe"* ]]; then
            echo "Series: Intel Iris Xe Detected - Medium Performance Mode (Integrated)"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="MEDIUM"
            GPU_TYPE="INTEGRATED"
            IS_INTEGRATED="YES"
        elif [[ $GPU_INFO == *"Iris"* ]]; then
            echo "Series: Intel Iris Detected - Low Performance Mode (Integrated)"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="LOW"
            GPU_TYPE="INTEGRATED"
            IS_INTEGRATED="YES"
        elif [[ $GPU_INFO == *"UHD"* ]]; then
            echo "Series: Intel UHD Detected - Low Performance Mode (Integrated)"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="LOW"
            GPU_TYPE="INTEGRATED"
            IS_INTEGRATED="YES"
        else
            echo "Series: Intel Integrated Graphics - Conservative Settings"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="LOW"
            GPU_TYPE="INTEGRATED"
            IS_INTEGRATED="YES"
        fi

    # AMD Detection
    elif [[ $GPU_INFO == *"AMD"* ]] || [[ $GPU_INFO == *"Radeon"* ]]; then
        echo "Vendor: AMD Detected"
        if [[ $GPU_INFO == *"RX 7"* ]]; then
            echo "Series: AMD RX 7000 Detected - Ultra Performance Mode"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="ULTRA"
            GPU_TYPE="DISCRETE"
        elif [[ $GPU_INFO == *"RX 6"* ]]; then
            echo "Series: AMD RX 6000 Detected - High Performance Mode"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="HIGH"
            GPU_TYPE="DISCRETE"
        elif [[ $GPU_INFO == *"Vega"* ]]; then
            if [[ $GPU_INFO == *"Graphics"* ]]; then
                echo "Series: AMD Vega APU Detected - Medium Performance Mode (Integrated)"
                RECOMMENDED="gl2"
                PERFORMANCE_TIER="MEDIUM"
                GPU_TYPE="APU"
                IS_INTEGRATED="YES"
            else
                echo "Series: AMD Vega Detected - Medium Performance Mode"
                RECOMMENDED="gl2"
                PERFORMANCE_TIER="MEDIUM"
                GPU_TYPE="DISCRETE"
            fi
        else
            echo "Series: AMD Graphics - Balanced Settings"
            RECOMMENDED="gl2"
            PERFORMANCE_TIER="MEDIUM"
            GPU_TYPE="DISCRETE"
        fi
    else
        echo "Unknown GPU - Using balanced settings"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="MEDIUM"
        GPU_TYPE="UNKNOWN"
    fi
elif [[ "$OS" == "Darwin" ]]; then
    # macOS GPU detection
    GPU_INFO=$(system_profiler SPDisplaysDataType | grep "Chipset Model" | head -1)
    echo "GPU: $GPU_INFO"
    
    if [[ $GPU_INFO == *"AMD"* ]]; then
        echo "macOS with AMD GPU - Optimized Settings"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="MEDIUM"
    elif [[ $GPU_INFO == *"Intel"* ]]; then
        echo "macOS with Intel GPU - Conservative Settings"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="LOW"
    else
        echo "macOS with Unknown GPU - Balanced Settings"
        RECOMMENDED="gl2"
        PERFORMANCE_TIER="MEDIUM"
    fi
fi

echo
echo "Recommended Renderer: $RECOMMENDED"
echo "Performance Tier: $PERFORMANCE_TIER"
echo "GPU Type: $GPU_TYPE"
if [ "$IS_INTEGRATED" = "YES" ]; then
    echo
    echo "*** INTEGRATED GRAPHICS DETECTED ***"
    echo "Optimizations applied for shared memory systems"
    echo "Conservative settings enabled for better performance"
fi
echo
echo "================================================================"
echo "Available Renderer Options:"
echo "================================================================"
echo "1. GL2 (Recommended for most systems) - $RECOMMENDED"
echo "2. GL (Legacy OpenGL, better macOS compatibility)"
echo "3. GLES2 (Mobile/embedded systems)"
echo "4. Auto-detect (let Ren'Py choose)"
echo "5. Launch with graphics configuration menu"
echo "6. Performance benchmark mode"
echo

read -p "Select renderer option (1-6): " choice

case $choice in
    1)
        echo
        echo "Launching with GL2 renderer (OpenGL 2.0+)..."
        echo "Optimized for: RTX 20/30/40 series, modern AMD/Intel"
        export RENPY_RENDERER=gl2
        ;;
    2)
        echo
        echo "Launching with GL renderer (Legacy OpenGL)..."
        echo "Best for: macOS compatibility, older systems"
        export RENPY_RENDERER=gl
        ;;
    3)
        echo
        echo "Launching with GLES2 renderer (OpenGL ES)..."
        echo "Best for: Mobile devices, embedded systems"
        export RENPY_RENDERER=gles2
        ;;
    4)
        echo
        echo "Launching with auto-detection..."
        echo "Ren'Py will automatically choose the best renderer"
        ;;
    5)
        echo
        echo "Launching with graphics configuration menu..."
        echo "Access the universal graphics test from the main menu"
        ;;
    6)
        echo
        echo "Launching in performance benchmark mode..."
        echo "Monitor performance with the in-game tools"
        ;;
    *)
        echo
        echo "Invalid choice. Using recommended renderer: $RECOMMENDED"
        export RENPY_RENDERER=$RECOMMENDED
        ;;
esac

echo
echo "Starting game..."
echo "Press Shift+G during gameplay to change renderer"
echo

# Launch the game (adjust path as needed)
if [ -f "./netcode_the_protogen_and_more" ]; then
    ./netcode_the_protogen_and_more
elif [ -f "./netcode the protogen and more" ]; then
    "./netcode the protogen and more"
elif [ -f "./game.py" ]; then
    python game.py
else
    echo "Game executable not found. Please run from the game directory."
    echo "Or launch the game manually and press Shift+G to change renderer."
fi

echo
echo "Game session ended."
read -p "Press Enter to exit..."
