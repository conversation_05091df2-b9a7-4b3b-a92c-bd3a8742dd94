#!/usr/bin/env python3
"""
Anti-Cheat System Setup Script
Configures the hardcore anti-cheat system for production deployment
"""

import os
import json
import hashlib
import secrets
import sys

def generate_api_key():
    """Generate secure API key for server communication"""
    return secrets.token_urlsafe(32)

def generate_encryption_key():
    """Generate encryption key for sensitive data"""
    return secrets.token_hex(32)

def create_config_file():
    """Create anti-cheat configuration file"""
    config = {
        "server_config": {
            "api_url": "https://your-anticheat-server.com/api",
            "api_key": generate_api_key(),
            "encryption_key": generate_encryption_key(),
            "timeout": 10,
            "retry_attempts": 3
        },
        "protection_levels": {
            "memory_protection": True,
            "kernel_checks": True,
            "process_monitoring": True,
            "file_integrity": True,
            "behavioral_analysis": True,
            "network_validation": True
        },
        "thresholds": {
            "violation_limit": 3,
            "ban_duration_days": 30,
            "behavioral_score_minimum": 0,
            "check_interval_seconds": 1.0
        },
        "monitoring": {
            "log_violations": True,
            "log_level": "INFO",
            "analytics_enabled": True,
            "performance_monitoring": True
        }
    }
    
    with open("anticheat_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Configuration file created: anticheat_config.json")
    return config

def verify_dependencies():
    """Verify required dependencies are available"""
    required_modules = [
        "hashlib", "threading", "time", "json", "ssl", "urllib"
    ]
    
    optional_modules = {
        "psutil": "Enhanced process monitoring",
        "cryptography": "Advanced encryption features",
        "requests": "Improved HTTP handling"
    }
    
    print("🔍 Checking dependencies...")
    
    missing_required = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_required.append(module)
            print(f"  ❌ {module} (REQUIRED)")
    
    if missing_required:
        print(f"\n❌ Missing required modules: {', '.join(missing_required)}")
        return False
    
    print("\n📦 Optional modules:")
    for module, description in optional_modules.items():
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
        except ImportError:
            print(f"  ⚠️  {module} - {description} (optional)")
    
    return True

def setup_file_integrity():
    """Setup file integrity checking"""
    print("\n🔒 Setting up file integrity checking...")
    
    critical_files = [
        "game/script.rpy",
        "game/options.rpy",
        "game/screens.rpy",
        "game/anticheat_core.rpy",
        "game/anticheat_kernel.rpy",
        "game/anticheat_network.rpy",
        "game/anticheat_ui.rpy"
    ]
    
    file_hashes = {}
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                content = f.read()
                file_hash = hashlib.sha256(content).hexdigest()
                file_hashes[file_path] = file_hash
                print(f"  ✅ {file_path}: {file_hash[:16]}...")
        else:
            print(f"  ⚠️  {file_path}: File not found")
    
    # Save hashes to file
    with open("file_integrity.json", "w") as f:
        json.dump(file_hashes, f, indent=2)
    
    print(f"📁 File integrity database created with {len(file_hashes)} files")

def create_server_setup_guide():
    """Create server setup guide"""
    guide = """
# Anti-Cheat Server Setup Guide

## Requirements
- Python 3.8+ or Node.js 14+
- PostgreSQL or MongoDB database
- SSL certificate for HTTPS
- Redis for session management

## API Endpoints Required

### Session Management
- POST /api/session/create
- POST /api/session/heartbeat
- POST /api/session/end

### Violation Reporting
- POST /api/violation/report
- GET /api/violation/status

### Game Validation
- POST /api/game/validate
- GET /api/game/progress

## Database Schema

### Sessions Table
- session_id (UUID, Primary Key)
- hardware_id (String)
- game_version (String)
- created_at (Timestamp)
- last_heartbeat (Timestamp)
- status (Enum: active, expired, banned)

### Violations Table
- violation_id (UUID, Primary Key)
- session_id (UUID, Foreign Key)
- violation_type (String)
- details (JSON)
- severity (Enum: low, medium, high, critical)
- timestamp (Timestamp)

### Bans Table
- ban_id (UUID, Primary Key)
- hardware_id (String)
- reason (String)
- duration (Integer, seconds)
- created_at (Timestamp)
- expires_at (Timestamp)

## Security Configuration
- Enable HTTPS only
- Implement rate limiting
- Use HMAC request signing
- Enable CORS for game domain only
- Implement IP whitelisting if needed

## Monitoring
- Set up violation alerts
- Monitor false positive rates
- Track ban effectiveness
- Performance monitoring

## Example Server Response Format
```json
{
    "success": true,
    "data": {
        "session_token": "abc123...",
        "expires_in": 3600
    },
    "error": null,
    "error_code": null
}
```
"""
    
    with open("SERVER_SETUP.md", "w") as f:
        f.write(guide)
    
    print("📋 Server setup guide created: SERVER_SETUP.md")

def run_security_test():
    """Run basic security tests"""
    print("\n🧪 Running security tests...")
    
    tests = [
        ("File permissions", lambda: os.access("game/anticheat_core.rpy", os.R_OK)),
        ("Configuration security", lambda: os.path.exists("anticheat_config.json")),
        ("Integrity database", lambda: os.path.exists("file_integrity.json")),
        ("Python version", lambda: sys.version_info >= (3, 7)),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"  ✅ {test_name}")
                passed += 1
            else:
                print(f"  ❌ {test_name}")
        except Exception as e:
            print(f"  ❌ {test_name}: {str(e)}")
    
    print(f"\n📊 Security tests: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All security tests passed!")
        return True
    else:
        print("⚠️  Some security tests failed. Please review the issues above.")
        return False

def main():
    """Main setup function"""
    print("🛡️  Anti-Cheat System Setup")
    print("=" * 50)
    
    # Check dependencies
    if not verify_dependencies():
        print("\n❌ Setup failed due to missing dependencies")
        return False
    
    # Create configuration
    config = create_config_file()
    
    # Setup file integrity
    setup_file_integrity()
    
    # Create server guide
    create_server_setup_guide()
    
    # Run security tests
    if run_security_test():
        print("\n🎉 Anti-cheat system setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Review anticheat_config.json and update server URLs")
        print("2. Set up your anti-cheat server using SERVER_SETUP.md")
        print("3. Test the system in developer mode")
        print("4. Deploy to production")
        print("\n⚠️  Important: Keep your API keys secure!")
        return True
    else:
        print("\n❌ Setup completed with warnings. Please review the issues above.")
        return False

if __name__ == "__main__":
    main()
