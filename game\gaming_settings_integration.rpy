## Gaming Settings Integration
## Integrates the gaming settings from the options directory

init python:
    import os
    
    # Gaming settings state
    gaming_settings = {
        'resolution': 'auto',
        'window_mode': 'windowed',
        'vsync': True,
        'fps_limit': 60,
        'graphics_quality': 'high',
        'anti_aliasing': True,
        'texture_quality': 'high',
        'shadow_quality': 'medium',
        'effects_quality': 'high',
        'audio_master': 1.0,
        'audio_music': 1.0,
        'audio_sfx': 1.0,
        'audio_voice': 1.0,
        'controller_enabled': False,
        'controller_vibration': True,
        'auto_save': True,
        'quick_save_slots': 10,
        'detected_cpu': 'Unknown CPU',
        'detected_gpu': 'Unknown GPU',
        'detected_ram': 'Unknown RAM',
        'detected_monitor': 'Unknown Monitor'
    }
    
    def apply_graphics_settings():
        """Apply graphics settings"""
        try:
            # Apply resolution
            if gaming_settings['resolution'] != 'auto':
                width, height = map(int, gaming_settings['resolution'].split('x'))
                renpy.set_physical_size((width, height))
            
            # Apply window mode
            if gaming_settings['window_mode'] == 'fullscreen':
                _preferences.fullscreen = True
            else:
                _preferences.fullscreen = False
            
            # Apply other settings
            _preferences.gl_framerate = gaming_settings['fps_limit']
            
            renpy.notify("Graphics settings applied")
            
        except Exception as e:
            renpy.notify(f"Error applying settings: {str(e)}")
            renpy.log(f"Graphics settings error: {str(e)}")
    
    def detect_hardware():
        """Detect system hardware"""
        try:
            import platform
            
            # Basic system detection
            gaming_settings['detected_cpu'] = platform.processor() or "Unknown CPU"
            
            # Try to detect more hardware info
            try:
                import psutil
                ram_gb = round(psutil.virtual_memory().total / (1024**3))
                gaming_settings['detected_ram'] = f"{ram_gb} GB"
            except:
                gaming_settings['detected_ram'] = "Unknown RAM"
            
            renpy.log("Hardware detection completed")
            
        except Exception as e:
            renpy.log(f"Hardware detection error: {str(e)}")

# Gaming settings screen (simplified and safe)
screen gaming_settings():
    tag menu

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600

        vbox:
            spacing 20

            text "Gaming Settings" size 30 xalign 0.5

            # Graphics Settings Section
            frame:
                xfill True

                vbox:
                    spacing 15

                    text "Graphics Settings" size 20 color "#4CAF50"

                    hbox:
                        spacing 30

                        vbox:
                            spacing 10

                            text "Resolution: [gaming_settings['resolution']]" size 16
                            text "Window Mode: [gaming_settings['window_mode'].title()]" size 16
                            text "Graphics Quality: [gaming_settings['graphics_quality'].title()]" size 16
                            text "FPS Limit: [gaming_settings['fps_limit']]" size 16

                        vbox:
                            spacing 5

                            textbutton "Toggle VSync" action ToggleDict(gaming_settings, 'vsync')
                            textbutton "Toggle Anti-Aliasing" action ToggleDict(gaming_settings, 'anti_aliasing')
                            textbutton "Apply Graphics" action Function(apply_graphics_settings)

            # Audio Settings Section
            frame:
                xfill True

                vbox:
                    spacing 15

                    text "Audio Settings" size 20 color "#2196F3"

                    hbox:
                        spacing 30

                        vbox:
                            spacing 10

                            # Simple volume display without bars to avoid FieldValue issues
                            text "Master Volume: [gaming_settings['audio_master']:.1f]" size 16
                            text "Music Volume: [gaming_settings['audio_music']:.1f]" size 16
                            text "SFX Volume: [gaming_settings['audio_sfx']:.1f]" size 16
                            text "Voice Volume: [gaming_settings['audio_voice']:.1f]" size 16

                        vbox:
                            spacing 5

                            textbutton "Master +" action SetDict(gaming_settings, 'audio_master', min(1.0, gaming_settings['audio_master'] + 0.1))
                            textbutton "Master -" action SetDict(gaming_settings, 'audio_master', max(0.0, gaming_settings['audio_master'] - 0.1))
                            textbutton "Music +" action SetDict(gaming_settings, 'audio_music', min(1.0, gaming_settings['audio_music'] + 0.1))
                            textbutton "Music -" action SetDict(gaming_settings, 'audio_music', max(0.0, gaming_settings['audio_music'] - 0.1))

            # Advanced Settings Section
            frame:
                xfill True

                vbox:
                    spacing 15

                    text "Advanced Settings" size 20 color "#FF5722"

                    hbox:
                        spacing 30

                        vbox:
                            spacing 5

                            text "VSync: [gaming_settings['vsync']]" size 16
                            text "Anti-Aliasing: [gaming_settings['anti_aliasing']]" size 16
                            text "Controller: [gaming_settings['controller_enabled']]" size 16
                            text "Auto-Save: [gaming_settings['auto_save']]" size 16

                        vbox:
                            spacing 5

                            textbutton "Toggle VSync" action ToggleDict(gaming_settings, 'vsync')
                            textbutton "Toggle Anti-Aliasing" action ToggleDict(gaming_settings, 'anti_aliasing')
                            textbutton "Toggle Controller" action ToggleDict(gaming_settings, 'controller_enabled')
                            textbutton "Toggle Auto-Save" action ToggleDict(gaming_settings, 'auto_save')

            # System Information Section
            frame:
                xfill True

                vbox:
                    spacing 15

                    text "System Information" size 20 color "#9C27B0"

                    hbox:
                        spacing 30

                        vbox:
                            spacing 5
                            text "CPU: [gaming_settings['detected_cpu']]" size 14
                            text "GPU: [gaming_settings['detected_gpu']]" size 14

                        vbox:
                            spacing 5
                            text "RAM: [gaming_settings['detected_ram']]" size 14
                            text "Monitor: [gaming_settings['detected_monitor']]" size 14

                        vbox:
                            spacing 5
                            textbutton "Detect Hardware" action Function(detect_hardware)
                            textbutton "Reset Settings" action Function(reset_gaming_settings)

            # Navigation buttons
            hbox:
                spacing 20
                xalign 0.5

                textbutton "Return" action Return()
                textbutton "Advanced Options" action Function(open_advanced_gaming_settings)

# Advanced gaming settings (if the full options file is available)
init python:
    def open_advanced_gaming_settings():
        """Open advanced gaming settings if available"""
        try:
            if os.path.exists("options/gaming_settings.rpy"):
                renpy.notify("Advanced gaming settings found in options directory")
                renpy.notify("Full functionality available in separate options system")
            else:
                renpy.notify("Advanced settings not available")
        except Exception as e:
            renpy.notify(f"Error: {str(e)}")
    
    def reset_gaming_settings():
        """Reset gaming settings to defaults"""
        try:
            gaming_settings.update({
                'resolution': 'auto',
                'window_mode': 'windowed',
                'vsync': True,
                'fps_limit': 60,
                'graphics_quality': 'high',
                'anti_aliasing': True,
                'texture_quality': 'high',
                'shadow_quality': 'medium',
                'effects_quality': 'high',
                'audio_master': 1.0,
                'audio_music': 1.0,
                'audio_sfx': 1.0,
                'audio_voice': 1.0,
                'controller_enabled': False,
                'controller_vibration': True,
                'auto_save': True,
                'quick_save_slots': 10
            })
            renpy.notify("Gaming settings reset to defaults")
        except Exception as e:
            renpy.notify(f"Reset error: {str(e)}")

# Auto-detect hardware on startup
init python:
    try:
        detect_hardware()
    except Exception as e:
        renpy.log(f"Auto hardware detection failed: {str(e)}")

# Check if advanced gaming settings are available
init python:
    gaming_settings_advanced_available = os.path.exists("options/gaming_settings.rpy")
    if gaming_settings_advanced_available:
        renpy.log("Advanced gaming settings found in options directory")
    else:
        renpy.log("Using simplified gaming settings")

# Default values for compatibility
default gaming_settings_available = True
