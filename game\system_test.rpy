# System Test - Verify all systems are working correctly
# This file tests the conflict resolution and integration systems

# Simple test label to verify the system works
label system_test_main:
    "🧪 System Test"
    "Testing all game systems for conflicts and functionality..."
    ""
    
    # Test game context system
    python:
        test_context = get_game_context()
        if test_context:
            renpy.say(None, f"✅ Game context system working: {test_context}")
        else:
            renpy.say(None, "✅ Game context system working: No context set")
    
    # Test conflict resolution
    "Testing conflict resolution system..."
    
    menu:
        "Which system would you like to test?"
        
        "🌐 Test Universal Router":
            jump safe_game_launcher

# Quick conflict test
label quick_conflict_test:
    "🔍 Quick Conflict Test"
    
    # Test each conflicting label
    $ set_game_context("lumetric")
    "Testing Lumetric context..."
    call whisker_and_bean_scene from _call_whisker_and_bean_scene_3
    
    $ set_game_context("netcode")
    "Testing Netcode context..."
    call whisker_and_bean_scene from _call_whisker_and_bean_scene_4
    
    $ set_game_context(None)
    "Testing no context (should show selection)..."
    call whisker_and_bean_scene from _call_whisker_and_bean_scene_5
    
    "✅ All conflict tests passed!"
    return
