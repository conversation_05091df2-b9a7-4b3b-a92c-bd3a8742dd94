## Terms of Service Compliance Handler
## Handles platform-specific terms of service and compliance requirements

init -70 python:
    import json
    import os
    from datetime import datetime

    # Terms compliance state
    compliance_state = {
        'platform_terms': {},
        'compliance_status': {},
        'violations': [],
        'warnings': [],
        'last_check': None,
        'auto_compliance': True
    }

    # Terms compliance configuration
    TERMS_CONFIG = {
        'enabled': True,
        'auto_check': True,
        'enforce_compliance': True,
        'show_warnings': True,
        'log_violations': True,
        'strict_mode': False,
        'grace_period': 86400,  # 24 hours
        'check_interval': 3600  # 1 hour
    }

    class TermsComplianceHandler:
        def __init__(self):
            self.platform_requirements = {}
            self.compliance_rules = {}
            self.violation_handlers = {}
            self.load_platform_requirements()
            
        def load_platform_requirements(self):
            """Load platform-specific terms and requirements"""
            try:
                # Steam requirements
                self.platform_requirements['steam'] = {
                    'content_rating': {
                        'required': True,
                        'valid_ratings': ['E', 'E10+', 'T', 'M', 'AO'],
                        'default': 'T'
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 13
                    },
                    'drm_compliance': {
                        'steam_drm_required': False,
                        'third_party_drm_allowed': True
                    },
                    'content_restrictions': {
                        'no_hate_speech': True,
                        'no_illegal_content': True,
                        'no_malware': True,
                        'respect_copyright': True
                    },
                    'technical_requirements': {
                        'steam_api_integration': False,
                        'achievement_system': False,
                        'cloud_saves': False
                    }
                }
                
                # Epic Games Store requirements
                self.platform_requirements['epic'] = {
                    'content_rating': {
                        'required': True,
                        'valid_ratings': ['E', 'E10+', 'T', 'M'],
                        'default': 'T'
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 13
                    },
                    'drm_compliance': {
                        'epic_drm_allowed': True,
                        'third_party_drm_restricted': True
                    },
                    'content_restrictions': {
                        'no_hate_speech': True,
                        'no_illegal_content': True,
                        'family_friendly_preferred': True
                    },
                    'technical_requirements': {
                        'eos_integration_preferred': True,
                        'epic_achievements': False
                    }
                }
                
                # itch.io requirements
                self.platform_requirements['itch'] = {
                    'content_rating': {
                        'required': False,
                        'self_rated': True
                    },
                    'age_verification': {
                        'required_for_adult': True,
                        'minimum_age': 18
                    },
                    'content_restrictions': {
                        'adult_content_allowed': True,
                        'proper_tagging_required': True,
                        'no_illegal_content': True
                    },
                    'monetization': {
                        'pay_what_you_want': True,
                        'free_games_allowed': True,
                        'donations_allowed': True
                    }
                }
                
                # GameJolt requirements
                self.platform_requirements['gamejolt'] = {
                    'content_rating': {
                        'required': True,
                        'community_rated': True
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 13
                    },
                    'content_restrictions': {
                        'community_guidelines': True,
                        'no_spam': True,
                        'original_content_preferred': True
                    },
                    'social_features': {
                        'trophy_system': True,
                        'user_comments': True,
                        'rating_system': True
                    }
                }
                
                # Amazon Games requirements
                self.platform_requirements['amazon'] = {
                    'content_rating': {
                        'required': True,
                        'esrb_required': True
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 13
                    },
                    'technical_requirements': {
                        'aws_integration_preferred': True,
                        'gamelift_compatible': False
                    },
                    'content_restrictions': {
                        'family_friendly_preferred': True,
                        'no_controversial_content': True
                    }
                }
                
                # Google Play Store requirements
                self.platform_requirements['googleplay'] = {
                    'content_rating': {
                        'required': True,
                        'iarc_rating': True
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 13
                    },
                    'technical_requirements': {
                        'target_api_level': 30,
                        'permissions_justified': True,
                        'privacy_policy_required': True
                    },
                    'content_restrictions': {
                        'no_hate_speech': True,
                        'no_violence_against_real_entities': True,
                        'appropriate_metadata': True
                    },
                    'monetization': {
                        'google_play_billing': True,
                        'subscription_rules': True
                    }
                }
                
                # Apple App Store requirements
                self.platform_requirements['appstore'] = {
                    'content_rating': {
                        'required': True,
                        'apple_rating_system': True
                    },
                    'age_verification': {
                        'required_for_mature': True,
                        'minimum_age': 17
                    },
                    'technical_requirements': {
                        'ios_guidelines': True,
                        'app_store_review': True,
                        'privacy_policy_required': True
                    },
                    'content_restrictions': {
                        'family_friendly_required': True,
                        'no_adult_content': True,
                        'appropriate_for_platform': True
                    },
                    'monetization': {
                        'apple_iap_required': True,
                        'subscription_guidelines': True
                    }
                }
                
                renpy.log("Platform requirements loaded successfully")
                
            except Exception as e:
                renpy.log(f"Error loading platform requirements: {str(e)}")
        
        def check_platform_compliance(self, platform_name=None):
            """Check compliance with platform-specific terms"""
            try:
                if not platform_name:
                    platform_name = platform_detector.get_primary_platform()
                
                if platform_name not in self.platform_requirements:
                    renpy.log(f"No compliance requirements found for platform: {platform_name}")
                    return True
                
                requirements = self.platform_requirements[platform_name]
                compliance_results = {}
                violations = []
                warnings = []
                
                # Check content rating compliance
                if 'content_rating' in requirements:
                    rating_result = self._check_content_rating(requirements['content_rating'])
                    compliance_results['content_rating'] = rating_result
                    if not rating_result['compliant']:
                        violations.extend(rating_result.get('violations', []))
                
                # Check age verification compliance
                if 'age_verification' in requirements:
                    age_result = self._check_age_verification(requirements['age_verification'])
                    compliance_results['age_verification'] = age_result
                    if not age_result['compliant']:
                        warnings.extend(age_result.get('warnings', []))
                
                # Check DRM compliance
                if 'drm_compliance' in requirements:
                    drm_result = self._check_drm_compliance(requirements['drm_compliance'])
                    compliance_results['drm_compliance'] = drm_result
                    if not drm_result['compliant']:
                        violations.extend(drm_result.get('violations', []))
                
                # Check content restrictions
                if 'content_restrictions' in requirements:
                    content_result = self._check_content_restrictions(requirements['content_restrictions'])
                    compliance_results['content_restrictions'] = content_result
                    if not content_result['compliant']:
                        violations.extend(content_result.get('violations', []))
                
                # Check technical requirements
                if 'technical_requirements' in requirements:
                    tech_result = self._check_technical_requirements(requirements['technical_requirements'])
                    compliance_results['technical_requirements'] = tech_result
                    if not tech_result['compliant']:
                        warnings.extend(tech_result.get('warnings', []))
                
                # Update compliance state
                compliance_state['platform_terms'][platform_name] = requirements
                compliance_state['compliance_status'][platform_name] = compliance_results
                compliance_state['violations'].extend(violations)
                compliance_state['warnings'].extend(warnings)
                compliance_state['last_check'] = datetime.now().isoformat()
                
                # Overall compliance status
                overall_compliant = len(violations) == 0
                
                if violations:
                    renpy.log(f"Platform compliance violations found for {platform_name}: {len(violations)}")
                    for violation in violations:
                        renpy.log(f"  - {violation}")
                
                if warnings:
                    renpy.log(f"Platform compliance warnings for {platform_name}: {len(warnings)}")
                    for warning in warnings:
                        renpy.log(f"  - {warning}")
                
                return overall_compliant
                
            except Exception as e:
                renpy.log(f"Error checking platform compliance: {str(e)}")
                return False
        
        def _check_content_rating(self, rating_requirements):
            """Check content rating compliance"""
            try:
                result = {'compliant': True, 'violations': [], 'warnings': []}
                
                # Check if rating is required
                if rating_requirements.get('required', False):
                    # For this example, we'll assume the game has a rating
                    # In a real implementation, you'd check your game's actual rating
                    game_rating = getattr(config, 'content_rating', None)
                    
                    if not game_rating:
                        result['compliant'] = False
                        result['violations'].append("Content rating is required but not set")
                    elif rating_requirements.get('valid_ratings'):
                        if game_rating not in rating_requirements['valid_ratings']:
                            result['compliant'] = False
                            result['violations'].append(f"Invalid content rating: {game_rating}")
                
                return result
                
            except Exception as e:
                renpy.log(f"Content rating check error: {str(e)}")
                return {'compliant': False, 'violations': [str(e)]}
        
        def _check_age_verification(self, age_requirements):
            """Check age verification compliance"""
            try:
                result = {'compliant': True, 'violations': [], 'warnings': []}
                
                # Check if age verification is implemented
                if age_requirements.get('required_for_mature', False):
                    # Check if game has mature content
                    has_mature_content = getattr(config, 'has_mature_content', False)
                    has_age_verification = getattr(config, 'age_verification_enabled', False)
                    
                    if has_mature_content and not has_age_verification:
                        result['warnings'].append("Age verification recommended for mature content")
                
                return result
                
            except Exception as e:
                renpy.log(f"Age verification check error: {str(e)}")
                return {'compliant': True, 'warnings': [str(e)]}
        
        def _check_drm_compliance(self, drm_requirements):
            """Check DRM compliance"""
            try:
                result = {'compliant': True, 'violations': [], 'warnings': []}
                
                # Check DRM requirements
                # This would integrate with your encryption system
                encryption_enabled = getattr(config, 'encryption_enabled', False)
                
                if drm_requirements.get('steam_drm_required', False):
                    # Check for Steam DRM implementation
                    if not encryption_enabled:
                        result['warnings'].append("Steam DRM integration recommended")
                
                return result
                
            except Exception as e:
                renpy.log(f"DRM compliance check error: {str(e)}")
                return {'compliant': True, 'warnings': [str(e)]}
        
        def _check_content_restrictions(self, content_requirements):
            """Check content restrictions compliance"""
            try:
                result = {'compliant': True, 'violations': [], 'warnings': []}
                
                # Basic content checks
                if content_requirements.get('no_hate_speech', False):
                    # In a real implementation, you'd scan content for hate speech
                    result['warnings'].append("Ensure content does not contain hate speech")
                
                if content_requirements.get('no_illegal_content', False):
                    result['warnings'].append("Ensure content complies with local laws")
                
                return result
                
            except Exception as e:
                renpy.log(f"Content restrictions check error: {str(e)}")
                return {'compliant': True, 'warnings': [str(e)]}
        
        def _check_technical_requirements(self, tech_requirements):
            """Check technical requirements compliance"""
            try:
                result = {'compliant': True, 'violations': [], 'warnings': []}
                
                # Check technical integrations
                if tech_requirements.get('steam_api_integration', False):
                    # Check if Steam API is integrated
                    steam_integrated = platform_integrations.is_feature_available('achievements', 'steam')
                    if not steam_integrated:
                        result['warnings'].append("Steam API integration recommended")
                
                return result
                
            except Exception as e:
                renpy.log(f"Technical requirements check error: {str(e)}")
                return {'compliant': True, 'warnings': [str(e)]}
        
        def get_compliance_status(self, platform_name=None):
            """Get compliance status for a platform"""
            if platform_name:
                return compliance_state['compliance_status'].get(platform_name, {})
            return compliance_state['compliance_status']
        
        def get_violations(self):
            """Get all compliance violations"""
            return compliance_state['violations']
        
        def get_warnings(self):
            """Get all compliance warnings"""
            return compliance_state['warnings']
        
        def is_compliant(self, platform_name=None):
            """Check if platform is compliant"""
            if platform_name:
                return len([v for v in compliance_state['violations'] if platform_name in str(v)]) == 0
            return len(compliance_state['violations']) == 0

    # Initialize terms compliance handler
    terms_compliance = TermsComplianceHandler()

# Auto-check compliance on startup
init python:
    if TERMS_CONFIG.get('enabled', True) and TERMS_CONFIG.get('auto_check', True):
        try:
            terms_compliance.check_platform_compliance()
        except Exception as e:
            renpy.log(f"Terms compliance auto-check failed: {str(e)}")

# Terms compliance functions for use in game
define terms_compliance_enabled = TERMS_CONFIG.get('enabled', True)
define platform_compliant = terms_compliance.is_compliant() if terms_compliance else True
define compliance_violations = len(compliance_state.get('violations', []))
define compliance_warnings = len(compliance_state.get('warnings', []))
