## Engine Communication UI
## User interface for monitoring Ren'Py engine detection and communication

# Engine status display screen
screen engine_communication_status():
    
    frame:
        xpos 20
        ypos 400
        xsize 350
        ysize 250
        background "#000000dd"
        
        vbox:
            spacing 10
            
            # Header
            hbox:
                spacing 10
                text "🔗" size 20 color "#00ff00"
                text "ENGINE COMMUNICATION" size 12 color "#00ff00" bold True
            
            # SDK Detection Status
            frame:
                background "#1a1a1a"
                padding (10, 8)
                
                vbox:
                    spacing 5
                    
                    text "SDK Detection:" size 12 color "#ffaa00"
                    
                    if engine_detector.get('sdk_detected', False):
                        text "✓ SDK Found" size 10 color "#00ff00"
                        text "Version: {}".format(engine_detector.get('sdk_version_detected', 'Unknown')) size 9 color "#cccccc"
                        text "Path: {}".format(
                            engine_detector.get('sdk_path', 'Unknown')[-30:] + "..." 
                            if len(engine_detector.get('sdk_path', '')) > 30 
                            else engine_detector.get('sdk_path', 'Unknown')
                        ) size 8 color "#888888"
                    else:
                        text "❌ SDK Not Found" size 10 color "#ff6666"
                        text "Searching..." size 9 color "#ffaa00"
            
            # Communication Status
            frame:
                background "#1a1a1a"
                padding (10, 8)
                
                vbox:
                    spacing 5
                    
                    text "Communication:" size 12 color "#ffaa00"
                    
                    if engine_detector.get('engine_communication_active', False):
                        text "✓ Active" size 10 color "#00ff00"
                    else:
                        text "❌ Inactive" size 10 color "#ff6666"
                    
                    if engine_detector.get('encryption_bridge_active', False):
                        text "✓ Encryption Bridge" size 9 color "#00ff00"
                    else:
                        text "○ Encryption Bridge" size 9 color "#888888"
                    
                    if engine_detector.get('all_files_through_anticheat', False):
                        text "✓ File Loading Active" size 9 color "#00ff00"
                    else:
                        text "○ File Loading Inactive" size 9 color "#888888"
            
            # Quick actions
            hbox:
                spacing 8
                
                textbutton "Rescan" action Function(rescan_engine) text_size 9 xsize 60
                textbutton "Details" action Call("engine_details") text_size 9 xsize 60

# Detailed engine information screen
screen engine_details_screen():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 600
        
        vbox:
            spacing 20
            
            text "🔗 Engine Communication Details" size 20 xalign 0.5 color "#00ff00"
            
            # SDK Information
            frame:
                background "#2e2e2e"
                padding (20, 15)
                xfill True
                
                vbox:
                    spacing 15
                    
                    text "Ren'Py SDK Information" size 16 color "#ffaa00"
                    
                    grid 2 1:
                        spacing 20
                        
                        vbox:
                            spacing 8
                            
                            text "Detection Status:" size 12 color "#ffffff"
                            if engine_detector.get('sdk_detected', False):
                                text "✓ SDK Detected and Validated" size 11 color "#00ff00"
                            else:
                                text "❌ SDK Not Found" size 11 color "#ff6666"
                            
                            text "SDK Path:" size 12 color "#ffffff"
                            text engine_detector.get('sdk_path', 'Not detected') size 10 color "#cccccc"
                            
                            text "SDK Version:" size 12 color "#ffffff"
                            text engine_detector.get('sdk_version_detected', 'Unknown') size 11 color "#cccccc"
                        
                        vbox:
                            spacing 8
                            
                            text "Storage Scan Results:" size 12 color "#ffffff"

                            viewport:
                                scrollbars "vertical"
                                mousewheel True
                                xsize 300
                                ysize 100

                                vbox:
                                    spacing 3

                                    text "Drives Scanned: {}".format(len([p for p in engine_detector.get('sdk_search_paths', []) if len(p) <= 4])) size 9 color "#ffaa00"
                                    text "Total Locations: {}".format(len(engine_detector.get('sdk_search_paths', []))) size 9 color "#ffaa00"

                                    null height 5

                                    for search_path in engine_detector.get('sdk_search_paths', [])[:10]:  # Show first 10
                                        if os.path.exists(search_path):
                                            text "✓ {}".format(search_path) size 8 color "#00ff00"
                                        else:
                                            text "❌ {}".format(search_path) size 8 color "#888888"

                                    if len(engine_detector.get('sdk_search_paths', [])) > 10:
                                        text "... and {} more locations".format(len(engine_detector.get('sdk_search_paths', [])) - 10) size 8 color "#888888"
            
            # Communication Status
            frame:
                background "#2e2e2e"
                padding (20, 15)
                xfill True
                
                vbox:
                    spacing 15
                    
                    text "Communication Status" size 16 color "#ffaa00"
                    
                    grid 2 2:
                        spacing 15
                        
                        vbox:
                            spacing 5
                            text "Engine Communication:" size 12 color "#ffffff"
                            if engine_detector.get('engine_communication_active', False):
                                text "✓ Active" size 11 color "#00ff00"
                            else:
                                text "❌ Inactive" size 11 color "#ff6666"
                        
                        vbox:
                            spacing 5
                            text "File Loading:" size 12 color "#ffffff"
                            if engine_detector.get('all_files_through_anticheat', False):
                                text "✓ Through Anti-Cheat" size 11 color "#00ff00"
                            else:
                                text "○ Standard Loading" size 11 color "#888888"
                        
                        vbox:
                            spacing 5
                            text "Encryption Bridge:" size 12 color "#ffffff"
                            if engine_detector.get('encryption_bridge_active', False):
                                text "✓ Active" size 11 color "#00ff00"
                            else:
                                text "❌ Inactive" size 11 color "#ff6666"
                        
                        vbox:
                            spacing 5
                            text "File System Hook:" size 12 color "#ffffff"
                            if engine_detector.get('file_system_hooked', False):
                                text "✓ Hooked" size 11 color "#00ff00"
                            else:
                                text "○ Not Hooked" size 11 color "#888888"
            
            # File Loading Statistics
            if hasattr(renpy.store, 'secure_file_loader'):
                frame:
                    background "#2e2e2e"
                    padding (20, 15)
                    xfill True
                    
                    vbox:
                        spacing 10
                        
                        text "File Loading Statistics" size 16 color "#ffaa00"
                        
                        hbox:
                            spacing 30
                            
                            vbox:
                                spacing 5
                                text "Loaded Files: {}".format(len(secure_file_loader.get('loaded_files', {}))) size 12 color "#ffffff"
                                text "Cached Files: {}".format(len(secure_file_loader.get('file_cache', {}))) size 12 color "#ffffff"
                                text "Access Log Entries: {}".format(len(secure_file_loader.get('access_log', []))) size 12 color "#ffffff"
                            
                            vbox:
                                spacing 5
                                text "Encryption Requests: {}".format(len(secure_file_loader.get('encryption_requests', []))) size 12 color "#ffffff"
                                text "Decryption Queue: {}".format(len(secure_file_loader.get('decryption_queue', []))) size 12 color "#ffffff"
                                text "Integrity Checks: {}".format(len(secure_file_loader.get('file_integrity_map', {}))) size 12 color "#ffffff"
            
            # Control buttons
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "🔄 Rescan SDK" action Function(rescan_engine) text_size 14
                textbutton "💾 Drive Scan Details" action Call("drive_scan_details") text_size 14
                textbutton "🔗 Test Communication" action Function(test_engine_communication) text_size 14
                textbutton "🔐 Test Encryption" action Function(test_encryption_bridge) text_size 14
                textbutton "Close" action Return() text_size 14

# Engine communication test screen
screen communication_test_results():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 400
        
        vbox:
            spacing 20
            
            text "🧪 Communication Test Results" size 18 xalign 0.5 color "#00ff00"
            
            # Test results display
            frame:
                background "#1a1a1a"
                padding (15, 10)
                xfill True
                yfill True
                
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        for test_result in communication_test_results:
                            hbox:
                                spacing 10
                                
                                if test_result['status'] == 'pass':
                                    text "✓" size 14 color "#00ff00"
                                elif test_result['status'] == 'fail':
                                    text "❌" size 14 color "#ff6666"
                                else:
                                    text "⚠️" size 14 color "#ffaa00"
                                
                                vbox:
                                    spacing 3
                                    text test_result['test'] size 12 color "#ffffff"
                                    text test_result['message'] size 10 color "#cccccc"
            
            textbutton "Close" action Return() xalign 0.5

# Helper functions
init python:
    
    communication_test_results = []
    
    def rescan_engine():
        """Rescan for Ren'Py engine and SDK"""
        try:
            if hasattr(renpy.store, 'renpy_engine_detector'):
                renpy_engine_detector.detect_renpy_sdk_installation()
                renpy_engine_detector.setup_engine_communication()
                renpy.notify("🔄 Engine rescan completed")
            else:
                renpy.notify("❌ Engine detector not available")
        except Exception as e:
            renpy.log("Engine rescan failed: {}".format(str(e)))
            renpy.notify("❌ Rescan failed")
    
    def test_engine_communication():
        """Test engine communication functionality"""
        global communication_test_results
        communication_test_results = []
        
        try:
            # Test SDK detection
            if engine_detector.get('sdk_detected', False):
                communication_test_results.append({
                    'test': 'SDK Detection',
                    'status': 'pass',
                    'message': 'SDK found and validated'
                })
            else:
                communication_test_results.append({
                    'test': 'SDK Detection',
                    'status': 'fail',
                    'message': 'SDK not detected'
                })
            
            # Test communication channel
            if engine_detector.get('engine_communication_active', False):
                communication_test_results.append({
                    'test': 'Communication Channel',
                    'status': 'pass',
                    'message': 'Communication active'
                })
            else:
                communication_test_results.append({
                    'test': 'Communication Channel',
                    'status': 'fail',
                    'message': 'Communication not established'
                })
            
            # Test file loading
            if engine_detector.get('all_files_through_anticheat', False):
                communication_test_results.append({
                    'test': 'File Loading Hook',
                    'status': 'pass',
                    'message': 'Files loading through anti-cheat'
                })
            else:
                communication_test_results.append({
                    'test': 'File Loading Hook',
                    'status': 'warning',
                    'message': 'Standard file loading active'
                })
            
            # Test encryption bridge
            if engine_detector.get('encryption_bridge_active', False):
                communication_test_results.append({
                    'test': 'Encryption Bridge',
                    'status': 'pass',
                    'message': 'Encryption communication active'
                })
            else:
                communication_test_results.append({
                    'test': 'Encryption Bridge',
                    'status': 'warning',
                    'message': 'Encryption bridge not active'
                })
            
            renpy.call_screen("communication_test_results")
            
        except Exception as e:
            renpy.log("Communication test failed: {}".format(str(e)))
            renpy.notify("❌ Communication test failed")
    
    def test_encryption_bridge():
        """Test encryption bridge functionality"""
        try:
            if engine_detector.get('encryption_bridge_active', False):
                # Test file encryption/decryption
                test_file = "test_encryption.txt"
                
                if hasattr(renpy.store, 'renpy_engine_detector'):
                    # Test decryption request
                    result = renpy_engine_detector.request_decryption(test_file)
                    if result is not None:
                        renpy.notify("✓ Encryption bridge test passed")
                    else:
                        renpy.notify("⚠️ Encryption bridge test inconclusive")
                else:
                    renpy.notify("❌ Engine detector not available")
            else:
                renpy.notify("❌ Encryption bridge not active")
                
        except Exception as e:
            renpy.log("Encryption test failed: {}".format(str(e)))
            renpy.notify("❌ Encryption test failed")

# Drive scan details screen
screen drive_scan_details():

    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 700

        vbox:
            spacing 20

            text "💾 Comprehensive Drive Scan Results" size 20 xalign 0.5 color "#00ff00"

            # Drive summary
            frame:
                background "#2e2e2e"
                padding (20, 15)
                xfill True

                vbox:
                    spacing 15

                    text "Storage Device Summary" size 16 color "#ffaa00"

                    hbox:
                        spacing 30

                        vbox:
                            spacing 8
                            text "Total Drives Detected:" size 12 color "#ffffff"
                            text str(len([p for p in engine_detector.get('sdk_search_paths', []) if len(p) <= 4])) size 14 color "#00ff00" bold True

                        vbox:
                            spacing 8
                            text "Total Search Locations:" size 12 color "#ffffff"
                            text str(len(engine_detector.get('sdk_search_paths', []))) size 14 color "#00ff00" bold True

                        vbox:
                            spacing 8
                            text "External Drives:" size 12 color "#ffffff"
                            text str(len([p for p in engine_detector.get('sdk_search_paths', []) if 'removable' in p.lower()])) size 14 color "#ffaa00" bold True

            # Detailed scan results
            frame:
                background "#1a1a1a"
                padding (15, 10)
                xfill True
                yfill True

                vbox:
                    spacing 10

                    text "All Scanned Locations" size 16 color "#00aaff"

                    viewport:
                        scrollbars "vertical"
                        mousewheel True

                        vbox:
                            spacing 5

                            for search_path in engine_detector.get('sdk_search_paths', []):
                                hbox:
                                    spacing 10

                                    # Status indicator
                                    if os.path.exists(search_path):
                                        text "✓" size 12 color "#00ff00"
                                    else:
                                        text "❌" size 12 color "#ff6666"

                                    # Drive type indicator
                                    if len(search_path) <= 4:  # Drive root
                                        text "💾" size 12 color "#ffaa00"
                                    elif 'users' in search_path.lower():
                                        text "👤" size 12 color "#00aaff"
                                    elif any(x in search_path.lower() for x in ['program', 'software']):
                                        text "⚙️" size 12 color "#888888"
                                    elif any(x in search_path.lower() for x in ['downloads', 'desktop', 'documents']):
                                        text "📁" size 12 color "#ffffff"
                                    else:
                                        text "📂" size 12 color "#cccccc"

                                    # Path
                                    vbox:
                                        spacing 2
                                        text search_path size 10 color "#ffffff"

                                        # Additional info for drives
                                        if len(search_path) <= 4 and os.path.exists(search_path):
                                            try:
                                                import shutil
                                                total, used, free = shutil.disk_usage(search_path)
                                                free_gb = free // (1024**3)
                                                total_gb = total // (1024**3)
                                                text "Free: {} GB / {} GB".format(free_gb, total_gb) size 8 color "#888888"
                                            except:
                                                text "Drive accessible" size 8 color "#888888"

            # Control buttons
            hbox:
                spacing 20
                xalign 0.5

                textbutton "🔄 Rescan All Drives" action Function(rescan_all_drives) text_size 14
                textbutton "🔍 Deep Scan" action Function(perform_deep_scan) text_size 14
                textbutton "Close" action Return() text_size 14

# Labels for screen calls
label engine_details:
    call screen engine_details_screen
    return

label drive_scan_details:
    call screen drive_scan_details
    return

# Enhanced helper functions
init python:

    def rescan_all_drives():
        """Rescan all drives and storage devices"""
        try:
            if hasattr(renpy.store, 'renpy_engine_detector'):
                # Clear existing paths
                engine_detector['sdk_search_paths'] = []

                # Rediscover all storage locations
                renpy_engine_detector.discover_all_storage_locations()

                # Rescan for SDK
                renpy_engine_detector.detect_renpy_sdk_installation()

                renpy.notify("🔄 Complete drive rescan completed")
            else:
                renpy.notify("❌ Engine detector not available")
        except Exception as e:
            renpy.log("Drive rescan failed: {}".format(str(e)))
            renpy.notify("❌ Drive rescan failed")

    def perform_deep_scan():
        """Perform deep scan of all storage devices"""
        try:
            if hasattr(renpy.store, 'renpy_engine_detector'):
                # Enable deep scanning
                engine_detector['deep_scan_enabled'] = True

                # Perform comprehensive scan
                renpy_engine_detector.discover_all_storage_locations()
                renpy_engine_detector.detect_renpy_sdk_installation()

                renpy.notify("🔍 Deep scan completed - {} locations scanned".format(
                    len(engine_detector.get('sdk_search_paths', []))
                ))
            else:
                renpy.notify("❌ Engine detector not available")
        except Exception as e:
            renpy.log("Deep scan failed: {}".format(str(e)))
            renpy.notify("❌ Deep scan failed")

# Default values
default communication_test_results = []
