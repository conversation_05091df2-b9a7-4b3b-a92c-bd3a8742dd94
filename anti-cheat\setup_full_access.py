#!/usr/bin/env python3
"""
Full Access Setup Script
Configures complete developer recognition for all systems
"""

import os
import sys
import json
import hashlib
import platform
import uuid
import getpass

def generate_all_signatures():
    """Generate all possible signatures for this computer"""
    signatures = {}
    
    # Hardware signature
    try:
        system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version()
        }
        
        # Get MAC address
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0,2*6,2)][::-1])
        system_info['mac_address'] = mac
        
        # Get CPU info (Windows)
        try:
            if platform.system() == 'Windows':
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                cpu_name = winreg.QueryValueEx(key, "ProcessorNameString")[0]
                system_info['cpu_name'] = cpu_name
                winreg.CloseKey(key)
        except:
            pass
        
        # Create hardware signature
        signature_string = json.dumps(system_info, sort_keys=True)
        hardware_signature = hashlib.sha256(signature_string.encode()).hexdigest()
        signatures['hardware'] = hardware_signature
        
    except Exception as e:
        print(f"Error generating hardware signature: {e}")
    
    # System signature
    try:
        system_info_simple = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'system': platform.system()
        }
        
        system_signature = hashlib.sha256(str(system_info_simple).encode()).hexdigest()
        signatures['system'] = system_signature
        
    except Exception as e:
        print(f"Error generating system signature: {e}")
    
    # User account info
    try:
        current_user = getpass.getuser().lower()
        computer_name = os.environ.get('COMPUTERNAME', '').lower()
        
        signatures['user'] = current_user
        signatures['computer'] = computer_name
        
    except Exception as e:
        print(f"Error getting user info: {e}")
    
    return signatures

def update_anticheat_signatures(signatures):
    """Update anti-cheat core with all signatures"""
    try:
        anticheat_file = "anti-cheat/anticheat_core.rpy"
        
        if not os.path.exists(anticheat_file):
            print(f"Anti-cheat file not found: {anticheat_file}")
            return False
        
        # Read the file
        with open(anticheat_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update hardware signatures
        if 'hardware' in signatures:
            old_line = '                    "your_primary_signature_here",'
            new_line = f'                    "{signatures["hardware"]}",'
            content = content.replace(old_line, new_line)
        
        # Update user accounts
        if 'user' in signatures:
            old_line = "                'your_username',"
            new_line = f"                '{signatures['user']}',"
            content = content.replace(old_line, new_line)
        
        # Update computer names
        if 'computer' in signatures:
            old_line = "                'your_computer_name',"
            new_line = f"                '{signatures['computer']}',"
            content = content.replace(old_line, new_line)
        
        # Update system signatures
        if 'system' in signatures:
            old_line = '                "your_system_signature_here",'
            new_line = f'                "{signatures["system"]}",'
            content = content.replace(old_line, new_line)
        
        # Write back to file
        with open(anticheat_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ Anti-cheat signatures updated")
        return True
        
    except Exception as e:
        print(f"Error updating anti-cheat signatures: {e}")
        return False

def update_legal_protection_signatures(signatures):
    """Update legal protection with signatures"""
    try:
        legal_file = "anti-cheat/legal_protection.rpy"
        
        if not os.path.exists(legal_file):
            print(f"Legal protection file not found: {legal_file}")
            return False
        
        # Read the file
        with open(legal_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update hardware signature
        if 'hardware' in signatures:
            old_line = '                "your_developer_signature_hash_here",'
            new_line = f'                "{signatures["hardware"]}",'
            content = content.replace(old_line, new_line)
        
        # Write back to file
        with open(legal_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ Legal protection signatures updated")
        return True
        
    except Exception as e:
        print(f"Error updating legal protection signatures: {e}")
        return False

def create_master_config(signatures):
    """Create master configuration file"""
    try:
        config = {
            'computer_info': {
                'user': signatures.get('user', 'unknown'),
                'computer_name': signatures.get('computer', 'unknown'),
                'platform': platform.platform(),
                'system': platform.system()
            },
            'signatures': signatures,
            'setup_timestamp': platform.time.time() if hasattr(platform, 'time') else 0,
            'developer_access': {
                'full_access_enabled': True,
                'all_systems_unlocked': True,
                'master_override': True
            },
            'instructions': [
                "This computer is configured for full developer access",
                "All anti-cheat, encryption, and legal systems will recognize this computer",
                "You have unlimited access to all developer features",
                "Keep this configuration file secure"
            ]
        }
        
        config_file = "anti-cheat/master_developer_config.json"
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        print(f"✓ Master configuration created: {config_file}")
        return True
        
    except Exception as e:
        print(f"Error creating master config: {e}")
        return False

def verify_setup():
    """Verify the setup is working"""
    try:
        # Check if all files exist
        required_files = [
            "anti-cheat/anticheat_core.rpy",
            "anti-cheat/legal_protection.rpy",
            "anti-cheat/developer_panel.rpy",
            ".vscode/encryption_core.py",
            ".vscode/resource_manager.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print("⚠️  Missing files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False
        
        print("✓ All required files present")
        return True
        
    except Exception as e:
        print(f"Error verifying setup: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 70)
    print("FULL ACCESS SETUP - COMPLETE DEVELOPER RECOGNITION")
    print("=" * 70)
    print()
    
    print("This script will configure your computer for full access to:")
    print("• Anti-cheat system controls")
    print("• Encryption system access")
    print("• Legal document management")
    print("• NSFW detection configuration")
    print("• All developer tools and overrides")
    print()
    
    # Generate all signatures
    print("Generating computer signatures...")
    signatures = generate_all_signatures()
    
    print("Computer Information:")
    print(f"  User: {signatures.get('user', 'unknown')}")
    print(f"  Computer: {signatures.get('computer', 'unknown')}")
    print(f"  Platform: {platform.platform()}")
    print(f"  Hardware Signature: {signatures.get('hardware', 'unknown')[:16]}...")
    print(f"  System Signature: {signatures.get('system', 'unknown')[:16]}...")
    print()
    
    # Update all systems
    print("Updating system configurations...")
    
    success_count = 0
    
    if update_anticheat_signatures(signatures):
        success_count += 1
    
    if update_legal_protection_signatures(signatures):
        success_count += 1
    
    if create_master_config(signatures):
        success_count += 1
    
    if verify_setup():
        success_count += 1
    
    print()
    print("=" * 70)
    if success_count >= 3:
        print("✅ SETUP COMPLETED SUCCESSFULLY")
        print("=" * 70)
        print()
        print("🔓 FULL DEVELOPER ACCESS CONFIGURED")
        print()
        print("Your computer is now recognized by:")
        print("✓ Anti-cheat system (full control)")
        print("✓ Encryption system (bypass all restrictions)")
        print("✓ Legal protection (auto-create documents)")
        print("✓ NSFW detection (configuration access)")
        print("✓ Developer panel (all tools unlocked)")
        print()
        print("WHAT YOU CAN NOW DO:")
        print("• Access developer control panel in-game")
        print("• Override any bans or restrictions")
        print("• Decrypt/encrypt all project files")
        print("• Modify all system configurations")
        print("• Bypass all protection systems")
        print("• Access all debugging tools")
        print()
        print("NEXT STEPS:")
        print("1. Run your visual novel")
        print("2. Look for 'Developer Access Granted' notification")
        print("3. Access developer panel from main menu")
        print("4. Test all systems are working")
        print()
        print("If you change hardware or reinstall, run this script again.")
    else:
        print("❌ SETUP FAILED")
        print("=" * 70)
        print()
        print("Some components could not be configured.")
        print("Check the error messages above and try again.")
    
    print()

if __name__ == "__main__":
    main()
