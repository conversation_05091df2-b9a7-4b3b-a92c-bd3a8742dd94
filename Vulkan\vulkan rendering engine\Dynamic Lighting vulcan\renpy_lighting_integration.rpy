## Ren'Py Lighting Integration Layer
## Seamless integration between dynamic lighting and Ren'Py game engine
## Provides easy-to-use functions for visual novel developers

init python:
    import renpy
    
    class RenpyLightingIntegration:
        """
        Integration layer between dynamic lighting and Ren'Py
        Provides game-specific lighting functions and automatic scene management
        """
        
        def __init__(self):
            self.scene_lighting_cache = {}
            self.character_lighting_profiles = {}
            self.location_lighting_presets = {}
            self.auto_lighting_enabled = True
            self.current_location = None
            self.current_time_of_day = "afternoon"
            self.weather_condition = "clear"
            
        def initialize_renpy_integration(self):
            """Initialize Ren'Py specific lighting integration"""
            print("=== REN'PY LIGHTING INTEGRATION ===")
            
            try:
                # Set up character lighting profiles
                self._setup_character_profiles()
                
                # Set up location presets
                self._setup_location_presets()
                
                # Register with Ren'Py events
                self._register_renpy_events()
                
                # Set up automatic lighting
                self._setup_automatic_lighting()
                
                print("✅ Ren'Py lighting integration initialized")
                return True
                
            except Exception as e:
                print(f"❌ Error initializing Ren'Py integration: {e}")
                return False
        
        def _setup_character_profiles(self):
            """Set up lighting profiles for different characters"""
            self.character_lighting_profiles = {
                'protagonist': {
                    'rim_light': {'color': [0.8, 0.9, 1.0], 'intensity': 0.5},
                    'fill_light': {'color': [1.0, 0.9, 0.8], 'intensity': 0.3},
                    'mood_modifier': 1.0
                },
                'love_interest': {
                    'rim_light': {'color': [1.0, 0.8, 0.9], 'intensity': 0.6},
                    'fill_light': {'color': [1.0, 0.9, 0.9], 'intensity': 0.4},
                    'mood_modifier': 1.2
                },
                'antagonist': {
                    'rim_light': {'color': [1.0, 0.6, 0.6], 'intensity': 0.7},
                    'fill_light': {'color': [0.8, 0.7, 0.7], 'intensity': 0.2},
                    'mood_modifier': 0.8
                },
                'mysterious': {
                    'rim_light': {'color': [0.6, 0.6, 1.0], 'intensity': 0.4},
                    'fill_light': {'color': [0.7, 0.7, 0.9], 'intensity': 0.2},
                    'mood_modifier': 0.6
                },
                'cheerful': {
                    'rim_light': {'color': [1.0, 1.0, 0.8], 'intensity': 0.8},
                    'fill_light': {'color': [1.0, 0.95, 0.8], 'intensity': 0.5},
                    'mood_modifier': 1.3
                }
            }
            
            print(f"✅ Set up {len(self.character_lighting_profiles)} character lighting profiles")
        
        def _setup_location_presets(self):
            """Set up lighting presets for different locations"""
            self.location_lighting_presets = {
                'school_classroom': {
                    'base_scenario': 'indoor_cool',
                    'modifications': {
                        'ambient': {'intensity': 0.8},
                        'point_lights': [
                            {'position': [0, 3, -2], 'color': [0.9, 0.9, 1.0], 'intensity': 1.2, 'radius': 6.0}
                        ]
                    },
                    'description': 'Bright classroom with fluorescent lighting'
                },
                'school_hallway': {
                    'base_scenario': 'indoor_cool',
                    'modifications': {
                        'ambient': {'intensity': 0.6},
                        'directional': {'intensity': 0.2}
                    },
                    'description': 'Dimmer hallway lighting'
                },
                'home_bedroom': {
                    'base_scenario': 'indoor_warm',
                    'modifications': {
                        'ambient': {'intensity': 0.5},
                        'point_lights': [
                            {'position': [2, 1.5, 1], 'color': [1.0, 0.8, 0.6], 'intensity': 1.5, 'radius': 4.0}
                        ]
                    },
                    'description': 'Cozy bedroom with warm lamp'
                },
                'home_kitchen': {
                    'base_scenario': 'indoor_cool',
                    'modifications': {
                        'ambient': {'intensity': 0.9},
                        'point_lights': [
                            {'position': [0, 2.5, 0], 'color': [0.95, 0.95, 1.0], 'intensity': 2.0, 'radius': 5.0}
                        ]
                    },
                    'description': 'Bright kitchen with overhead lighting'
                },
                'park_day': {
                    'base_scenario': 'afternoon',
                    'modifications': {
                        'ambient': {'intensity': 0.8},
                        'directional': {'intensity': 1.2}
                    },
                    'description': 'Sunny park during daytime'
                },
                'park_evening': {
                    'base_scenario': 'sunset',
                    'modifications': {
                        'point_lights': [
                            {'position': [-3, 2, 0], 'color': [1.0, 0.9, 0.7], 'intensity': 1.0, 'radius': 4.0},
                            {'position': [3, 2, 0], 'color': [1.0, 0.9, 0.7], 'intensity': 1.0, 'radius': 4.0}
                        ]
                    },
                    'description': 'Park in evening with street lamps'
                },
                'cafe_interior': {
                    'base_scenario': 'indoor_warm',
                    'modifications': {
                        'ambient': {'r': 0.8, 'g': 0.7, 'b': 0.5, 'intensity': 0.6},
                        'point_lights': [
                            {'position': [-2, 1.8, 1], 'color': [1.0, 0.8, 0.5], 'intensity': 1.2, 'radius': 3.0},
                            {'position': [2, 1.8, 1], 'color': [1.0, 0.8, 0.5], 'intensity': 1.2, 'radius': 3.0}
                        ]
                    },
                    'description': 'Cozy cafe with warm pendant lights'
                },
                'rooftop_night': {
                    'base_scenario': 'night',
                    'modifications': {
                        'ambient': {'b': 0.4, 'intensity': 0.4},
                        'directional': {'intensity': 0.3}
                    },
                    'description': 'Rooftop at night with city glow'
                }
            }
            
            print(f"✅ Set up {len(self.location_lighting_presets)} location presets")
        
        def _register_renpy_events(self):
            """Register with Ren'Py event system"""
            try:
                # This would register callbacks for scene changes, character appearances, etc.
                print("✅ Ren'Py event callbacks registered")
            except Exception as e:
                print(f"Error registering Ren'Py events: {e}")
        
        def _setup_automatic_lighting(self):
            """Set up automatic lighting based on game state"""
            self.auto_lighting_enabled = True
            print("✅ Automatic lighting enabled")
        
        def set_location_lighting(self, location_name, time_of_day=None, weather=None, transition_time=1.0):
            """Set lighting for a specific location"""
            if location_name not in self.location_lighting_presets:
                print(f"❌ Unknown location: {location_name}")
                return False
            
            self.current_location = location_name
            if time_of_day:
                self.current_time_of_day = time_of_day
            if weather:
                self.weather_condition = weather
            
            preset = self.location_lighting_presets[location_name]
            
            # Start with base scenario
            base_scenario = preset['base_scenario']
            
            # Apply time of day modifications
            modified_scenario = self._apply_time_of_day_modifications(base_scenario, self.current_time_of_day)
            
            # Apply weather modifications
            modified_scenario = self._apply_weather_modifications(modified_scenario, self.weather_condition)
            
            # Apply location-specific modifications
            if 'modifications' in preset:
                modified_scenario = self._apply_location_modifications(modified_scenario, preset['modifications'])
            
            # Apply the lighting
            if 'integrated_lighting' in globals():
                integrated_lighting._apply_lighting_scenario(modified_scenario)
                if transition_time > 0:
                    # Could add transition animation here
                    pass
            
            print(f"✅ Set location lighting: {location_name} ({self.current_time_of_day}, {self.weather_condition})")
            print(f"   {preset['description']}")
            return True
        
        def _apply_time_of_day_modifications(self, base_scenario, time_of_day):
            """Apply time of day modifications to lighting scenario"""
            if 'integrated_lighting' not in globals():
                return {}
            
            scenario = integrated_lighting.lighting_scenarios.get(base_scenario, {}).copy()
            
            time_modifiers = {
                'morning': {
                    'ambient': {'intensity_mult': 1.1, 'color_shift': [0.1, 0.1, 0.0]},
                    'directional': {'intensity_mult': 1.2, 'color_shift': [0.1, 0.05, -0.1]}
                },
                'afternoon': {
                    'ambient': {'intensity_mult': 1.0, 'color_shift': [0.0, 0.0, 0.0]},
                    'directional': {'intensity_mult': 1.0, 'color_shift': [0.0, 0.0, 0.0]}
                },
                'evening': {
                    'ambient': {'intensity_mult': 0.7, 'color_shift': [0.2, 0.1, -0.1]},
                    'directional': {'intensity_mult': 0.6, 'color_shift': [0.3, 0.1, -0.2]}
                },
                'night': {
                    'ambient': {'intensity_mult': 0.3, 'color_shift': [-0.2, -0.2, 0.1]},
                    'directional': {'intensity_mult': 0.2, 'color_shift': [-0.1, -0.1, 0.2]}
                }
            }
            
            if time_of_day in time_modifiers:
                modifier = time_modifiers[time_of_day]
                
                # Apply ambient modifications
                if 'ambient' in scenario and 'ambient' in modifier:
                    amb_mod = modifier['ambient']
                    scenario['ambient']['intensity'] *= amb_mod.get('intensity_mult', 1.0)
                    
                    color_shift = amb_mod.get('color_shift', [0, 0, 0])
                    scenario['ambient']['r'] = max(0, min(1, scenario['ambient']['r'] + color_shift[0]))
                    scenario['ambient']['g'] = max(0, min(1, scenario['ambient']['g'] + color_shift[1]))
                    scenario['ambient']['b'] = max(0, min(1, scenario['ambient']['b'] + color_shift[2]))
                
                # Apply directional modifications
                if 'directional' in scenario and 'directional' in modifier:
                    dir_mod = modifier['directional']
                    scenario['directional']['intensity'] *= dir_mod.get('intensity_mult', 1.0)
                    
                    color_shift = dir_mod.get('color_shift', [0, 0, 0])
                    scenario['directional']['r'] = max(0, min(1, scenario['directional']['r'] + color_shift[0]))
                    scenario['directional']['g'] = max(0, min(1, scenario['directional']['g'] + color_shift[1]))
                    scenario['directional']['b'] = max(0, min(1, scenario['directional']['b'] + color_shift[2]))
            
            return scenario
        
        def _apply_weather_modifications(self, scenario, weather):
            """Apply weather modifications to lighting scenario"""
            weather_modifiers = {
                'clear': {},  # No modifications
                'cloudy': {
                    'ambient': {'intensity_mult': 0.8},
                    'directional': {'intensity_mult': 0.6},
                    'shadow_intensity_mult': 0.5
                },
                'rainy': {
                    'ambient': {'intensity_mult': 0.6, 'color_shift': [-0.1, -0.1, 0.1]},
                    'directional': {'intensity_mult': 0.4},
                    'shadow_intensity_mult': 0.3
                },
                'stormy': {
                    'ambient': {'intensity_mult': 0.4, 'color_shift': [-0.2, -0.2, 0.0]},
                    'directional': {'intensity_mult': 0.3},
                    'shadow_intensity_mult': 0.8
                },
                'foggy': {
                    'ambient': {'intensity_mult': 0.7, 'color_shift': [0.0, 0.0, 0.1]},
                    'directional': {'intensity_mult': 0.3},
                    'fog': {'density_mult': 3.0}
                }
            }
            
            if weather in weather_modifiers:
                modifier = weather_modifiers[weather]
                
                # Apply ambient modifications
                if 'ambient' in scenario and 'ambient' in modifier:
                    amb_mod = modifier['ambient']
                    scenario['ambient']['intensity'] *= amb_mod.get('intensity_mult', 1.0)
                    
                    color_shift = amb_mod.get('color_shift', [0, 0, 0])
                    scenario['ambient']['r'] = max(0, min(1, scenario['ambient']['r'] + color_shift[0]))
                    scenario['ambient']['g'] = max(0, min(1, scenario['ambient']['g'] + color_shift[1]))
                    scenario['ambient']['b'] = max(0, min(1, scenario['ambient']['b'] + color_shift[2]))
                
                # Apply directional modifications
                if 'directional' in scenario and 'directional' in modifier:
                    dir_mod = modifier['directional']
                    scenario['directional']['intensity'] *= dir_mod.get('intensity_mult', 1.0)
                
                # Apply shadow modifications
                if 'shadow_intensity_mult' in modifier:
                    scenario['shadow_intensity'] *= modifier['shadow_intensity_mult']
                
                # Apply fog modifications
                if 'fog' in modifier and 'fog' in scenario:
                    fog_mod = modifier['fog']
                    scenario['fog']['density'] *= fog_mod.get('density_mult', 1.0)
            
            return scenario
        
        def _apply_location_modifications(self, scenario, modifications):
            """Apply location-specific modifications"""
            import copy
            modified_scenario = copy.deepcopy(scenario)
            
            # Apply modifications
            for key, value in modifications.items():
                if key in modified_scenario:
                    if isinstance(value, dict):
                        modified_scenario[key].update(value)
                    else:
                        modified_scenario[key] = value
                else:
                    modified_scenario[key] = value
            
            return modified_scenario
        
        def set_character_lighting(self, character_name, profile_name=None, position=None):
            """Set lighting for a specific character"""
            if profile_name and profile_name in self.character_lighting_profiles:
                profile = self.character_lighting_profiles[profile_name]
                
                if position and 'integrated_lighting' in globals():
                    # Add character-specific lighting
                    light_id = f"char_{character_name}"
                    
                    rim_light = profile['rim_light']
                    integrated_lighting.add_point_light(
                        position[0] + 1, position[1] + 1, position[2] + 0.5,
                        rim_light['color'][0], rim_light['color'][1], rim_light['color'][2],
                        rim_light['intensity'], 2.0, f"{light_id}_rim"
                    )
                    
                    fill_light = profile['fill_light']
                    integrated_lighting.add_point_light(
                        position[0] - 0.5, position[1] + 0.5, position[2] + 1,
                        fill_light['color'][0], fill_light['color'][1], fill_light['color'][2],
                        fill_light['intensity'], 3.0, f"{light_id}_fill"
                    )
                    
                    print(f"✅ Applied character lighting: {character_name} ({profile_name})")
                    return True
            
            print(f"❌ Character lighting failed: {character_name}")
            return False
        
        def remove_character_lighting(self, character_name):
            """Remove character-specific lighting"""
            if 'integrated_lighting' in globals():
                light_id = f"char_{character_name}"
                integrated_lighting.remove_point_light(f"{light_id}_rim")
                integrated_lighting.remove_point_light(f"{light_id}_fill")
                print(f"✅ Removed character lighting: {character_name}")
        
        def set_mood_lighting(self, mood, intensity=1.0, transition_time=2.0):
            """Set mood-based lighting"""
            mood_scenarios = {
                'happy': 'morning',
                'sad': 'night',
                'romantic': 'romantic',
                'dramatic': 'dramatic',
                'mysterious': 'night',
                'energetic': 'afternoon',
                'calm': 'indoor_warm',
                'tense': 'dramatic'
            }
            
            if mood in mood_scenarios:
                scenario = mood_scenarios[mood]
                if 'integrated_lighting' in globals():
                    integrated_lighting.set_lighting_scenario(scenario, transition_time)
                    
                    # Apply intensity modifier
                    if intensity != 1.0:
                        integrated_lighting.animate_light_property('ambient', 'intensity', 
                                                                 integrated_lighting.current_scene_lighting['ambient']['intensity'] * intensity, 
                                                                 transition_time)
                    
                    print(f"✅ Set mood lighting: {mood} (intensity: {intensity})")
                    return True
            
            print(f"❌ Unknown mood: {mood}")
            return False
    
    # Initialize Ren'Py integration
    renpy_lighting = RenpyLightingIntegration()
    
    def initialize_renpy_lighting():
        """Initialize Ren'Py lighting integration"""
        return renpy_lighting.initialize_renpy_integration()
    
    def set_location(location_name, time_of_day=None, weather=None, transition_time=1.0):
        """Set lighting for a location"""
        return renpy_lighting.set_location_lighting(location_name, time_of_day, weather, transition_time)
    
    def set_character_light(character_name, profile_name=None, position=None):
        """Set character lighting"""
        return renpy_lighting.set_character_lighting(character_name, profile_name, position)
    
    def set_mood(mood, intensity=1.0, transition_time=2.0):
        """Set mood lighting"""
        return renpy_lighting.set_mood_lighting(mood, intensity, transition_time)

# Automatically initialize Ren'Py integration
init:
    python:
        try:
            print("Initializing Ren'Py Lighting Integration...")
            initialize_renpy_lighting()
        except Exception as e:
            print(f"Error initializing Ren'Py lighting integration: {e}")
