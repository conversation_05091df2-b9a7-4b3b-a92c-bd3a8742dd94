## Lighting Effects Library
## Pre-built lighting effects and animations for visual novels
## Includes atmospheric effects, weather lighting, and special effects

init python:
    import random
    import math
    import time
    
    class LightingEffectsLibrary:
        """
        Library of pre-built lighting effects and animations
        Provides easy-to-use lighting effects for visual novels
        """
        
        def __init__(self, lighting_system):
            self.lighting_system = lighting_system
            self.active_effects = {}
            self.effect_timers = {}
            
        def flickering_candle(self, light_id, base_intensity=1.0, flicker_amount=0.3, speed=2.0):
            """Create a flickering candle effect"""
            effect_name = f"flickering_candle_{light_id}"
            
            self.active_effects[effect_name] = {
                'type': 'flickering_candle',
                'light_id': light_id,
                'base_intensity': base_intensity,
                'flicker_amount': flicker_amount,
                'speed': speed,
                'start_time': time.time()
            }
            
            print(f"✅ Started flickering candle effect for light {light_id}")
            return effect_name
        
        def lightning_flash(self, duration=0.2, intensity=3.0, color=(0.9, 0.9, 1.0)):
            """Create a lightning flash effect"""
            effect_name = f"lightning_flash_{int(time.time() * 1000)}"
            
            # Store original ambient light
            original_ambient = self.lighting_system.ambient_light.copy()
            
            # Create bright flash
            flash_ambient = {
                'r': color[0] * intensity,
                'g': color[1] * intensity,
                'b': color[2] * intensity,
                'intensity': intensity
            }
            
            self.active_effects[effect_name] = {
                'type': 'lightning_flash',
                'duration': duration,
                'original_ambient': original_ambient,
                'flash_ambient': flash_ambient,
                'start_time': time.time()
            }
            
            # Apply flash immediately
            self.lighting_system.ambient_light = flash_ambient
            
            print(f"✅ Lightning flash effect started")
            return effect_name
        
        def sunrise_sunset(self, duration=10.0, is_sunrise=True):
            """Create a sunrise or sunset transition"""
            effect_name = f"{'sunrise' if is_sunrise else 'sunset'}_{int(time.time())}"
            
            if is_sunrise:
                # Sunrise: night -> day
                start_scenario = 'night'
                end_scenario = 'day_outdoor'
                start_color = (0.1, 0.1, 0.3)
                end_color = (1.0, 0.95, 0.8)
            else:
                # Sunset: day -> night
                start_scenario = 'day_outdoor'
                end_scenario = 'night'
                start_color = (1.0, 0.95, 0.8)
                end_color = (1.0, 0.6, 0.3)
            
            self.active_effects[effect_name] = {
                'type': 'sunrise_sunset',
                'duration': duration,
                'is_sunrise': is_sunrise,
                'start_color': start_color,
                'end_color': end_color,
                'start_time': time.time()
            }
            
            print(f"✅ {'Sunrise' if is_sunrise else 'Sunset'} effect started ({duration}s)")
            return effect_name
        
        def fire_glow(self, x, y, z, base_intensity=2.0, flicker_speed=3.0, color_variation=True):
            """Create a fire glow effect with multiple point lights"""
            effect_name = f"fire_glow_{int(time.time() * 1000)}"
            
            # Create multiple point lights for fire effect
            fire_lights = []
            for i in range(3):
                offset_x = random.uniform(-0.2, 0.2)
                offset_y = random.uniform(-0.1, 0.1)
                offset_z = random.uniform(-0.2, 0.2)
                
                light_id = self.lighting_system.add_point_light(
                    x + offset_x, y + offset_y, z + offset_z,
                    r=1.0, g=0.7, b=0.3,
                    intensity=base_intensity * random.uniform(0.8, 1.2),
                    radius=1.5 + random.uniform(-0.3, 0.3)
                )
                
                if light_id >= 0:
                    fire_lights.append(light_id)
            
            self.active_effects[effect_name] = {
                'type': 'fire_glow',
                'fire_lights': fire_lights,
                'base_position': (x, y, z),
                'base_intensity': base_intensity,
                'flicker_speed': flicker_speed,
                'color_variation': color_variation,
                'start_time': time.time()
            }
            
            print(f"✅ Fire glow effect started with {len(fire_lights)} lights")
            return effect_name
        
        def storm_lighting(self, lightning_frequency=5.0, rain_intensity=0.7):
            """Create a storm lighting effect with random lightning"""
            effect_name = f"storm_lighting_{int(time.time())}"
            
            # Set storm base lighting
            self.lighting_system.set_lighting_scenario('storm')
            
            self.active_effects[effect_name] = {
                'type': 'storm_lighting',
                'lightning_frequency': lightning_frequency,
                'rain_intensity': rain_intensity,
                'last_lightning': time.time(),
                'start_time': time.time()
            }
            
            print(f"✅ Storm lighting effect started")
            return effect_name
        
        def magical_aura(self, x, y, z, color=(0.5, 0.3, 1.0), pulse_speed=1.5, intensity=1.5):
            """Create a magical aura effect"""
            effect_name = f"magical_aura_{int(time.time() * 1000)}"
            
            # Create magical point light
            light_id = self.lighting_system.add_point_light(
                x, y, z,
                r=color[0], g=color[1], b=color[2],
                intensity=intensity,
                radius=2.0
            )
            
            if light_id >= 0:
                self.active_effects[effect_name] = {
                    'type': 'magical_aura',
                    'light_id': light_id,
                    'base_color': color,
                    'pulse_speed': pulse_speed,
                    'base_intensity': intensity,
                    'start_time': time.time()
                }
                
                print(f"✅ Magical aura effect started for light {light_id}")
                return effect_name
            else:
                print("❌ Failed to create magical aura - no available light slots")
                return None
        
        def disco_lights(self, num_lights=4, change_speed=0.5):
            """Create a disco lighting effect with changing colors"""
            effect_name = f"disco_lights_{int(time.time())}"
            
            disco_lights = []
            colors = [
                (1.0, 0.0, 0.0),  # Red
                (0.0, 1.0, 0.0),  # Green
                (0.0, 0.0, 1.0),  # Blue
                (1.0, 1.0, 0.0),  # Yellow
                (1.0, 0.0, 1.0),  # Magenta
                (0.0, 1.0, 1.0)   # Cyan
            ]
            
            for i in range(min(num_lights, len(colors))):
                angle = (i / num_lights) * 2 * math.pi
                x = math.cos(angle) * 2.0
                z = math.sin(angle) * 2.0
                
                color = colors[i]
                light_id = self.lighting_system.add_point_light(
                    x, 1.0, z,
                    r=color[0], g=color[1], b=color[2],
                    intensity=2.0,
                    radius=3.0
                )
                
                if light_id >= 0:
                    disco_lights.append(light_id)
            
            self.active_effects[effect_name] = {
                'type': 'disco_lights',
                'disco_lights': disco_lights,
                'colors': colors,
                'change_speed': change_speed,
                'last_change': time.time(),
                'start_time': time.time()
            }
            
            print(f"✅ Disco lights effect started with {len(disco_lights)} lights")
            return effect_name
        
        def underwater_caustics(self, wave_speed=1.0, intensity_variation=0.5):
            """Create underwater caustics lighting effect"""
            effect_name = f"underwater_caustics_{int(time.time())}"
            
            # Set underwater base lighting
            underwater_ambient = {"r": 0.2, "g": 0.4, "b": 0.6, "intensity": 0.6}
            self.lighting_system.ambient_light = underwater_ambient
            
            self.active_effects[effect_name] = {
                'type': 'underwater_caustics',
                'wave_speed': wave_speed,
                'intensity_variation': intensity_variation,
                'base_ambient': underwater_ambient,
                'start_time': time.time()
            }
            
            print(f"✅ Underwater caustics effect started")
            return effect_name
        
        def update_effects(self):
            """Update all active lighting effects"""
            current_time = time.time()
            completed_effects = []
            
            for effect_name, effect in self.active_effects.items():
                try:
                    if effect['type'] == 'flickering_candle':
                        self._update_flickering_candle(effect, current_time)
                    elif effect['type'] == 'lightning_flash':
                        if self._update_lightning_flash(effect, current_time):
                            completed_effects.append(effect_name)
                    elif effect['type'] == 'sunrise_sunset':
                        if self._update_sunrise_sunset(effect, current_time):
                            completed_effects.append(effect_name)
                    elif effect['type'] == 'fire_glow':
                        self._update_fire_glow(effect, current_time)
                    elif effect['type'] == 'storm_lighting':
                        self._update_storm_lighting(effect, current_time)
                    elif effect['type'] == 'magical_aura':
                        self._update_magical_aura(effect, current_time)
                    elif effect['type'] == 'disco_lights':
                        self._update_disco_lights(effect, current_time)
                    elif effect['type'] == 'underwater_caustics':
                        self._update_underwater_caustics(effect, current_time)
                        
                except Exception as e:
                    print(f"Error updating effect {effect_name}: {e}")
                    completed_effects.append(effect_name)
            
            # Remove completed effects
            for effect_name in completed_effects:
                del self.active_effects[effect_name]
                print(f"✅ Effect completed: {effect_name}")
        
        def _update_flickering_candle(self, effect, current_time):
            """Update flickering candle effect"""
            elapsed = current_time - effect['start_time']
            flicker = math.sin(elapsed * effect['speed']) * effect['flicker_amount']
            flicker += random.uniform(-0.1, 0.1) * effect['flicker_amount']
            
            new_intensity = effect['base_intensity'] + flicker
            new_intensity = max(0.1, min(2.0, new_intensity))  # Clamp intensity
            
            # Update the point light intensity
            for light in self.lighting_system.point_lights:
                if light.get('id') == effect['light_id']:
                    light['intensity'] = new_intensity
                    break
        
        def _update_lightning_flash(self, effect, current_time):
            """Update lightning flash effect"""
            elapsed = current_time - effect['start_time']
            
            if elapsed >= effect['duration']:
                # Restore original ambient light
                self.lighting_system.ambient_light = effect['original_ambient']
                return True  # Effect completed
            
            return False
        
        def _update_sunrise_sunset(self, effect, current_time):
            """Update sunrise/sunset effect"""
            elapsed = current_time - effect['start_time']
            progress = min(elapsed / effect['duration'], 1.0)
            
            # Interpolate colors
            start_color = effect['start_color']
            end_color = effect['end_color']
            
            current_r = start_color[0] + (end_color[0] - start_color[0]) * progress
            current_g = start_color[1] + (end_color[1] - start_color[1]) * progress
            current_b = start_color[2] + (end_color[2] - start_color[2]) * progress
            
            # Update directional light
            self.lighting_system.directional_light['r'] = current_r
            self.lighting_system.directional_light['g'] = current_g
            self.lighting_system.directional_light['b'] = current_b
            
            return progress >= 1.0  # Effect completed when progress reaches 1.0
        
        def _update_fire_glow(self, effect, current_time):
            """Update fire glow effect"""
            elapsed = current_time - effect['start_time']
            
            for light_id in effect['fire_lights']:
                # Create flickering for each fire light
                flicker = math.sin(elapsed * effect['flicker_speed'] + light_id) * 0.3
                flicker += random.uniform(-0.2, 0.2)
                
                new_intensity = effect['base_intensity'] + flicker
                new_intensity = max(0.5, min(3.0, new_intensity))
                
                # Update light intensity
                for light in self.lighting_system.point_lights:
                    if light.get('id') == light_id:
                        light['intensity'] = new_intensity
                        
                        # Color variation
                        if effect['color_variation']:
                            color_shift = random.uniform(-0.1, 0.1)
                            light['r'] = max(0.8, min(1.0, 1.0 + color_shift))
                            light['g'] = max(0.5, min(0.9, 0.7 + color_shift))
                        break
        
        def _update_storm_lighting(self, effect, current_time):
            """Update storm lighting effect"""
            # Random lightning flashes
            if current_time - effect['last_lightning'] > effect['lightning_frequency']:
                if random.random() < 0.3:  # 30% chance of lightning
                    self.lightning_flash(duration=0.15, intensity=2.5)
                    effect['last_lightning'] = current_time
        
        def _update_magical_aura(self, effect, current_time):
            """Update magical aura effect"""
            elapsed = current_time - effect['start_time']
            pulse = math.sin(elapsed * effect['pulse_speed']) * 0.5 + 0.5
            
            new_intensity = effect['base_intensity'] * (0.5 + pulse * 0.5)
            
            # Update light intensity
            for light in self.lighting_system.point_lights:
                if light.get('id') == effect['light_id']:
                    light['intensity'] = new_intensity
                    break
        
        def _update_disco_lights(self, effect, current_time):
            """Update disco lights effect"""
            if current_time - effect['last_change'] > effect['change_speed']:
                # Change colors randomly
                for light_id in effect['disco_lights']:
                    new_color = random.choice(effect['colors'])
                    
                    for light in self.lighting_system.point_lights:
                        if light.get('id') == light_id:
                            light['r'] = new_color[0]
                            light['g'] = new_color[1]
                            light['b'] = new_color[2]
                            break
                
                effect['last_change'] = current_time
        
        def _update_underwater_caustics(self, effect, current_time):
            """Update underwater caustics effect"""
            elapsed = current_time - effect['start_time']
            
            # Create wave-like intensity variation
            wave1 = math.sin(elapsed * effect['wave_speed']) * 0.3
            wave2 = math.sin(elapsed * effect['wave_speed'] * 1.7 + 1.0) * 0.2
            
            intensity_mod = 1.0 + (wave1 + wave2) * effect['intensity_variation']
            
            # Update ambient light intensity
            base_intensity = effect['base_ambient']['intensity']
            self.lighting_system.ambient_light['intensity'] = base_intensity * intensity_mod
        
        def stop_effect(self, effect_name):
            """Stop a specific lighting effect"""
            if effect_name in self.active_effects:
                effect = self.active_effects[effect_name]
                
                # Clean up effect-specific resources
                if effect['type'] in ['fire_glow', 'magical_aura', 'disco_lights']:
                    # Remove associated point lights
                    if 'fire_lights' in effect:
                        for light_id in effect['fire_lights']:
                            self.lighting_system.remove_point_light(light_id)
                    elif 'light_id' in effect:
                        self.lighting_system.remove_point_light(effect['light_id'])
                    elif 'disco_lights' in effect:
                        for light_id in effect['disco_lights']:
                            self.lighting_system.remove_point_light(light_id)
                
                del self.active_effects[effect_name]
                print(f"✅ Stopped effect: {effect_name}")
                return True
            else:
                print(f"❌ Effect not found: {effect_name}")
                return False
        
        def stop_all_effects(self):
            """Stop all active lighting effects"""
            effect_names = list(self.active_effects.keys())
            for effect_name in effect_names:
                self.stop_effect(effect_name)
            print("✅ All lighting effects stopped")
        
        def get_active_effects(self):
            """Get list of active effects"""
            return list(self.active_effects.keys())
    
    # Initialize lighting effects library
    if 'dynamic_lighting' in globals():
        lighting_effects = LightingEffectsLibrary(dynamic_lighting)
    else:
        lighting_effects = None
        print("⚠️  Dynamic lighting system not found - effects library disabled")

# Update effects each frame
init python:
    def effects_update_callback():
        """Callback to update lighting effects each frame"""
        try:
            if lighting_effects:
                lighting_effects.update_effects()
        except Exception as e:
            print(f"Error in effects update: {e}")
    
    # Register effects update callback
    # config.periodic_callbacks.append(effects_update_callback)
