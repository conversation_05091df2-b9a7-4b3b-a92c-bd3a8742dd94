#!/usr/bin/env python3
"""
Python Version Adapter for Ren'Py Engine
Specifically handles the transition from Python 3.9.10 to newer versions
Maintains engine compatibility while enabling new features
"""

import sys
import os
import json
import platform
from pathlib import Path
from typing import Dict, Any, Optional, List

class RenPyVersionAdapter:
    """
    Adapts Python 3.9.10 Ren'Py engine code to work with newer Python versions
    while maintaining backward compatibility
    """
    
    def __init__(self, project_dir: Optional[str] = None):
        self.project_dir = Path(project_dir) if project_dir else Path.cwd().parent
        self.config_dir = Path(__file__).parent
        self.engine_python_version = "3.9.10"  # Original engine version
        self.current_python_version = platform.python_version()
        self.version_info = sys.version_info
        
        # Load existing configuration
        self.legacy_config = self._load_legacy_config()
        
    def _load_legacy_config(self) -> Dict[str, Any]:
        """Load the existing Python 3.9.10 configuration"""
        config_file = self.config_dir / "python config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Warning: Could not load legacy config: {e}")
        
        # Return default legacy config structure
        return {
            "project_name": "netcode_protogen_game",
            "python_version": "3.9.10",
            "renpy_detected": "Ren'Py (config.version: 1.13.05.07.123v)",
            "system_info": {
                "os": "Windows",
                "platform": "Windows-10",
                "processor": "AMD64"
            },
            "python_paths": {
                "site_packages": "lib/python3.9/site-packages",
                "executable": "python.exe"
            },
            "modules_available": {
                "cryptography": True,
                "pillow": True,
                "requests": True,
                "numpy": True,
                "pygame": True,
                "future": True
            },
            "last_updated": "2023-05-07"
        }
    
    def create_adapted_config(self) -> Dict[str, Any]:
        """Create a new configuration that bridges old and new Python versions"""
        
        # Start with legacy config as base
        adapted_config = self.legacy_config.copy()
        
        # Add compatibility layer information
        adapted_config.update({
            "compatibility_info": {
                "engine_python_version": self.engine_python_version,
                "current_python_version": self.current_python_version,
                "is_newer_version": self.version_info >= (3, 10),
                "compatibility_mode": "bridge",
                "adapter_version": "1.0.0"
            },
            
            "version_mapping": {
                "3.9": {
                    "compatible_with": ["3.10", "3.11", "3.12"],
                    "features_available": ["dict_union", "type_hints", "pathlib", "asyncio"],
                    "limitations": ["no_pattern_matching", "no_union_types"]
                },
                "3.10": {
                    "new_features": ["pattern_matching", "union_types", "better_error_messages"],
                    "compatibility_notes": "Fully compatible with 3.9 code"
                },
                "3.11": {
                    "new_features": ["exception_groups", "improved_asyncio", "faster_cpython"],
                    "compatibility_notes": "Enhanced performance, backward compatible"
                },
                "3.12": {
                    "new_features": ["improved_error_locations", "per_interpreter_gil"],
                    "compatibility_notes": "Latest features, full backward compatibility"
                }
            },
            
            "module_compatibility": self._check_module_compatibility(),
            "path_adaptations": self._create_path_adaptations(),
            "feature_flags": self._create_feature_flags()
        })
        
        return adapted_config
    
    def _check_module_compatibility(self) -> Dict[str, Any]:
        """Check module compatibility across Python versions"""
        modules_to_check = [
            "cryptography", "pillow", "requests", "numpy", "pygame", "future",
            "ssl", "json", "urllib", "asyncio", "pathlib", "typing", "dataclasses"
        ]
        
        compatibility_info = {}
        
        for module_name in modules_to_check:
            try:
                module = __import__(module_name)
                version = getattr(module, '__version__', 'unknown')
                
                compatibility_info[module_name] = {
                    "available": True,
                    "version": version,
                    "python_39_compatible": True,
                    "newer_version_compatible": self.version_info >= (3, 10)
                }
            except ImportError:
                compatibility_info[module_name] = {
                    "available": False,
                    "version": None,
                    "python_39_compatible": False,
                    "newer_version_compatible": False
                }
        
        return compatibility_info
    
    def _create_path_adaptations(self) -> Dict[str, Any]:
        """Create path adaptations for different Python versions"""
        base_paths = [
            "python configure",
            "anti-cheat",
            "platform_detection",
            "options",
            "PROJECT_LINKER"
        ]
        
        adaptations = {
            "base_paths": base_paths,
            "python_39_paths": {
                "site_packages": "lib/python3.9/site-packages",
                "scripts": "Scripts",
                "include": "include"
            },
            "newer_version_paths": {
                "site_packages": f"lib/python{self.version_info.major}.{self.version_info.minor}/site-packages",
                "scripts": "Scripts",
                "include": "include"
            },
            "unified_paths": []
        }
        
        # Create unified paths that work across versions
        for path in base_paths:
            full_path = self.project_dir / path
            if full_path.exists():
                adaptations["unified_paths"].append(str(full_path))
        
        return adaptations
    
    def _create_feature_flags(self) -> Dict[str, bool]:
        """Create feature flags for version-specific functionality"""
        return {
            # Python 3.9 baseline features
            "dict_union_operator": self.version_info >= (3, 9),
            "type_hints_support": self.version_info >= (3, 9),
            "pathlib_support": True,
            "asyncio_support": True,
            
            # Python 3.10+ features
            "pattern_matching": self.version_info >= (3, 10),
            "union_type_syntax": self.version_info >= (3, 10),
            "better_error_messages": self.version_info >= (3, 10),
            
            # Python 3.11+ features
            "exception_groups": self.version_info >= (3, 11),
            "task_groups": self.version_info >= (3, 11),
            "improved_performance": self.version_info >= (3, 11),
            
            # Python 3.12+ features
            "improved_error_locations": self.version_info >= (3, 12),
            "per_interpreter_gil": self.version_info >= (3, 12),
            
            # Ren'Py specific compatibility
            "renpy_engine_compatible": True,
            "legacy_code_support": True,
            "bridge_mode_active": True
        }
    
    def generate_compatibility_report(self) -> str:
        """Generate a detailed compatibility report"""
        config = self.create_adapted_config()
        
        report = f"""
Python Version Compatibility Report
===================================

Engine Information:
- Original Engine Python: {self.engine_python_version}
- Current Python Version: {self.current_python_version}
- Compatibility Mode: {'Active' if config['compatibility_info']['is_newer_version'] else 'Not Required'}

Module Compatibility:
"""
        
        for module, info in config['module_compatibility'].items():
            status = "✅" if info['available'] else "❌"
            report += f"- {module}: {status} (Version: {info.get('version', 'N/A')})\n"
        
        report += f"""
Feature Availability:
"""
        
        for feature, available in config['feature_flags'].items():
            status = "✅" if available else "❌"
            report += f"- {feature}: {status}\n"
        
        report += f"""
Path Adaptations:
- Unified paths configured: {len(config['path_adaptations']['unified_paths'])}
- Cross-version compatibility: Active

Recommendations:
"""
        
        if config['compatibility_info']['is_newer_version']:
            report += "- ✅ Your Python version supports enhanced features\n"
            report += "- ✅ Backward compatibility with engine is maintained\n"
            report += "- ✅ You can use newer Python syntax when available\n"
        else:
            report += "- ℹ️ Running on engine's original Python version\n"
            report += "- ✅ All features are natively supported\n"
        
        return report
    
    def save_adapted_config(self) -> bool:
        """Save the adapted configuration"""
        try:
            config = self.create_adapted_config()
            config_file = self.config_dir / "python_config_adapted.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            print(f"Adapted configuration saved to: {config_file}")
            return True
            
        except Exception as e:
            print(f"Error saving adapted configuration: {e}")
            return False
    
    def create_bridge_integration(self) -> str:
        """Create integration code for Ren'Py"""
        return '''# Python Version Bridge Integration for Ren'Py
# Add this to your game/script.rpy file

init -999 python:
    # Import the version adapter
    import sys
    import os
    
    # Add python configure to path
    python_config_path = os.path.join(config.basedir, "python configure")
    if python_config_path not in sys.path:
        sys.path.insert(0, python_config_path)
    
    try:
        from version_adapter import RenPyVersionAdapter
        from compatibility_bridge import compatibility_bridge
        
        # Initialize the adapter
        version_adapter = RenPyVersionAdapter(config.basedir)
        
        # Load adapted configuration
        adapted_config = version_adapter.create_adapted_config()
        
        # Set up compatibility
        if adapted_config['compatibility_info']['is_newer_version']:
            print("Python Version Bridge: Newer Python detected, enabling compatibility mode")
        else:
            print("Python Version Bridge: Running on original engine Python version")
        
        # Make adapter available globally
        config.python_version_adapter = version_adapter
        config.python_compatibility = compatibility_bridge
        
    except ImportError as e:
        print(f"Warning: Could not load Python version adapter: {e}")
'''
