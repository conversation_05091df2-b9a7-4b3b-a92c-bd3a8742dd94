# Universal Game Router System
# This system allows the engine to recognize and route to separate game files
# without deleting them or causing label conflicts

init -10 python:
    # Game routing configuration
    game_routes = {
        "netcode": {
            "path": "separate games/netcode/",
            "main_file": "netcode bedroom 1.rpy",
            "entry_label": "Netcode_the_protogen_bedroom_1",
            "backup_entry": "netcode_complete.rpy",
            "external_integration": "netcode_bedroom_1_external_integration",
            "display_name": "Netcode the Protogen",
            "description": "Complete deno of netcode the protogen and visual novel with branching storylines"
        },
        "lumetric": {
            "path": "separate games/Lumrtric/",
            "main_file": "Lumrtric bedroom 1.rpy", 
            "entry_label": "Lumetric_1",
            "backup_entry": "lumetric_game.rpy",
            "display_name": "Lumetric",
            "description": "femboy and streamer story with multiple paths"
        },
        "bearwithus": {
            "path": "separate games/BearWithUs/",
            "main_file": "BearWithUs.rpy",
            "entry_label": "BearWithUs_1", 
            "backup_entry": "bearwithus_game.rpy",
            "display_name": "BearWithUs",
            "description": "gaming bear of the arcade"
        },
        "demo": {
            "path": "separate games/Demo Game/",
            "main_file": "DemoGame.rpy",
            "entry_label": "demo_game_start",
            "backup_entry": None,
            "display_name": "Demo Game",
            "description": "Demonstration of the launcher system capabilities"
        }
    }

    # Function to check if a game file exists
    def check_game_exists(game_key):
        if game_key not in game_routes:
            return False
        
        route = game_routes[game_key]
        main_path = route["path"] + route["main_file"]
        
        # Check if main file exists
        try:
            if renpy.loadable(main_path):
                return True
        except:
            pass
        
        # Check backup file if available
        if route["backup_entry"]:
            backup_path = route["path"] + route["backup_entry"]
            try:
                if renpy.loadable(backup_path):
                    return True
            except:
                pass
        
        return False

    # Function to get available games
    def get_available_games():
        available = []
        for game_key, route in game_routes.items():
            if check_game_exists(game_key):
                available.append({
                    "key": game_key,
                    "name": route["display_name"],
                    "description": route["description"],
                    "entry_label": route["entry_label"]
                })
        return available

    # Function to safely route to a game
    def route_to_game(game_key):
        if game_key not in game_routes:
            return False
        
        route = game_routes[game_key]
        entry_label = route["entry_label"]
        
        # Check if the label exists and is callable
        try:
            if renpy.has_label(entry_label):
                return entry_label
        except:
            pass
        
        return False

# Label conflict resolution system
init python:
    # Store original label mappings to prevent conflicts
    original_labels = {}
    
    def register_label_mapping(original_label, redirect_label):
        """Register a label mapping for conflict resolution"""
        original_labels[original_label] = redirect_label
    
    def resolve_label_conflict(label_name):
        """Resolve label conflicts by returning the correct redirect"""
        if label_name in original_labels:
            return original_labels[label_name]
        return label_name

# Register label mappings to resolve conflicts
init python:
    # Map conflicting labels to their integrated versions
    register_label_mapping("BearWithUs_1_separate", "BearWithUs_1")
    register_label_mapping("Lumetric_1_separate", "Lumetric_1") 
    register_label_mapping("Netcode_the_protogen_bedroom_1_separate", "Netcode_the_protogen_bedroom_1")



# Game selection screen
screen game_selection_screen(games):
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        
        vbox:
            spacing 20
            xalign 0.5
            yalign 0.5
            
            text "Select a Game to Play" size 40 xalign 0.5
            
            viewport:
                scrollbars "vertical"
                mousewheel True
                xsize 750
                ysize 400
                
                vbox:
                    spacing 15
                    
                    for game_name, game_key in games:
                        if game_key != "return":
                            python:
                                game_info = None
                                for g in get_available_games():
                                    if g["key"] == game_key:
                                        game_info = g
                                        break
                            
                            frame:
                                xsize 700
                                
                                hbox:
                                    spacing 20
                                    
                                    vbox:
                                        spacing 5
                                        xsize 500
                                        
                                        text "[game_name]" size 30 color "#ffffff"
                                        if game_info:
                                            text "[game_info['description']]" size 20 color "#cccccc"
                                    
                                    textbutton "Play" action [Return(game_key)] xsize 150 ysize 50
            
            textbutton "Return to Main Menu" action Return("return") xsize 200 ysize 50 xalign 0.5

# Enhanced game selection screen with smooth visuals
screen game_selection(available_games):
    modal True

    # Multi-layer animated background
    add "#000814"

    # Gradient overlay layers
    add "#001d3d" alpha 0.4

    add "#003566" alpha 0.2

    # Top buttons with enhanced styling
    hbox:
        xalign 0.5
        ypos 40
        spacing 20

        # Gaming Settings button
        textbutton "🎮 Gaming Settings":
            action Return("settings")
            xsize 220
            ysize 50
            text_size 16
            text_color "#00b4d8"
            background "#023e8a"
            hover_background "#0077b6"

        # Security Status button
        textbutton "🛡️ Security Status":
            action Return("advanced")
            xsize 220
            ysize 50
            text_size 16
            text_color "#0077b6"
            background "#023e8a"
            hover_background "#0077b6"

    # Main content area
    frame:
        xalign 0.5
        yalign 0.5
        xsize 1100
        ysize 520
        background "#001122"
        padding (20, 20)

        # Subtle inner glow
        add "#0077b6" alpha 0.1

        hbox:
            spacing 30
            xalign 0.5
            yalign 0.5

            # Left side - Enhanced game preview
            frame:
                xsize 280
                ysize 460
                background "#003366"
                padding (15, 15)

                vbox:
                    spacing 15
                    xalign 0.5

                    text "Game Preview" size 18 xalign 0.5 color "#90e0ef"

                    # Main preview image
                    frame:
                        xsize 250
                        ysize 180
                        background "#023e8a"
                        padding (10, 10)

                        vbox:
                            xalign 0.5
                            yalign 0.5
                            spacing 5
                            text "🎮" size 40 xalign 0.5
                            text "Game Image" size 14 xalign 0.5 color "#caf0f8"
                            text "Hover over games to preview" size 10 xalign 0.5 color "#90e0ef"

                    text "Description" size 16 xalign 0.5 color "#90e0ef"

                    # Description area
                    frame:
                        xsize 250
                        ysize 180
                        background "#023e8a"
                        padding (10, 10)

                        viewport:
                            scrollbars "vertical"
                            mousewheel True

                            text "Select a game from the center panel to see its detailed description and features here." size 12 color "#caf0f8" text_align 0.5

            # Center - Enhanced game selection
            frame:
                xsize 480
                ysize 460
                background "#002244"
                padding (20, 20)

                vbox:
                    spacing 20
                    xalign 0.5

                    # Title
                    text "Select a Game to Play" size 28 xalign 0.5 color "#00b4d8"

                    # Game list with enhanced styling
                    viewport:
                        xsize 440
                        ysize 320
                        scrollbars "vertical"
                        mousewheel True

                        vbox:
                            spacing 8

                            for game in available_games:
                                frame:
                                    xsize 420
                                    ysize 70
                                    background "#003366"
                                    padding (10, 10)

                                    hbox:
                                        spacing 15
                                        yalign 0.5
                                        xpos 15

                                        # Game icon
                                        text "🎮" size 24 yalign 0.5

                                        # Game info
                                        vbox:
                                            xsize 280
                                            yalign 0.5
                                            spacing 3

                                            text "[game['name']]" size 18 color "#caf0f8"
                                            text "[game.get('description', 'Visual novel experience')]" size 12 color "#90e0ef"

                                        # Play button
                                        textbutton "Play":
                                            action Return(game["key"])
                                            xsize 90
                                            ysize 45
                                            text_size 16
                                            text_color "#ffffff"
                                            text_hover_color "#00b4d8"
                                            background "#0077b6"
                                            hover_background "#00b4d8"

                    # Return button
                    textbutton "RETURN BACK TO MAIN MENU":
                        action Return("return")
                        xsize 350
                        ysize 55
                        xalign 0.5
                        text_size 16
                        text_color "#ffffff"
                        text_hover_color "#00b4d8"
                        background "#023e8a"
                        hover_background "#0077b6"

            # Right side - Enhanced game details
            frame:
                xsize 280
                ysize 460
                background "#003366"
                padding (15, 15)

                vbox:
                    spacing 15
                    xalign 0.5

                    text "Game Details" size 18 xalign 0.5 color "#90e0ef"

                    # Game details area
                    frame:
                        xsize 250
                        ysize 380
                        background "#023e8a"
                        padding (15, 15)

                        vbox:
                            spacing 20

                            # Stats section
                            vbox:
                                spacing 8
                                text "📊 Statistics" size 14 color "#caf0f8"
                                text "• Available Games: [len(available_games)]" size 11 color "#90e0ef"
                                text "• System Status: Online" size 11 color "#90e0ef"
                                text "• Security: Active" size 11 color "#90e0ef"

                            # Features section
                            vbox:
                                spacing 8
                                text "✨ Features" size 14 color "#caf0f8"
                                text "• Visual Novel Engine" size 11 color "#90e0ef"
                                text "• Multiple Storylines" size 11 color "#90e0ef"
                                text "• Character Development" size 11 color "#90e0ef"
                                text "• Save System" size 11 color "#90e0ef"

                            # Tips section
                            vbox:
                                spacing 8
                                text "💡 Tips" size 14 color "#caf0f8"
                                text "Hover over games for previews" size 11 color "#90e0ef"
                                text "Use Gaming Settings for options" size 11 color "#90e0ef"
                                text "Check Security Status regularly" size 11 color "#90e0ef"

# Safe game launcher with conflict resolution - Universal Game Router
label safe_game_launcher:
    # Get available games
    python:
        available_games = get_available_games()

    # Check if games are available
    if len(available_games) == 0:
        "No games found in the separate games directories."
        "Please check that the game files are properly placed."
        return

    # Directly show the game selection screen like in the image
    call screen game_selection(available_games)

    # Handle the returned selection
    python:
        selected_game = _return

    if selected_game == "return":
        return
    elif selected_game == "settings":
        jump router_settings
    elif selected_game == "advanced":
        jump advanced_router_options
    elif selected_game:
        # Launch the selected game
        python:
            entry_label = route_to_game(selected_game)

        if entry_label:
            # Set appropriate game context
            if selected_game == "lumetric":
                $ set_game_context("lumetric")
            elif selected_game == "netcode":
                $ set_game_context("netcode")
            elif selected_game == "bearwithus":
                $ set_game_context("bearwithus")
            else:
                $ set_game_context(selected_game)

            jump expression entry_label
        else:
            "Error: Could not launch the selected game."
            jump safe_game_launcher
    else:
        jump safe_game_launcher

# Advanced router options
label advanced_router_options:

    menu:
        "⚙️ Advanced Options"

        "🎮 Launch Games":
            call universal_game_launcher
            jump safe_game_launcher

        "📊 System Status":
            jump comprehensive_system_status

        "🔙 Back":
            jump safe_game_launcher

# Router settings
label router_settings:

    menu:
        "🔧 Settings"

        "🎮 Gaming Settings":
            call screen safe_gaming_settings
            jump router_settings

        "🛡️ Security Settings":
            call screen safe_anticheat_status
            jump router_settings

        "📋 Preferences":
            call screen preferences
            jump router_settings

        "🔙 Back":
            jump safe_game_launcher

# Original game launcher (moved to Advanced Options)
label universal_game_launcher:
    python:
        available_games = get_available_games()

    if len(available_games) == 0:
        "No games found in the separate games directories."
        "Please check that the game files are properly placed."
        return

    "Universal Game Launcher"
    "Detected [len(available_games)] available games:"
    ""

    python:
        game_menu_items = []
        for game in available_games:
            game_menu_items.append((game["name"], game["key"]))
        game_menu_items.append(("Return to Main Menu", "return"))

    call screen game_selection_screen(game_menu_items)

    # Return the selected game key
    python:
        selected_game = _return

    if selected_game == "return":
        return

    # Check if selected_game is valid
    if selected_game is None:
        "Error: No game was selected."
        "Please try selecting a game again."
        jump universal_game_launcher

    python:
        entry_label = route_to_game(selected_game)

    if entry_label:
        # Check if the game exists in game_routes before accessing it
        python:
            game_display_name = "Unknown Game"
            if selected_game in game_routes:
                game_display_name = game_routes[selected_game]['display_name']
            else:
                game_display_name = selected_game.title()

        "Launching [game_display_name]..."

        # Set appropriate game context to resolve conflicts
        if selected_game == "lumetric":
            $ set_game_context("lumetric")
        elif selected_game == "netcode":
            $ set_game_context("netcode")
        elif selected_game == "bearwithus":
            $ set_game_context("bearwithus")
        else:
            $ set_game_context(selected_game)

        # Record the game launch for anti-cheat
        $ safe_record_user_action("game_launch", selected_game)

        # Jump to the game
        jump expression entry_label
    else:
        # Safe error handling when game routing fails
        python:
            error_game_name = "Unknown Game"
            if selected_game and selected_game in game_routes:
                error_game_name = game_routes[selected_game]['display_name']
            elif selected_game:
                error_game_name = selected_game.title()

        "Error: Could not launch [error_game_name]."
        "The game files may be missing or corrupted."
        "Selected game key: [selected_game]"
        jump universal_game_launcher

# Enhanced game selection that integrates with existing system
label enhanced_game_selection_with_routing:
    menu:
        "Game Selection - Enhanced with Routing"
        
        "Launch games through Universal Router":
            jump safe_game_launcher
        
        "Launch integrated games directly":
            jump enhanced_game_selection_safe
        
        "View game routing status":
            jump display_routing_status
        
        "Return to main menu":
            return

# Display routing status
label display_routing_status:
    "Game Routing System Status"
    ""
    
    python:
        available_games = get_available_games()
        total_games = len(game_routes)
        available_count = len(available_games)
    
    "Total configured games: [total_games]"
    "Available games: [available_count]"
    ""
    
    if available_count > 0:
        "✅ Available Games:"
        python:
            for game in available_games:
                renpy.say(None, f"• {game['name']} - {game['description']}")
    
    python:
        missing_games = []
        for game_key, route in game_routes.items():
            if not check_game_exists(game_key):
                missing_games.append(route["display_name"])
    
    if len(missing_games) > 0:
        ""
        "❌ Missing Games:"
        python:
            for game_name in missing_games:
                renpy.say(None, f"• {game_name}")
    
    ""
    "Routing system allows games to coexist without label conflicts."
    "All separate game files are preserved and accessible."
    
    return

# Integration with existing anti-cheat system
init python:
    def safe_record_game_route(game_key, action):
        """Record game routing actions for anti-cheat system"""
        try:
            if hasattr(renpy.store, 'safe_record_user_action'):
                safe_record_user_action(f"route_{action}", game_key)
        except:
            pass

# Comprehensive system status display
label comprehensive_system_status:
    "🔍 Comprehensive System Status"
    ""

    # Game availability status
    python:
        available_games = get_available_games()
        total_games = len(game_routes)
        available_count = len(available_games)

    "📊 Game Availability:"
    "• Total configured games: [total_games]"
    "• Available games: [available_count]"
    ""

    # Conflict resolution status
    python:
        current_context = get_game_context()

    "🛡️ Conflict Resolution Status:"
    if current_context:
        "• Current game context: [current_context]"
    else:
        "• Current game context: None"

    "• Conflict resolution method: Label redirection"
    "• Original separate game files: Preserved with unique names"

    ""

    # Label conflict status
    "🏷️ Known Label Conflicts:"
    python:
        conflict_labels = ["whisker_and_bean_scene", "howling_hearth_scene", "binary_bistro_scene", "act_4_conclusion"]
        for label in conflict_labels:
            renpy.say(None, f"• {label} - Redirected ✅")

    ""

    # Available launchers
    "🚀 Available Launchers:"
    "• Universal Game Router (Separate games)"

    menu:
        "System Status Options"

        "🔄 Refresh Status":
            jump comprehensive_system_status

        "🎮 Test Game Launching":
            jump test_all_launchers

        "🔧 Test Conflicts":
            jump test_conflict_resolution

        "🔙 Back to Main Menu":
            jump enhanced_game_selection_safe

# Test all launcher systems
label test_all_launchers:
    "🧪 Launcher System Testing"
    ""

    menu:
        "Which launcher would you like to test?"

        "Test Universal Router":
            call safe_game_launcher
            jump test_all_launchers

        "Test Conflict Resolution":
            call test_conflict_resolution
            jump test_all_launchers

        "Test Namespace Launcher":
            call namespace_game_launcher
            jump test_all_launchers

        "Back to Status":
            jump comprehensive_system_status
