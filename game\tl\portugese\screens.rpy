﻿# TODO: Translation updated at 2025-01-22 05:05

translate portugese strings:

    # game/screens.rpy:354
    old "Back"
    new ""

    # game/screens.rpy:355
    old "History"
    new ""

    # game/screens.rpy:356
    old "Skip"
    new ""

    # game/screens.rpy:357
    old "Auto"
    new ""

    # game/screens.rpy:358
    old "Save"
    new ""

    # game/screens.rpy:359
    old "Q.Save"
    new ""

    # game/screens.rpy:360
    old "Q.Load"
    new ""

    # game/screens.rpy:361
    old "Prefs"
    new ""

    # game/screens.rpy:402
    old "Start"
    new ""

    # game/screens.rpy:410
    old "Load"
    new ""

    # game/screens.rpy:412
    old "Preferences"
    new ""

    # game/screens.rpy:416
    old "End Replay"
    new ""

    # game/screens.rpy:420
    old "Main Menu"
    new ""

    # game/screens.rpy:422
    old "About"
    new ""

    # game/screens.rpy:427
    old "Help"
    new ""

    # game/screens.rpy:433
    old "Quit"
    new ""

    # game/screens.rpy:578
    old "Return"
    new ""

    # game/screens.rpy:662
    old "Version [config.version!t]\n"
    new ""

    # game/screens.rpy:668
    old "here is my {a=https://zodiaczvr.itch.io/visual-novel-netcode-the-protogen-and-more}itch.io {/a}and my social media {a=https://portaly.cc/zodiaczvr}portaly.cc{/a} and Ran'py [renpy.version_only].\n\n[renpy.license!t]"
    new ""

    # game/screens.rpy:703
    old "Page {}"
    new ""

    # game/screens.rpy:703
    old "Automatic saves"
    new ""

    # game/screens.rpy:703
    old "Quick saves"
    new ""

    # game/screens.rpy:745
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new ""

    # game/screens.rpy:745
    old "empty slot"
    new ""

    # game/screens.rpy:765
    old "<"
    new ""

    # game/screens.rpy:769
    old "{#auto_page}A"
    new ""

    # game/screens.rpy:772
    old "{#quick_page}Q"
    new ""

    # game/screens.rpy:778
    old ">"
    new ""

    # game/screens.rpy:783
    old "Upload Sync"
    new ""

    # game/screens.rpy:787
    old "Download Sync"
    new ""

    # game/screens.rpy:846
    old "Display"
    new ""

    # game/screens.rpy:847
    old "Window"
    new ""

    # game/screens.rpy:848
    old "Fullscreen"
    new ""

    # game/screens.rpy:853
    old "Unseen Text"
    new ""

    # game/screens.rpy:854
    old "After Choices"
    new ""

    # game/screens.rpy:855
    old "Transitions"
    new ""

    # game/screens.rpy:868
    old "Text Speed"
    new ""

    # game/screens.rpy:872
    old "Auto-Forward Time"
    new ""

    # game/screens.rpy:879
    old "Music Volume"
    new ""

    # game/screens.rpy:886
    old "Sound Volume"
    new ""

    # game/screens.rpy:892
    old "Test"
    new ""

    # game/screens.rpy:896
    old "Voice Volume"
    new ""

    # game/screens.rpy:907
    old "Mute All"
    new ""

    # game/screens.rpy:1026
    old "The dialogue history is empty."
    new ""

    # game/screens.rpy:1094
    old "Keyboard"
    new ""

    # game/screens.rpy:1095
    old "Mouse"
    new ""

    # game/screens.rpy:1098
    old "Gamepad"
    new ""

    # game/screens.rpy:1111
    old "Enter"
    new ""

    # game/screens.rpy:1112
    old "Advances dialogue and activates the interface."
    new ""

    # game/screens.rpy:1115
    old "Space"
    new ""

    # game/screens.rpy:1116
    old "Advances dialogue without selecting choices."
    new ""

    # game/screens.rpy:1119
    old "Arrow Keys"
    new ""

    # game/screens.rpy:1120
    old "Navigate the interface."
    new ""

    # game/screens.rpy:1123
    old "Escape"
    new ""

    # game/screens.rpy:1124
    old "Accesses the game menu."
    new ""

    # game/screens.rpy:1127
    old "Ctrl"
    new ""

    # game/screens.rpy:1128
    old "Skips dialogue while held down."
    new ""

    # game/screens.rpy:1131
    old "Tab"
    new ""

    # game/screens.rpy:1132
    old "Toggles dialogue skipping."
    new ""

    # game/screens.rpy:1135
    old "Page Up"
    new ""

    # game/screens.rpy:1136
    old "Rolls back to earlier dialogue."
    new ""

    # game/screens.rpy:1139
    old "Page Down"
    new ""

    # game/screens.rpy:1140
    old "Rolls forward to later dialogue."
    new ""

    # game/screens.rpy:1144
    old "Hides the user interface."
    new ""

    # game/screens.rpy:1148
    old "Takes a screenshot."
    new ""

    # game/screens.rpy:1152
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    new ""

    # game/screens.rpy:1156
    old "Opens the accessibility menu."
    new ""

    # game/screens.rpy:1162
    old "Left Click"
    new ""

    # game/screens.rpy:1166
    old "Middle Click"
    new ""

    # game/screens.rpy:1170
    old "Right Click"
    new ""

    # game/screens.rpy:1174
    old "Mouse Wheel Up"
    new ""

    # game/screens.rpy:1178
    old "Mouse Wheel Down"
    new ""

    # game/screens.rpy:1185
    old "Right Trigger\nA/Bottom Button"
    new ""

    # game/screens.rpy:1189
    old "Left Trigger\nLeft Shoulder"
    new ""

    # game/screens.rpy:1193
    old "Right Shoulder"
    new ""

    # game/screens.rpy:1197
    old "D-Pad, Sticks"
    new ""

    # game/screens.rpy:1201
    old "Start, Guide, B/Right Button"
    new ""

    # game/screens.rpy:1205
    old "Y/Top Button"
    new ""

    # game/screens.rpy:1208
    old "Calibrate"
    new ""

    # game/screens.rpy:1273
    old "Yes"
    new ""

    # game/screens.rpy:1274
    old "No"
    new ""

    # game/screens.rpy:1320
    old "Skipping"
    new ""

    # game/screens.rpy:1632
    old "Menu"
    new ""

