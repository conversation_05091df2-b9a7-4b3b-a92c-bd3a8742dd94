{
"@path": "res://addons/dialogic/Resources/character.gd",
"@subpath": NodePath(""),
"_translation_id": "",
"color": Color(1, 1, 1, 1),
"custom_info": {
"sound_mood_default": "",
"sound_moods": {},
"style": ""
},
"default_portrait": "donations",
"description": "",
"display_name": "donations",
"mirror": false,
"nicknames": [""],
"offset": Vector2(0, 0),
"portraits": {
"donations": {
"export_overrides": {
"image": "\"res://asset/characters/donations/donate.jpg\""
},
"ignore_char_scale": true,
"mirror": false,
"offset": Vector2(0, 0),
"scale": 0.5,
"scene": ""
}
},
"scale": 1.0
}