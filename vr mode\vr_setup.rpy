# VR Mode Setup for Quest Headsets
# Automatically detects Quest VR and converts Android packages to 3D VR mode

# VR Mode Configuration
init -20 python:
    # VR detection and setup
    def setup_vr_mode():
        """Setup VR mode for Quest headsets"""
        try:
            # Enable VR-specific OpenGL settings
            config.gl_enable = True
            config.gl_resize = True
            config.gl_powersave = False  # Disable power saving for VR performance
            
            # VR display configuration
            config.screen_width = 2880   # Quest 2/3 combined eye resolution
            config.screen_height = 1700  # Quest 2/3 resolution height
            
            # VR performance optimizations
            config.image_cache_size = 32  # Larger cache for VR
            config.sound_cache_size = 16
            config.predict_statements = 50  # Better prediction for VR
            
            # VR interaction settings
            config.mouse_hide_time = 0  # Always show cursor/pointer in VR
            config.gestures = True      # Enable hand tracking
            
            return True
        except Exception as e:
            # VR setup failed, fallback to mobile mode
            return False
    
    # Quest VR detection
    def detect_quest_vr():
        """Detect if running on Quest VR headset"""
        if not renpy.variant("android"):
            return False
            
        try:
            import os
            import subprocess
            
            # Quest-specific detection methods
            quest_indicators = [
                "quest",
                "oculus", 
                "meta",
                "vr"
            ]
            
            # Method 1: Check device model
            try:
                model = subprocess.check_output(
                    ['getprop', 'ro.product.model'], 
                    stderr=subprocess.DEVNULL, 
                    universal_newlines=True
                ).lower().strip()
                
                for indicator in quest_indicators:
                    if indicator in model:
                        return True
            except:
                pass
            
            # Method 2: Check manufacturer
            try:
                manufacturer = subprocess.check_output(
                    ['getprop', 'ro.product.manufacturer'], 
                    stderr=subprocess.DEVNULL, 
                    universal_newlines=True
                ).lower().strip()
                
                if "oculus" in manufacturer or "meta" in manufacturer:
                    return True
            except:
                pass
            
            # Method 3: Check for VR-specific system properties
            vr_props = [
                'ro.hardware.vr',
                'ro.oculus.build',
                'ro.meta.build'
            ]
            
            for prop in vr_props:
                try:
                    result = subprocess.check_output(
                        ['getprop', prop], 
                        stderr=subprocess.DEVNULL, 
                        universal_newlines=True
                    ).strip()
                    if result and result != "":
                        return True
                except:
                    pass
            
            # Method 4: Check environment variables
            vr_env_vars = [
                'VR_MODE',
                'OCULUS_VR', 
                'QUEST_VR',
                'META_VR'
            ]
            
            for var in vr_env_vars:
                if os.environ.get(var):
                    return True
            
            # Method 5: Check for VR-specific directories
            vr_dirs = [
                '/system/etc/vr',
                '/vendor/etc/vr',
                '/system/lib/libovr',
                '/vendor/lib/liboculus'
            ]
            
            for vr_dir in vr_dirs:
                if os.path.exists(vr_dir):
                    return True
            
            return False
            
        except Exception as e:
            # Detection failed, assume not VR
            return False
    
    # Auto VR mode activation
    def auto_enable_vr():
        """Automatically enable VR mode if Quest detected"""
        if detect_quest_vr():
            if setup_vr_mode():
                store.vr_mode_active = True
                store.current_platform = "vr"
                return True
        return False

# VR Mode Transforms and Effects
init python:
    # VR-specific transforms for 3D depth effect
    transform vr_depth_near:
        zoom 1.1
        alpha 0.95
        
    transform vr_depth_mid:
        zoom 1.0
        alpha 1.0
        
    transform vr_depth_far:
        zoom 0.9
        alpha 0.85
    
    # VR button hover effect
    transform vr_button_hover:
        zoom 1.0
        easein 0.2 zoom 1.05
        easeout 0.2 zoom 1.0
    
    # VR text floating effect
    transform vr_text_float:
        yoffset 0
        easein 2.0 yoffset -5
        easeout 2.0 yoffset 0
        repeat

# VR Mode Styles
init python:
    if renpy.variant("android"):
        # VR-specific button styles
        style.vr_button = Style(style.button)
        style.vr_button.minimum = (120, 80)
        style.vr_button.padding = (20, 20, 20, 20)
        style.vr_button.margin = (10, 10, 10, 10)
        style.vr_button.background = "#001a33"
        style.vr_button.hover_background = "#003366"
        
        # VR text styles
        style.vr_text = Style(style.default)
        style.vr_text.size = 24
        style.vr_text.color = "#00ff88"
        style.vr_text.outlines = [(2, "#000000", 0, 0)]
        
        style.vr_title = Style(style.default)
        style.vr_title.size = 36
        style.vr_title.color = "#00ffff"
        style.vr_title.bold = True
        style.vr_title.outlines = [(3, "#000000", 0, 0)]

# VR Mode Initialization
label vr_mode_init:
    """Initialize VR mode if Quest detected"""
    
    python:
        # Check if we should auto-enable VR
        if renpy.variant("android") and not hasattr(store, 'vr_checked'):
            store.vr_checked = True
            
            if auto_enable_vr():
                renpy.say(None, "🥽 Quest VR Detected! Switching to VR Mode...")
                renpy.say(None, "VR Mode Activated - Optimized for 3D viewing")
            else:
                # Not VR, continue with mobile mode
                store.current_platform = "mobile"
    
    return

# VR Mode Status Screen
screen vr_status():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#001122"
        padding (30, 30)
        
        vbox:
            spacing 20
            xalign 0.5
            
            text "🥽 VR Mode Status" size 28 xalign 0.5 color "#00ffff"
            
            if vr_mode_active:
                text "✅ VR Mode: ACTIVE" size 18 color "#00ff88"
                text "🎮 Platform: Quest VR" size 16 color "#ffffff"
                text "📱 Android Package: Converted to VR" size 16 color "#ffffff"
                text "🖼️ Resolution: 2880x1700 (Quest optimized)" size 16 color "#ffffff"
                text "👁️ 3D Depth: Enabled" size 16 color "#ffffff"
                text "👋 Hand Tracking: Available" size 16 color "#ffffff"
            else:
                text "❌ VR Mode: INACTIVE" size 18 color "#ff6666"
                text "📱 Platform: Mobile" size 16 color "#ffffff"
                text "ℹ️ VR not detected or unavailable" size 16 color "#ffffff"
            
            hbox:
                xalign 0.5
                spacing 20
                
                if vr_mode_active:
                    textbutton "Disable VR":
                        action [Function(disable_vr_mode), Return()]
                        xsize 150
                        ysize 50
                else:
                    if renpy.variant("android"):
                        textbutton "Enable VR":
                            action [Function(enable_vr_mode), Return()]
                            xsize 150
                            ysize 50
                
                textbutton "Close":
                    action Return()
                    xsize 100
                    ysize 50

# VR Mode Auto-Detection on Startup
init python:
    # Add VR detection to startup callbacks
    def vr_startup_check():
        """Check for VR on game startup"""
        if renpy.variant("android") and not hasattr(store, 'startup_vr_checked'):
            store.startup_vr_checked = True
            
            # Auto-detect and enable VR if Quest found
            if detect_quest_vr():
                auto_enable_vr()
    
    # Register startup callback
    config.start_callbacks.append(vr_startup_check)
