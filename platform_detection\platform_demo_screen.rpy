## Platform Detection Demo Screen
## Demonstrates the platform detection system functionality

# Demo screen to show platform detection results
screen platform_detection_demo():
    tag menu
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        
        vbox:
            spacing 20
            
            # Title
            text "Platform Detection System Demo" size 30 xalign 0.5
            
            # System Status
            frame:
                xfill True
                vbox:
                    spacing 10
                    
                    text "System Status" size 24 color "#4CAF50"
                    
                    if platform_system_operational:
                        text "✓ Platform System: OPERATIONAL" color "#4CAF50"
                    else:
                        text "✗ Platform System: NOT OPERATIONAL" color "#F44336"
                    
                    if platform_system_secure:
                        text "✓ Security Status: SECURE" color "#4CAF50"
                    else:
                        text "⚠ Security Status: WARNINGS PRESENT" color "#FF9800"
            
            # Platform Detection Results
            frame:
                xfill True
                vbox:
                    spacing 10
                    
                    text "Platform Detection Results" size 24 color "#2196F3"
                    
                    text "Primary Platform: [detected_distribution_platform]" color "#333"
                    
                    if 'platform_detector' in globals():
                        $ all_platforms = platform_detector.detected_platforms
                        if all_platforms:
                            text "All Detected Platforms:"
                            for platform in all_platforms:
                                text "  • [platform]" color "#666"
                        else:
                            text "No platforms detected (Direct download)" color "#666"
                    else:
                        text "Platform detector not available" color "#F44336"
            
            # Distribution Information
            frame:
                xfill True
                vbox:
                    spacing 10
                    
                    text "Distribution Information" size 24 color "#9C27B0"
                    
                    if distribution_verified:
                        text "✓ Distribution Source: VERIFIED" color "#4CAF50"
                    else:
                        text "✗ Distribution Source: NOT VERIFIED" color "#F44336"
                    
                    if 'distribution_tracker' in globals():
                        $ dist_info = distribution_tracker.get_distribution_info()
                        if dist_info:
                            text "Detection Method: [dist_info.get('detected_platform', 'Unknown')]"
                            text "Creation Time: [dist_info.get('creation_timestamp', 'Unknown')]"
                    
            # Compliance Status
            frame:
                xfill True
                vbox:
                    spacing 10
                    
                    text "Compliance Status" size 24 color "#FF5722"
                    
                    if platform_compliant:
                        text "✓ Platform Compliance: PASSED" color "#4CAF50"
                    else:
                        text "✗ Platform Compliance: VIOLATIONS DETECTED" color "#F44336"
                    
                    if compliance_violations > 0:
                        text "Violations: [compliance_violations]" color "#F44336"
                    
                    if compliance_warnings > 0:
                        text "Warnings: [compliance_warnings]" color "#FF9800"
            
            # Security Information
            frame:
                xfill True
                vbox:
                    spacing 10
                    
                    text "Security Information" size 24 color "#795548"
                    
                    if platform_secure:
                        text "✓ Platform Security: SECURE" color "#4CAF50"
                    else:
                        text "⚠ Platform Security: ISSUES DETECTED" color "#FF9800"
                    
                    if platform_violations > 0:
                        text "Security Violations: [platform_violations]" color "#F44336"
                    else:
                        text "Security Violations: 0" color "#4CAF50"
            
            # Action Buttons
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "Refresh Detection" action Function(refresh_platform_detection)
                textbutton "Run Tests" action Function(run_platform_tests)
                textbutton "View Details" action Show("platform_details_screen")
                textbutton "Close" action Return()

# Detailed platform information screen
screen platform_details_screen():
    tag menu
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 700
        
        vbox:
            spacing 15
            
            text "Detailed Platform Information" size 28 xalign 0.5
            
            viewport:
                scrollbars "vertical"
                mousewheel True
                xfill True
                yfill True
                
                vbox:
                    spacing 15
                    
                    # Platform Detection Details
                    if 'platform_detector' in globals():
                        frame:
                            xfill True
                            vbox:
                                spacing 10
                                text "Platform Detection Details" size 20 color "#2196F3"
                                
                                $ platform_info = platform_detector.get_platform_info()
                                for platform, details in platform_info.items():
                                    text "[platform.upper()]:" color "#333"
                                    for key, value in details.items():
                                        text "  [key]: [value]" size 16 color "#666"
                    
                    # Distribution Details
                    if 'distribution_tracker' in globals():
                        frame:
                            xfill True
                            vbox:
                                spacing 10
                                text "Distribution Details" size 20 color "#9C27B0"
                                
                                $ dist_info = distribution_tracker.get_distribution_info()
                                if dist_info:
                                    for key, value in dist_info.items():
                                        text "[key]: [value]" size 16 color "#666"
                    
                    # System Status Details
                    if 'platform_system' in globals():
                        frame:
                            xfill True
                            vbox:
                                spacing 10
                                text "System Status Details" size 20 color "#4CAF50"
                                
                                $ system_status = platform_system.get_system_status()
                                if system_status:
                                    for category, details in system_status.items():
                                        text "[category.upper()]:" color "#333"
                                        if isinstance(details, dict):
                                            for key, value in details.items():
                                                text "  [key]: [value]" size 16 color "#666"
                                        else:
                                            text "  [details]" size 16 color "#666"
                    
                    # Test Results
                    if 'platform_tester' in globals():
                        frame:
                            xfill True
                            vbox:
                                spacing 10
                                text "Test Results" size 20 color "#FF5722"
                                
                                $ test_data = platform_tester.get_test_results()
                                if test_data.get('total_tests', 0) > 0:
                                    text "Total Tests: [test_data['total_tests']]"
                                    text "Passed: [test_data['passed_tests']]"
                                    text "Failed: [test_data['failed_tests']]"
                                    
                                    if test_data.get('test_details'):
                                        text "Recent Test Details:" color "#333"
                                        for test in test_data['test_details'][-5:]:  # Show last 5 tests
                                            if test['status'] == 'PASSED':
                                                text "  ✓ [test['test']]: [test['message']]" size 14 color "#4CAF50"
                                            else:
                                                text "  ✗ [test['test']]: [test['message']]" size 14 color "#F44336"
                                else:
                                    text "No tests have been run yet."
            
            textbutton "Close" action Hide("platform_details_screen") xalign 0.5

# Functions for the demo screen
init python:
    def refresh_platform_detection():
        """Refresh platform detection"""
        try:
            if 'platform_detector' in globals():
                platform_detector.detect_all_platforms()
                renpy.notify("Platform detection refreshed")
            else:
                renpy.notify("Platform detector not available")
        except Exception as e:
            renpy.notify(f"Error refreshing detection: {str(e)}")
    
    def run_platform_tests():
        """Run platform system tests"""
        try:
            if 'platform_tester' in globals():
                success = platform_tester.run_comprehensive_tests()
                if success:
                    renpy.notify("Platform tests completed successfully")
                else:
                    renpy.notify("Platform tests completed with failures")
            else:
                renpy.notify("Platform tester not available")
        except Exception as e:
            renpy.notify(f"Error running tests: {str(e)}")

# Add a menu item to access the demo (optional)
# You can add this to your main menu or call it from anywhere in your game

label platform_detection_demo_label:
    "Welcome to the Platform Detection System Demo!"
    "This system automatically detects which platform your game was downloaded from."
    "It also tracks the distribution source and ensures compliance with platform terms."
    
    call screen platform_detection_demo
    
    "Demo completed. The platform detection system is now active and monitoring your game."
    
    return

# Quick access function for testing
define quick_platform_demo = Function(renpy.call_screen, "platform_detection_demo")
