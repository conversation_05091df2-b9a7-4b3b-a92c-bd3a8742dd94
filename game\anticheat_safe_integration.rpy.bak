## Safe Anti-Cheat Integration
## Provides safe initialization and fallback for anti-cheat system

init python:
    import os
    import time
    
    # Safe anti-cheat state
    safe_anticheat_state = {
        'initialized': False,
        'full_system_available': False,
        'basic_protection_active': False,
        'violations': 0,
        'session_id': None,
        'last_check': 0,
        'banned': False,
        'ban_reason': None
    }
    
    # Check if full anti-cheat system is available
    anticheat_files = [
        'anti-cheat/anticheat_core.rpy',
        'anti-cheat/anticheat_engine_ui.rpy',
        'anti-cheat/anticheat_ui.rpy'
    ]
    
    full_anticheat_available = all(os.path.exists(f) for f in anticheat_files)
    safe_anticheat_state['full_system_available'] = full_anticheat_available
    
    if full_anticheat_available:
        renpy.log("Full anti-cheat system detected")
    else:
        renpy.log("Using safe anti-cheat fallback system")

# Safe anti-cheat initialization label
label initialize_anticheat_safe:
    
    python:
        try:
            # Check if full anti-cheat system is available
            if safe_anticheat_state['full_system_available']:
                # Try to call the full anti-cheat initialization
                if renpy.has_label("initialize_anticheat_ui"):
                    renpy.call("initialize_anticheat_ui")
                    safe_anticheat_state['initialized'] = True
                    renpy.log("Full anti-cheat system initialized")
                else:
                    # Fall back to basic protection
                    initialize_basic_protection()
            else:
                # Use basic protection system
                initialize_basic_protection()
                
        except Exception as e:
            renpy.log(f"Anti-cheat initialization error: {str(e)}")
            # Always fall back to basic protection
            initialize_basic_protection()
    
    return

# Basic protection system (fallback)
init python:
    def initialize_basic_protection():
        """Initialize basic protection when full anti-cheat is not available"""
        try:
            # Generate basic session ID
            import random
            import hashlib
            session_data = f"{time.time()}_{random.randint(1000, 9999)}"
            safe_anticheat_state['session_id'] = hashlib.md5(session_data.encode()).hexdigest()[:16]
            
            # Set basic protection as active
            safe_anticheat_state['basic_protection_active'] = True
            safe_anticheat_state['initialized'] = True
            safe_anticheat_state['last_check'] = time.time()
            
            renpy.log("Basic anti-cheat protection initialized")
            renpy.notify("🛡️ Security system active")
            
        except Exception as e:
            renpy.log(f"Basic protection initialization error: {str(e)}")
            # Even if this fails, mark as initialized to prevent crashes
            safe_anticheat_state['initialized'] = True

# Safe anti-cheat status screen
screen safe_anticheat_status():
    
    if config.developer:
        frame:
            xpos 10
            ypos 10
            xsize 350
            ysize 200
            background "#000000AA"
            
            vbox:
                spacing 5
                
                if safe_anticheat_state['full_system_available']:
                    text "Anti-Cheat: Full System" size 14 color "#00FF00"
                else:
                    text "Anti-Cheat: Basic Protection" size 14 color "#FFAA00"
                
                text "Initialized: {}".format(safe_anticheat_state['initialized']) size 12
                text "Violations: {}".format(safe_anticheat_state['violations']) size 12
                text "Session: {}".format(safe_anticheat_state['session_id'][:8] + "..." if safe_anticheat_state['session_id'] else "None") size 12
                
                if safe_anticheat_state['banned']:
                    text "STATUS: BANNED" size 12 color "#FF0000"
                else:
                    text "STATUS: ACTIVE" size 12 color "#00FF00"

# Safe ban checking
init python:
    def check_safe_ban_status():
        """Check if user is banned (safe version)"""
        try:
            # Check persistent ban status
            if hasattr(persistent, 'anticheat_banned') and persistent.anticheat_banned:
                safe_anticheat_state['banned'] = True
                safe_anticheat_state['ban_reason'] = getattr(persistent, 'anticheat_ban_reason', 'Unknown violation')
                return True
            
            return False
            
        except Exception as e:
            renpy.log(f"Ban status check error: {str(e)}")
            return False

# Safe ban screen (fallback)
screen safe_ban_screen(reason="Unknown violation", days_remaining=30):
    
    modal True
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            text "⚠️" size 80 color "#FF0000" xalign 0.5
            
            text "ACCESS DENIED" size 40 color "#FF0000" bold True xalign 0.5
            text "Anti-Cheat Violation Detected" size 20 color "#FFFFFF" xalign 0.5
            
            frame:
                background "#2E2E2E"
                padding (30, 20)
                xalign 0.5
                
                vbox:
                    spacing 15
                    
                    text "Violation Reason:" size 16 color "#FF9800"
                    text "[reason]" size 14 color "#FFFFFF"
                    
                    text "Ban Duration:" size 16 color "#FF9800"
                    text "[days_remaining] days remaining" size 14 color "#FFFFFF"
            
            text "This ban cannot be appealed." size 14 color "#CCCCCC" xalign 0.5
            
            textbutton "Exit Game" action Quit() xalign 0.5 text_size 20

# Safe legal compliance check
label check_legal_compliance_safe:
    
    python:
        try:
            # Check if full legal system is available
            if os.path.exists('anti-cheat/legal_protection.rpy'):
                if renpy.has_label("check_legal_compliance"):
                    renpy.call("check_legal_compliance")
                else:
                    renpy.log("Legal compliance check not available")
            else:
                renpy.log("Legal protection system not found - using basic compliance")
                
        except Exception as e:
            renpy.log(f"Legal compliance check error: {str(e)}")
    
    return

# Enhanced game selection (safe version)
label enhanced_game_selection_safe:
    
    # Show safe anti-cheat status in developer mode
    if config.developer:
        show screen safe_anticheat_status
    
    # Check ban status
    if check_safe_ban_status():
        call screen safe_ban_screen(
            reason=safe_anticheat_state.get('ban_reason', 'Unknown violation'),
            days_remaining=30
        )
        return
    
    # Show game selection menu
    scene black
    
    menu:
        "🎮 Select your visual novel experience:"

        "Visual novel netcode the protogen":
            $ safe_record_user_action("game_selection", "netcode_protogen")
            jump Netcode_the_protogen_bedroom_1

        "Visual novel Lumetric":
            $ safe_record_user_action("game_selection", "lumetric")
            jump Lumetric_1

        "Visual novel BearWithUs":
            $ safe_record_user_action("game_selection", "bearwithus")
            jump BearWithUs_1

        "🌐 Universal Game Router":
            $ safe_record_user_action("menu_access", "universal_router")
            jump safe_game_launcher

        "⚙️ Advanced Options":
            menu:
                "Advanced Game Selection Options"

                "🔧 Namespace-Aware Launcher":
                    $ safe_record_user_action("menu_access", "namespace_launcher")
                    jump namespace_game_launcher

                "📊 System Status":
                    $ safe_record_user_action("menu_access", "system_status")
                    jump comprehensive_system_status

                "🔧 Test Conflict Resolution":
                    $ safe_record_user_action("menu_access", "conflict_testing")
                    jump test_conflict_resolution

                "📥 External Content Integration":
                    $ safe_record_user_action("menu_access", "external_integration")
                    jump integrate_external_netcode_content

                "🧪 System Test":
                    $ safe_record_user_action("menu_access", "system_test")
                    jump system_test_main

                "🔙 Back":
                    jump enhanced_game_selection_safe

        "🔧 Settings" if config.developer:
            jump safe_settings_menu

        "❌ Exit":
            return

# Safe user action recording
init python:
    def safe_record_user_action(action_type, details=""):
        """Safely record user actions"""
        try:
            if safe_anticheat_state['initialized']:
                current_time = time.time()
                safe_anticheat_state['last_check'] = current_time
                
                # Log the action
                renpy.log(f"User action: {action_type} - {details}")
                
                # If full anti-cheat is available, try to use it
                if safe_anticheat_state['full_system_available'] and 'anticheat_core' in globals():
                    try:
                        anticheat_core.record_user_action(action_type, details)
                    except:
                        pass  # Fail silently if full system has issues
                        
        except Exception as e:
            renpy.log(f"Safe action recording error: {str(e)}")

# Safe settings menu
label safe_settings_menu:
    
    menu:
        "🔧 Settings Menu"
        
        "🎮 Gaming Settings":
            call screen gaming_settings
            jump safe_settings_menu
        
        "🛡️ Security Status":
            call screen safe_security_status
            jump safe_settings_menu
        
        "🔙 Back":
            jump enhanced_game_selection_safe

# Safe security status screen
screen safe_security_status():
    
    tag menu
    
    frame:
        style "menu_frame"
        
        vbox:
            spacing 20
            
            text "Security System Status" style "menu_text_title"
            
            frame:
                style "menu_frame"
                xfill True
                
                vbox:
                    spacing 15
                    
                    if safe_anticheat_state['full_system_available']:
                        text "✓ Full Anti-Cheat System: Available" color "#00FF00"
                    else:
                        text "⚠ Basic Protection: Active" color "#FFAA00"
                    
                    if safe_anticheat_state['initialized']:
                        text "✓ Security System: Initialized" color "#00FF00"
                    else:
                        text "✗ Security System: Not Initialized" color "#FF0000"
                    
                    text "Session ID: {}".format(safe_anticheat_state['session_id'][:16] + "..." if safe_anticheat_state['session_id'] else "None")
                    text "Violations: {}".format(safe_anticheat_state['violations'])
                    text "Last Check: {:.1f}s ago".format(time.time() - safe_anticheat_state['last_check'])
                    
                    if safe_anticheat_state['banned']:
                        text "Status: BANNED" color "#FF0000"
                        text "Reason: {}".format(safe_anticheat_state['ban_reason'])
                    else:
                        text "Status: SECURE" color "#00FF00"
            
            textbutton "Close" action Return() xalign 0.5

# Update the main script to use safe versions
label enhanced_game_selection:
    call enhanced_game_selection_safe
    return

# Game routing - these labels now properly route to the integrated games
# The actual game content is now in the main game directory

# Initialize safe anti-cheat on startup
init python:
    try:
        # Perform basic initialization checks
        if not safe_anticheat_state['initialized']:
            initialize_basic_protection()
    except Exception as e:
        renpy.log(f"Safe anti-cheat startup error: {str(e)}")
        # Ensure we're always marked as initialized to prevent crashes
        safe_anticheat_state['initialized'] = True
