## Platform Detection System - Testing and Validation
## Comprehensive testing suite for the platform detection system

init -40 python:
    import json
    import os
    from datetime import datetime

    # Test configuration
    TEST_CONFIG = {
        'enabled': True,
        'run_on_startup': False,  # Set to True for automatic testing
        'comprehensive_tests': True,
        'performance_tests': True,
        'security_tests': True,
        'integration_tests': True,
        'log_test_results': True
    }

    # Test results storage
    test_results = {
        'last_run': None,
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'test_details': [],
        'performance_metrics': {},
        'security_assessment': {}
    }

    class PlatformSystemTester:
        def __init__(self):
            self.test_suite = []
            self.current_test = None
            self.test_start_time = None
            
        def run_comprehensive_tests(self):
            """Run all platform system tests"""
            try:
                renpy.log("Starting comprehensive platform system tests...")
                
                # Reset test results
                test_results['last_run'] = datetime.now().isoformat()
                test_results['total_tests'] = 0
                test_results['passed_tests'] = 0
                test_results['failed_tests'] = 0
                test_results['test_details'] = []
                
                # Run test categories
                if TEST_CONFIG.get('comprehensive_tests', True):
                    self._run_basic_functionality_tests()
                    self._run_component_integration_tests()
                
                if TEST_CONFIG.get('performance_tests', True):
                    self._run_performance_tests()
                
                if TEST_CONFIG.get('security_tests', True):
                    self._run_security_tests()
                
                if TEST_CONFIG.get('integration_tests', True):
                    self._run_integration_tests()
                
                # Calculate final results
                success_rate = (test_results['passed_tests'] / test_results['total_tests']) * 100 if test_results['total_tests'] > 0 else 0
                
                renpy.log(f"Platform system tests completed: {test_results['passed_tests']}/{test_results['total_tests']} passed ({success_rate:.1f}%)")
                
                if TEST_CONFIG.get('log_test_results', True):
                    self._log_detailed_results()
                
                return success_rate >= 80  # 80% pass rate required
                
            except Exception as e:
                renpy.log(f"Platform system testing error: {str(e)}")
                return False
        
        def _run_basic_functionality_tests(self):
            """Test basic functionality of all components"""
            
            # Test 1: Platform Detector Availability
            self._start_test("Platform Detector Availability")
            if 'platform_detector' in globals() and platform_detector:
                self._pass_test("Platform detector is available")
            else:
                self._fail_test("Platform detector not available")
            
            # Test 2: Platform Detection
            self._start_test("Platform Detection")
            if 'platform_detector' in globals():
                detected = platform_detector.detect_all_platforms()
                if detected or platform_detector.get_primary_platform():
                    self._pass_test(f"Platform detection successful: {platform_detector.get_primary_platform()}")
                else:
                    self._pass_test("No platforms detected (valid for direct download)")
            else:
                self._fail_test("Platform detector not available for testing")
            
            # Test 3: Distribution Tracker
            self._start_test("Distribution Tracker")
            if 'distribution_tracker' in globals() and distribution_tracker:
                if distribution_tracker.verify_source_file() or distribution_tracker.create_source_file():
                    self._pass_test("Distribution tracker operational")
                else:
                    self._fail_test("Distribution tracker failed")
            else:
                self._fail_test("Distribution tracker not available")
            
            # Test 4: Platform Integrations
            self._start_test("Platform Integrations")
            if 'platform_integrations' in globals() and platform_integrations:
                status = platform_integrations.get_integration_status()
                if status.get('active_platform'):
                    self._pass_test(f"Platform integrations active: {status['active_platform']}")
                else:
                    self._pass_test("Platform integrations available (no active platform)")
            else:
                self._fail_test("Platform integrations not available")
            
            # Test 5: Terms Compliance
            self._start_test("Terms Compliance")
            if 'terms_compliance' in globals() and terms_compliance:
                if terms_compliance.is_compliant():
                    self._pass_test("Terms compliance check passed")
                else:
                    violations = len(terms_compliance.get_violations())
                    warnings = len(terms_compliance.get_warnings())
                    if violations == 0:
                        self._pass_test(f"Terms compliance passed with {warnings} warnings")
                    else:
                        self._fail_test(f"Terms compliance failed: {violations} violations")
            else:
                self._fail_test("Terms compliance not available")
            
            # Test 6: Anti-Cheat Integration
            self._start_test("Anti-Cheat Integration")
            if 'platform_anticheat' in globals() and platform_anticheat:
                if platform_anticheat.is_platform_secure():
                    self._pass_test("Anti-cheat integration secure")
                else:
                    violations = platform_anticheat.get_violation_count()
                    self._fail_test(f"Anti-cheat security issues: {violations} violations")
            else:
                self._fail_test("Anti-cheat integration not available")
        
        def _run_component_integration_tests(self):
            """Test integration between components"""
            
            # Test 7: Platform Detection -> Distribution Tracking
            self._start_test("Platform-Distribution Integration")
            if 'platform_detector' in globals() and 'distribution_tracker' in globals():
                platform_data = platform_detector.create_detection_report()
                if distribution_tracker.create_source_file(platform_data):
                    self._pass_test("Platform data successfully integrated with distribution tracking")
                else:
                    self._fail_test("Failed to integrate platform data with distribution tracking")
            else:
                self._fail_test("Required components not available for integration test")
            
            # Test 8: Platform Detection -> Integrations
            self._start_test("Platform-Integrations Integration")
            if 'platform_detector' in globals() and 'platform_integrations' in globals():
                primary_platform = platform_detector.get_primary_platform()
                if primary_platform and primary_platform != 'unknown':
                    features = platform_integrations.get_platform_features(primary_platform)
                    if features:
                        self._pass_test(f"Platform features loaded for {primary_platform}")
                    else:
                        self._pass_test(f"No specific features for {primary_platform} (acceptable)")
                else:
                    self._pass_test("No platform detected for feature integration (acceptable)")
            else:
                self._fail_test("Required components not available for integration test")
            
            # Test 9: Distribution -> Anti-Cheat
            self._start_test("Distribution-AntiCheat Integration")
            if 'distribution_tracker' in globals() and 'platform_anticheat' in globals():
                if distribution_state.get('source_verified', False):
                    if not distribution_state.get('tamper_detected', False):
                        self._pass_test("Distribution integrity verified by anti-cheat")
                    else:
                        self._fail_test("Distribution tampering detected by anti-cheat")
                else:
                    self._fail_test("Distribution not verified for anti-cheat integration")
            else:
                self._fail_test("Required components not available for integration test")
        
        def _run_performance_tests(self):
            """Test system performance"""
            
            # Test 10: Detection Speed
            self._start_test("Platform Detection Performance")
            if 'platform_detector' in globals():
                start_time = time.time()
                platform_detector.detect_all_platforms()
                detection_time = time.time() - start_time
                
                test_results['performance_metrics']['detection_time'] = detection_time
                
                if detection_time < 5.0:  # Should complete within 5 seconds
                    self._pass_test(f"Platform detection completed in {detection_time:.2f}s")
                else:
                    self._fail_test(f"Platform detection too slow: {detection_time:.2f}s")
            else:
                self._fail_test("Platform detector not available for performance test")
            
            # Test 11: System Initialization Speed
            self._start_test("System Initialization Performance")
            if 'platform_system' in globals():
                start_time = time.time()
                platform_system.perform_system_check()
                check_time = time.time() - start_time
                
                test_results['performance_metrics']['system_check_time'] = check_time
                
                if check_time < 10.0:  # Should complete within 10 seconds
                    self._pass_test(f"System check completed in {check_time:.2f}s")
                else:
                    self._fail_test(f"System check too slow: {check_time:.2f}s")
            else:
                self._fail_test("Platform system not available for performance test")
        
        def _run_security_tests(self):
            """Test security features"""
            
            # Test 12: Distribution File Security
            self._start_test("Distribution File Security")
            if 'distribution_tracker' in globals():
                # Test file creation and verification
                if distribution_tracker.create_source_file():
                    if distribution_tracker.verify_source_file():
                        self._pass_test("Distribution file security verified")
                    else:
                        self._fail_test("Distribution file verification failed")
                else:
                    self._fail_test("Distribution file creation failed")
            else:
                self._fail_test("Distribution tracker not available for security test")
            
            # Test 13: Platform Spoofing Detection
            self._start_test("Platform Spoofing Detection")
            if 'platform_anticheat' in globals():
                # This would test the spoofing detection mechanisms
                # For now, we'll check if the detection system is active
                if platform_anticheat.is_platform_secure():
                    self._pass_test("Platform spoofing detection active")
                else:
                    violations = platform_anticheat.get_violation_count()
                    if violations > 0:
                        self._fail_test(f"Platform security violations detected: {violations}")
                    else:
                        self._pass_test("Platform spoofing detection active (no violations)")
            else:
                self._fail_test("Platform anti-cheat not available for security test")
            
            # Test 14: Terms Compliance Security
            self._start_test("Terms Compliance Security")
            if 'terms_compliance' in globals():
                violations = terms_compliance.get_violations()
                warnings = terms_compliance.get_warnings()
                
                test_results['security_assessment']['compliance_violations'] = len(violations)
                test_results['security_assessment']['compliance_warnings'] = len(warnings)
                
                if len(violations) == 0:
                    self._pass_test(f"Terms compliance secure ({len(warnings)} warnings)")
                else:
                    self._fail_test(f"Terms compliance violations: {len(violations)}")
            else:
                self._fail_test("Terms compliance not available for security test")
        
        def _run_integration_tests(self):
            """Test integration with existing systems"""
            
            # Test 15: Anti-Cheat System Integration
            self._start_test("Anti-Cheat System Integration")
            # Check if the existing anti-cheat system is available
            existing_anticheat_files = [
                'anti-cheat/anticheat_core.rpy',
                'anti-cheat/anticheat_kernel.rpy',
                'anti-cheat/anticheat_network.rpy'
            ]
            
            anticheat_available = any(os.path.exists(f) for f in existing_anticheat_files)
            
            if anticheat_available:
                self._pass_test("Existing anti-cheat system detected and available for integration")
            else:
                self._pass_test("No existing anti-cheat system found (platform system can operate independently)")
            
            # Test 16: Encryption System Integration
            self._start_test("Encryption System Integration")
            encryption_files = [
                'unencrypt and re-encrypt/encryption_core.py',
                'unencrypt and re-encrypt/renpy_encryption.rpy'
            ]
            
            encryption_available = any(os.path.exists(f) for f in encryption_files)
            
            if encryption_available:
                self._pass_test("Existing encryption system detected and available for integration")
            else:
                self._pass_test("No existing encryption system found (platform system can operate independently)")
        
        def _start_test(self, test_name):
            """Start a new test"""
            self.current_test = test_name
            self.test_start_time = time.time()
            test_results['total_tests'] += 1
        
        def _pass_test(self, message):
            """Mark current test as passed"""
            if self.current_test:
                test_duration = time.time() - self.test_start_time if self.test_start_time else 0
                
                test_results['passed_tests'] += 1
                test_results['test_details'].append({
                    'test': self.current_test,
                    'status': 'PASSED',
                    'message': message,
                    'duration': test_duration,
                    'timestamp': datetime.now().isoformat()
                })
                
                renpy.log(f"TEST PASSED: {self.current_test} - {message}")
        
        def _fail_test(self, message):
            """Mark current test as failed"""
            if self.current_test:
                test_duration = time.time() - self.test_start_time if self.test_start_time else 0
                
                test_results['failed_tests'] += 1
                test_results['test_details'].append({
                    'test': self.current_test,
                    'status': 'FAILED',
                    'message': message,
                    'duration': test_duration,
                    'timestamp': datetime.now().isoformat()
                })
                
                renpy.log(f"TEST FAILED: {self.current_test} - {message}")
        
        def _log_detailed_results(self):
            """Log detailed test results"""
            try:
                renpy.log("=== PLATFORM SYSTEM TEST RESULTS ===")
                renpy.log(f"Total Tests: {test_results['total_tests']}")
                renpy.log(f"Passed: {test_results['passed_tests']}")
                renpy.log(f"Failed: {test_results['failed_tests']}")
                renpy.log(f"Success Rate: {(test_results['passed_tests'] / test_results['total_tests']) * 100:.1f}%")
                
                if test_results['performance_metrics']:
                    renpy.log("=== PERFORMANCE METRICS ===")
                    for metric, value in test_results['performance_metrics'].items():
                        renpy.log(f"{metric}: {value:.2f}s")
                
                if test_results['security_assessment']:
                    renpy.log("=== SECURITY ASSESSMENT ===")
                    for assessment, value in test_results['security_assessment'].items():
                        renpy.log(f"{assessment}: {value}")
                
                renpy.log("=== FAILED TESTS ===")
                for test_detail in test_results['test_details']:
                    if test_detail['status'] == 'FAILED':
                        renpy.log(f"- {test_detail['test']}: {test_detail['message']}")
                
                renpy.log("=== END TEST RESULTS ===")
                
            except Exception as e:
                renpy.log(f"Error logging test results: {str(e)}")
        
        def get_test_results(self):
            """Get the latest test results"""
            return test_results

    # Initialize the platform system tester
    platform_tester = PlatformSystemTester()

# Auto-run tests on startup if enabled
init python:
    if TEST_CONFIG.get('enabled', True) and TEST_CONFIG.get('run_on_startup', False):
        try:
            platform_tester.run_comprehensive_tests()
        except Exception as e:
            renpy.log(f"Platform system auto-testing failed: {str(e)}")

# Test functions for use in game
define platform_testing_enabled = TEST_CONFIG.get('enabled', True)
define platform_tests_passed = test_results.get('passed_tests', 0)
define platform_tests_total = test_results.get('total_tests', 0)
