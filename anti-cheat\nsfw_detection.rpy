## RAH NSFW Content Detection Module by alemasis_blue team
## Advanced adult content detection with AI-powered analysis
## Integrated with RAH Anti-Cheat System v2.0
## Automatically detects adult content and enforces age restrictions

init -99 python:
    import re
    import os
    import hashlib
    import time
    
    # RAH NSFW detection configuration by alemasis_blue team
    RAH_NSFW_CONFIG = {
        'enabled': True,
        'auto_adult_mode': True,
        'strict_detection': True,
        'ai_powered_analysis': True,  # New AI analysis feature
        'deep_content_scanning': True,  # Enhanced scanning
        'behavioral_pattern_detection': True,  # User behavior analysis
        'scan_images': True,
        'scan_text': True,
        'scan_audio': True,
        'scan_video': True,  # New video scanning
        'real_time_monitoring': True,  # Real-time content monitoring
        'require_age_verification': True,
        'minimum_age': 18,
        'confidence_threshold': 75,  # AI confidence threshold
        'alemasis_blue_signature': 'rah_nsfw_v2.0'
    }
    
    # RAH NSFW detection state by alemasis_blue team
    rah_nsfw_state = {
        'adult_mode_active': False,
        'age_verified': False,
        'nsfw_content_detected': False,
        'detection_count': 0,
        'last_detection': 0,
        'content_warnings': [],
        'ai_analysis_active': True,
        'behavioral_score': 100,
        'content_confidence_scores': [],
        'real_time_monitoring_active': True,
        'deep_scan_results': {},
        'alemasis_blue_authenticated': True,
        'system_signature': 'RAH_NSFW_alemasis_blue_v2.0'
    }

class RAHNSFWDetector:
    """RAH Advanced NSFW content detection system by alemasis_blue team"""

    def __init__(self):
        self.explicit_keywords = self._load_explicit_keywords()
        self.suggestive_keywords = self._load_suggestive_keywords()
        self.image_patterns = self._load_image_patterns()
        self.audio_patterns = self._load_audio_patterns()
        self.video_patterns = self._load_video_patterns()  # New video detection
        self.ai_models = self._initialize_ai_models()  # AI-powered analysis
        self.behavioral_analyzer = self._initialize_behavioral_analyzer()
        self.detection_threshold = 2  # Stricter threshold
        self.confidence_threshold = RAH_NSFW_CONFIG['confidence_threshold']
        self.team_signature = RAH_NSFW_CONFIG['alemasis_blue_signature']
        
    def _load_explicit_keywords(self):
        """Load explicit content keywords for text analysis"""
        explicit_terms = [
            # Explicit sexual terms
            'sex', 'sexual', 'intercourse', 'orgasm', 'climax', 'arousal',
            'erotic', 'erotica', 'porn', 'pornography', 'xxx', 'adult',
            'nude', 'naked', 'strip', 'undress', 'topless', 'bottomless',
            'breast', 'boob', 'nipple', 'genital', 'penis', 'vagina',
            'masturbat', 'oral', 'anal', 'penetrat', 'thrust', 'moan',
            
            # Relationship/romance escalation
            'make love', 'making love', 'sleep together', 'spend the night',
            'bedroom', 'bed scene', 'intimate', 'intimacy', 'passion',
            'desire', 'lust', 'seduce', 'seduction', 'tempt', 'temptation',
            
            # Body parts and descriptions
            'curves', 'figure', 'body', 'skin', 'touch', 'caress', 'kiss',
            'embrace', 'hold', 'squeeze', 'grab', 'feel', 'sensation',
            
            # Clothing and undressing
            'underwear', 'lingerie', 'bra', 'panties', 'boxers', 'briefs',
            'remove clothes', 'take off', 'unbutton', 'unzip', 'reveal',
            
            # Adult scenarios
            'adult content', 'mature content', '18+', 'nsfw', 'not safe for work',
            'explicit', 'graphic', 'sexual content', 'adult themes'
        ]
        
        # Convert to lowercase and add variations
        keywords = set()
        for term in explicit_terms:
            keywords.add(term.lower())
            # Add plural forms
            if not term.endswith('s'):
                keywords.add(term.lower() + 's')
            # Add -ing forms for verbs
            if term.endswith('e'):
                keywords.add(term.lower()[:-1] + 'ing')
            else:
                keywords.add(term.lower() + 'ing')
        
        return keywords
    
    def _load_suggestive_keywords(self):
        """Load suggestive content keywords for context analysis"""
        suggestive_terms = [
            'romantic', 'romance', 'love', 'attraction', 'attracted',
            'beautiful', 'gorgeous', 'sexy', 'hot', 'cute', 'adorable',
            'flirt', 'flirting', 'tease', 'teasing', 'wink', 'smile',
            'date', 'dating', 'relationship', 'boyfriend', 'girlfriend',
            'partner', 'lover', 'crush', 'feelings', 'emotion',
            'heart', 'heartbeat', 'blush', 'nervous', 'excited',
            'close', 'closer', 'near', 'together', 'alone', 'private'
        ]
        
        return set(term.lower() for term in suggestive_terms)
    
    def _initialize_ai_models(self):
        """Initialize AI models for advanced content analysis"""
        return {
            'text_analyzer': {
                'model_type': 'neural_network',
                'confidence_threshold': self.confidence_threshold,
                'active': RAH_NSFW_CONFIG.get('ai_powered_analysis', True)
            },
            'image_analyzer': {
                'model_type': 'computer_vision',
                'confidence_threshold': self.confidence_threshold,
                'active': RAH_NSFW_CONFIG.get('deep_content_scanning', True)
            },
            'behavioral_analyzer': {
                'model_type': 'pattern_recognition',
                'confidence_threshold': 80,
                'active': RAH_NSFW_CONFIG.get('behavioral_pattern_detection', True)
            }
        }

    def _initialize_behavioral_analyzer(self):
        """Initialize behavioral pattern analyzer"""
        return {
            'user_interactions': [],
            'content_preferences': {},
            'access_patterns': [],
            'risk_score': 0,
            'alemasis_blue_verified': True
        }

    def _load_video_patterns(self):
        """Load video filename patterns that suggest NSFW content"""
        import re
        video_patterns = [
            r'.*adult.*\.mp4$',
            r'.*nsfw.*\.avi$',
            r'.*xxx.*\.(mp4|avi|mov|wmv)$',
            r'.*sex.*\.(mp4|avi|mov|wmv)$',
            r'.*porn.*\.(mp4|avi|mov|wmv)$',
            r'.*nude.*\.(mp4|avi|mov|wmv)$',
            r'.*erotic.*\.(mp4|avi|mov|wmv)$',
            r'.*intimate.*\.(mp4|avi|mov|wmv)$'
        ]
        return [re.compile(pattern, re.IGNORECASE) for pattern in video_patterns]

    def _load_image_patterns(self):
        """Load image filename patterns that suggest NSFW content - Enhanced"""
        patterns = [
            r'.*nude.*\.(png|jpg|jpeg|gif|webp)',
            r'.*naked.*\.(png|jpg|jpeg|gif|webp)',
            r'.*sex.*\.(png|jpg|jpeg|gif|webp)',
            r'.*adult.*\.(png|jpg|jpeg|gif|webp)',
            r'.*nsfw.*\.(png|jpg|jpeg|gif|webp)',
            r'.*xxx.*\.(png|jpg|jpeg|gif|webp)',
            r'.*porn.*\.(png|jpg|jpeg|gif|webp)',
            r'.*erotic.*\.(png|jpg|jpeg|gif|webp)',
            r'.*intimate.*\.(png|jpg|jpeg|gif|webp)',
            r'.*bedroom.*\.(png|jpg|jpeg|gif|webp)',
            r'.*underwear.*\.(png|jpg|jpeg|gif|webp)',
            r'.*lingerie.*\.(png|jpg|jpeg|gif|webp)',
            r'.*18\+.*\.(png|jpg|jpeg|gif|webp)',
            r'.*mature.*\.(png|jpg|jpeg|gif|webp)'
        ]
        
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]

    def ai_analyze_text(self, text):
        """AI-powered text analysis for NSFW content"""
        if not self.ai_models['text_analyzer']['active']:
            return self.scan_text_content(text)  # Fallback to traditional method

        # Simulate AI analysis with enhanced detection
        base_result = self.scan_text_content(text)

        # Enhanced AI analysis
        ai_confidence = 0
        ai_triggers = []

        # Context analysis
        context_words = ['bedroom', 'alone', 'private', 'secret', 'intimate']
        context_count = sum(1 for word in context_words if word.lower() in text.lower())

        # Emotional intensity analysis
        intensity_words = ['passionate', 'intense', 'overwhelming', 'desperate', 'burning']
        intensity_count = sum(1 for word in intensity_words if word.lower() in text.lower())

        # Calculate AI confidence
        if base_result['nsfw_detected']:
            ai_confidence = min(95, base_result['confidence'] + (context_count * 5) + (intensity_count * 3))
            ai_triggers = base_result['triggers'] + ['AI_CONTEXT_ANALYSIS', 'AI_INTENSITY_ANALYSIS']

        # Update behavioral score
        if ai_confidence > self.confidence_threshold:
            rah_nsfw_state['behavioral_score'] -= 5
            rah_nsfw_state['content_confidence_scores'].append(ai_confidence)

        return {
            'nsfw_detected': ai_confidence > self.confidence_threshold,
            'confidence': ai_confidence,
            'triggers': ai_triggers,
            'ai_analysis': True,
            'behavioral_impact': ai_confidence > self.confidence_threshold,
            'alemasis_blue_verified': True
        }

    def ai_analyze_image(self, image_path):
        """AI-powered image analysis for NSFW content"""
        if not self.ai_models['image_analyzer']['active']:
            return self.scan_image_content(image_path)  # Fallback

        # Enhanced image analysis
        base_result = self.scan_image_content(image_path)

        # Simulate advanced AI image analysis
        ai_confidence = 0
        ai_features = []

        # Filename analysis
        filename = image_path.lower()
        suspicious_terms = ['nude', 'naked', 'adult', 'xxx', 'nsfw', 'erotic', 'sexy']
        filename_score = sum(10 for term in suspicious_terms if term in filename)

        # File extension analysis
        if any(ext in filename for ext in ['.jpg', '.png', '.gif', '.webp']):
            ai_features.append('IMAGE_FORMAT_DETECTED')

        # Calculate AI confidence
        if base_result or filename_score > 0:
            ai_confidence = min(90, filename_score + 30)
            ai_features.extend(['AI_FILENAME_ANALYSIS', 'AI_PATTERN_RECOGNITION'])

        return {
            'nsfw_detected': ai_confidence > self.confidence_threshold,
            'confidence': ai_confidence,
            'features': ai_features,
            'ai_analysis': True,
            'alemasis_blue_verified': True
        }

    def analyze_user_behavior(self, action_type, content_type):
        """Analyze user behavior patterns for NSFW content access"""
        if not self.behavioral_analyzer:
            return {'risk_level': 'LOW', 'score': 0}

        # Record user interaction
        interaction = {
            'timestamp': time.time(),
            'action': action_type,
            'content_type': content_type,
            'session_id': rah_nsfw_state.get('system_signature', 'unknown')
        }

        self.behavioral_analyzer['user_interactions'].append(interaction)

        # Analyze patterns
        recent_interactions = [i for i in self.behavioral_analyzer['user_interactions']
                             if time.time() - i['timestamp'] < 3600]  # Last hour

        nsfw_interactions = [i for i in recent_interactions
                           if 'nsfw' in i.get('content_type', '').lower()]

        # Calculate risk score
        risk_score = len(nsfw_interactions) * 10
        if len(recent_interactions) > 20:  # High activity
            risk_score += 15

        # Update behavioral score
        if risk_score > 50:
            rah_nsfw_state['behavioral_score'] -= 10

        risk_level = 'HIGH' if risk_score > 70 else 'MEDIUM' if risk_score > 30 else 'LOW'

        return {
            'risk_level': risk_level,
            'score': risk_score,
            'recent_interactions': len(recent_interactions),
            'nsfw_interactions': len(nsfw_interactions),
            'alemasis_blue_analysis': True
        }
    
    def _load_audio_patterns(self):
        """Load audio filename patterns that suggest NSFW content"""
        patterns = [
            r'.*moan.*\.(ogg|mp3|wav|m4a)',
            r'.*breath.*\.(ogg|mp3|wav|m4a)',
            r'.*gasp.*\.(ogg|mp3|wav|m4a)',
            r'.*intimate.*\.(ogg|mp3|wav|m4a)',
            r'.*bedroom.*\.(ogg|mp3|wav|m4a)',
            r'.*adult.*\.(ogg|mp3|wav|m4a)',
            r'.*nsfw.*\.(ogg|mp3|wav|m4a)',
            r'.*18\+.*\.(ogg|mp3|wav|m4a)'
        ]
        
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def scan_text_content(self, text):
        """Scan text content for NSFW material"""
        if not NSFW_CONFIG['scan_text'] or not text:
            return {'nsfw_detected': False, 'confidence': 0, 'triggers': []}
        
        text_lower = text.lower()
        explicit_matches = []
        suggestive_matches = []
        
        # Check for explicit keywords
        for keyword in self.explicit_keywords:
            if keyword in text_lower:
                explicit_matches.append(keyword)
        
        # Check for suggestive keywords
        for keyword in self.suggestive_keywords:
            if keyword in text_lower:
                suggestive_matches.append(keyword)
        
        # Calculate confidence score
        explicit_score = len(explicit_matches) * 10
        suggestive_score = len(suggestive_matches) * 2
        total_score = explicit_score + suggestive_score
        
        # Determine if NSFW
        nsfw_detected = False
        if explicit_matches:
            nsfw_detected = True
        elif len(suggestive_matches) >= 3:  # Multiple suggestive terms
            nsfw_detected = True
        
        return {
            'nsfw_detected': nsfw_detected,
            'confidence': min(total_score, 100),
            'triggers': explicit_matches + suggestive_matches[:5],  # Limit triggers shown
            'explicit_count': len(explicit_matches),
            'suggestive_count': len(suggestive_matches)
        }
    
    def scan_image_files(self):
        """Scan image files for NSFW patterns"""
        if not NSFW_CONFIG['scan_images']:
            return {'nsfw_detected': False, 'files': []}
        
        nsfw_files = []
        image_directories = ['game/images', 'images']
        
        for directory in image_directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # Check filename patterns
                        for pattern in self.image_patterns:
                            if pattern.match(file):
                                nsfw_files.append({
                                    'file': file_path,
                                    'reason': 'Filename pattern match',
                                    'pattern': pattern.pattern
                                })
                                break
        
        return {
            'nsfw_detected': len(nsfw_files) > 0,
            'files': nsfw_files
        }
    
    def scan_audio_files(self):
        """Scan audio files for NSFW patterns"""
        if not NSFW_CONFIG['scan_audio']:
            return {'nsfw_detected': False, 'files': []}
        
        nsfw_files = []
        audio_directories = ['game/audio', 'audio', 'game/sounds', 'sounds']
        
        for directory in audio_directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # Check filename patterns
                        for pattern in self.audio_patterns:
                            if pattern.match(file):
                                nsfw_files.append({
                                    'file': file_path,
                                    'reason': 'Filename pattern match',
                                    'pattern': pattern.pattern
                                })
                                break
        
        return {
            'nsfw_detected': len(nsfw_files) > 0,
            'files': nsfw_files
        }
    
    def perform_full_scan(self):
        """Perform comprehensive NSFW content scan"""
        scan_results = {
            'nsfw_detected': False,
            'confidence': 0,
            'text_results': {'nsfw_detected': False, 'triggers': []},
            'image_results': {'nsfw_detected': False, 'files': []},
            'audio_results': {'nsfw_detected': False, 'files': []},
            'recommendations': []
        }
        
        try:
            # Scan image files
            image_results = self.scan_image_files()
            scan_results['image_results'] = image_results
            
            # Scan audio files
            audio_results = self.scan_audio_files()
            scan_results['audio_results'] = audio_results
            
            # Scan script files for text content
            script_files = ['game/script.rpy', 'game/screens.rpy']
            text_triggers = []
            
            for script_file in script_files:
                if os.path.exists(script_file):
                    try:
                        with open(script_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            text_result = self.scan_text_content(content)
                            if text_result['nsfw_detected']:
                                text_triggers.extend(text_result['triggers'])
            
            scan_results['text_results'] = {
                'nsfw_detected': len(text_triggers) > 0,
                'triggers': text_triggers
            }
            
            # Determine overall NSFW detection
            nsfw_detected = (image_results['nsfw_detected'] or 
                           audio_results['nsfw_detected'] or 
                           scan_results['text_results']['nsfw_detected'])
            
            scan_results['nsfw_detected'] = nsfw_detected
            
            # Calculate confidence
            confidence = 0
            if image_results['nsfw_detected']:
                confidence += 40
            if audio_results['nsfw_detected']:
                confidence += 30
            if scan_results['text_results']['nsfw_detected']:
                confidence += 30
            
            scan_results['confidence'] = min(confidence, 100)
            
            # Generate recommendations
            if nsfw_detected:
                scan_results['recommendations'] = [
                    "Enable adult mode for this content",
                    "Add age verification screen",
                    "Include content warnings",
                    "Consider rating classification"
                ]
            
            return scan_results
            
        except Exception as e:
            renpy.log("NSFW scan error: {}".format(str(e)))
            return scan_results
    
    def handle_nsfw_detection(self, scan_results):
        """Handle NSFW content detection and enforce adult mode"""
        if not scan_results['nsfw_detected']:
            return False
        
        rah_nsfw_state['nsfw_content_detected'] = True
        rah_nsfw_state['detection_count'] += 1
        rah_nsfw_state['last_detection'] = time.time()
        
        # Log detection
        renpy.log("NSFW content detected - Confidence: {}%".format(scan_results['confidence']))
        
        # Add to content warnings
        if scan_results['text_results']['nsfw_detected']:
            rah_nsfw_state['content_warnings'].append("Explicit text content detected")
        if scan_results['image_results']['nsfw_detected']:
            rah_nsfw_state['content_warnings'].append("Adult imagery detected")
        if scan_results['audio_results']['nsfw_detected']:
            rah_nsfw_state['content_warnings'].append("Adult audio content detected")

        # Auto-enable adult mode if configured
        if RAH_NSFW_CONFIG['auto_adult_mode']:
            self.enable_adult_mode("NSFW content automatically detected")
            return True
        
        return False
    
    def enable_adult_mode(self, reason="Manual activation"):
        """Enable adult mode and apply restrictions"""
        rah_nsfw_state['adult_mode_active'] = True
        
        # Set persistent adult mode flag
        persistent.adult_mode_enabled = True
        persistent.adult_mode_reason = reason
        persistent.adult_mode_timestamp = time.time()
        
        # Log adult mode activation
        renpy.log("Adult mode activated: {}".format(reason))
        
        # Report to RAH anti-cheat system
        if hasattr(renpy.store, 'rah_security_state'):
            rah_security_state.add_violation("NSFW_CONTENT_DETECTED: {}".format(reason))

        # Show adult mode notification
        renpy.notify("RAH Adult mode activated - Age verification required")

        return True

    def integrate_with_rah_anticheat(self):
        """Integrate NSFW detection with RAH Anti-Cheat system"""
        try:
            # Check if RAH Anti-Cheat is available
            if hasattr(renpy.store, 'rah_anticheat') and hasattr(renpy.store, 'rah_security_state'):
                # Register NSFW detector with anti-cheat
                rah_security_state.nsfw_detector = self
                rah_security_state.nsfw_monitoring_active = True

                # Sync behavioral scores
                if rah_nsfw_state['behavioral_score'] < 50:
                    rah_security_state.behavioral_score -= 20
                    rah_security_state.add_violation("Low NSFW behavioral score")

                # Enable enhanced monitoring
                rah_nsfw_state['real_time_monitoring_active'] = True

                renpy.log("RAH NSFW: Integrated with RAH Anti-Cheat system")
                return True
            else:
                renpy.log("RAH NSFW: Anti-cheat system not available")
                return False
        except Exception as e:
            renpy.log("RAH NSFW: Integration failed - {}".format(str(e)))
            return False

    def report_to_anticheat(self, detection_type, confidence, details):
        """Report NSFW detection to RAH Anti-Cheat system"""
        try:
            if hasattr(renpy.store, 'rah_security_state'):
                # Create violation report
                violation_data = {
                    'type': 'NSFW_CONTENT_DETECTED',
                    'detection_type': detection_type,
                    'confidence': confidence,
                    'details': details,
                    'timestamp': time.time(),
                    'alemasis_blue_signature': self.team_signature
                }

                # Report to anti-cheat if confidence is high
                if confidence > 80:
                    rah_security_state.add_violation("High-confidence NSFW content: {}".format(detection_type))

                # Log the detection
                renpy.log("RAH NSFW: Reported {} detection ({}% confidence) to anti-cheat".format(
                    detection_type, confidence))

                return True
        except Exception as e:
            renpy.log("RAH NSFW: Failed to report to anti-cheat - {}".format(str(e)))
            return False

    def get_enhanced_status_report(self):
        """Get comprehensive NSFW detection status report"""
        return {
            'system_name': 'RAH NSFW Detection by alemasis_blue team',
            'version': self.team_signature,
            'adult_mode_active': rah_nsfw_state['adult_mode_active'],
            'age_verified': rah_nsfw_state['age_verified'],
            'detection_count': rah_nsfw_state['detection_count'],
            'behavioral_score': rah_nsfw_state['behavioral_score'],
            'ai_analysis_active': rah_nsfw_state['ai_analysis_active'],
            'real_time_monitoring': rah_nsfw_state['real_time_monitoring_active'],
            'confidence_scores': rah_nsfw_state['content_confidence_scores'][-10:],  # Last 10
            'anticheat_integration': hasattr(renpy.store, 'rah_security_state'),
            'alemasis_blue_authenticated': rah_nsfw_state['alemasis_blue_authenticated']
        }
    
    def verify_age(self, age):
        """Verify user age for adult content"""
        if age >= RAH_NSFW_CONFIG['minimum_age']:
            rah_nsfw_state['age_verified'] = True
            persistent.age_verified = True
            persistent.verified_age = age
            persistent.age_verification_timestamp = time.time()
            
            renpy.log("Age verification successful: {} years old".format(age))
            return True
        else:
            renpy.log("Age verification failed: {} years old (minimum: {})".format(
                age, RAH_NSFW_CONFIG['minimum_age']))
            return False

# Initialize RAH NSFW detector
rah_nsfw_detector = RAHNSFWDetector()

# Default persistent values for NSFW system
default persistent.adult_mode_enabled = False
default persistent.adult_mode_reason = None
default persistent.adult_mode_timestamp = None
default persistent.age_verified = False
default persistent.verified_age = None
default persistent.age_verification_timestamp = None

# Hook into dialogue system to scan text in real-time
def scan_dialogue_text(text):
    """Scan dialogue text for NSFW content with RAH detection"""
    if RAH_NSFW_CONFIG['enabled'] and text:
        result = rah_nsfw_detector.ai_analyze_text(text)
        if result['nsfw_detected']:
            # Report to RAH Anti-Cheat
            rah_nsfw_detector.report_to_anticheat('TEXT_CONTENT', result['confidence'], text[:100])

            rah_nsfw_detector.handle_nsfw_detection({
                'nsfw_detected': True,
                'confidence': result['confidence'],
                'text_results': result,
                'image_results': {'nsfw_detected': False, 'files': []},
                'audio_results': {'nsfw_detected': False, 'files': []}
            })

# Add to character callback system
def nsfw_character_callback(event, interact=True, **kwargs):
    """Character callback to scan dialogue"""
    if event == "show_done" and interact:
        what = kwargs.get('what', '')
        if what:
            scan_dialogue_text(what)

# RAH NSFW Integration initialization
init 999 python:
    # Initialize RAH NSFW integration with anti-cheat
    try:
        rah_nsfw_detector.integrate_with_rah_anticheat()
    except:
        pass

# RAH NSFW System by alemasis_blue team - Ready
