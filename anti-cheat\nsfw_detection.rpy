## NSFW Content Detection Module
## Automatically detects adult content and enforces age restrictions

init -99 python:
    import re
    import os
    import hashlib
    import time
    
    # NSFW detection configuration
    NSFW_CONFIG = {
        'enabled': True,
        'auto_adult_mode': True,
        'strict_detection': True,
        'scan_images': True,
        'scan_text': True,
        'scan_audio': True,
        'require_age_verification': True,
        'minimum_age': 18
    }
    
    # NSFW detection state
    nsfw_state = {
        'adult_mode_active': False,
        'age_verified': False,
        'nsfw_content_detected': False,
        'detection_count': 0,
        'last_detection': 0,
        'content_warnings': []
    }

class NSFWDetector:
    """Advanced NSFW content detection system"""
    
    def __init__(self):
        self.explicit_keywords = self._load_explicit_keywords()
        self.suggestive_keywords = self._load_suggestive_keywords()
        self.image_patterns = self._load_image_patterns()
        self.audio_patterns = self._load_audio_patterns()
        self.detection_threshold = 3  # Number of detections before auto-adult mode
        
    def _load_explicit_keywords(self):
        """Load explicit content keywords for text analysis"""
        explicit_terms = [
            # Explicit sexual terms
            'sex', 'sexual', 'intercourse', 'orgasm', 'climax', 'arousal',
            'erotic', 'erotica', 'porn', 'pornography', 'xxx', 'adult',
            'nude', 'naked', 'strip', 'undress', 'topless', 'bottomless',
            'breast', 'boob', 'nipple', 'genital', 'penis', 'vagina',
            'masturbat', 'oral', 'anal', 'penetrat', 'thrust', 'moan',
            
            # Relationship/romance escalation
            'make love', 'making love', 'sleep together', 'spend the night',
            'bedroom', 'bed scene', 'intimate', 'intimacy', 'passion',
            'desire', 'lust', 'seduce', 'seduction', 'tempt', 'temptation',
            
            # Body parts and descriptions
            'curves', 'figure', 'body', 'skin', 'touch', 'caress', 'kiss',
            'embrace', 'hold', 'squeeze', 'grab', 'feel', 'sensation',
            
            # Clothing and undressing
            'underwear', 'lingerie', 'bra', 'panties', 'boxers', 'briefs',
            'remove clothes', 'take off', 'unbutton', 'unzip', 'reveal',
            
            # Adult scenarios
            'adult content', 'mature content', '18+', 'nsfw', 'not safe for work',
            'explicit', 'graphic', 'sexual content', 'adult themes'
        ]
        
        # Convert to lowercase and add variations
        keywords = set()
        for term in explicit_terms:
            keywords.add(term.lower())
            # Add plural forms
            if not term.endswith('s'):
                keywords.add(term.lower() + 's')
            # Add -ing forms for verbs
            if term.endswith('e'):
                keywords.add(term.lower()[:-1] + 'ing')
            else:
                keywords.add(term.lower() + 'ing')
        
        return keywords
    
    def _load_suggestive_keywords(self):
        """Load suggestive content keywords for context analysis"""
        suggestive_terms = [
            'romantic', 'romance', 'love', 'attraction', 'attracted',
            'beautiful', 'gorgeous', 'sexy', 'hot', 'cute', 'adorable',
            'flirt', 'flirting', 'tease', 'teasing', 'wink', 'smile',
            'date', 'dating', 'relationship', 'boyfriend', 'girlfriend',
            'partner', 'lover', 'crush', 'feelings', 'emotion',
            'heart', 'heartbeat', 'blush', 'nervous', 'excited',
            'close', 'closer', 'near', 'together', 'alone', 'private'
        ]
        
        return set(term.lower() for term in suggestive_terms)
    
    def _load_image_patterns(self):
        """Load image filename patterns that suggest NSFW content"""
        patterns = [
            r'.*nude.*\.(png|jpg|jpeg|gif|webp)',
            r'.*naked.*\.(png|jpg|jpeg|gif|webp)',
            r'.*sex.*\.(png|jpg|jpeg|gif|webp)',
            r'.*adult.*\.(png|jpg|jpeg|gif|webp)',
            r'.*nsfw.*\.(png|jpg|jpeg|gif|webp)',
            r'.*xxx.*\.(png|jpg|jpeg|gif|webp)',
            r'.*porn.*\.(png|jpg|jpeg|gif|webp)',
            r'.*erotic.*\.(png|jpg|jpeg|gif|webp)',
            r'.*intimate.*\.(png|jpg|jpeg|gif|webp)',
            r'.*bedroom.*\.(png|jpg|jpeg|gif|webp)',
            r'.*underwear.*\.(png|jpg|jpeg|gif|webp)',
            r'.*lingerie.*\.(png|jpg|jpeg|gif|webp)',
            r'.*18\+.*\.(png|jpg|jpeg|gif|webp)',
            r'.*mature.*\.(png|jpg|jpeg|gif|webp)'
        ]
        
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def _load_audio_patterns(self):
        """Load audio filename patterns that suggest NSFW content"""
        patterns = [
            r'.*moan.*\.(ogg|mp3|wav|m4a)',
            r'.*breath.*\.(ogg|mp3|wav|m4a)',
            r'.*gasp.*\.(ogg|mp3|wav|m4a)',
            r'.*intimate.*\.(ogg|mp3|wav|m4a)',
            r'.*bedroom.*\.(ogg|mp3|wav|m4a)',
            r'.*adult.*\.(ogg|mp3|wav|m4a)',
            r'.*nsfw.*\.(ogg|mp3|wav|m4a)',
            r'.*18\+.*\.(ogg|mp3|wav|m4a)'
        ]
        
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def scan_text_content(self, text):
        """Scan text content for NSFW material"""
        if not NSFW_CONFIG['scan_text'] or not text:
            return {'nsfw_detected': False, 'confidence': 0, 'triggers': []}
        
        text_lower = text.lower()
        explicit_matches = []
        suggestive_matches = []
        
        # Check for explicit keywords
        for keyword in self.explicit_keywords:
            if keyword in text_lower:
                explicit_matches.append(keyword)
        
        # Check for suggestive keywords
        for keyword in self.suggestive_keywords:
            if keyword in text_lower:
                suggestive_matches.append(keyword)
        
        # Calculate confidence score
        explicit_score = len(explicit_matches) * 10
        suggestive_score = len(suggestive_matches) * 2
        total_score = explicit_score + suggestive_score
        
        # Determine if NSFW
        nsfw_detected = False
        if explicit_matches:
            nsfw_detected = True
        elif len(suggestive_matches) >= 3:  # Multiple suggestive terms
            nsfw_detected = True
        
        return {
            'nsfw_detected': nsfw_detected,
            'confidence': min(total_score, 100),
            'triggers': explicit_matches + suggestive_matches[:5],  # Limit triggers shown
            'explicit_count': len(explicit_matches),
            'suggestive_count': len(suggestive_matches)
        }
    
    def scan_image_files(self):
        """Scan image files for NSFW patterns"""
        if not NSFW_CONFIG['scan_images']:
            return {'nsfw_detected': False, 'files': []}
        
        nsfw_files = []
        image_directories = ['game/images', 'images']
        
        for directory in image_directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # Check filename patterns
                        for pattern in self.image_patterns:
                            if pattern.match(file):
                                nsfw_files.append({
                                    'file': file_path,
                                    'reason': 'Filename pattern match',
                                    'pattern': pattern.pattern
                                })
                                break
        
        return {
            'nsfw_detected': len(nsfw_files) > 0,
            'files': nsfw_files
        }
    
    def scan_audio_files(self):
        """Scan audio files for NSFW patterns"""
        if not NSFW_CONFIG['scan_audio']:
            return {'nsfw_detected': False, 'files': []}
        
        nsfw_files = []
        audio_directories = ['game/audio', 'audio', 'game/sounds', 'sounds']
        
        for directory in audio_directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # Check filename patterns
                        for pattern in self.audio_patterns:
                            if pattern.match(file):
                                nsfw_files.append({
                                    'file': file_path,
                                    'reason': 'Filename pattern match',
                                    'pattern': pattern.pattern
                                })
                                break
        
        return {
            'nsfw_detected': len(nsfw_files) > 0,
            'files': nsfw_files
        }
    
    def perform_full_scan(self):
        """Perform comprehensive NSFW content scan"""
        scan_results = {
            'nsfw_detected': False,
            'confidence': 0,
            'text_results': {'nsfw_detected': False, 'triggers': []},
            'image_results': {'nsfw_detected': False, 'files': []},
            'audio_results': {'nsfw_detected': False, 'files': []},
            'recommendations': []
        }
        
        try:
            # Scan image files
            image_results = self.scan_image_files()
            scan_results['image_results'] = image_results
            
            # Scan audio files
            audio_results = self.scan_audio_files()
            scan_results['audio_results'] = audio_results
            
            # Scan script files for text content
            script_files = ['game/script.rpy', 'game/screens.rpy']
            text_triggers = []
            
            for script_file in script_files:
                if os.path.exists(script_file):
                    try:
                        with open(script_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            text_result = self.scan_text_content(content)
                            if text_result['nsfw_detected']:
                                text_triggers.extend(text_result['triggers'])
            
            scan_results['text_results'] = {
                'nsfw_detected': len(text_triggers) > 0,
                'triggers': text_triggers
            }
            
            # Determine overall NSFW detection
            nsfw_detected = (image_results['nsfw_detected'] or 
                           audio_results['nsfw_detected'] or 
                           scan_results['text_results']['nsfw_detected'])
            
            scan_results['nsfw_detected'] = nsfw_detected
            
            # Calculate confidence
            confidence = 0
            if image_results['nsfw_detected']:
                confidence += 40
            if audio_results['nsfw_detected']:
                confidence += 30
            if scan_results['text_results']['nsfw_detected']:
                confidence += 30
            
            scan_results['confidence'] = min(confidence, 100)
            
            # Generate recommendations
            if nsfw_detected:
                scan_results['recommendations'] = [
                    "Enable adult mode for this content",
                    "Add age verification screen",
                    "Include content warnings",
                    "Consider rating classification"
                ]
            
            return scan_results
            
        except Exception as e:
            renpy.log("NSFW scan error: {}".format(str(e)))
            return scan_results
    
    def handle_nsfw_detection(self, scan_results):
        """Handle NSFW content detection and enforce adult mode"""
        if not scan_results['nsfw_detected']:
            return False
        
        nsfw_state['nsfw_content_detected'] = True
        nsfw_state['detection_count'] += 1
        nsfw_state['last_detection'] = time.time()
        
        # Log detection
        renpy.log("NSFW content detected - Confidence: {}%".format(scan_results['confidence']))
        
        # Add to content warnings
        if scan_results['text_results']['nsfw_detected']:
            nsfw_state['content_warnings'].append("Explicit text content detected")
        if scan_results['image_results']['nsfw_detected']:
            nsfw_state['content_warnings'].append("Adult imagery detected")
        if scan_results['audio_results']['nsfw_detected']:
            nsfw_state['content_warnings'].append("Adult audio content detected")
        
        # Auto-enable adult mode if configured
        if NSFW_CONFIG['auto_adult_mode']:
            self.enable_adult_mode("NSFW content automatically detected")
            return True
        
        return False
    
    def enable_adult_mode(self, reason="Manual activation"):
        """Enable adult mode and apply restrictions"""
        nsfw_state['adult_mode_active'] = True
        
        # Set persistent adult mode flag
        persistent.adult_mode_enabled = True
        persistent.adult_mode_reason = reason
        persistent.adult_mode_timestamp = time.time()
        
        # Log adult mode activation
        renpy.log("Adult mode activated: {}".format(reason))
        
        # Report to anti-cheat system
        if hasattr(renpy.store, 'anticheat_core'):
            anticheat_core._report_violation("NSFW_CONTENT_DETECTED", 
                "Adult mode auto-enabled: {}".format(reason))
        
        # Show adult mode notification
        renpy.notify("Adult mode activated - Age verification required")
        
        return True
    
    def verify_age(self, age):
        """Verify user age for adult content"""
        if age >= NSFW_CONFIG['minimum_age']:
            nsfw_state['age_verified'] = True
            persistent.age_verified = True
            persistent.verified_age = age
            persistent.age_verification_timestamp = time.time()
            
            renpy.log("Age verification successful: {} years old".format(age))
            return True
        else:
            renpy.log("Age verification failed: {} years old (minimum: {})".format(
                age, NSFW_CONFIG['minimum_age']))
            return False

# Initialize NSFW detector
nsfw_detector = NSFWDetector()

# Default persistent values for NSFW system
default persistent.adult_mode_enabled = False
default persistent.adult_mode_reason = None
default persistent.adult_mode_timestamp = None
default persistent.age_verified = False
default persistent.verified_age = None
default persistent.age_verification_timestamp = None

# Hook into dialogue system to scan text in real-time
def scan_dialogue_text(text):
    """Scan dialogue text for NSFW content"""
    if NSFW_CONFIG['enabled'] and text:
        result = nsfw_detector.scan_text_content(text)
        if result['nsfw_detected']:
            nsfw_detector.handle_nsfw_detection({
                'nsfw_detected': True,
                'confidence': result['confidence'],
                'text_results': result,
                'image_results': {'nsfw_detected': False, 'files': []},
                'audio_results': {'nsfw_detected': False, 'files': []}
            })

# Add to character callback system
def nsfw_character_callback(event, interact=True, **kwargs):
    """Character callback to scan dialogue"""
    if event == "show_done" and interact:
        what = kwargs.get('what', '')
        if what:
            scan_dialogue_text(what)

# Register the callback
config.all_character_callbacks.append(nsfw_character_callback)
