# Dedicated GPU Detection and Configuration System

This comprehensive system automatically detects NVIDIA, AMD, and Intel dedicated GPUs and configures Ren'Py for optimal performance while enforcing specific performance caps.

## Performance Caps

### 🔒 **NVIDIA Performance Cap**
- **Maximum**: GTX 1080 performance level
- **Enforcement**: GPUs exceeding GTX 1080 performance are artificially limited
- **Affected GPUs**: RTX 20/30/40 series, GTX 1080 Ti and above

### 🎯 **AMD Performance Requirement**
- **Minimum**: AMD Radeon RX 560 performance level
- **Enforcement**: GPUs below RX 560 performance are flagged as insufficient
- **Affected GPUs**: RX 550 and below, older R7/R9 series

### ✅ **Intel Arc Support**
- **Status**: Generally acceptable performance
- **Models**: Arc A310, A380, A580, A750, A770
- **Notes**: Conservative settings due to driver maturity

## Supported GPUs

### 🟢 **NVIDIA GPUs**

#### RTX 40 Series (Performance Capped)
- RTX 4090, 4080, 4070 Ti, 4070, 4060 Ti, 4060
- **Status**: 🔒 Limited to GTX 1080 performance level

#### RTX 30 Series (Performance Capped)
- RTX 3090 Ti, 3090, 3080 Ti, 3080, 3070 Ti, 3070, 3060 Ti, 3060
- **Status**: 🔒 Limited to GTX 1080 performance level

#### RTX 20 Series
- RTX 2080 Ti (🔒 Capped), 2080 Super, 2080, 2070 Super, 2070, 2060 Super, 2060
- **Status**: Mixed - some capped, others within limits

#### GTX 16 Series
- GTX 1660 Ti, 1660 Super, 1660, 1650 Super, 1650
- **Status**: ✅ Within acceptable range

#### GTX 10 Series (Reference Point)
- GTX 1080 Ti (🔒 Capped), **GTX 1080** (Reference), 1070 Ti, 1070, 1060, 1050 Ti, 1050
- **Status**: GTX 1080 is the performance ceiling
- **Note**: GTX 900 series and older are not supported (below minimum requirements)

### 🔴 **AMD GPUs**

#### RX 7000 Series (RDNA 3)
- RX 7900 XTX, 7900 XT, 7800 XT, 7700 XT, 7600
- **Status**: ✅ All exceed RX 560 minimum

#### RX 6000 Series (RDNA 2)
- RX 6950 XT, 6900 XT, 6800 XT, 6800, 6750 XT, 6700 XT, 6650 XT, 6600 XT, 6600
- **Status**: ✅ All exceed RX 560 minimum
- RX 6500 XT, 6400: ⚠️ Below minimum requirement

#### RX 5000 Series (RDNA 1)
- RX 5700 XT, 5700, 5600 XT, 5500 XT
- **Status**: ✅ All exceed RX 560 minimum

#### RX 500 Series (Polaris) - Reference Point
- RX 590, 580, 570, **RX 560** (Minimum), RX 550
- **Status**: RX 560 is the minimum requirement
- **Note**: RX 400 series and older R9/R7 series are not supported (below minimum requirements)

### 🔵 **Intel Arc GPUs**

#### Arc A-Series
- Arc A770, A750, A580, A380, A310
- **Status**: ✅ Generally acceptable with conservative settings

## Configuration Profiles

### 🔒 **High-End Capped (NVIDIA)**
```
Renderer: gl2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 4096
Anisotropic Filtering: Enabled
Framerate Limit: 60 FPS
Quality Preset: high_capped
```

### ✅ **Standard Performance**
```
Renderer: gl2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 2048
Anisotropic Filtering: Enabled
Framerate Limit: 60 FPS
Quality Preset: standard
```

### ⚠️ **Conservative (Intel Arc)**
```
Renderer: gl2
VSync: Enabled
Texture Scaling: Linear
Max Texture Size: 2048
Anisotropic Filtering: Disabled
Framerate Limit: 60 FPS
Quality Preset: conservative
```

### ❌ **Minimum Fallback**
```
Renderer: gl
VSync: Enabled
Texture Scaling: Nearest
Max Texture Size: 1024
Anisotropic Filtering: Disabled
Framerate Limit: 30 FPS
Quality Preset: minimum
```

## Detection Process

### 🔍 **Hardware Detection**
1. **System Scan**: Uses WMI (Windows) or lspci (Linux) to enumerate GPUs
2. **Vendor Identification**: Detects NVIDIA, AMD, or Intel GPUs
3. **Model Recognition**: Identifies specific GPU model and series
4. **Dedicated GPU Filtering**: Excludes integrated graphics from detection

### 📊 **Performance Assessment**
1. **Database Lookup**: Compares detected GPU against performance database
2. **Cap Enforcement**: Applies NVIDIA GTX 1080 cap or AMD RX 560 minimum
3. **Tier Assignment**: Assigns performance tier (capped_high, acceptable, below_minimum, limited)
4. **Configuration Selection**: Chooses appropriate Ren'Py settings

### ⚙️ **Ren'Py Configuration**
1. **Renderer Selection**: Chooses optimal renderer (gl2 vs gl)
2. **Quality Settings**: Configures texture size, filtering, and effects
3. **Performance Limits**: Sets framerate caps and optimization flags
4. **Vendor Optimizations**: Applies vendor-specific tweaks

## Usage

### 🚀 **Automatic Operation**
The system runs automatically at Ren'Py startup and configures settings based on detected hardware.

### 🧪 **Manual Testing**
```renpy
# Test GPU detection
call test_dedicated_gpu_detection

# Check current GPU performance
call check_gpu_performance
```

### 📊 **Console Output Example**
```
=== DEDICATED GPU DETECTION AND CONFIGURATION ===
Detected GPUs: ['NVIDIA GeForce RTX 4070']
✅ NVIDIA GPU detected: NVIDIA GeForce RTX 4070
Checking performance caps for NVIDIA GPU...
🔒 GPU performance (120%) exceeds GTX 1080 cap, limiting to GTX 1080 level
🔒 Applying GTX 1080-capped settings for high-end NVIDIA GPU
✅ Applied high_capped quality settings

DEDICATED GPU DETECTION AND CONFIGURATION REPORT
================================================================
Detected GPU: NVIDIA GeForce RTX 4070
GPU Vendor: NVIDIA
Performance Tier: capped_high
Within Performance Caps: ✅ Yes
NVIDIA Performance Cap: GTX 1080 maximum
🔒 GPU performance limited to GTX 1080 level
```

## Performance Database

### 📈 **NVIDIA Performance Scale** (GTX 1080 = 100%)
- **RTX 4090**: 200% → 🔒 Capped to 100%
- **RTX 4070**: 120% → 🔒 Capped to 100%
- **RTX 3070**: 110% → 🔒 Capped to 100%
- **GTX 1080**: 100% → ✅ Reference point
- **GTX 1070**: 80% → ✅ Acceptable
- **GTX 1060**: 60% → ✅ Acceptable
- **Note**: GTX 900 series and older are not supported

### 📈 **AMD Performance Scale** (RX 560 = 100%)
- **RX 7900 XTX**: 400% → ✅ Acceptable
- **RX 6700 XT**: 200% → ✅ Acceptable
- **RX 580**: 120% → ✅ Acceptable
- **RX 560**: 100% → ✅ Minimum requirement
- **RX 550**: 70% → ❌ Below minimum
- **Note**: RX 400 series and older R9/R7 series are not supported

### 📈 **Intel Arc Performance Scale** (RX 560 = 100%)
- **Arc A770**: 180% → ✅ Acceptable
- **Arc A750**: 160% → ✅ Acceptable
- **Arc A380**: 110% → ✅ Acceptable
- **Arc A310**: 90% → ⚠️ Limited

## Troubleshooting

### ❌ **Common Issues**

#### "No compatible dedicated GPU found"
- **Cause**: Only integrated graphics detected or unsupported GPU
- **Solution**: Check if dedicated GPU is enabled in BIOS/Device Manager

#### "GPU does not meet RX 560 minimum requirement"
- **Cause**: AMD GPU below RX 560 performance level
- **Solution**: Upgrade GPU or accept limited performance

#### "GPU performance limited to GTX 1080 level"
- **Cause**: NVIDIA GPU exceeds GTX 1080 performance cap
- **Solution**: This is intentional behavior - consider using GTX 1080 or lower

### 🔧 **Manual Override**
If automatic detection fails, you can manually configure:
```renpy
# Force specific settings
$ renpy.config.renderer = "gl2"
$ renpy.config.gl_maximum_texture_size = 2048
$ renpy.config.gl_framerate = 60
```

This system ensures consistent performance across different GPU configurations while enforcing the specified performance caps for optimal visual novel experience.
