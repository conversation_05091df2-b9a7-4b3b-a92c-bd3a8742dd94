# ...existing code...

## Mobile graphics test screen
screen mobile_graphics_test():

    modal True

    # Use a local variable for the toggle, defaulting to True if not set
    default use_mobile_optimizations = mobile_info.get("use_mobile_optimizations", True)

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600

        vbox:
            spacing 20

            text "Mobile Graphics Configuration" size 28 xalign 0.5

            if mobile_info:
                vbox:
                    spacing 10

                    text "Platform: {}".format(mobile_info.get('platform', 'Unknown').title()) size 20
                    text "Renderer: {}".format(mobile_info.get('recommended_renderer', 'Unknown').upper()) size 18
                    text "Performance Class: {}".format(mobile_info.get('performance_class', 'Unknown').title()) size 18
                    text "Vulkan Support: {}".format("Yes" if mobile_info.get('vulkan_support', False) else "No") size 18
                    text "Battery Optimization: {}".format("Enabled" if mobile_info.get('battery_optimization', True) else "Disabled") size 18

                null height 20

                # Only show toggle for Android or iOS, and disable if platform is 'pc'
                if mobile_info.get('platform') in ['android', 'ios']:
                    hbox:
                        spacing 10
                        xalign 0.5
                        text "Mobile Optimizations:" size 18
                        toggle use_mobile_optimizations:
                            action [SetDict(mobile_info, "use_mobile_optimizations", If(use_mobile_optimizations, False, True)), Function(apply_mobile_graphics)]
                            value True
                            text "On"
                            alternate_text "Off"
                            style "radio_button"
                            insensitive mobile_info.get('platform') == 'pc'

                elif mobile_info.get('platform') == 'pc':
                    hbox:
                        spacing 10
                        xalign 0.5
                        text "Mobile Optimizations:" size 18
                        text "Disabled on PC" size 18 color "#888"

                null height 10

                if mobile_info.get('platform') == 'android':
                    frame:
                        background "#2E7D32"
                        xfill True

                        vbox:
                            spacing 5
                            text "Android Optimizations:" size 16 color "#FFFFFF"
                            text "• GLES2 renderer for compatibility" size 14 color "#FFFFFF"
                            text "• Vulkan-aware optimizations" size 14 color "#FFFFFF"
                            text "• Battery-conscious settings" size 14 color "#FFFFFF"
                            text "• Adaptive performance scaling" size 14 color "#FFFFFF"

                elif mobile_info.get('platform') == 'ios':
                    frame:
                        background "#1976D2"
                        xfill True

                        vbox:
                            spacing 5
                            text "iOS Optimizations:" size 16 color "#FFFFFF"
                            text "• GLES2 renderer optimized for iOS" size 14 color "#FFFFFF"
                            text "• Metal-ready configuration" size 14 color "#FFFFFF"
                            text "• Power efficiency focus" size 14 color "#FFFFFF"
                            text "• iOS-specific memory management" size 14 color "#FFFFFF"
            else:
                text "Mobile platform not detected" size 18
                text "Running in desktop mode" size 16

            null height 20

            hbox:
                spacing 20
                xalign 0.5

                textbutton "Refresh Detection" action Function(apply_mobile_graphics)
                textbutton "Performance Test" action Function(check_mobile_performance)
                textbutton "Close" action Return()

# ...existing code...