# Placeholder Replacement Demo
# This demonstrates how the dynamic image system replaces placeholder images

# Define character image references
# define character image Netcode = "netcode.jpg"
# define character image Austin = "austin.png"

label test_placeholder_replacement:
    "Testing placeholder replacement system..."
    
    # Test showing Netcode - should show actual image if netcode.jpg exists
    show Netcode
    "This should show the actual Netcode image instead of a placeholder."
    
    # Test with lowercase
    hide Netcode
    show netcode
    "This should also show the Netcode image (lowercase version)."
    
    # Test Austin if available
    hide netcode
    show Austin
    "This should show Austin if austin.png exists in the directories."
    
    # Test background replacement
    scene bedroom
    "This should show the bedroom background if bedroom.jpg exists."
    
    hide Austin
    "Placeholder replacement test complete!"
    
    python:
        # Show what images were actually defined
        print("=== PLACEHOLDER REPLACEMENT RESULTS ===")
        try:
            # Try to get image information
            if hasattr(renpy.store, 'config') and hasattr(renpy.config, 'images'):
                defined_images = list(renpy.config.images.keys())
                character_images = [img for img in defined_images if any(char in img.lower() for char in ['netcode', 'austin', 'player'])]
                
                if character_images:
                    print("Character images successfully defined:")
                    for img in character_images:
                        print(f"  - {img}")
                else:
                    print("No character images found - check if image files exist in directories")
            
        except Exception as e:
            print(f"Could not retrieve image information: {e}")
    
    return
