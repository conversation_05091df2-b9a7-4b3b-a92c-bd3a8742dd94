## Project Linker Tools and Quick Access
## Easy access tools for the project auto-linker system

# Quick access hotkeys
init python:
    
    # Hotkey bindings for project linker
    config.keymap['project_linker'] = ['shift_K+shift_L']
    config.keymap['quick_move'] = ['shift_K+shift_M']
    config.keymap['validate_links'] = ['shift_K+shift_V']
    config.keymap['rescan_project'] = ['shift_K+shift_R']

# Quick access screen overlay
screen project_linker_overlay():
    
    # Only show if developer mode or linker is enabled
    if config.developer or project_linker.get('enabled', True):
        
        frame:
            xpos 1200
            ypos 300
            xsize 250
            ysize 200
            background "#000000dd"
            
            vbox:
                spacing 10
                
                text "🔗 Project Linker" size 14 color "#00ff00" bold True
                
                # Status indicators
                vbox:
                    spacing 5
                    
                    hbox:
                        spacing 5
                        if project_linker.get('enabled', True):
                            text "●" size 12 color "#00ff00"
                            text "Active" size 10 color "#ffffff"
                        else:
                            text "●" size 12 color "#ff6666"
                            text "Disabled" size 10 color "#ffffff"
                    
                    text "Files: {}".format(len(project_linker.get('file_registry', {}))) size 10 color "#cccccc"
                    text "Links: {}".format(sum(len(deps) for deps in project_linker.get('dependency_graph', {}).values())) size 10 color "#cccccc"
                
                # Quick action buttons
                vbox:
                    spacing 5
                    
                    textbutton "🔗 Manager" action Function(open_project_linker) text_size 10 xsize 200
                    textbutton "📁 Quick Move" action Call("quick_file_mover") text_size 10 xsize 200
                    textbutton "🔄 Rescan" action Function(project_linker_instance.scan_project_files) text_size 10 xsize 200
                    textbutton "🔍 Validate" action Function(quick_validate_links) text_size 10 xsize 200

# Quick file mover
label quick_file_mover:
    
    call screen quick_move_dialog
    
    return

screen quick_move_dialog():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 300
        
        vbox:
            spacing 15
            
            text "📁 Quick File Mover" size 18 xalign 0.5 color "#00ff00"
            
            vbox:
                spacing 10
                
                text "Enter file paths (relative to project root):" size 14
                
                hbox:
                    spacing 10
                    text "From:" size 12
                    input default "" value ScreenVariableInputValue("quick_from_path") length 40
                
                hbox:
                    spacing 10
                    text "To:" size 12
                    input default "" value ScreenVariableInputValue("quick_to_path") length 40
                
                text "Example: game/images/old.png → game/images/new.png" size 10 color "#888888"
            
            hbox:
                spacing 15
                xalign 0.5
                
                textbutton "Move" action Function(quick_move_file) text_size 14
                textbutton "Cancel" action Return() text_size 14

# Drag and drop file mover
screen drag_drop_mover():
    
    # File drag and drop interface
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#1a1a1a"
        
        vbox:
            spacing 20
            xalign 0.5
            yalign 0.5
            
            text "🎯 Drag & Drop File Mover" size 20 color "#00ff00" xalign 0.5
            
            # Drop zones
            hbox:
                spacing 30
                xalign 0.5
                
                # Source zone
                frame:
                    background "#2e2e2e"
                    xsize 250
                    ysize 200
                    
                    vbox:
                        spacing 10
                        xalign 0.5
                        yalign 0.5
                        
                        text "📂 SOURCE" size 16 color "#ffaa00" xalign 0.5
                        text "Drop file here" size 12 color "#cccccc" xalign 0.5
                        
                        if drag_source_file:
                            text drag_source_file size 10 color "#ffffff" xalign 0.5
                
                # Arrow
                text "→" size 30 color "#00ff00"
                
                # Destination zone
                frame:
                    background "#2e2e2e"
                    xsize 250
                    ysize 200
                    
                    vbox:
                        spacing 10
                        xalign 0.5
                        yalign 0.5
                        
                        text "📁 DESTINATION" size 16 color "#ffaa00" xalign 0.5
                        text "Drop target here" size 12 color "#cccccc" xalign 0.5
                        
                        if drag_dest_path:
                            text drag_dest_path size 10 color "#ffffff" xalign 0.5
            
            # Action buttons
            hbox:
                spacing 15
                xalign 0.5
                
                if drag_source_file and drag_dest_path:
                    textbutton "🚀 Execute Move" action Function(execute_drag_move) text_size 14
                
                textbutton "Clear" action Function(clear_drag_data) text_size 14
                textbutton "Close" action Return() text_size 14

# Batch file operations
screen batch_operations():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        
        vbox:
            spacing 20
            
            text "⚡ Batch File Operations" size 20 xalign 0.5 color "#00ff00"
            
            # Operation selection
            hbox:
                spacing 15
                xalign 0.5
                
                textbutton "Move Multiple Files" action SetScreenVariable("batch_operation", "move") text_size 14
                textbutton "Rename Pattern" action SetScreenVariable("batch_operation", "rename") text_size 14
                textbutton "Reorganize by Type" action SetScreenVariable("batch_operation", "organize") text_size 14
            
            # Operation interface
            if batch_operation == "move":
                use batch_move_interface
            elif batch_operation == "rename":
                use batch_rename_interface
            elif batch_operation == "organize":
                use batch_organize_interface
            
            textbutton "Close" action Return() xalign 0.5

screen batch_move_interface():
    
    vbox:
        spacing 15
        
        text "📁 Batch Move Files" size 16 color "#ffaa00"
        
        # File selection
        frame:
            background "#2e2e2e"
            padding (15, 10)
            xfill True
            ysize 200
            
            vbox:
                spacing 10
                
                text "Select files to move:" size 14
                
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 5
                        
                        for file_path in sorted(project_linker.get('file_registry', {}).keys()):
                            textbutton file_path action ToggleSetMembership(batch_selected_files, file_path) text_size 10
        
        # Destination
        hbox:
            spacing 10
            text "Destination folder:" size 14
            input default "game/" value ScreenVariableInputValue("batch_dest_folder") length 30
        
        textbutton "Move Selected Files" action Function(execute_batch_move) text_size 14

screen batch_rename_interface():
    
    vbox:
        spacing 15
        
        text "✏️ Batch Rename Files" size 16 color "#ffaa00"
        
        vbox:
            spacing 10
            
            hbox:
                spacing 10
                text "Find pattern:" size 14
                input default "" value ScreenVariableInputValue("rename_find_pattern") length 20
            
            hbox:
                spacing 10
                text "Replace with:" size 14
                input default "" value ScreenVariableInputValue("rename_replace_pattern") length 20
            
            text "Example: Find 'old_' Replace 'new_'" size 10 color "#888888"
        
        textbutton "Preview Rename" action Function(preview_batch_rename) text_size 14
        textbutton "Execute Rename" action Function(execute_batch_rename) text_size 14

screen batch_organize_interface():
    
    vbox:
        spacing 15
        
        text "🗂️ Organize Files by Type" size 16 color "#ffaa00"
        
        vbox:
            spacing 10
            
            text "This will organize files into folders by type:" size 14
            text "• Images → game/images/" size 12 color "#cccccc"
            text "• Audio → game/audio/" size 12 color "#cccccc"
            text "• Scripts → game/scripts/" size 12 color "#cccccc"
            text "• Fonts → game/fonts/" size 12 color "#cccccc"
        
        hbox:
            spacing 15
            
            textbutton "Preview Organization" action Function(preview_organization) text_size 14
            textbutton "Execute Organization" action Function(execute_organization) text_size 14

# Helper functions
init python:
    
    # Quick access variables
    quick_from_path = ""
    quick_to_path = ""
    drag_source_file = ""
    drag_dest_path = ""
    batch_operation = ""
    batch_selected_files = set()
    batch_dest_folder = "game/"
    rename_find_pattern = ""
    rename_replace_pattern = ""
    
    def quick_validate_links():
        """Quick link validation with notification"""
        broken_links = project_linker_instance.validate_links()
        if broken_links:
            renpy.notify("⚠️ {} broken links found".format(len(broken_links)))
        else:
            renpy.notify("✅ All links valid")
    
    def quick_move_file():
        """Quick file move function"""
        global quick_from_path, quick_to_path
        
        if not quick_from_path or not quick_to_path:
            renpy.notify("Please enter both source and destination paths")
            return
        
        old_full_path = os.path.join(project_linker['project_root'], quick_from_path)
        new_full_path = os.path.join(project_linker['project_root'], quick_to_path)
        
        if not os.path.exists(old_full_path):
            renpy.notify("Source file does not exist")
            return
        
        if project_linker_instance.move_file(old_full_path, new_full_path):
            renpy.notify("✅ File moved successfully")
            quick_from_path = ""
            quick_to_path = ""
        else:
            renpy.notify("❌ Failed to move file")
    
    def clear_drag_data():
        """Clear drag and drop data"""
        global drag_source_file, drag_dest_path
        drag_source_file = ""
        drag_dest_path = ""
    
    def execute_drag_move():
        """Execute drag and drop move"""
        global drag_source_file, drag_dest_path
        
        if not drag_source_file or not drag_dest_path:
            renpy.notify("Please set both source and destination")
            return
        
        old_full_path = os.path.join(project_linker['project_root'], drag_source_file)
        new_full_path = os.path.join(project_linker['project_root'], drag_dest_path)
        
        if project_linker_instance.move_file(old_full_path, new_full_path):
            renpy.notify("✅ Drag move completed")
            clear_drag_data()
        else:
            renpy.notify("❌ Drag move failed")
    
    def execute_batch_move():
        """Execute batch file move"""
        global batch_selected_files, batch_dest_folder
        
        if not batch_selected_files:
            renpy.notify("Please select files to move")
            return
        
        moved_count = 0
        for file_path in batch_selected_files:
            old_full_path = os.path.join(project_linker['project_root'], file_path)
            filename = os.path.basename(file_path)
            new_full_path = os.path.join(project_linker['project_root'], batch_dest_folder, filename)
            
            if project_linker_instance.move_file(old_full_path, new_full_path):
                moved_count += 1
        
        renpy.notify("✅ Moved {} of {} files".format(moved_count, len(batch_selected_files)))
        batch_selected_files.clear()
    
    def preview_batch_rename():
        """Preview batch rename operation"""
        global rename_find_pattern, rename_replace_pattern
        
        if not rename_find_pattern:
            renpy.notify("Please enter a find pattern")
            return
        
        affected_files = []
        for file_path in project_linker.get('file_registry', {}).keys():
            if rename_find_pattern in os.path.basename(file_path):
                affected_files.append(file_path)
        
        renpy.notify("Would rename {} files".format(len(affected_files)))
    
    def execute_batch_rename():
        """Execute batch rename operation"""
        global rename_find_pattern, rename_replace_pattern
        
        if not rename_find_pattern or not rename_replace_pattern:
            renpy.notify("Please enter both find and replace patterns")
            return
        
        renamed_count = 0
        for file_path in list(project_linker.get('file_registry', {}).keys()):
            filename = os.path.basename(file_path)
            if rename_find_pattern in filename:
                new_filename = filename.replace(rename_find_pattern, rename_replace_pattern)
                new_path = os.path.join(os.path.dirname(file_path), new_filename)
                
                old_full_path = os.path.join(project_linker['project_root'], file_path)
                new_full_path = os.path.join(project_linker['project_root'], new_path)
                
                if project_linker_instance.move_file(old_full_path, new_full_path):
                    renamed_count += 1
        
        renpy.notify("✅ Renamed {} files".format(renamed_count))
    
    def preview_organization():
        """Preview file organization"""
        type_counts = defaultdict(int)
        for file_info in project_linker.get('file_registry', {}).values():
            type_counts[file_info.get('file_type', 'other')] += 1
        
        message = "Would organize: "
        for file_type, count in type_counts.items():
            message += "{} {} files, ".format(count, file_type)
        
        renpy.notify(message.rstrip(', '))
    
    def execute_organization():
        """Execute file organization by type"""
        organized_count = 0
        
        for file_path, file_info in list(project_linker.get('file_registry', {}).items()):
            file_type = file_info.get('file_type', 'other')
            
            if file_type in ['images', 'audio', 'fonts', 'videos']:
                target_dir = os.path.join('game', file_type)
                filename = os.path.basename(file_path)
                new_path = os.path.join(target_dir, filename)
                
                if file_path != new_path:
                    old_full_path = os.path.join(project_linker['project_root'], file_path)
                    new_full_path = os.path.join(project_linker['project_root'], new_path)
                    
                    if project_linker_instance.move_file(old_full_path, new_full_path):
                        organized_count += 1
        
        renpy.notify("✅ Organized {} files".format(organized_count))

# Default screen variables
default quick_from_path = ""
default quick_to_path = ""
default drag_source_file = ""
default drag_dest_path = ""
default batch_operation = ""
default batch_selected_files = set()
default batch_dest_folder = "game/"
default rename_find_pattern = ""
default rename_replace_pattern = ""
