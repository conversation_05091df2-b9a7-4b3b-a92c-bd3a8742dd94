## NSFW Detection Integration with Anti-Cheat System
## Automatic adult mode activation and content monitoring

# Enhanced game start with NSFW detection
label start_with_nsfw_protection:
    
    # Initialize anti-cheat system first
    call initialize_anticheat
    
    # Check if adult mode was auto-activated during initialization
    if persistent.adult_mode_enabled and not nsfw_state.get('age_verified', False):
        "Adult content has been detected in this visual novel."
        "Age verification is required to continue."
        call age_verification_process
    
    # Show NSFW status in developer mode
    if config.developer:
        show screen nsfw_status
    
    return

# Enhanced character definition with NSFW monitoring
define narrator_nsfw = Character(None, callback=nsfw_character_callback)

# NSFW-aware dialogue function
init python:
    
    def say_with_nsfw_check(character, text, **kwargs):
        """Enhanced say function with NSFW content checking"""
        
        # Scan text for NSFW content before displaying
        if NSFW_CONFIG.get('enabled', True) and text:
            scan_result = nsfw_detector.scan_text_content(text)
            
            if scan_result['nsfw_detected']:
                # Handle NSFW detection
                confidence = scan_result['confidence']
                triggers = scan_result['triggers']
                
                renpy.log("NSFW text detected - Confidence: {}%, Triggers: {}".format(
                    confidence, triggers[:3]))
                
                # Auto-enable adult mode if not already active
                if not nsfw_state.get('adult_mode_active', False):
                    nsfw_detector.enable_adult_mode("Explicit dialogue detected")
                    
                    # Show adult mode activation screen
                    renpy.call_screen("adult_mode_activated")
                
                # Show content warning for high-confidence detections
                if confidence > 70:
                    content_type = "explicit" if scan_result['explicit_count'] > 0 else "suggestive"
                    proceed = renpy.call_screen("nsfw_content_warning", content_type=content_type)
                    
                    if not proceed:
                        # User chose to skip content
                        renpy.say(character, "[Content skipped due to user preference]")
                        return
                
                # Verify age if not already done
                if not nsfw_state.get('age_verified', False):
                    renpy.call("age_verification_process")
        
        # Display the text normally
        renpy.say(character, text, **kwargs)
    
    # Function to check and handle NSFW images
    def show_image_with_nsfw_check(image_name):
        """Show image with NSFW content checking"""
        
        if NSFW_CONFIG.get('scan_images', True):
            # Check if image filename suggests NSFW content
            for pattern in nsfw_detector.image_patterns:
                if pattern.match(image_name):
                    renpy.log("NSFW image detected: {}".format(image_name))
                    
                    # Auto-enable adult mode
                    if not nsfw_state.get('adult_mode_active', False):
                        nsfw_detector.enable_adult_mode("Adult imagery detected: {}".format(image_name))
                        renpy.call_screen("adult_mode_activated")
                    
                    # Show content warning
                    proceed = renpy.call_screen("nsfw_content_warning", content_type="explicit")
                    
                    if not proceed:
                        # Show placeholder or skip
                        renpy.show("black")
                        return
                    
                    # Verify age
                    if not nsfw_state.get('age_verified', False):
                        renpy.call("age_verification_process")
                    
                    break
        
        # Show the image normally
        renpy.show(image_name)
    
    # Function to play audio with NSFW checking
    def play_audio_with_nsfw_check(audio_file):
        """Play audio with NSFW content checking"""
        
        if NSFW_CONFIG.get('scan_audio', True):
            # Check if audio filename suggests NSFW content
            for pattern in nsfw_detector.audio_patterns:
                if pattern.match(audio_file):
                    renpy.log("NSFW audio detected: {}".format(audio_file))
                    
                    # Auto-enable adult mode
                    if not nsfw_state.get('adult_mode_active', False):
                        nsfw_detector.enable_adult_mode("Adult audio detected: {}".format(audio_file))
                        renpy.call_screen("adult_mode_activated")
                    
                    # Show content warning
                    proceed = renpy.call_screen("nsfw_content_warning", content_type="explicit")
                    
                    if not proceed:
                        # Skip audio
                        return
                    
                    # Verify age
                    if not nsfw_state.get('age_verified', False):
                        renpy.call("age_verification_process")
                    
                    break
        
        # Play audio normally
        renpy.play(audio_file)

# Enhanced menu system with NSFW awareness
screen main_menu_nsfw():
    
    tag menu
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            text "Netcode the Protogen and More" size 40 color "#FFFFFF" xalign 0.5
            
            # Adult mode indicator
            if nsfw_state.get('adult_mode_active', False):
                frame:
                    background "#8B0000"
                    padding (20, 10)
                    
                    hbox:
                        spacing 10
                        xalign 0.5
                        
                        text "🔞" size 24
                        text "ADULT MODE ACTIVE" size 16 color "#FFFFFF" bold True
                        
                        if nsfw_state.get('age_verified', False):
                            text "✓" size 20 color "#4CAF50"
                        else:
                            text "⚠" size 20 color "#FF9800"
            
            vbox:
                spacing 15
                xalign 0.5
                
                textbutton "Start Game" action [
                    Function(anticheat_core.record_user_action, "menu_selection"),
                    Start()
                ]
                
                textbutton "Load Game" action ShowMenu("load")
                textbutton "Preferences" action ShowMenu("preferences")
                textbutton "About" action ShowMenu("about")
                
                if nsfw_state.get('adult_mode_active', False) and not nsfw_state.get('age_verified', False):
                    textbutton "Age Verification" action Call("age_verification_process")
                
                if config.developer:
                    textbutton "NSFW Configuration" action Call("nsfw_config_menu")
                    textbutton "Anti-Cheat Status" action Call("anticheat_config")
                
                textbutton "Quit" action Quit()

# NSFW configuration menu (developer mode)
label nsfw_config_menu:
    if config.developer:
        call screen nsfw_config
    return

# Example of NSFW-aware scene
label example_nsfw_scene:
    
    scene bg bedroom
    
    # This text will trigger NSFW detection
    narrator_nsfw "The intimate atmosphere of the bedroom was undeniable..."
    
    # This would trigger adult mode if not already active
    narrator_nsfw "Their bodies pressed together in passionate embrace..."
    
    # Show NSFW image with checking
    $ show_image_with_nsfw_check("intimate_scene.png")
    
    # Play NSFW audio with checking
    $ play_audio_with_nsfw_check("romantic_music.ogg")
    
    return

# Automatic NSFW scanning on game start
label auto_nsfw_scan:
    
    if NSFW_CONFIG.get('enabled', True):
        "Scanning content for age-appropriate material..."
        
        python:
            # Perform comprehensive scan
            scan_results = nsfw_detector.perform_full_scan()
            
            if scan_results['nsfw_detected']:
                # Handle detection
                nsfw_detector.handle_nsfw_detection(scan_results)
        
        if nsfw_state.get('adult_mode_active', False):
            "Adult content detected. Age verification required."
            call age_verification_process
    
    return

# Integration with save/load system
init python:
    
    def save_nsfw_state():
        """Save NSFW state with game save"""
        return {
            'adult_mode_active': nsfw_state.get('adult_mode_active', False),
            'age_verified': nsfw_state.get('age_verified', False),
            'detection_count': nsfw_state.get('detection_count', 0),
            'content_warnings': nsfw_state.get('content_warnings', [])
        }
    
    def load_nsfw_state(state_data):
        """Load NSFW state from game save"""
        if state_data:
            nsfw_state.update(state_data)
    
    # Hook into save/load callbacks
    config.save_json_callbacks.append(save_nsfw_state)
    config.load_json_callbacks.append(load_nsfw_state)

# Parental controls integration
init python:
    
    def check_parental_controls():
        """Check if parental controls should block content"""
        
        # Check time-based restrictions
        import datetime
        current_hour = datetime.datetime.now().hour
        
        # Block adult content during certain hours (example: 6 AM to 10 PM)
        if nsfw_state.get('adult_mode_active', False) and 6 <= current_hour <= 22:
            if not persistent.parental_override:
                return False
        
        return True
    
    def enable_parental_override(password):
        """Enable parental override with password"""
        # Simple password check (in production, use proper hashing)
        if password == "adult123":  # Replace with secure password
            persistent.parental_override = True
            renpy.notify("Parental controls overridden")
            return True
        else:
            renpy.notify("Invalid password")
            return False

# Default persistent values for parental controls
default persistent.parental_override = False

# Enhanced character callback for real-time monitoring
def enhanced_nsfw_callback(event, interact=True, **kwargs):
    """Enhanced character callback with comprehensive NSFW monitoring"""
    
    if event == "show_done" and interact:
        what = kwargs.get('what', '')
        
        if what and NSFW_CONFIG.get('enabled', True):
            # Real-time text scanning
            scan_result = nsfw_detector.scan_text_content(what)
            
            if scan_result['nsfw_detected']:
                # Record detection
                nsfw_state['detection_count'] += 1
                nsfw_state['last_detection'] = time.time()
                
                # Auto-enable adult mode if threshold reached
                if (nsfw_state['detection_count'] >= nsfw_detector.detection_threshold and 
                    not nsfw_state.get('adult_mode_active', False)):
                    
                    nsfw_detector.enable_adult_mode(
                        "Multiple NSFW detections: {}".format(nsfw_state['detection_count']))

# Register enhanced callback
config.all_character_callbacks.append(enhanced_nsfw_callback)
