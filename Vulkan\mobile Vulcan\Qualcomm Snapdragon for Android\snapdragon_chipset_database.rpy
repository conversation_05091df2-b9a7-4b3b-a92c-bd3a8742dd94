## Qualcomm Snapdragon Chipset Database
## Comprehensive specifications for Snapdragon 835 and higher chipsets
## Optimized for Android mobile gaming with Vulkan support

init python:
    
    # Snapdragon Chipset Database with detailed specifications
    SNAPDRAGON_CHIPSET_DATABASE = {
        # Snapdragon 8 Gen Series (4nm-5nm) - 2021-2024
        "8_gen_series": {
            "Snapdragon 8 Gen 3": {
                "model_number": "SM8650",
                "process_node": "4nm (TSMC N4P)",
                "cpu_cores": "8 (1+5+2)",
                "cpu_config": "1x Cortex-X4 @ 3.3GHz + 5x Cortex-A720 @ 3.2GHz + 2x Cortex-A520 @ 2.3GHz",
                "adreno_gpu": "Adreno 750",
                "gpu_frequency": "770 MHz",
                "memory_type": "LPDDR5X",
                "memory_bandwidth": "77 GB/s",
                "vulkan_version": "1.3",
                "opengl_es": "3.2",
                "opencl": "3.0",
                "performance_tier": "Flagship",
                "ray_tracing": True,
                "variable_rate_shading": True,
                "mesh_shaders": True,
                "release_year": 2023,
                "antutu_score": "1500000+",
                "power_efficiency": "Excellent",
                "gaming_performance": "Ultra High"
            },
            "Snapdragon 8 Gen 2": {
                "model_number": "SM8550",
                "process_node": "4nm (TSMC N4)",
                "cpu_cores": "8 (1+4+3)",
                "cpu_config": "1x Cortex-X3 @ 3.2GHz + 4x Cortex-A715 @ 2.8GHz + 3x Cortex-A510 @ 2.0GHz",
                "adreno_gpu": "Adreno 740",
                "gpu_frequency": "680 MHz",
                "memory_type": "LPDDR5X",
                "memory_bandwidth": "64 GB/s",
                "vulkan_version": "1.3",
                "opengl_es": "3.2",
                "opencl": "3.0",
                "performance_tier": "Flagship",
                "ray_tracing": True,
                "variable_rate_shading": True,
                "mesh_shaders": False,
                "release_year": 2022,
                "antutu_score": "1300000+",
                "power_efficiency": "Excellent",
                "gaming_performance": "Ultra High"
            },
            "Snapdragon 8 Gen 1": {
                "model_number": "SM8450",
                "process_node": "4nm (Samsung 4LPE)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-X2 @ 3.0GHz + 3x Cortex-A710 @ 2.5GHz + 4x Cortex-A510 @ 1.8GHz",
                "adreno_gpu": "Adreno 730",
                "gpu_frequency": "818 MHz",
                "memory_type": "LPDDR5",
                "memory_bandwidth": "51.2 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Flagship",
                "ray_tracing": False,
                "variable_rate_shading": True,
                "mesh_shaders": False,
                "release_year": 2021,
                "antutu_score": "1000000+",
                "power_efficiency": "Good",
                "gaming_performance": "Very High"
            }
        },
        
        # Snapdragon 800 Series (5nm-10nm) - 2016-2021
        "800_series": {
            "Snapdragon 888": {
                "model_number": "SM8350",
                "process_node": "5nm (Samsung 5LPE)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-X1 @ 2.84GHz + 3x Cortex-A78 @ 2.42GHz + 4x Cortex-A55 @ 1.8GHz",
                "adreno_gpu": "Adreno 660",
                "gpu_frequency": "840 MHz",
                "memory_type": "LPDDR5",
                "memory_bandwidth": "44 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "High-End",
                "ray_tracing": False,
                "variable_rate_shading": True,
                "mesh_shaders": False,
                "release_year": 2020,
                "antutu_score": "800000+",
                "power_efficiency": "Good",
                "gaming_performance": "Very High"
            },
            "Snapdragon 865": {
                "model_number": "SM8250",
                "process_node": "7nm (TSMC N7P)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-A77 @ 2.84GHz + 3x Cortex-A77 @ 2.42GHz + 4x Cortex-A55 @ 1.8GHz",
                "adreno_gpu": "Adreno 650",
                "gpu_frequency": "587 MHz",
                "memory_type": "LPDDR5/LPDDR4X",
                "memory_bandwidth": "44 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "High-End",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2019,
                "antutu_score": "600000+",
                "power_efficiency": "Good",
                "gaming_performance": "High"
            },
            "Snapdragon 855": {
                "model_number": "SM8150",
                "process_node": "7nm (TSMC N7)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-A76 @ 2.84GHz + 3x Cortex-A76 @ 2.42GHz + 4x Cortex-A55 @ 1.78GHz",
                "adreno_gpu": "Adreno 640",
                "gpu_frequency": "585 MHz",
                "memory_type": "LPDDR4X",
                "memory_bandwidth": "34.1 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "High-End",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2018,
                "antutu_score": "450000+",
                "power_efficiency": "Good",
                "gaming_performance": "High"
            },
            "Snapdragon 845": {
                "model_number": "SDM845",
                "process_node": "10nm (TSMC N10)",
                "cpu_cores": "8 (4+4)",
                "cpu_config": "4x Cortex-A75 @ 2.8GHz + 4x Cortex-A55 @ 1.77GHz",
                "adreno_gpu": "Adreno 630",
                "gpu_frequency": "710 MHz",
                "memory_type": "LPDDR4X",
                "memory_bandwidth": "29.8 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Upper Mid-Range",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2017,
                "antutu_score": "300000+",
                "power_efficiency": "Fair",
                "gaming_performance": "Medium-High"
            },
            "Snapdragon 835": {
                "model_number": "MSM8998",
                "process_node": "10nm (Samsung 10LPE)",
                "cpu_cores": "8 (4+4)",
                "cpu_config": "4x Cortex-A73 @ 2.45GHz + 4x Cortex-A53 @ 1.9GHz",
                "adreno_gpu": "Adreno 540",
                "gpu_frequency": "710 MHz",
                "memory_type": "LPDDR4X",
                "memory_bandwidth": "29.8 GB/s",
                "vulkan_version": "1.0",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Upper Mid-Range",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2016,
                "antutu_score": "200000+",
                "power_efficiency": "Fair",
                "gaming_performance": "Medium"
            }
        },
        
        # Snapdragon 700 Series (6nm-8nm) - 2020-2023
        "700_series": {
            "Snapdragon 7 Gen 3": {
                "model_number": "SM7550",
                "process_node": "4nm (TSMC N4)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-A715 @ 2.63GHz + 3x Cortex-A715 @ 2.4GHz + 4x Cortex-A510 @ 1.8GHz",
                "adreno_gpu": "Adreno 720",
                "gpu_frequency": "550 MHz",
                "memory_type": "LPDDR5",
                "memory_bandwidth": "32 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Mid-Range",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2023,
                "antutu_score": "700000+",
                "power_efficiency": "Very Good",
                "gaming_performance": "Medium-High"
            },
            "Snapdragon 780G": {
                "model_number": "SM7350",
                "process_node": "5nm (TSMC N5)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-A78 @ 2.4GHz + 3x Cortex-A78 @ 2.2GHz + 4x Cortex-A55 @ 1.9GHz",
                "adreno_gpu": "Adreno 642",
                "gpu_frequency": "490 MHz",
                "memory_type": "LPDDR5",
                "memory_bandwidth": "29 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Mid-Range",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2021,
                "antutu_score": "550000+",
                "power_efficiency": "Very Good",
                "gaming_performance": "Medium"
            },
            "Snapdragon 778G": {
                "model_number": "SM7325",
                "process_node": "6nm (TSMC N6)",
                "cpu_cores": "8 (1+3+4)",
                "cpu_config": "1x Cortex-A78 @ 2.4GHz + 3x Cortex-A78 @ 2.2GHz + 4x Cortex-A55 @ 1.9GHz",
                "adreno_gpu": "Adreno 642L",
                "gpu_frequency": "490 MHz",
                "memory_type": "LPDDR5",
                "memory_bandwidth": "29 GB/s",
                "vulkan_version": "1.1",
                "opengl_es": "3.2",
                "opencl": "2.0",
                "performance_tier": "Mid-Range",
                "ray_tracing": False,
                "variable_rate_shading": False,
                "mesh_shaders": False,
                "release_year": 2021,
                "antutu_score": "500000+",
                "power_efficiency": "Very Good",
                "gaming_performance": "Medium"
            }
        }
    }
    
    # Android Device to Snapdragon Mapping (Popular Models)
    ANDROID_DEVICE_SNAPDRAGON_MAPPING = {
        # Samsung Galaxy S Series
        "Galaxy S24 Ultra": "Snapdragon 8 Gen 3",
        "Galaxy S24+": "Snapdragon 8 Gen 3",
        "Galaxy S24": "Snapdragon 8 Gen 3",
        "Galaxy S23 Ultra": "Snapdragon 8 Gen 2",
        "Galaxy S23+": "Snapdragon 8 Gen 2",
        "Galaxy S23": "Snapdragon 8 Gen 2",
        "Galaxy S22 Ultra": "Snapdragon 8 Gen 1",
        "Galaxy S21 Ultra": "Snapdragon 888",
        "Galaxy S20 Ultra": "Snapdragon 865",
        
        # OnePlus
        "OnePlus 12": "Snapdragon 8 Gen 3",
        "OnePlus 11": "Snapdragon 8 Gen 2",
        "OnePlus 10 Pro": "Snapdragon 8 Gen 1",
        "OnePlus 9 Pro": "Snapdragon 888",
        "OnePlus 8 Pro": "Snapdragon 865",
        
        # Xiaomi
        "Xiaomi 14 Ultra": "Snapdragon 8 Gen 3",
        "Xiaomi 13 Ultra": "Snapdragon 8 Gen 2",
        "Xiaomi 12 Ultra": "Snapdragon 8 Gen 1",
        "Xiaomi Mi 11 Ultra": "Snapdragon 888",
        
        # Google Pixel (Tensor chips, but some models use Snapdragon)
        "Pixel 7a": "Snapdragon 778G",
        "Pixel 6a": "Snapdragon 778G"
    }
    
    def get_detailed_snapdragon_specs(chipset_name):
        """Get detailed specifications for a specific Snapdragon chipset"""
        for series, chipsets in SNAPDRAGON_CHIPSET_DATABASE.items():
            if chipset_name in chipsets:
                return chipsets[chipset_name]
        return None
    
    def get_snapdragon_performance_comparison():
        """Get performance comparison of all Snapdragon chipsets"""
        performance_tiers = {
            'Flagship': [],
            'High-End': [],
            'Upper Mid-Range': [],
            'Mid-Range': []
        }
        
        for series, chipsets in SNAPDRAGON_CHIPSET_DATABASE.items():
            for chipset_name, specs in chipsets.items():
                tier = specs.get('performance_tier', 'Mid-Range')
                if tier in performance_tiers:
                    performance_tiers[tier].append(chipset_name)
        
        return performance_tiers
    
    def get_vulkan_supported_chipsets():
        """Get list of Snapdragon chipsets with Vulkan support"""
        vulkan_chipsets = []
        for series, chipsets in SNAPDRAGON_CHIPSET_DATABASE.items():
            for chipset_name, specs in chipsets.items():
                if specs.get('vulkan_version'):
                    vulkan_chipsets.append({
                        'chipset': chipset_name,
                        'vulkan_version': specs['vulkan_version'],
                        'performance_tier': specs['performance_tier']
                    })
        return vulkan_chipsets
    
    def print_snapdragon_database_summary():
        """Print summary of Snapdragon chipset database"""
        print("\n=== SNAPDRAGON CHIPSET DATABASE SUMMARY ===")
        
        for series, chipsets in SNAPDRAGON_CHIPSET_DATABASE.items():
            print(f"\n{series.replace('_', ' ').title()}:")
            for chipset_name, specs in chipsets.items():
                vulkan_status = f"Vulkan {specs.get('vulkan_version', 'N/A')}"
                print(f"  {chipset_name}: {specs['adreno_gpu']}, {specs['performance_tier']}, {vulkan_status}")
        
        total_chipsets = sum(len(chipsets) for chipsets in SNAPDRAGON_CHIPSET_DATABASE.values())
        vulkan_count = len(get_vulkan_supported_chipsets())
        print(f"\nTotal chipsets in database: {total_chipsets}")
        print(f"Vulkan-supported chipsets: {vulkan_count}")
        
        print("\n" + "="*50)
