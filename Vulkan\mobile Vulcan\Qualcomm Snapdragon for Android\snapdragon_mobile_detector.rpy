## Qualcomm Snapdragon Mobile Detection and Configuration System
## Detects Snapdragon 835 and higher chipsets on Android devices
## Supports Snapdragon 8 Gen series, 7 series, 6 series with Vulkan optimization

init python:
    import os
    import sys
    import platform
    import subprocess
    import re
    
    class SnapdragonMobileDetector:
        """
        Comprehensive Qualcomm Snapdragon detection system for Android devices
        Optimizes Ren'Py for Snapdragon 835 and higher with Vulkan support
        """
        
        def __init__(self):
            self.detected_chipset = None
            self.chipset_series = None
            self.chipset_generation = None
            self.is_android_device = False
            self.is_mobile = False
            self.chipset_capabilities = {}
            self.recommended_settings = {}
            self.performance_tier = None
            self.vulkan_support = False
            self.adreno_gpu = None
            
        def detect_snapdragon_chipset(self):
            """
            Main function to detect Snapdragon chipsets and configure settings
            """
            print("=== QUALCOMM SNAPDRAGON DETECTION ===")
            
            try:
                # Check if running on Android device
                if not self._is_android_device():
                    print("❌ Not running on Android device")
                    return False
                
                # Check if running on mobile device
                if not self._is_mobile_device():
                    print("❌ Not running on mobile device")
                    return False
                
                # Detect specific Snapdragon chipset
                chipset_info = self._detect_chipset_model()
                
                if chipset_info:
                    self.detected_chipset = chipset_info['chipset']
                    self.chipset_series = chipset_info['series']
                    self.chipset_generation = chipset_info['generation']
                    self.adreno_gpu = chipset_info.get('adreno_gpu')
                    
                    # Check if chipset meets minimum requirements (835+)
                    if not self._meets_minimum_requirements():
                        print(f"❌ {self.detected_chipset} does not meet Snapdragon 835 minimum requirement")
                        return False
                    
                    # Get chipset capabilities
                    self._analyze_chipset_capabilities()
                    
                    # Configure Ren'Py settings
                    self._configure_renpy_for_snapdragon()
                    
                    # Generate report
                    self._generate_snapdragon_report()
                    
                    return True
                else:
                    print("⚠️  Could not detect Snapdragon chipset")
                    return False
                    
            except Exception as e:
                print(f"Error in Snapdragon detection: {e}")
                return False
        
        def _is_android_device(self):
            """Check if running on Android device"""
            try:
                # Check platform
                if platform.system() == "Linux":
                    # Check for Android-specific indicators
                    if hasattr(sys, 'platform'):
                        if 'android' in sys.platform.lower():
                            self.is_android_device = True
                            return True
                
                # Check Ren'Py variants for Android
                if hasattr(renpy, 'variant'):
                    if 'android' in renpy.variant():
                        self.is_android_device = True
                        return True
                
                # Check for Android environment variables
                if os.environ.get('ANDROID_ROOT') or os.environ.get('ANDROID_DATA'):
                    self.is_android_device = True
                    return True
                
                return False
                
            except Exception as e:
                print(f"Error checking Android device: {e}")
                return False
        
        def _is_mobile_device(self):
            """Check if running on mobile Android device"""
            try:
                # Check Ren'Py variants
                if hasattr(renpy, 'variant'):
                    variants = renpy.variant()
                    if any(variant in variants for variant in ['android', 'mobile', 'touch', 'small']):
                        self.is_mobile = True
                        print("✅ Android mobile device detected")
                        return True
                
                # Check screen size indicators for mobile
                if hasattr(renpy, 'config'):
                    screen_width = getattr(renpy.config, 'screen_width', 0)
                    screen_height = getattr(renpy.config, 'screen_height', 0)
                    
                    # Typical mobile screen dimensions
                    if (screen_width <= 1920 and screen_height <= 2400) or (screen_width <= 1080):
                        self.is_mobile = True
                        print("✅ Mobile screen dimensions detected")
                        return True
                
                return False
                
            except Exception as e:
                print(f"Error checking mobile device: {e}")
                return False
        
        def _detect_chipset_model(self):
            """Detect specific Snapdragon chipset model"""
            try:
                chipset_info = None
                
                # Method 1: Try to read from /proc/cpuinfo
                chipset_info = self._detect_from_cpuinfo()
                
                # Method 2: Try to read from system properties
                if not chipset_info:
                    chipset_info = self._detect_from_system_properties()
                
                # Method 3: Try alternative detection methods
                if not chipset_info:
                    chipset_info = self._detect_chipset_alternative()
                
                return chipset_info
                
            except Exception as e:
                print(f"Error detecting chipset model: {e}")
                return None
        
        def _detect_from_cpuinfo(self):
            """Detect chipset from /proc/cpuinfo"""
            try:
                if os.path.exists('/proc/cpuinfo'):
                    with open('/proc/cpuinfo', 'r') as f:
                        cpuinfo = f.read().lower()
                    
                    return self._parse_chipset_from_text(cpuinfo)
                
                return None
                
            except Exception as e:
                print(f"Error reading /proc/cpuinfo: {e}")
                return None
        
        def _detect_from_system_properties(self):
            """Detect chipset from Android system properties"""
            try:
                # Try to get system properties using getprop
                result = subprocess.run(['getprop', 'ro.board.platform'], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0 and result.stdout.strip():
                    platform_info = result.stdout.strip().lower()
                    return self._parse_chipset_from_text(platform_info)
                
                # Try alternative property
                result = subprocess.run(['getprop', 'ro.chipname'], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0 and result.stdout.strip():
                    chipname = result.stdout.strip().lower()
                    return self._parse_chipset_from_text(chipname)
                
                return None
                
            except Exception as e:
                print(f"Error getting system properties: {e}")
                return None
        
        def _parse_chipset_from_text(self, text):
            """Parse Snapdragon chipset information from text"""
            text_lower = text.lower()
            
            # Snapdragon 8 Gen 3 (2023-2024)
            if any(pattern in text_lower for pattern in ['8 gen 3', '8gen3', 'sm8650']):
                return {
                    'chipset': 'Snapdragon 8 Gen 3',
                    'series': '8 Gen',
                    'generation': 'Gen 3',
                    'adreno_gpu': 'Adreno 750'
                }
            
            # Snapdragon 8 Gen 2 (2022-2023)
            elif any(pattern in text_lower for pattern in ['8 gen 2', '8gen2', 'sm8550']):
                return {
                    'chipset': 'Snapdragon 8 Gen 2',
                    'series': '8 Gen',
                    'generation': 'Gen 2',
                    'adreno_gpu': 'Adreno 740'
                }
            
            # Snapdragon 8 Gen 1 (2021-2022)
            elif any(pattern in text_lower for pattern in ['8 gen 1', '8gen1', 'sm8450']):
                return {
                    'chipset': 'Snapdragon 8 Gen 1',
                    'series': '8 Gen',
                    'generation': 'Gen 1',
                    'adreno_gpu': 'Adreno 730'
                }
            
            # Snapdragon 888 (2020-2021)
            elif any(pattern in text_lower for pattern in ['888', 'sm8350']):
                return {
                    'chipset': 'Snapdragon 888',
                    'series': '800',
                    'generation': '888',
                    'adreno_gpu': 'Adreno 660'
                }
            
            # Snapdragon 865 (2019-2020)
            elif any(pattern in text_lower for pattern in ['865', 'sm8250']):
                return {
                    'chipset': 'Snapdragon 865',
                    'series': '800',
                    'generation': '865',
                    'adreno_gpu': 'Adreno 650'
                }
            
            # Snapdragon 855 (2018-2019)
            elif any(pattern in text_lower for pattern in ['855', 'sm8150']):
                return {
                    'chipset': 'Snapdragon 855',
                    'series': '800',
                    'generation': '855',
                    'adreno_gpu': 'Adreno 640'
                }
            
            # Snapdragon 845 (2017-2018)
            elif any(pattern in text_lower for pattern in ['845', 'sdm845']):
                return {
                    'chipset': 'Snapdragon 845',
                    'series': '800',
                    'generation': '845',
                    'adreno_gpu': 'Adreno 630'
                }
            
            # Snapdragon 835 (2016-2017) - Minimum requirement
            elif any(pattern in text_lower for pattern in ['835', 'msm8998']):
                return {
                    'chipset': 'Snapdragon 835',
                    'series': '800',
                    'generation': '835',
                    'adreno_gpu': 'Adreno 540'
                }
            
            # Snapdragon 7 series
            elif any(pattern in text_lower for pattern in ['7 gen', '7gen', '778', '780', '765', '750']):
                if '778' in text_lower:
                    return {
                        'chipset': 'Snapdragon 778G',
                        'series': '700',
                        'generation': '778',
                        'adreno_gpu': 'Adreno 642L'
                    }
                elif '780' in text_lower:
                    return {
                        'chipset': 'Snapdragon 780G',
                        'series': '700',
                        'generation': '780',
                        'adreno_gpu': 'Adreno 642'
                    }
                else:
                    return {
                        'chipset': 'Snapdragon 7 Series',
                        'series': '700',
                        'generation': '7xx',
                        'adreno_gpu': 'Adreno 6xx'
                    }
            
            return None

        def _detect_chipset_alternative(self):
            """Alternative methods to detect Snapdragon chipset"""
            try:
                # Check for Qualcomm/Snapdragon indicators in environment
                if any(var in os.environ for var in ['QCOM_', 'QUALCOMM_', 'SNAPDRAGON_']):
                    return {
                        'chipset': 'Snapdragon (Unknown Model)',
                        'series': 'Unknown',
                        'generation': 'Unknown',
                        'adreno_gpu': 'Adreno GPU'
                    }

                return None

            except Exception as e:
                print(f"Error in alternative chipset detection: {e}")
                return None

        def _meets_minimum_requirements(self):
            """Check if chipset meets Snapdragon 835 minimum requirement"""
            if not self.detected_chipset:
                return False

            # List of chipsets that meet or exceed Snapdragon 835
            supported_chipsets = [
                # 8 Gen series (flagship)
                'snapdragon 8 gen 3', 'snapdragon 8 gen 2', 'snapdragon 8 gen 1',
                # 800 series (flagship)
                'snapdragon 888', 'snapdragon 865', 'snapdragon 855', 'snapdragon 845', 'snapdragon 835',
                # 700 series (upper mid-range) - some models
                'snapdragon 780g', 'snapdragon 778g', 'snapdragon 765g'
            ]

            chipset_lower = self.detected_chipset.lower()

            # Check if chipset is in supported list
            for supported in supported_chipsets:
                if supported in chipset_lower:
                    return True

            # Check by series number for 8xx series
            if self.chipset_generation and self.chipset_generation.isdigit():
                gen_number = int(self.chipset_generation)
                if gen_number >= 835:
                    return True

            return False

        def _analyze_chipset_capabilities(self):
            """Analyze capabilities of detected Snapdragon chipset"""
            if not self.detected_chipset:
                return

            # Base capabilities for Snapdragon 835+
            base_capabilities = {
                'architecture': 'ARM64',
                'vulkan_support': True,
                'opengl_es_version': '3.2',
                'opencl_support': True,
                'max_resolution': '4K',
                'hdr_support': True,
                'manufacturer': 'Qualcomm'
            }

            # Series-specific capabilities
            if self.chipset_series == '8 Gen':
                self.chipset_capabilities = {
                    **base_capabilities,
                    'process_node': '4nm-5nm',
                    'cpu_cores': '8 (1+3+4 or 1+4+3)',
                    'cpu_architecture': 'Cortex-X3/A715/A510',
                    'max_cpu_frequency': '3.2-3.36 GHz',
                    'performance_tier': 'Flagship',
                    'ray_tracing': True,
                    'variable_rate_shading': True,
                    'vulkan_version': '1.3',
                    'memory_type': 'LPDDR5X'
                }
            elif self.chipset_series == '800':
                gen = int(self.chipset_generation) if self.chipset_generation.isdigit() else 0
                if gen >= 865:
                    self.chipset_capabilities = {
                        **base_capabilities,
                        'process_node': '5nm-7nm',
                        'cpu_cores': '8 (1+3+4)',
                        'cpu_architecture': 'Cortex-A78/A55',
                        'max_cpu_frequency': '2.8-3.1 GHz',
                        'performance_tier': 'High-End',
                        'ray_tracing': False,
                        'variable_rate_shading': True,
                        'vulkan_version': '1.1-1.3',
                        'memory_type': 'LPDDR5'
                    }
                else:  # 835-855
                    self.chipset_capabilities = {
                        **base_capabilities,
                        'process_node': '7nm-10nm',
                        'cpu_cores': '8 (4+4)',
                        'cpu_architecture': 'Cortex-A75/A55',
                        'max_cpu_frequency': '2.4-2.8 GHz',
                        'performance_tier': 'Upper Mid-Range',
                        'ray_tracing': False,
                        'variable_rate_shading': False,
                        'vulkan_version': '1.1',
                        'memory_type': 'LPDDR4X'
                    }
            elif self.chipset_series == '700':
                self.chipset_capabilities = {
                    **base_capabilities,
                    'process_node': '6nm-8nm',
                    'cpu_cores': '8 (2+6)',
                    'cpu_architecture': 'Cortex-A78/A55',
                    'max_cpu_frequency': '2.4-2.5 GHz',
                    'performance_tier': 'Mid-Range',
                    'ray_tracing': False,
                    'variable_rate_shading': False,
                    'vulkan_version': '1.1',
                    'memory_type': 'LPDDR4X'
                }

            # Set Vulkan support flag
            self.vulkan_support = self.chipset_capabilities.get('vulkan_support', False)

            # Set performance tier
            self.performance_tier = self.chipset_capabilities.get('performance_tier', 'Standard')

        def _configure_renpy_for_snapdragon(self):
            """Configure Ren'Py settings optimized for Snapdragon chipsets"""
            print(f"Configuring Ren'Py for {self.detected_chipset}...")

            try:
                # Get settings based on performance tier
                if self.performance_tier == 'Flagship':
                    settings = self._get_flagship_settings()
                elif self.performance_tier == 'High-End':
                    settings = self._get_high_end_settings()
                elif self.performance_tier == 'Upper Mid-Range':
                    settings = self._get_upper_mid_settings()
                else:
                    settings = self._get_mid_range_settings()

                self._apply_renpy_settings(settings)
                self.recommended_settings = settings

            except Exception as e:
                print(f"Error configuring Ren'Py for Snapdragon: {e}")

        def _get_flagship_settings(self):
            """Settings for flagship Snapdragon chipsets (8 Gen series)"""
            return {
                'renderer': 'gles2',
                'vsync': True,
                'texture_scaling': 'linear',
                'max_texture_size': 2048,
                'anisotropic_filtering': True,
                'framerate_limit': 90,
                'quality_preset': 'flagship_android',
                'vulkan_enabled': True,
                'hdr_support': True
            }

        def _get_high_end_settings(self):
            """Settings for high-end Snapdragon chipsets (865+)"""
            return {
                'renderer': 'gles2',
                'vsync': True,
                'texture_scaling': 'linear',
                'max_texture_size': 2048,
                'anisotropic_filtering': True,
                'framerate_limit': 60,
                'quality_preset': 'high_android',
                'vulkan_enabled': True,
                'hdr_support': True
            }

        def _get_upper_mid_settings(self):
            """Settings for upper mid-range Snapdragon chipsets (835-855)"""
            return {
                'renderer': 'gles2',
                'vsync': True,
                'texture_scaling': 'linear',
                'max_texture_size': 1024,
                'anisotropic_filtering': False,
                'framerate_limit': 60,
                'quality_preset': 'standard_android',
                'vulkan_enabled': True,
                'hdr_support': False
            }

        def _get_mid_range_settings(self):
            """Settings for mid-range Snapdragon chipsets (7xx series)"""
            return {
                'renderer': 'gles2',
                'vsync': True,
                'texture_scaling': 'nearest',
                'max_texture_size': 1024,
                'anisotropic_filtering': False,
                'framerate_limit': 30,
                'quality_preset': 'balanced_android',
                'vulkan_enabled': False,
                'hdr_support': False
            }

        def _apply_renpy_settings(self, settings):
            """Apply settings to Ren'Py configuration"""
            try:
                # Core renderer settings
                renpy.config.renderer = settings.get('renderer', 'gles2')
                renpy.config.gl_vsync = settings.get('vsync', True)

                # Texture settings
                if settings.get('texture_scaling') == 'linear':
                    renpy.config.gl_texture_scaling = True
                else:
                    renpy.config.gl_texture_scaling = False

                renpy.config.gl_maximum_texture_size = settings.get('max_texture_size', 1024)

                # Android-specific optimizations
                renpy.config.gl_anisotropic = settings.get('anisotropic_filtering', False)
                renpy.config.gl_framerate = settings.get('framerate_limit', 60)

                # Snapdragon-specific optimizations
                renpy.config.gl_resize = True
                renpy.config.gl_tearing = False
                renpy.config.gl_powersave = True  # Important for mobile battery life

                # Memory optimizations for mobile
                renpy.config.gl_depth_size = 16  # Reduce depth buffer for mobile
                renpy.config.image_cache_size = 4  # Conservative cache for mobile

                # Vulkan settings if supported
                if settings.get('vulkan_enabled') and self.vulkan_support:
                    # Enable Vulkan optimizations (Ren'Py may not directly support Vulkan)
                    # But we can set environment variables for the underlying system
                    os.environ['VULKAN_SDK'] = '1'
                    os.environ['VK_LAYER_PATH'] = '/system/lib64/vulkan'

                print(f"✅ Applied {settings['quality_preset']} settings for Snapdragon")

            except Exception as e:
                print(f"Error applying Ren'Py settings: {e}")

        def _generate_snapdragon_report(self):
            """Generate comprehensive Snapdragon detection report"""
            print(f"\n{'='*60}")
            print("QUALCOMM SNAPDRAGON DETECTION REPORT")
            print(f"{'='*60}")

            # Device Information
            print(f"Device Type: Android Mobile Device")
            print(f"Detected Chipset: {self.detected_chipset}")
            print(f"Chipset Series: {self.chipset_series}")
            print(f"Chipset Generation: {self.chipset_generation}")
            print(f"Adreno GPU: {self.adreno_gpu}")
            print(f"Performance Tier: {self.performance_tier}")
            print(f"Vulkan Support: {'Yes' if self.vulkan_support else 'No'}")

            # Chipset Capabilities
            if self.chipset_capabilities:
                print(f"\nChipset Capabilities:")
                caps = self.chipset_capabilities
                print(f"  Architecture: {caps.get('architecture', 'Unknown')}")
                print(f"  Process Node: {caps.get('process_node', 'Unknown')}")
                print(f"  CPU Cores: {caps.get('cpu_cores', 'Unknown')}")
                print(f"  CPU Architecture: {caps.get('cpu_architecture', 'Unknown')}")
                print(f"  Max CPU Frequency: {caps.get('max_cpu_frequency', 'Unknown')}")
                print(f"  Vulkan Version: {caps.get('vulkan_version', 'Unknown')}")
                print(f"  OpenGL ES: {caps.get('opengl_es_version', 'Unknown')}")
                print(f"  Memory Type: {caps.get('memory_type', 'Unknown')}")
                print(f"  Ray Tracing: {'Yes' if caps.get('ray_tracing') else 'No'}")
                print(f"  Variable Rate Shading: {'Yes' if caps.get('variable_rate_shading') else 'No'}")

            # Applied Settings
            if self.recommended_settings:
                print(f"\nApplied Ren'Py Settings:")
                for key, value in self.recommended_settings.items():
                    print(f"  {key}: {value}")

            # Performance Recommendations
            print(f"\nPerformance Recommendations:")
            if self.performance_tier == 'Flagship':
                print("  • Excellent performance expected")
                print("  • High refresh rate gaming supported (90fps)")
                print("  • 2K textures and advanced effects recommended")
                print("  • Vulkan API optimization available")
            elif self.performance_tier == 'High-End':
                print("  • Very good performance expected")
                print("  • Standard 60fps gaming supported")
                print("  • 2K textures supported")
                print("  • Vulkan API optimization available")
            elif self.performance_tier == 'Upper Mid-Range':
                print("  • Good performance expected")
                print("  • Standard 60fps gaming supported")
                print("  • 1K textures recommended")
                print("  • Basic Vulkan support")
            else:
                print("  • Balanced performance expected")
                print("  • 30fps for optimal battery life")
                print("  • Conservative settings for stability")
                print("  • OpenGL ES optimization")

            print(f"{'='*60}")

    # Initialize Snapdragon detector
    snapdragon_detector = SnapdragonMobileDetector()

    def detect_and_configure_snapdragon():
        """Main function to detect and configure Snapdragon chipsets"""
        return snapdragon_detector.detect_snapdragon_chipset()

    def get_snapdragon_info():
        """Get current Snapdragon chipset information"""
        return {
            'detected_chipset': snapdragon_detector.detected_chipset,
            'chipset_series': snapdragon_detector.chipset_series,
            'chipset_generation': snapdragon_detector.chipset_generation,
            'performance_tier': snapdragon_detector.performance_tier,
            'is_android_device': snapdragon_detector.is_android_device,
            'is_mobile': snapdragon_detector.is_mobile,
            'vulkan_support': snapdragon_detector.vulkan_support,
            'adreno_gpu': snapdragon_detector.adreno_gpu,
            'chipset_capabilities': snapdragon_detector.chipset_capabilities,
            'recommended_settings': snapdragon_detector.recommended_settings
        }

# Automatically run Snapdragon detection at startup (only on Android devices)
init:
    python:
        try:
            if platform.system() == "Linux" or (hasattr(renpy, 'variant') and 'android' in renpy.variant()):
                print("Starting Snapdragon chipset detection...")
                detect_and_configure_snapdragon()
        except Exception as e:
            print(f"Error in Snapdragon detection: {e}")

# Manual Snapdragon detection label for testing
label test_snapdragon_detection:
    "Testing Snapdragon chipset detection..."

    python:
        detect_and_configure_snapdragon()

    "Check the console for detailed Snapdragon detection results!"
    return

# Snapdragon performance check label
label check_snapdragon_performance:
    "Checking Snapdragon performance configuration..."

    python:
        chipset_info = get_snapdragon_info()

        if chipset_info['is_android_device']:
            narrator(f"Android Device: {'Yes' if chipset_info['is_android_device'] else 'No'}")
            narrator(f"Mobile Device: {'Yes' if chipset_info['is_mobile'] else 'No'}")

            if chipset_info['detected_chipset']:
                narrator(f"Detected Chipset: {chipset_info['detected_chipset']}")
                narrator(f"Series: {chipset_info['chipset_series']}")
                narrator(f"Generation: {chipset_info['chipset_generation']}")
                narrator(f"Performance Tier: {chipset_info['performance_tier']}")
                narrator(f"Vulkan Support: {'Yes' if chipset_info['vulkan_support'] else 'No'}")
                narrator(f"Adreno GPU: {chipset_info['adreno_gpu']}")

                if chipset_info['recommended_settings']:
                    narrator("Applied Settings:")
                    for key, value in chipset_info['recommended_settings'].items():
                        narrator(f"  {key}: {value}")
            else:
                narrator("No Snapdragon chipset detected or unsupported model")
        else:
            narrator("Not running on Android device")

    "Snapdragon performance check complete!"
    return
