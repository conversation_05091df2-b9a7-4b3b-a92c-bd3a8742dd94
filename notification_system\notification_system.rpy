## Enhanced Notification System
## Side sliding notification boxes for anti-cheat and graphics status

init -40 python:
    import time
    
    # Notification system state
    notification_state = {
        'anticheat_box_visible': True,
        'graphics_box_visible': True,
        'slide_animation_time': 0.5,
        'auto_hide_delay': 0,  # 0 = always visible
        'last_update': time.time()
    }
    
    # Notification box positions
    notification_positions = {
        'anticheat_box': {'x': 20, 'y': 20},
        'graphics_box': {'x': 20, 'y': 160},
        'developer_box': {'x': 20, 'y': 280}
    }

# Achievement-style anti-cheat notification box
screen anticheat_achievement_box():
    
    # Only show if enabled
    if notification_state['anticheat_box_visible']:
        
        # Sliding animation
        frame:
            at slide_in_left
            xpos notification_positions['anticheat_box']['x']
            ypos notification_positions['anticheat_box']['y']
            xsize 320
            ysize 140
            background Frame("gui/notify.png", 16, 16, 16, 16)
            
            vbox:
                spacing 8
                xalign 0.5
                yalign 0.5
                
                # Header with icon and title
                hbox:
                    spacing 12
                    xalign 0.5
                    
                    # Anti-cheat icon
                    text "🛡️" size 24 color "#00ff00"
                    
                    vbox:
                        spacing 2
                        
                        text "ANTI-CHEAT SYSTEM" size 14 color "#00ff00" bold True
                        
                        if anticheat_state.get('developer_authenticated', False):
                            text "👑 DEVELOPER MODE" size 10 color "#ffd700"
                        elif anticheat_state.get('initialized', False):
                            text "🔒 PROTECTION ACTIVE" size 10 color "#00ff00"
                        else:
                            text "⚠️ INITIALIZING..." size 10 color "#ffaa00"
                
                # Status information
                frame:
                    background "#000000aa"
                    padding (8, 6)
                    
                    vbox:
                        spacing 4
                        
                        hbox:
                            spacing 8
                            text "Status:" size 11 color "#ffffff"
                            if anticheat_state.get('banned', False):
                                text "BANNED" size 11 color "#ff0000" bold True
                            elif anticheat_state.get('initialized', False):
                                text "SECURE" size 11 color "#00ff00" bold True
                            else:
                                text "LOADING" size 11 color "#ffaa00"
                        
                        hbox:
                            spacing 8
                            text "Violations:" size 11 color "#ffffff"
                            $ violation_count = anticheat_state.get('violations', 0)
                            if violation_count > 0:
                                text str(violation_count) size 11 color "#ff6666" bold True
                            else:
                                text "0" size 11 color "#00ff00"
                        
                        hbox:
                            spacing 8
                            text "Security Level:" size 11 color "#ffffff"
                            if anticheat_state.get('developer_authenticated', False):
                                text "DEV" size 11 color "#ffd700" bold True
                            else:
                                text "MAX" size 11 color "#00ff00" bold True

# Graphics card status notification box
screen graphics_achievement_box():
    
    # Only show if enabled
    if notification_state['graphics_box_visible']:
        
        # Sliding animation
        frame:
            at slide_in_left
            xpos notification_positions['graphics_box']['x']
            ypos notification_positions['graphics_box']['y']
            xsize 320
            ysize 120
            background Frame("gui/notify.png", 16, 16, 16, 16)
            
            vbox:
                spacing 8
                xalign 0.5
                yalign 0.5
                
                # Header with GPU type icon
                hbox:
                    spacing 12
                    xalign 0.5
                    
                    # GPU type icon
                    if graphics_info['is_integrated']:
                        text "🔧" size 24 color "#ffaa00"
                    elif graphics_info['is_discrete']:
                        text "🎮" size 24 color "#00ff00"
                    else:
                        text "❓" size 24 color "#cccccc"
                    
                    vbox:
                        spacing 2
                        
                        if graphics_info['is_integrated']:
                            text "INTEGRATED GRAPHICS" size 12 color "#ffaa00" bold True
                            text "Performance may vary" size 9 color "#ffaa00"
                        elif graphics_info['is_discrete']:
                            text "DISCRETE GRAPHICS" size 12 color "#00ff00" bold True
                            text "Optimal performance" size 9 color "#00ff00"
                        else:
                            text "GRAPHICS DETECTED" size 12 color "#cccccc" bold True
                            text "Generic driver" size 9 color "#cccccc"
                
                # GPU information
                frame:
                    background "#000000aa"
                    padding (8, 6)
                    
                    vbox:
                        spacing 4
                        
                        text "GPU: {}".format(
                            graphics_info['gpu_name'][:28] + "..." 
                            if len(graphics_info['gpu_name']) > 28 
                            else graphics_info['gpu_name']
                        ) size 10 color "#ffffff"
                        
                        hbox:
                            spacing 8
                            text "Type:" size 10 color "#ffffff"
                            if graphics_info['is_integrated']:
                                text "Integrated" size 10 color "#ffaa00"
                            elif graphics_info['is_discrete']:
                                text "Discrete" size 10 color "#00ff00"
                            else:
                                text "Unknown" size 10 color "#cccccc"

# Developer access notification box
screen developer_achievement_box():
    
    # Only show if developer is authenticated
    if anticheat_state.get('developer_authenticated', False):
        
        frame:
            at slide_in_left
            xpos notification_positions['developer_box']['x']
            ypos notification_positions['developer_box']['y']
            xsize 320
            ysize 100
            background Frame("gui/notify.png", 16, 16, 16, 16)
            
            vbox:
                spacing 8
                xalign 0.5
                yalign 0.5
                
                # Header
                hbox:
                    spacing 12
                    xalign 0.5
                    
                    text "👑" size 24 color "#ffd700"
                    
                    vbox:
                        spacing 2
                        text "DEVELOPER ACCESS" size 12 color "#ffd700" bold True
                        text "Full system control" size 9 color "#ffd700"
                
                # Quick access buttons
                hbox:
                    spacing 10
                    xalign 0.5
                    
                    textbutton "Control Panel" action Call("open_developer_panel") text_size 10 xsize 100
                    textbutton "Override" action Function(toggle_god_mode) text_size 10 xsize 80

# Combined notification overlay
screen notification_overlay():
    
    # Show all notification boxes
    use anticheat_achievement_box
    use graphics_achievement_box
    use developer_achievement_box

# Slide-in animation
transform slide_in_left:
    xoffset -350
    easein notification_state['slide_animation_time'] xoffset 0

# Slide-out animation
transform slide_out_left:
    xoffset 0
    easeout notification_state['slide_animation_time'] xoffset -350

# Enhanced main menu with persistent notifications - DISABLED
# Using main menu from screens.rpy instead
# screen main_menu():
#
#     tag menu
#
#     # Background
#     add gui.main_menu_background
#
#     # Main menu content
#     frame:
#         style "main_menu_frame"
#
#         vbox:
#             style "main_menu_vbox"
#
#             text "NETCODE THE PROTOGEN AND MORE" style "main_menu_title"
#
#             # Game selection buttons
#             vbox:
#                 spacing 20
#
#                 textbutton _("Start Game") action Start() style "main_menu_button"
#                 textbutton _("Load Game") action ShowMenu("load") style "main_menu_button"
#                 textbutton _("Preferences") action ShowMenu("preferences") style "main_menu_button"
#                 textbutton _("About") action ShowMenu("about") style "main_menu_button"
#                 textbutton _("Quit") action Quit(confirm=not main_menu) style "main_menu_button"
#
#     # Always show notification overlay on main menu
#     use notification_overlay

# Enhanced game menu with notifications
screen game_menu(title, scroll=None, yinitial=0.0):
    
    style_prefix "game_menu"
    
    if main_menu:
        add gui.main_menu_background
    else:
        add gui.game_menu_background
    
    frame:
        style "game_menu_outer_frame"
        
        hbox:
            frame:
                style "game_menu_navigation_frame"
            
            frame:
                style "game_menu_content_frame"
                
                if scroll == "viewport":
                    viewport:
                        yinitial yinitial
                        scrollbars "vertical"
                        mousewheel True
                        draggable True
                        pagekeys True
                        
                        side_yfill True
                        
                        vbox:
                            transclude
                
                elif scroll == "vpgrid":
                    vpgrid:
                        cols 1
                        yinitial yinitial
                        
                        scrollbars "vertical"
                        mousewheel True
                        draggable True
                        pagekeys True
                        
                        side_yfill True
                        
                        transclude
                
                else:
                    transclude
    
    # Show notifications on all game menus
    use notification_overlay
    
    textbutton _("Return"):
        style "return_button"
        
        action Return()
    
    label title

# Notification control functions
init python:
    
    def toggle_anticheat_notification():
        """Toggle anti-cheat notification visibility"""
        notification_state['anticheat_box_visible'] = not notification_state['anticheat_box_visible']
        renpy.restart_interaction()
    
    def toggle_graphics_notification():
        """Toggle graphics notification visibility"""
        notification_state['graphics_box_visible'] = not notification_state['graphics_box_visible']
        renpy.restart_interaction()
    
    def update_notification_positions():
        """Update notification box positions"""
        # Recalculate positions based on visible boxes
        y_offset = 20
        
        if notification_state['anticheat_box_visible']:
            notification_positions['anticheat_box']['y'] = y_offset
            y_offset += 160
        
        if notification_state['graphics_box_visible']:
            notification_positions['graphics_box']['y'] = y_offset
            y_offset += 140
        
        if anticheat_state.get('developer_authenticated', False):
            notification_positions['developer_box']['y'] = y_offset
    
    def refresh_graphics_detection():
        """Refresh graphics card detection"""
        detect_graphics_card()
        renpy.restart_interaction()
    
    def show_notification_settings():
        """Show notification settings"""
        renpy.call_screen("notification_settings")

# Notification settings screen
screen notification_settings():
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 400
        
        vbox:
            spacing 20
            
            text "Notification Settings" size 24 xalign 0.5
            
            vbox:
                spacing 15
                
                hbox:
                    text "Anti-cheat Notification:" size 16
                    textbutton "{}".format("ON" if notification_state['anticheat_box_visible'] else "OFF") action Function(toggle_anticheat_notification)
                
                hbox:
                    text "Graphics Notification:" size 16
                    textbutton "{}".format("ON" if notification_state['graphics_box_visible'] else "OFF") action Function(toggle_graphics_notification)
                
                hbox:
                    text "Animation Speed:" size 16
                    bar value FieldValue(notification_state, 'slide_animation_time', 1.0, offset=0.1, step=0.1)
            
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "Refresh Graphics" action Function(refresh_graphics_detection)
                textbutton "Reset Positions" action Function(update_notification_positions)
                textbutton "Close" action Return()

# Auto-show notifications on startup
label after_load:
    # Update notification positions
    $ update_notification_positions()
    return

# Initialize notifications
init python:
    # Update positions on startup
    update_notification_positions()
