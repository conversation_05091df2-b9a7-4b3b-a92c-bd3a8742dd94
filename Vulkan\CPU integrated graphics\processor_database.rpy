## Comprehensive Processor Database for Integrated Graphics
## Detailed specifications for Intel and AMD processors with integrated graphics

init python:
    
    # Intel Processor Database with detailed specifications
    INTEL_PROCESSOR_DATABASE = {
        # 13th Generation Intel Core (Raptor Lake) - 2022-2023
        "13th_gen_intel": {
            "i3-13100": {
                "igpu": "Intel UHD Graphics 730",
                "base_clock": "3.4 GHz",
                "boost_clock": "4.5 GHz",
                "cores": 4,
                "threads": 8,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.5 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "60W"
            },
            "i5-13400": {
                "igpu": "Intel UHD Graphics 730",
                "base_clock": "2.5 GHz",
                "boost_clock": "4.6 GHz",
                "cores": 10,
                "threads": 16,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.5 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "65W"
            },
            "i5-13600K": {
                "igpu": "Intel UHD Graphics 770",
                "base_clock": "3.5 GHz",
                "boost_clock": "5.1 GHz",
                "cores": 14,
                "threads": 20,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.65 GHz",
                "memory_support": "DDR4-3200, DDR5-5600",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "125W"
            },
            "i7-13700K": {
                "igpu": "Intel UHD Graphics 770",
                "base_clock": "3.4 GHz",
                "boost_clock": "5.4 GHz",
                "cores": 16,
                "threads": 24,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.65 GHz",
                "memory_support": "DDR4-3200, DDR5-5600",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "125W"
            },
            "i9-13900K": {
                "igpu": "Intel UHD Graphics 770",
                "base_clock": "3.0 GHz",
                "boost_clock": "5.8 GHz",
                "cores": 24,
                "threads": 32,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.65 GHz",
                "memory_support": "DDR4-3200, DDR5-5600",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "125W"
            }
        },
        
        # 12th Generation Intel Core (Alder Lake) - 2021-2022
        "12th_gen_intel": {
            "i3-12100": {
                "igpu": "Intel UHD Graphics 730",
                "base_clock": "3.3 GHz",
                "boost_clock": "4.3 GHz",
                "cores": 4,
                "threads": 8,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.4 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "60W"
            },
            "i5-12400": {
                "igpu": "Intel UHD Graphics 730",
                "base_clock": "2.5 GHz",
                "boost_clock": "4.4 GHz",
                "cores": 6,
                "threads": 12,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.4 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "65W"
            },
            "i5-12600K": {
                "igpu": "Intel UHD Graphics 770",
                "base_clock": "3.7 GHz",
                "boost_clock": "4.9 GHz",
                "cores": 10,
                "threads": 16,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.45 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "125W"
            },
            "i7-12700K": {
                "igpu": "Intel UHD Graphics 770",
                "base_clock": "3.6 GHz",
                "boost_clock": "5.0 GHz",
                "cores": 12,
                "threads": 20,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.45 GHz",
                "memory_support": "DDR4-3200, DDR5-4800",
                "pcie_lanes": "PCIe 5.0 x16 + PCIe 4.0 x4",
                "tdp": "125W"
            }
        },
        
        # 11th Generation Intel Core (Tiger Lake) - 2020-2021
        "11th_gen_intel": {
            "i5-11400": {
                "igpu": "Intel UHD Graphics 730",
                "base_clock": "2.6 GHz",
                "boost_clock": "4.4 GHz",
                "cores": 6,
                "threads": 12,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.3 GHz",
                "memory_support": "DDR4-3200",
                "pcie_lanes": "PCIe 4.0 x16",
                "tdp": "65W"
            },
            "i7-11700K": {
                "igpu": "Intel UHD Graphics 750",
                "base_clock": "3.6 GHz",
                "boost_clock": "5.0 GHz",
                "cores": 8,
                "threads": 16,
                "igpu_base_freq": "300 MHz",
                "igpu_max_freq": "1.3 GHz",
                "memory_support": "DDR4-3200",
                "pcie_lanes": "PCIe 4.0 x16",
                "tdp": "125W"
            }
        }
    }
    
    # AMD Processor Database with detailed specifications
    AMD_PROCESSOR_DATABASE = {
        # AMD Ryzen 7000 Series (Zen 4 with RDNA 2) - 2022-2023
        "ryzen_7000_series": {
            "Ryzen 5 7600X": {
                "igpu": "AMD Radeon Graphics (RDNA 2)",
                "base_clock": "4.7 GHz",
                "boost_clock": "5.3 GHz",
                "cores": 6,
                "threads": 12,
                "igpu_cores": "2 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "2.2 GHz",
                "memory_support": "DDR5-5200",
                "pcie_lanes": "PCIe 5.0 x24",
                "tdp": "105W"
            },
            "Ryzen 7 7700X": {
                "igpu": "AMD Radeon Graphics (RDNA 2)",
                "base_clock": "4.5 GHz",
                "boost_clock": "5.4 GHz",
                "cores": 8,
                "threads": 16,
                "igpu_cores": "2 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "2.2 GHz",
                "memory_support": "DDR5-5200",
                "pcie_lanes": "PCIe 5.0 x24",
                "tdp": "105W"
            },
            "Ryzen 9 7900X": {
                "igpu": "AMD Radeon Graphics (RDNA 2)",
                "base_clock": "4.7 GHz",
                "boost_clock": "5.6 GHz",
                "cores": 12,
                "threads": 24,
                "igpu_cores": "2 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "2.2 GHz",
                "memory_support": "DDR5-5200",
                "pcie_lanes": "PCIe 5.0 x24",
                "tdp": "170W"
            }
        },
        
        # AMD Ryzen 6000 Series (Zen 3+ with RDNA 2) - 2022
        "ryzen_6000_series": {
            "Ryzen 5 6600H": {
                "igpu": "AMD Radeon 660M",
                "base_clock": "3.3 GHz",
                "boost_clock": "4.5 GHz",
                "cores": 6,
                "threads": 12,
                "igpu_cores": "6 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "1.9 GHz",
                "memory_support": "DDR5-4800, LPDDR5-6400",
                "pcie_lanes": "PCIe 4.0 x20",
                "tdp": "45W"
            },
            "Ryzen 7 6800H": {
                "igpu": "AMD Radeon 680M",
                "base_clock": "3.2 GHz",
                "boost_clock": "4.7 GHz",
                "cores": 8,
                "threads": 16,
                "igpu_cores": "12 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "2.2 GHz",
                "memory_support": "DDR5-4800, LPDDR5-6400",
                "pcie_lanes": "PCIe 4.0 x20",
                "tdp": "45W"
            }
        },
        
        # AMD Ryzen 5000 Series APUs (Zen 3 with Vega) - 2021
        "ryzen_5000_series": {
            "Ryzen 5 5600G": {
                "igpu": "AMD Radeon Vega 7",
                "base_clock": "3.9 GHz",
                "boost_clock": "4.4 GHz",
                "cores": 6,
                "threads": 12,
                "igpu_cores": "7 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "1.9 GHz",
                "memory_support": "DDR4-3200",
                "pcie_lanes": "PCIe 3.0 x16",
                "tdp": "65W"
            },
            "Ryzen 7 5700G": {
                "igpu": "AMD Radeon Vega 8",
                "base_clock": "3.8 GHz",
                "boost_clock": "4.6 GHz",
                "cores": 8,
                "threads": 16,
                "igpu_cores": "8 CUs",
                "igpu_base_freq": "400 MHz",
                "igpu_max_freq": "2.0 GHz",
                "memory_support": "DDR4-3200",
                "pcie_lanes": "PCIe 3.0 x16",
                "tdp": "65W"
            }
        }
    }
    
    def get_detailed_processor_info(cpu_name):
        """
        Get detailed processor information from the database
        """
        cpu_name_lower = cpu_name.lower()
        
        # Search Intel database
        for generation, processors in INTEL_PROCESSOR_DATABASE.items():
            for processor_model, specs in processors.items():
                if processor_model.lower() in cpu_name_lower:
                    return {
                        'vendor': 'Intel',
                        'generation': generation,
                        'model': processor_model,
                        'specifications': specs
                    }
        
        # Search AMD database
        for generation, processors in AMD_PROCESSOR_DATABASE.items():
            for processor_model, specs in processors.items():
                if any(part in cpu_name_lower for part in processor_model.lower().split()):
                    return {
                        'vendor': 'AMD',
                        'generation': generation,
                        'model': processor_model,
                        'specifications': specs
                    }
        
        return None
    
    def print_detailed_processor_specs(processor_info):
        """
        Print detailed processor specifications
        """
        if not processor_info:
            return
        
        specs = processor_info['specifications']
        print(f"\n{'='*50}")
        print(f"DETAILED PROCESSOR SPECIFICATIONS")
        print(f"{'='*50}")
        print(f"Vendor: {processor_info['vendor']}")
        print(f"Generation: {processor_info['generation']}")
        print(f"Model: {processor_info['model']}")
        print(f"Integrated GPU: {specs['igpu']}")
        print(f"CPU Base Clock: {specs['base_clock']}")
        print(f"CPU Boost Clock: {specs['boost_clock']}")
        print(f"Cores/Threads: {specs['cores']}/{specs['threads']}")
        print(f"iGPU Base Frequency: {specs['igpu_base_freq']}")
        print(f"iGPU Max Frequency: {specs['igpu_max_freq']}")
        print(f"Memory Support: {specs['memory_support']}")
        print(f"PCIe Lanes: {specs['pcie_lanes']}")
        print(f"TDP: {specs['tdp']}")
        
        if 'igpu_cores' in specs:
            print(f"iGPU Compute Units: {specs['igpu_cores']}")
        
        print(f"{'='*50}")
