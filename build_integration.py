#!/usr/bin/env python3
"""
Build Integration Script for Netcode Protogen Game
Ensures proper build configuration and anti-cheat integration
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path

class BuildIntegrator:
    """Handles build integration and validation"""
    
    def __init__(self, project_dir=None):
        self.project_dir = Path(project_dir) if project_dir else Path.cwd()
        self.game_dir = self.project_dir / "game"
        self.anticheat_dir = self.project_dir / "anti-cheat"
        self.build_dir = self.project_dir / "build"
        
    def validate_project_structure(self):
        """Validate project structure for build"""
        print("Validating project structure...")
        
        required_files = [
            self.game_dir / "options.rpy",
            self.game_dir / "game_router.rpy",
            self.game_dir / "build_config.rpy",
            self.anticheat_dir / "anticheat_kernel.rpy"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            print("Missing required files:")
            for file in missing_files:
                print(f"  - {file}")
            return False
        
        print("Project structure validation passed")
        return True
    
    def fix_build_configuration(self):
        """Fix common build configuration issues"""
        print("Fixing build configuration...")
        
        options_file = self.game_dir / "options.rpy"
        if not options_file.exists():
            print("options.rpy not found")
            return False
        
        # Read current options
        with open(options_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix build.name if it contains invalid characters
        if 'build.name = "netcode the protogen and more"' in content:
            content = content.replace(
                'build.name = "netcode the protogen and more"',
                'build.name = "netcode_protogen_game"'
            )
            print("Fixed build.name to use valid characters")
        
        # Write back the fixed content
        with open(options_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("Build configuration fixed")
        return True
    
    def prepare_build_environment(self):
        """Prepare build environment"""
        print("Preparing build environment...")
        
        # Create build directory if it doesn't exist
        self.build_dir.mkdir(exist_ok=True)
        
        # Create build info file
        build_info = {
            "project_name": "netcode_protogen_game",
            "version": "1.13.05.07.123v",
            "build_date": str(Path().cwd()),
            "includes_anticheat": True,
            "includes_game_router": True
        }
        
        build_info_file = self.build_dir / "build_info.json"
        with open(build_info_file, 'w', encoding='utf-8') as f:
            json.dump(build_info, f, indent=2)
        
        print("Build environment prepared")
        return True
    
    def validate_anticheat_integration(self):
        """Validate anti-cheat system integration"""
        print("Validating anti-cheat integration...")
        
        anticheat_files = [
            "anticheat_kernel.rpy",
            "anticheat_core.rpy",
            "anticheat_network.rpy"
        ]
        
        for file_name in anticheat_files:
            file_path = self.anticheat_dir / file_name
            if file_path.exists():
                print(f"  ✓ {file_name} found")
            else:
                print(f"  ✗ {file_name} missing")
        
        print("Anti-cheat integration validation completed")
        return True
    
    def create_build_script(self):
        """Create build script for Ren'Py"""
        print("Creating build script...")
        
        build_script = '''#!/bin/bash
# Build script for Netcode Protogen Game

echo "Starting build process..."

# Validate build configuration
python3 build_integration.py --validate

# Run Ren'Py build
if command -v renpy &> /dev/null; then
    echo "Building with Ren'Py..."
    renpy launcher distribute
else
    echo "Ren'Py not found in PATH"
    echo "Please run this from the Ren'Py launcher or add Ren'Py to PATH"
fi

echo "Build process completed"
'''
        
        build_script_file = self.project_dir / "build.sh"
        with open(build_script_file, 'w', encoding='utf-8') as f:
            f.write(build_script)
        
        # Make executable on Unix systems
        if os.name != 'nt':
            os.chmod(build_script_file, 0o755)
        
        print("Build script created")
        return True
    
    def run_integration(self):
        """Run full integration process"""
        print("=== Build Integration Process ===")
        
        steps = [
            ("Validate project structure", self.validate_project_structure),
            ("Fix build configuration", self.fix_build_configuration),
            ("Prepare build environment", self.prepare_build_environment),
            ("Validate anti-cheat integration", self.validate_anticheat_integration),
            ("Create build script", self.create_build_script)
        ]
        
        for step_name, step_func in steps:
            print(f"\n{step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed")
                return False
            print(f"✅ {step_name} completed")
        
        print("\n=== Integration completed successfully ===")
        print("Your project is now ready for building!")
        print("You can build using:")
        print("1. Ren'Py Launcher > Build Distributions")
        print("2. Run ./build.sh (on Unix systems)")
        
        return True

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build integration for Netcode Protogen Game")
    parser.add_argument("--validate", action="store_true", help="Only validate configuration")
    parser.add_argument("--project-dir", help="Project directory path")
    
    args = parser.parse_args()
    
    integrator = BuildIntegrator(args.project_dir)
    
    if args.validate:
        success = integrator.validate_project_structure()
        sys.exit(0 if success else 1)
    else:
        success = integrator.run_integration()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
