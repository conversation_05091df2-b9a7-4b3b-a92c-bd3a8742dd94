## Safe Gaming Settings - Rollback-Safe Version
## This version avoids complex bar values and field references that can cause rollback issues

# Simple gaming settings that won't cause rollback issues
default safe_gaming_settings = {
    'master_volume': 1.0,
    'music_volume': 1.0,
    'sfx_volume': 1.0,
    'voice_volume': 1.0,
    'fullscreen': False,
    'vsync': True,
    'auto_save': True,
    'controller': False
}

# Safe gaming settings screen
screen safe_gaming_settings():
    tag menu
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        
        vbox:
            spacing 20
            
            text "Gaming Settings (Safe Mode)" size 24 xalign 0.5
            
            # Audio Section
            frame:
                xfill True
                
                vbox:
                    spacing 10
                    
                    text "Audio Settings" size 18 color "#2196F3"
                    
                    hbox:
                        spacing 20
                        
                        vbox:
                            spacing 5
                            text "Master Volume: [safe_gaming_settings['master_volume']:.1f]"
                            text "Music Volume: [safe_gaming_settings['music_volume']:.1f]"
                            text "SFX Volume: [safe_gaming_settings['sfx_volume']:.1f]"
                            text "Voice Volume: [safe_gaming_settings['voice_volume']:.1f]"
                        
                        vbox:
                            spacing 5
                            hbox:
                                textbutton "Master +" action SetDict(safe_gaming_settings, 'master_volume', min(1.0, safe_gaming_settings['master_volume'] + 0.1))
                                textbutton "Master -" action SetDict(safe_gaming_settings, 'master_volume', max(0.0, safe_gaming_settings['master_volume'] - 0.1))
                            hbox:
                                textbutton "Music +" action SetDict(safe_gaming_settings, 'music_volume', min(1.0, safe_gaming_settings['music_volume'] + 0.1))
                                textbutton "Music -" action SetDict(safe_gaming_settings, 'music_volume', max(0.0, safe_gaming_settings['music_volume'] - 0.1))
                            hbox:
                                textbutton "SFX +" action SetDict(safe_gaming_settings, 'sfx_volume', min(1.0, safe_gaming_settings['sfx_volume'] + 0.1))
                                textbutton "SFX -" action SetDict(safe_gaming_settings, 'sfx_volume', max(0.0, safe_gaming_settings['sfx_volume'] - 0.1))
                            hbox:
                                textbutton "Voice +" action SetDict(safe_gaming_settings, 'voice_volume', min(1.0, safe_gaming_settings['voice_volume'] + 0.1))
                                textbutton "Voice -" action SetDict(safe_gaming_settings, 'voice_volume', max(0.0, safe_gaming_settings['voice_volume'] - 0.1))
            
            # Graphics Section
            frame:
                xfill True
                
                vbox:
                    spacing 10
                    
                    text "Graphics Settings" size 18 color "#4CAF50"
                    
                    hbox:
                        spacing 20
                        
                        vbox:
                            spacing 5
                            text "Fullscreen: [safe_gaming_settings['fullscreen']]"
                            text "VSync: [safe_gaming_settings['vsync']]"
                            text "Auto-Save: [safe_gaming_settings['auto_save']]"
                            text "Controller: [safe_gaming_settings['controller']]"
                        
                        vbox:
                            spacing 5
                            textbutton "Toggle Fullscreen" action [ToggleDict(safe_gaming_settings, 'fullscreen'), Function(apply_safe_settings)]
                            textbutton "Toggle VSync" action ToggleDict(safe_gaming_settings, 'vsync')
                            textbutton "Toggle Auto-Save" action ToggleDict(safe_gaming_settings, 'auto_save')
                            textbutton "Toggle Controller" action ToggleDict(safe_gaming_settings, 'controller')
            
            # Action Buttons
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "Apply Settings" action Function(apply_safe_settings)
                textbutton "Reset to Defaults" action Function(reset_safe_settings)
                textbutton "Close" action Return()

# Safe settings functions
init python:
    def apply_safe_settings():
        """Apply safe gaming settings"""
        try:
            # Apply fullscreen setting
            if safe_gaming_settings['fullscreen']:
                renpy.set_physical_size(None)  # Use native resolution
                _preferences.fullscreen = True
            else:
                _preferences.fullscreen = False
            
            # Apply audio settings to Ren'Py preferences
            _preferences.set_volume('master', safe_gaming_settings['master_volume'])
            _preferences.set_volume('music', safe_gaming_settings['music_volume'])
            _preferences.set_volume('sfx', safe_gaming_settings['sfx_volume'])
            _preferences.set_volume('voice', safe_gaming_settings['voice_volume'])
            
            renpy.notify("Settings applied successfully!")
            
        except Exception as e:
            renpy.notify(f"Error applying settings: {str(e)}")
            renpy.log(f"Safe settings application error: {str(e)}")
    
    def reset_safe_settings():
        """Reset safe gaming settings to defaults"""
        try:
            safe_gaming_settings.update({
                'master_volume': 1.0,
                'music_volume': 1.0,
                'sfx_volume': 1.0,
                'voice_volume': 1.0,
                'fullscreen': False,
                'vsync': True,
                'auto_save': True,
                'controller': False
            })
            
            renpy.notify("Settings reset to defaults!")
            
        except Exception as e:
            renpy.notify(f"Error resetting settings: {str(e)}")
            renpy.log(f"Safe settings reset error: {str(e)}")

# Override the problematic gaming_settings screen with the safe version
init python:
    def open_safe_gaming_settings():
        """Open the safe gaming settings screen"""
        try:
            renpy.call_screen("safe_gaming_settings")
        except Exception as e:
            renpy.notify("Gaming settings unavailable")
            renpy.log(f"Safe gaming settings error: {str(e)}")

# Create a safe wrapper for the gaming settings
screen gaming_settings_safe():
    use safe_gaming_settings

# Emergency fallback - if the main gaming_settings screen fails, use this
init python:
    def emergency_gaming_settings():
        """Emergency fallback for gaming settings"""
        try:
            renpy.call_screen("safe_gaming_settings")
        except Exception as e:
            renpy.notify("All gaming settings unavailable")
            renpy.log(f"Emergency gaming settings error: {str(e)}")

# Simple preferences integration
init python:
    def sync_safe_settings_with_preferences():
        """Sync safe settings with Ren'Py preferences"""
        try:
            # Get current Ren'Py preference values
            safe_gaming_settings['master_volume'] = _preferences.get_volume('master')
            safe_gaming_settings['music_volume'] = _preferences.get_volume('music')
            safe_gaming_settings['sfx_volume'] = _preferences.get_volume('sfx')
            safe_gaming_settings['voice_volume'] = _preferences.get_volume('voice')
            safe_gaming_settings['fullscreen'] = _preferences.fullscreen
            
        except Exception as e:
            renpy.log(f"Settings sync error: {str(e)}")

# Auto-sync on startup
init python:
    try:
        sync_safe_settings_with_preferences()
    except Exception as e:
        renpy.log(f"Auto-sync error: {str(e)}")

# Safe gaming settings label for menu access
label safe_gaming_settings_label:
    call screen safe_gaming_settings
    return

# Alternative simple settings screen
screen simple_settings():
    tag menu
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 400
        ysize 300
        
        vbox:
            spacing 20
            
            text "Simple Settings" size 20 xalign 0.5
            
            textbutton "Toggle Fullscreen" action Preference("display", "toggle")
            textbutton "Master Volume Up" action Preference("master volume", "+")
            textbutton "Master Volume Down" action Preference("master volume", "-")
            textbutton "Music Volume Up" action Preference("music volume", "+")
            textbutton "Music Volume Down" action Preference("music volume", "-")
            
            textbutton "Close" action Return() xalign 0.5

# Make the safe version easily accessible
define safe_gaming_settings_available = True
