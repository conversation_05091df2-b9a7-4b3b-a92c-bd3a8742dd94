## Platform Detection Integration
## Integrates the platform detection system with the main game

# Include the platform detection system files
# These will be automatically loaded by Ren'Py

init -200 python:
    # Platform detection system integration
    import os
    
    # Check if platform detection system is available
    platform_detection_available = os.path.exists("platform_detection/platform_main.rpy")
    
    if platform_detection_available:
        renpy.log("Platform Detection System: Available")
    else:
        renpy.log("Platform Detection System: Not found")

# Add platform detection to the main menu (optional)
# Uncomment the following lines to add a platform detection option to your main menu

# screen main_menu():
#     # ... your existing main menu code ...
#     
#     # Add this button to your main menu
#     textbutton "Platform Info" action Function(renpy.call_screen, "platform_detection_demo")

# Add platform detection information to the about screen (optional)
# You can add this to your existing about screen

init python:
    def get_platform_info_for_about():
        """Get platform information for the about screen"""
        try:
            if 'platform_detector' in globals() and platform_detector:
                primary_platform = platform_detector.get_primary_platform()
                if primary_platform and primary_platform != 'unknown':
                    return f"Platform: {primary_platform.title()}"
                else:
                    return "Platform: Direct Download"
            else:
                return "Platform: Unknown"
        except:
            return "Platform: Detection Error"

# Quick access functions for use in your game
init python:
    def show_platform_detection():
        """Show the platform detection demo screen"""
        try:
            renpy.call_screen("platform_detection_demo")
        except Exception as e:
            renpy.notify(f"Platform detection not available: {str(e)}")
    
    def get_current_platform():
        """Get the currently detected platform"""
        try:
            if 'platform_detector' in globals():
                return platform_detector.get_primary_platform()
            else:
                return 'unknown'
        except:
            return 'unknown'
    
    def is_platform_secure():
        """Check if the platform is secure"""
        try:
            if 'platform_system' in globals():
                return platform_system.is_system_operational() and platform_system_secure
            else:
                return True  # Assume secure if system not available
        except:
            return True

# Platform-specific game behavior (examples)
init python:
    def platform_specific_setup():
        """Setup platform-specific features"""
        try:
            current_platform = get_current_platform()
            
            if current_platform == 'steam':
                # Steam-specific setup
                renpy.log("Setting up Steam-specific features")
                # Enable Steam achievements, overlay, etc.
                
            elif current_platform == 'epic':
                # Epic Games-specific setup
                renpy.log("Setting up Epic Games-specific features")
                # Enable Epic Online Services, achievements, etc.
                
            elif current_platform == 'itch':
                # itch.io-specific setup
                renpy.log("Setting up itch.io-specific features")
                # Enable itch.io API, analytics, etc.
                
            elif current_platform == 'googleplay':
                # Google Play-specific setup
                renpy.log("Setting up Google Play-specific features")
                # Enable Play Services, achievements, etc.
                
            elif current_platform == 'appstore':
                # Apple App Store-specific setup
                renpy.log("Setting up App Store-specific features")
                # Enable Game Center, iCloud saves, etc.
                
            else:
                # Direct download or unknown platform
                renpy.log("Setting up generic features")
                # Enable basic features only
                
        except Exception as e:
            renpy.log(f"Platform-specific setup error: {str(e)}")

# Auto-run platform-specific setup
init python:
    # Run platform-specific setup after the platform detection system initializes
    if platform_detection_available:
        try:
            # Small delay to ensure platform detection is complete
            renpy.call_in_new_context("platform_setup_label")
        except:
            pass

label platform_setup_label:
    # This runs after the game starts to set up platform-specific features
    $ platform_specific_setup()
    return

# Platform detection status variables for use in your game
default platform_info_available = platform_detection_available
default current_detected_platform = "unknown"
default platform_security_status = True

# Update platform variables
init python:
    def update_platform_variables():
        """Update platform-related variables"""
        try:
            store.current_detected_platform = get_current_platform()
            store.platform_security_status = is_platform_secure()
        except:
            pass

# Example of how to use platform detection in your game scripts
label example_platform_usage:
    # Check what platform the game is running on
    $ current_platform = get_current_platform()
    
    if current_platform == "steam":
        "Welcome, Steam user! Enjoy the full Steam integration features."
        # You could enable Steam-specific content here
        
    elif current_platform == "epic":
        "Welcome, Epic Games user! Epic Online Services are available."
        # You could enable Epic-specific content here
        
    elif current_platform == "itch":
        "Welcome, itch.io user! Thanks for supporting indie games."
        # You could enable itch.io-specific content here
        
    elif current_platform == "googleplay":
        "Welcome, Android user! Google Play features are enabled."
        # You could enable mobile-specific content here
        
    elif current_platform == "appstore":
        "Welcome, iOS user! Game Center features are available."
        # You could enable iOS-specific content here
        
    else:
        "Welcome! You're running the direct download version."
        # Standard features only
    
    # Check platform security
    if not is_platform_secure():
        "Note: Platform security warnings detected. Some features may be limited."
    
    return

# Debug menu option (for development)
# Uncomment this to add a debug option to test platform detection

# screen debug_menu():
#     frame:
#         xalign 0.5
#         yalign 0.5
#         vbox:
#             text "Debug Menu"
#             textbutton "Show Platform Detection" action Function(show_platform_detection)
#             textbutton "Refresh Platform Detection" action Function(refresh_platform_detection)
#             textbutton "Run Platform Tests" action Function(run_platform_tests)
#             textbutton "Close" action Return()

# Quick debug function
# $ renpy.call_screen("debug_menu")

# Integration with existing anti-cheat system
init python:
    def integrate_platform_with_anticheat():
        """Integrate platform detection with existing anti-cheat"""
        try:
            # Check if existing anti-cheat system is available
            anticheat_files = [
                'anti-cheat/anticheat_core.rpy',
                'anti-cheat/anticheat_kernel.rpy'
            ]
            
            anticheat_available = any(os.path.exists(f) for f in anticheat_files)
            
            if anticheat_available and 'platform_anticheat' in globals():
                # Platform detection can report to existing anti-cheat
                renpy.log("Platform detection integrated with existing anti-cheat system")
                return True
            else:
                renpy.log("Platform detection running independently")
                return False
                
        except Exception as e:
            renpy.log(f"Platform-anticheat integration error: {str(e)}")
            return False

# Integration with existing encryption system
init python:
    def integrate_platform_with_encryption():
        """Integrate platform detection with existing encryption"""
        try:
            # Check if existing encryption system is available
            encryption_files = [
                'unencrypt and re-encrypt/encryption_core.py',
                'unencrypt and re-encrypt/renpy_encryption.rpy'
            ]
            
            encryption_available = any(os.path.exists(f) for f in encryption_files)
            
            if encryption_available and 'distribution_tracker' in globals():
                # Platform detection can use existing encryption
                renpy.log("Platform detection integrated with existing encryption system")
                return True
            else:
                renpy.log("Platform detection using built-in encryption")
                return False
                
        except Exception as e:
            renpy.log(f"Platform-encryption integration error: {str(e)}")
            return False

# Auto-integrate with existing systems
init python:
    if platform_detection_available:
        integrate_platform_with_anticheat()
        integrate_platform_with_encryption()

# Example achievement integration (if you have achievements)
init python:
    def unlock_platform_achievement(achievement_id):
        """Unlock achievement on the detected platform"""
        try:
            current_platform = get_current_platform()
            
            if current_platform == 'steam':
                # Unlock Steam achievement
                # steam_api.unlock_achievement(achievement_id)
                renpy.log(f"Steam achievement unlocked: {achievement_id}")
                
            elif current_platform == 'epic':
                # Unlock Epic achievement
                # epic_api.unlock_achievement(achievement_id)
                renpy.log(f"Epic achievement unlocked: {achievement_id}")
                
            elif current_platform == 'googleplay':
                # Unlock Google Play achievement
                # play_games.unlock_achievement(achievement_id)
                renpy.log(f"Google Play achievement unlocked: {achievement_id}")
                
            elif current_platform == 'appstore':
                # Unlock Game Center achievement
                # game_center.unlock_achievement(achievement_id)
                renpy.log(f"Game Center achievement unlocked: {achievement_id}")
                
            else:
                # Local achievement system
                renpy.log(f"Local achievement unlocked: {achievement_id}")
                
        except Exception as e:
            renpy.log(f"Achievement unlock error: {str(e)}")

# Platform-specific save handling (example)
init python:
    def save_to_platform_cloud(save_data):
        """Save data to platform-specific cloud storage"""
        try:
            current_platform = get_current_platform()
            
            if current_platform == 'steam':
                # Save to Steam Cloud
                renpy.log("Saving to Steam Cloud")
                
            elif current_platform == 'epic':
                # Save to Epic Cloud
                renpy.log("Saving to Epic Cloud")
                
            elif current_platform == 'googleplay':
                # Save to Google Play Games
                renpy.log("Saving to Google Play Games")
                
            elif current_platform == 'appstore':
                # Save to iCloud
                renpy.log("Saving to iCloud")
                
            else:
                # Local save only
                renpy.log("Saving locally only")
                
        except Exception as e:
            renpy.log(f"Cloud save error: {str(e)}")

# Make platform detection easily accessible
define platform_detection_demo_accessible = True

# Project Linker Integration
init python:
    def open_project_linker_safe():
        """Safe wrapper for opening project linker"""
        try:
            # Check if project linker is available
            if os.path.exists("PROJECT_LINKER/project_linker.rpy"):
                # Try to call the project linker screen
                if renpy.has_screen("project_linker_manager"):
                    renpy.call_screen("project_linker_manager")
                else:
                    renpy.notify("Project Linker screen not available")
            else:
                renpy.notify("Project Linker not found")
        except Exception as e:
            renpy.notify(f"Project Linker error: {str(e)}")
            renpy.log(f"Project Linker error: {str(e)}")

# Include PROJECT_LINKER files if they exist
init python:
    def load_project_linker():
        """Load project linker functionality"""
        try:
            project_linker_files = [
                "PROJECT_LINKER/project_linker.rpy",
                "PROJECT_LINKER/project_linker_tools.rpy"
            ]

            for file_path in project_linker_files:
                if os.path.exists(file_path):
                    renpy.log(f"Project Linker file found: {file_path}")
                else:
                    renpy.log(f"Project Linker file missing: {file_path}")

        except Exception as e:
            renpy.log(f"Project Linker loading error: {str(e)}")

    # Load project linker on startup
    load_project_linker()
