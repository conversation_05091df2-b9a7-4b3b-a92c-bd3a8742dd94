﻿# TODO: Translation updated at 2025-01-22 05:05

translate portugese strings:

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new ""

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled. "
    new ""

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled. "
    new ""

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new ""

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new ""

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new ""

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new ""

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new ""

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new ""

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new ""

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new ""

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new ""

    # renpy/common/00accessibility.rpy:121
    old "Accessibility Menu. Use up and down arrows to navigate, and enter to activate buttons and bars."
    new ""

    # renpy/common/00accessibility.rpy:140
    old "Font Override"
    new ""

    # renpy/common/00accessibility.rpy:144
    old "Default"
    new ""

    # renpy/common/00accessibility.rpy:148
    old "DejaVu Sans"
    new ""

    # renpy/common/00accessibility.rpy:152
    old "Opendyslexic"
    new ""

    # renpy/common/00accessibility.rpy:158
    old "Text Size Scaling"
    new ""

    # renpy/common/00accessibility.rpy:164
    old "Reset"
    new ""

    # renpy/common/00accessibility.rpy:170
    old "Line Spacing Scaling"
    new ""

    # renpy/common/00accessibility.rpy:182
    old "High Contrast Text"
    new ""

    # renpy/common/00accessibility.rpy:184
    old "Enable"
    new ""

    # renpy/common/00accessibility.rpy:188
    old "Disable"
    new ""

    # renpy/common/00accessibility.rpy:195
    old "Self-Voicing"
    new ""

    # renpy/common/00accessibility.rpy:198
    old "Self-voicing support is limited when using a touch screen."
    new ""

    # renpy/common/00accessibility.rpy:202
    old "Off"
    new ""

    # renpy/common/00accessibility.rpy:206
    old "Text-to-speech"
    new ""

    # renpy/common/00accessibility.rpy:210
    old "Clipboard"
    new ""

    # renpy/common/00accessibility.rpy:214
    old "Debug"
    new ""

    # renpy/common/00accessibility.rpy:228
    old "Self-Voicing Volume Drop"
    new ""

    # renpy/common/00accessibility.rpy:237
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new ""

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new ""

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new ""

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new ""

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new ""

    # renpy/common/00action_file.rpy:258
    old "%b %d, %H:%M"
    new ""

    # renpy/common/00action_file.rpy:395
    old "Save slot %s: [text]"
    new ""

    # renpy/common/00action_file.rpy:481
    old "Load slot %s: [text]"
    new ""

    # renpy/common/00action_file.rpy:534
    old "Delete slot [text]"
    new ""

    # renpy/common/00action_file.rpy:613
    old "File page auto"
    new ""

    # renpy/common/00action_file.rpy:615
    old "File page quick"
    new ""

    # renpy/common/00action_file.rpy:617
    old "File page [text]"
    new ""

    # renpy/common/00action_file.rpy:816
    old "Next file page."
    new ""

    # renpy/common/00action_file.rpy:888
    old "Previous file page."
    new ""

    # renpy/common/00action_file.rpy:949
    old "Quick save complete."
    new ""

    # renpy/common/00action_file.rpy:964
    old "Quick save."
    new ""

    # renpy/common/00action_file.rpy:983
    old "Quick load."
    new ""

    # renpy/common/00action_other.rpy:379
    old "Language [text]"
    new ""

    # renpy/common/00action_other.rpy:744
    old "Open [text] directory."
    new ""

    # renpy/common/00director.rpy:712
    old "The interactive director is not enabled here."
    new ""

    # renpy/common/00director.rpy:1512
    old "⬆"
    new ""

    # renpy/common/00director.rpy:1518
    old "⬇"
    new ""

    # renpy/common/00director.rpy:1582
    old "Done"
    new ""

    # renpy/common/00director.rpy:1592
    old "(statement)"
    new ""

    # renpy/common/00director.rpy:1593
    old "(tag)"
    new ""

    # renpy/common/00director.rpy:1594
    old "(attributes)"
    new ""

    # renpy/common/00director.rpy:1595
    old "(transform)"
    new ""

    # renpy/common/00director.rpy:1620
    old "(transition)"
    new ""

    # renpy/common/00director.rpy:1632
    old "(channel)"
    new ""

    # renpy/common/00director.rpy:1633
    old "(filename)"
    new ""

    # renpy/common/00director.rpy:1662
    old "Change"
    new ""

    # renpy/common/00director.rpy:1664
    old "Add"
    new ""

    # renpy/common/00director.rpy:1667
    old "Cancel"
    new ""

    # renpy/common/00director.rpy:1670
    old "Remove"
    new ""

    # renpy/common/00director.rpy:1705
    old "Statement:"
    new ""

    # renpy/common/00director.rpy:1726
    old "Tag:"
    new ""

    # renpy/common/00director.rpy:1742
    old "Attributes:"
    new ""

    # renpy/common/00director.rpy:1753
    old "Click to toggle attribute, right click to toggle negative attribute."
    new ""

    # renpy/common/00director.rpy:1765
    old "Transforms:"
    new ""

    # renpy/common/00director.rpy:1776
    old "Click to set transform, right click to add to transform list."
    new ""

    # renpy/common/00director.rpy:1777
    old "Customize director.transforms to add more transforms."
    new ""

    # renpy/common/00director.rpy:1789
    old "Behind:"
    new ""

    # renpy/common/00director.rpy:1800
    old "Click to set, right click to add to behind list."
    new ""

    # renpy/common/00director.rpy:1812
    old "Transition:"
    new ""

    # renpy/common/00director.rpy:1822
    old "Click to set."
    new ""

    # renpy/common/00director.rpy:1823
    old "Customize director.transitions to add more transitions."
    new ""

    # renpy/common/00director.rpy:1835
    old "Channel:"
    new ""

    # renpy/common/00director.rpy:1846
    old "Customize director.audio_channels to add more channels."
    new ""

    # renpy/common/00director.rpy:1858
    old "Audio Filename:"
    new ""

    # renpy/common/00gui.rpy:448
    old "Are you sure?"
    new ""

    # renpy/common/00gui.rpy:449
    old "Are you sure you want to delete this save?"
    new ""

    # renpy/common/00gui.rpy:450
    old "Are you sure you want to overwrite your save?"
    new ""

    # renpy/common/00gui.rpy:451
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new ""

    # renpy/common/00gui.rpy:452
    old "Are you sure you want to quit?"
    new ""

    # renpy/common/00gui.rpy:453
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new ""

    # renpy/common/00gui.rpy:454
    old "Are you sure you want to continue where you left off?"
    new ""

    # renpy/common/00gui.rpy:455
    old "Are you sure you want to end the replay?"
    new ""

    # renpy/common/00gui.rpy:456
    old "Are you sure you want to begin skipping?"
    new ""

    # renpy/common/00gui.rpy:457
    old "Are you sure you want to skip to the next choice?"
    new ""

    # renpy/common/00gui.rpy:458
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new ""

    # renpy/common/00gui.rpy:459
    old "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"
    new ""

    # renpy/common/00gui.rpy:460
    old "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."
    new ""

    # renpy/common/00keymap.rpy:325
    old "Failed to save screenshot as %s."
    new ""

    # renpy/common/00keymap.rpy:346
    old "Saved screenshot as %s."
    new ""

    # renpy/common/00library.rpy:251
    old "Skip Mode"
    new ""

    # renpy/common/00library.rpy:338
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new ""

    # renpy/common/00preferences.rpy:290
    old "display"
    new ""

    # renpy/common/00preferences.rpy:310
    old "transitions"
    new ""

    # renpy/common/00preferences.rpy:319
    old "skip transitions"
    new ""

    # renpy/common/00preferences.rpy:321
    old "video sprites"
    new ""

    # renpy/common/00preferences.rpy:330
    old "show empty window"
    new ""

    # renpy/common/00preferences.rpy:339
    old "text speed"
    new ""

    # renpy/common/00preferences.rpy:347
    old "joystick"
    new ""

    # renpy/common/00preferences.rpy:347
    old "joystick..."
    new ""

    # renpy/common/00preferences.rpy:354
    old "skip"
    new ""

    # renpy/common/00preferences.rpy:357
    old "skip unseen [text]"
    new ""

    # renpy/common/00preferences.rpy:362
    old "skip unseen text"
    new ""

    # renpy/common/00preferences.rpy:364
    old "begin skipping"
    new ""

    # renpy/common/00preferences.rpy:368
    old "after choices"
    new ""

    # renpy/common/00preferences.rpy:375
    old "skip after choices"
    new ""

    # renpy/common/00preferences.rpy:377
    old "auto-forward time"
    new ""

    # renpy/common/00preferences.rpy:391
    old "auto-forward"
    new ""

    # renpy/common/00preferences.rpy:398
    old "Auto forward"
    new ""

    # renpy/common/00preferences.rpy:401
    old "auto-forward after click"
    new ""

    # renpy/common/00preferences.rpy:410
    old "automatic move"
    new ""

    # renpy/common/00preferences.rpy:419
    old "wait for voice"
    new ""

    # renpy/common/00preferences.rpy:428
    old "voice sustain"
    new ""

    # renpy/common/00preferences.rpy:437
    old "self voicing"
    new ""

    # renpy/common/00preferences.rpy:440
    old "self voicing enable"
    new ""

    # renpy/common/00preferences.rpy:442
    old "self voicing disable"
    new ""

    # renpy/common/00preferences.rpy:446
    old "self voicing volume drop"
    new ""

    # renpy/common/00preferences.rpy:454
    old "clipboard voicing"
    new ""

    # renpy/common/00preferences.rpy:457
    old "clipboard voicing enable"
    new ""

    # renpy/common/00preferences.rpy:459
    old "clipboard voicing disable"
    new ""

    # renpy/common/00preferences.rpy:463
    old "debug voicing"
    new ""

    # renpy/common/00preferences.rpy:466
    old "debug voicing enable"
    new ""

    # renpy/common/00preferences.rpy:468
    old "debug voicing disable"
    new ""

    # renpy/common/00preferences.rpy:472
    old "emphasize audio"
    new ""

    # renpy/common/00preferences.rpy:481
    old "rollback side"
    new ""

    # renpy/common/00preferences.rpy:491
    old "gl powersave"
    new ""

    # renpy/common/00preferences.rpy:497
    old "gl framerate"
    new ""

    # renpy/common/00preferences.rpy:500
    old "gl tearing"
    new ""

    # renpy/common/00preferences.rpy:503
    old "font transform"
    new ""

    # renpy/common/00preferences.rpy:506
    old "font size"
    new ""

    # renpy/common/00preferences.rpy:514
    old "font line spacing"
    new ""

    # renpy/common/00preferences.rpy:522
    old "system cursor"
    new ""

    # renpy/common/00preferences.rpy:531
    old "renderer menu"
    new ""

    # renpy/common/00preferences.rpy:534
    old "accessibility menu"
    new ""

    # renpy/common/00preferences.rpy:537
    old "high contrast text"
    new ""

    # renpy/common/00preferences.rpy:546
    old "audio when minimized"
    new ""

    # renpy/common/00preferences.rpy:555
    old "audio when unfocused"
    new ""

    # renpy/common/00preferences.rpy:564
    old "web cache preload"
    new ""

    # renpy/common/00preferences.rpy:579
    old "voice after game menu"
    new ""

    # renpy/common/00preferences.rpy:588
    old "restore window position"
    new ""

    # renpy/common/00preferences.rpy:597
    old "reset"
    new ""

    # renpy/common/00preferences.rpy:610
    old "main volume"
    new ""

    # renpy/common/00preferences.rpy:611
    old "music volume"
    new ""

    # renpy/common/00preferences.rpy:612
    old "sound volume"
    new ""

    # renpy/common/00preferences.rpy:613
    old "voice volume"
    new ""

    # renpy/common/00preferences.rpy:614
    old "mute main"
    new ""

    # renpy/common/00preferences.rpy:615
    old "mute music"
    new ""

    # renpy/common/00preferences.rpy:616
    old "mute sound"
    new ""

    # renpy/common/00preferences.rpy:617
    old "mute voice"
    new ""

    # renpy/common/00preferences.rpy:618
    old "mute all"
    new ""

    # renpy/common/00preferences.rpy:701
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new ""

    # renpy/common/00preferences.rpy:703
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new ""

    # renpy/common/00preferences.rpy:705
    old "Self-voicing enabled. Press 'v' to disable."
    new ""

    # renpy/common/00speechbubble.rpy:416
    old "Speech Bubble Editor"
    new ""

    # renpy/common/00speechbubble.rpy:421
    old "(hide)"
    new ""

    # renpy/common/00speechbubble.rpy:432
    old "(clear retained bubbles)"
    new ""

    # renpy/common/00sync.rpy:70
    old "Sync downloaded."
    new ""

    # renpy/common/00sync.rpy:193
    old "Could not connect to the Ren'Py Sync server."
    new ""

    # renpy/common/00sync.rpy:195
    old "The Ren'Py Sync server timed out."
    new ""

    # renpy/common/00sync.rpy:197
    old "An unknown error occurred while connecting to the Ren'Py Sync server."
    new ""

    # renpy/common/00sync.rpy:213
    old "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."
    new ""

    # renpy/common/00sync.rpy:316
    old "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."
    new ""

    # renpy/common/00sync.rpy:335
    old "The sync ID is not in the correct format."
    new ""

    # renpy/common/00sync.rpy:355
    old "The sync could not be decrypted."
    new ""

    # renpy/common/00sync.rpy:378
    old "The sync belongs to a different game."
    new ""

    # renpy/common/00sync.rpy:383
    old "The sync contains a file with an invalid name."
    new ""

    # renpy/common/00sync.rpy:440
    old "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"
    new ""

    # renpy/common/00sync.rpy:472
    old "Enter Sync ID"
    new ""

    # renpy/common/00sync.rpy:483
    old "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."
    new ""

    # renpy/common/00sync.rpy:513
    old "Sync Success"
    new ""

    # renpy/common/00sync.rpy:516
    old "The Sync ID is:"
    new ""

    # renpy/common/00sync.rpy:522
    old "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."
    new ""

    # renpy/common/00sync.rpy:526
    old "Continue"
    new ""

    # renpy/common/00sync.rpy:551
    old "Sync Error"
    new ""

    # renpy/common/00translation.rpy:63
    old "Translation identifier: [identifier]"
    new ""

    # renpy/common/00translation.rpy:84
    old " translates [tl.filename]:[tl.linenumber]"
    new ""

    # renpy/common/00translation.rpy:101
    old "\n{color=#fff}Copied to clipboard.{/color}"
    new ""

    # renpy/common/00iap.rpy:231
    old "Contacting App Store\nPlease Wait..."
    new ""

    # renpy/common/00updater.rpy:505
    old "No update methods found."
    new ""

    # renpy/common/00updater.rpy:552
    old "Could not download file list: "
    new ""

    # renpy/common/00updater.rpy:555
    old "File list digest does not match."
    new ""

    # renpy/common/00updater.rpy:765
    old "An error is being simulated."
    new ""

    # renpy/common/00updater.rpy:953
    old "Either this project does not support updating, or the update status file was deleted."
    new ""

    # renpy/common/00updater.rpy:967
    old "This account does not have permission to perform an update."
    new ""

    # renpy/common/00updater.rpy:970
    old "This account does not have permission to write the update log."
    new ""

    # renpy/common/00updater.rpy:1050
    old "Could not verify update signature."
    new ""

    # renpy/common/00updater.rpy:1373
    old "The update file was not downloaded."
    new ""

    # renpy/common/00updater.rpy:1391
    old "The update file does not have the correct digest - it may have been corrupted."
    new ""

    # renpy/common/00updater.rpy:1541
    old "While unpacking {}, unknown type {}."
    new ""

    # renpy/common/00updater.rpy:2022
    old "Updater"
    new ""

    # renpy/common/00updater.rpy:2029
    old "An error has occured:"
    new ""

    # renpy/common/00updater.rpy:2031
    old "Checking for updates."
    new ""

    # renpy/common/00updater.rpy:2033
    old "This program is up to date."
    new ""

    # renpy/common/00updater.rpy:2035
    old "[u.version] is available. Do you want to install it?"
    new ""

    # renpy/common/00updater.rpy:2037
    old "Preparing to download the updates."
    new ""

    # renpy/common/00updater.rpy:2039
    old "Downloading the updates."
    new ""

    # renpy/common/00updater.rpy:2041
    old "Unpacking the updates."
    new ""

    # renpy/common/00updater.rpy:2043
    old "Finishing up."
    new ""

    # renpy/common/00updater.rpy:2045
    old "The updates have been installed. The program will restart."
    new ""

    # renpy/common/00updater.rpy:2047
    old "The updates have been installed."
    new ""

    # renpy/common/00updater.rpy:2049
    old "The updates were cancelled."
    new ""

    # renpy/common/00updater.rpy:2064
    old "Proceed"
    new ""

    # renpy/common/00updater.rpy:2080
    old "Preparing to download the game data."
    new ""

    # renpy/common/00updater.rpy:2082
    old "Downloading the game data."
    new ""

    # renpy/common/00updater.rpy:2084
    old "The game data has been downloaded."
    new ""

    # renpy/common/00updater.rpy:2086
    old "An error occured when trying to download game data:"
    new ""

    # renpy/common/00updater.rpy:2091
    old "This game cannot be run until the game data has been downloaded."
    new ""

    # renpy/common/00updater.rpy:2098
    old "Retry"
    new ""

    # renpy/common/00gallery.rpy:643
    old "Image [index] of [count] locked."
    new ""

    # renpy/common/00gallery.rpy:663
    old "prev"
    new ""

    # renpy/common/00gallery.rpy:664
    old "next"
    new ""

    # renpy/common/00gallery.rpy:665
    old "slideshow"
    new ""

    # renpy/common/00gallery.rpy:666
    old "return"
    new ""

    # renpy/common/00gltest.rpy:90
    old "Renderer"
    new ""

    # renpy/common/00gltest.rpy:94
    old "Automatically Choose"
    new ""

    # renpy/common/00gltest.rpy:101
    old "Force GL Renderer"
    new ""

    # renpy/common/00gltest.rpy:106
    old "Force ANGLE Renderer"
    new ""

    # renpy/common/00gltest.rpy:111
    old "Force GLES Renderer"
    new ""

    # renpy/common/00gltest.rpy:117
    old "Force GL2 Renderer"
    new ""

    # renpy/common/00gltest.rpy:122
    old "Force ANGLE2 Renderer"
    new ""

    # renpy/common/00gltest.rpy:127
    old "Force GLES2 Renderer"
    new ""

    # renpy/common/00gltest.rpy:137
    old "Enable (No Blocklist)"
    new ""

    # renpy/common/00gltest.rpy:160
    old "Powersave"
    new ""

    # renpy/common/00gltest.rpy:174
    old "Framerate"
    new ""

    # renpy/common/00gltest.rpy:178
    old "Screen"
    new ""

    # renpy/common/00gltest.rpy:182
    old "60"
    new ""

    # renpy/common/00gltest.rpy:186
    old "30"
    new ""

    # renpy/common/00gltest.rpy:192
    old "Tearing"
    new ""

    # renpy/common/00gltest.rpy:208
    old "Changes will take effect the next time this program is run."
    new ""

    # renpy/common/00gltest.rpy:244
    old "Performance Warning"
    new ""

    # renpy/common/00gltest.rpy:249
    old "This computer is using software rendering."
    new ""

    # renpy/common/00gltest.rpy:251
    old "This game requires use of GL2 that can't be initialised."
    new ""

    # renpy/common/00gltest.rpy:253
    old "This computer has a problem displaying graphics: [problem]."
    new ""

    # renpy/common/00gltest.rpy:257
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new ""

    # renpy/common/00gltest.rpy:261
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    new ""

    # renpy/common/00gltest.rpy:266
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    new ""

    # renpy/common/00gltest.rpy:271
    old "Continue, Show this warning again"
    new ""

    # renpy/common/00gltest.rpy:275
    old "Continue, Don't show warning again"
    new ""

    # renpy/common/00gltest.rpy:283
    old "Change render options"
    new ""

    # renpy/common/00gamepad.rpy:33
    old "Select Gamepad to Calibrate"
    new ""

    # renpy/common/00gamepad.rpy:36
    old "No Gamepads Available"
    new ""

    # renpy/common/00gamepad.rpy:56
    old "Calibrating [name] ([i]/[total])"
    new ""

    # renpy/common/00gamepad.rpy:60
    old "Press or move the '[control!s]' [kind]."
    new ""

    # renpy/common/00gamepad.rpy:70
    old "Skip (A)"
    new ""

    # renpy/common/00gamepad.rpy:73
    old "Back (B)"
    new ""

    # renpy/common/_errorhandling.rpym:674
    old "Open"
    new ""

    # renpy/common/_errorhandling.rpym:676
    old "Opens the traceback.txt file in a text editor."
    new ""

    # renpy/common/_errorhandling.rpym:678
    old "Copy BBCode"
    new ""

    # renpy/common/_errorhandling.rpym:680
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new ""

    # renpy/common/_errorhandling.rpym:682
    old "Copy Markdown"
    new ""

    # renpy/common/_errorhandling.rpym:684
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new ""

    # renpy/common/_errorhandling.rpym:716
    old "An exception has occurred."
    new ""

    # renpy/common/_errorhandling.rpym:739
    old "Rollback"
    new ""

    # renpy/common/_errorhandling.rpym:741
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new ""

    # renpy/common/_errorhandling.rpym:744
    old "Ignore"
    new ""

    # renpy/common/_errorhandling.rpym:748
    old "Ignores the exception, allowing you to continue."
    new ""

    # renpy/common/_errorhandling.rpym:750
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new ""

    # renpy/common/_errorhandling.rpym:754
    old "Reload"
    new ""

    # renpy/common/_errorhandling.rpym:756
    old "Reloads the game from disk, saving and restoring game state if possible."
    new ""

    # renpy/common/_errorhandling.rpym:759
    old "Console"
    new ""

    # renpy/common/_errorhandling.rpym:761
    old "Opens a console to allow debugging the problem."
    new ""

    # renpy/common/_errorhandling.rpym:774
    old "Quits the game."
    new ""

    # renpy/common/_errorhandling.rpym:796
    old "Parsing the script failed."
    new ""

