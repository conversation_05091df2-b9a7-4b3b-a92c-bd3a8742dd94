{"error": false, "size": [1920, 1080], "name": "netcode the protogen and more", "version": "**********.123v", "location": {"label": {}, "define": {"fade": ["game/images_definitions.rpy", 66], "dissolve": ["game/images_definitions.rpy", 67], "pixellate": ["renpy/common/00definitions.rpy", 329], "wiperight": ["renpy/common/00definitions.rpy", 332], "wipeleft": ["renpy/common/00definitions.rpy", 333], "wipeup": ["renpy/common/00definitions.rpy", 334], "wipedown": ["renpy/common/00definitions.rpy", 335], "slideright": ["renpy/common/00definitions.rpy", 337], "slideleft": ["renpy/common/00definitions.rpy", 338], "slideup": ["renpy/common/00definitions.rpy", 339], "slidedown": ["renpy/common/00definitions.rpy", 340], "slideawayright": ["renpy/common/00definitions.rpy", 342], "slideawayleft": ["renpy/common/00definitions.rpy", 343], "slideawayup": ["renpy/common/00definitions.rpy", 344], "slideawaydown": ["renpy/common/00definitions.rpy", 345], "irisout": ["renpy/common/00definitions.rpy", 347], "irisin": ["renpy/common/00definitions.rpy", 348], "pushright": ["renpy/common/00definitions.rpy", 351], "pushleft": ["renpy/common/00definitions.rpy", 352], "pushup": ["renpy/common/00definitions.rpy", 353], "pushdown": ["renpy/common/00definitions.rpy", 354], "zoomin": ["renpy/common/00definitions.rpy", 357], "zoomout": ["renpy/common/00definitions.rpy", 358], "zoominout": ["renpy/common/00definitions.rpy", 359], "vpunch": ["renpy/common/00definitions.rpy", 363], "hpunch": ["renpy/common/00definitions.rpy", 364], "blinds": ["renpy/common/00definitions.rpy", 367], "squares": ["renpy/common/00definitions.rpy", 368], "config.check_conflicting_properties": ["game/gui.rpy", 15], "gui.accent_color": ["game/gui.rpy", 28], "gui.idle_color": ["game/gui.rpy", 31], "gui.idle_small_color": ["game/gui.rpy", 35], "gui.hover_color": ["game/gui.rpy", 38], "gui.selected_color": ["game/gui.rpy", 42], "gui.insensitive_color": ["game/gui.rpy", 45], "gui.muted_color": ["game/gui.rpy", 49], "gui.hover_muted_color": ["game/gui.rpy", 50], "gui.text_color": ["game/gui.rpy", 53], "gui.interface_text_color": ["game/gui.rpy", 54], "gui.text_font": ["game/gui.rpy", 60], "gui.name_text_font": ["game/gui.rpy", 63], "gui.interface_text_font": ["game/gui.rpy", 66], "gui.text_size": ["game/gui.rpy", 69], "gui.name_text_size": ["game/gui.rpy", 72], "gui.interface_text_size": ["game/gui.rpy", 75], "gui.label_text_size": ["game/gui.rpy", 78], "gui.notify_text_size": ["game/gui.rpy", 81], "gui.title_text_size": ["game/gui.rpy", 84], "gui.main_menu_background": ["game/gui.rpy", 90], "gui.game_menu_background": ["game/gui.rpy", 91], "gui.textbox_height": ["game/gui.rpy", 100], "gui.textbox_yalign": ["game/gui.rpy", 104], "gui.name_xpos": ["game/gui.rpy", 109], "gui.name_ypos": ["game/gui.rpy", 110], "gui.name_xalign": ["game/gui.rpy", 114], "gui.namebox_width": ["game/gui.rpy", 118], "gui.namebox_height": ["game/gui.rpy", 119], "gui.namebox_borders": ["game/gui.rpy", 123], "gui.namebox_tile": ["game/gui.rpy", 127], "gui.dialogue_xpos": ["game/gui.rpy", 133], "gui.dialogue_ypos": ["game/gui.rpy", 134], "gui.dialogue_width": ["game/gui.rpy", 137], "gui.dialogue_text_xalign": ["game/gui.rpy", 141], "gui.button_width": ["game/gui.rpy", 150], "gui.button_height": ["game/gui.rpy", 151], "gui.button_borders": ["game/gui.rpy", 154], "gui.button_tile": ["game/gui.rpy", 158], "gui.button_text_font": ["game/gui.rpy", 161], "gui.button_text_size": ["game/gui.rpy", 164], "gui.button_text_idle_color": ["game/gui.rpy", 167], "gui.button_text_hover_color": ["game/gui.rpy", 168], "gui.button_text_selected_color": ["game/gui.rpy", 169], "gui.button_text_insensitive_color": ["game/gui.rpy", 170], "gui.button_text_xalign": ["game/gui.rpy", 174], "gui.radio_button_borders": ["game/gui.rpy", 183], "gui.check_button_borders": ["game/gui.rpy", 185], "gui.confirm_button_text_xalign": ["game/gui.rpy", 187], "gui.page_button_borders": ["game/gui.rpy", 189], "gui.quick_button_borders": ["game/gui.rpy", 191], "gui.quick_button_text_size": ["game/gui.rpy", 192], "gui.quick_button_text_idle_color": ["game/gui.rpy", 193], "gui.quick_button_text_selected_color": ["game/gui.rpy", 194], "gui.choice_button_width": ["game/gui.rpy", 207], "gui.choice_button_height": ["game/gui.rpy", 208], "gui.choice_button_tile": ["game/gui.rpy", 209], "gui.choice_button_borders": ["game/gui.rpy", 210], "gui.choice_button_text_font": ["game/gui.rpy", 211], "gui.choice_button_text_size": ["game/gui.rpy", 212], "gui.choice_button_text_xalign": ["game/gui.rpy", 213], "gui.choice_button_text_idle_color": ["game/gui.rpy", 214], "gui.choice_button_text_hover_color": ["game/gui.rpy", 215], "gui.choice_button_text_insensitive_color": ["game/gui.rpy", 216], "gui.slot_button_width": ["game/gui.rpy", 226], "gui.slot_button_height": ["game/gui.rpy", 227], "gui.slot_button_borders": ["game/gui.rpy", 228], "gui.slot_button_text_size": ["game/gui.rpy", 229], "gui.slot_button_text_xalign": ["game/gui.rpy", 230], "gui.slot_button_text_idle_color": ["game/gui.rpy", 231], "gui.slot_button_text_selected_idle_color": ["game/gui.rpy", 232], "gui.slot_button_text_selected_hover_color": ["game/gui.rpy", 233], "config.thumbnail_width": ["game/gui.rpy", 236], "config.thumbnail_height": ["game/gui.rpy", 237], "gui.file_slot_cols": ["game/gui.rpy", 240], "gui.file_slot_rows": ["game/gui.rpy", 241], "gui.navigation_xpos": ["game/gui.rpy", 251], "gui.skip_ypos": ["game/gui.rpy", 254], "gui.notify_ypos": ["game/gui.rpy", 257], "gui.choice_spacing": ["game/gui.rpy", 260], "gui.navigation_spacing": ["game/gui.rpy", 263], "gui.pref_spacing": ["game/gui.rpy", 266], "gui.pref_button_spacing": ["game/gui.rpy", 269], "gui.page_spacing": ["game/gui.rpy", 272], "gui.slot_spacing": ["game/gui.rpy", 275], "gui.main_menu_text_xalign": ["game/gui.rpy", 278], "gui.frame_borders": ["game/gui.rpy", 287], "gui.confirm_frame_borders": ["game/gui.rpy", 290], "gui.skip_frame_borders": ["game/gui.rpy", 293], "gui.notify_frame_borders": ["game/gui.rpy", 296], "gui.frame_tile": ["game/gui.rpy", 299], "gui.bar_size": ["game/gui.rpy", 311], "gui.scrollbar_size": ["game/gui.rpy", 312], "gui.slider_size": ["game/gui.rpy", 313], "gui.bar_tile": ["game/gui.rpy", 316], "gui.scrollbar_tile": ["game/gui.rpy", 317], "gui.slider_tile": ["game/gui.rpy", 318], "gui.bar_borders": ["game/gui.rpy", 321], "gui.scrollbar_borders": ["game/gui.rpy", 322], "gui.slider_borders": ["game/gui.rpy", 323], "gui.vbar_borders": ["game/gui.rpy", 326], "gui.vscrollbar_borders": ["game/gui.rpy", 327], "gui.vslider_borders": ["game/gui.rpy", 328], "gui.unscrollable": ["game/gui.rpy", 332], "config.history_length": ["game/gui.rpy", 340], "gui.history_height": ["game/gui.rpy", 344], "gui.history_spacing": ["game/gui.rpy", 347], "gui.history_name_xpos": ["game/gui.rpy", 351], "gui.history_name_ypos": ["game/gui.rpy", 352], "gui.history_name_width": ["game/gui.rpy", 353], "gui.history_name_xalign": ["game/gui.rpy", 354], "gui.history_text_xpos": ["game/gui.rpy", 357], "gui.history_text_ypos": ["game/gui.rpy", 358], "gui.history_text_width": ["game/gui.rpy", 359], "gui.history_text_xalign": ["game/gui.rpy", 360], "gui.nvl_borders": ["game/gui.rpy", 368], "gui.nvl_list_length": ["game/gui.rpy", 372], "gui.nvl_height": ["game/gui.rpy", 376], "gui.nvl_spacing": ["game/gui.rpy", 380], "gui.nvl_name_xpos": ["game/gui.rpy", 384], "gui.nvl_name_ypos": ["game/gui.rpy", 385], "gui.nvl_name_width": ["game/gui.rpy", 386], "gui.nvl_name_xalign": ["game/gui.rpy", 387], "gui.nvl_text_xpos": ["game/gui.rpy", 390], "gui.nvl_text_ypos": ["game/gui.rpy", 391], "gui.nvl_text_width": ["game/gui.rpy", 392], "gui.nvl_text_xalign": ["game/gui.rpy", 393], "gui.nvl_thought_xpos": ["game/gui.rpy", 397], "gui.nvl_thought_ypos": ["game/gui.rpy", 398], "gui.nvl_thought_width": ["game/gui.rpy", 399], "gui.nvl_thought_xalign": ["game/gui.rpy", 400], "gui.nvl_button_xpos": ["game/gui.rpy", 403], "gui.nvl_button_xalign": ["game/gui.rpy", 404], "gui.language": ["game/gui.rpy", 413], "gui.history_allow_tags": ["game/screens.rpy", 1526], "config.nvl_list_length": ["game/screens.rpy", 1953], "bubble.frame": ["game/screens.rpy", 2062], "bubble.thoughtframe": ["game/screens.rpy", 2063], "bubble.properties": ["game/screens.rpy", 2065], "bubble.expand_area": ["game/screens.rpy", 2091], "_console.traced_expressions": ["renpy/common/00console.rpy", 1154], "_version": ["renpy/common/00defaults.rpy", 247], "_menu": ["renpy/common/00gamemenu.rpy", 137], "_history": ["renpy/common/00library.rpy", 492], "_history_list": ["renpy/common/00library.rpy", 493], "_nvl_language": ["renpy/common/00nvl_mode.rpy", 45], "bubble.tag_properties": ["renpy/common/00speechbubble.rpy", 24], "bubble.current_dialogue": ["renpy/common/00speechbubble.rpy", 28], "mobile_tab": ["game/game_router.rpy", 235], "mobile_selected_game": ["game/game_router.rpy", 236], "mobile_selected_game_data": ["game/game_router.rpy", 237], "mobile_double_tap_time": ["game/game_router.rpy", 238], "current_platform": ["game/game_router.rpy", 239], "vr_mode_active": ["game/game_router.rpy", 240], "steamvr_mode_active": ["game/game_router.rpy", 241], "vr_depth_enabled": ["game/game_router.rpy", 242], "vr_hand_tracking": ["game/game_router.rpy", 243], "eye_tracking_active": ["game/game_router.rpy", 246], "full_body_tracking_active": ["game/game_router.rpy", 247], "advanced_haptics_active": ["game/game_router.rpy", 248], "vr_laser_visible": ["game/game_router.rpy", 249], "gaming_settings_available": ["game/gaming_settings_integration.rpy", 265], "loading_progress": ["game/loading_screen.rpy", 5], "loading_game_name": ["game/loading_screen.rpy", 6], "loading_game_description": ["game/loading_screen.rpy", 7], "loading_complete": ["game/loading_screen.rpy", 8], "mobile_orientation": ["game/mobile_styles.rpy", 144], "config.name": ["game/options.rpy", 15], "gui.show_name": ["game/options.rpy", 21], "config.version": ["game/options.rpy", 26], "gui.about": ["game/options.rpy", 32], "build.name": ["game/options.rpy", 40], "config.has_sound": ["game/options.rpy", 49], "config.has_music": ["game/options.rpy", 50], "config.has_voice": ["game/options.rpy", 51], "config.enter_transition": ["game/options.rpy", 76], "config.exit_transition": ["game/options.rpy", 77], "config.intra_transition": ["game/options.rpy", 82], "config.after_load_transition": ["game/options.rpy", 87], "config.end_game_transition": ["game/options.rpy", 92], "config.window": ["game/options.rpy", 109], "config.window_show_transition": ["game/options.rpy", 114], "config.window_hide_transition": ["game/options.rpy", 115], "config.save_directory": ["game/options.rpy", 146], "config.window_icon": ["game/options.rpy", 153], "config.gl_test_image": ["game/options.rpy", 161], "config.gl_resize": ["game/options.rpy", 162], "config.nearest_neighbor": ["game/options.rpy", 165], "config.gl_clear_color": ["game/options.rpy", 166], "config.gl_lod_bias": ["game/options.rpy", 169], "config.optimize_texture_bounds": ["game/options.rpy", 170], "config.mipmap_dissolves": ["game/options.rpy", 173], "config.mipmap_text": ["game/options.rpy", 174], "config.predict_statements": ["game/options.rpy", 177], "config.image_cache_size_mb": ["game/options.rpy", 178], "platform_info_available": ["game/platform_detection_integration.rpy", 131], "current_detected_platform": ["game/platform_detection_integration.rpy", 132], "platform_security_status": ["game/platform_detection_integration.rpy", 133], "platform_detection_demo_accessible": ["game/platform_detection_integration.rpy", 318], "safe_gaming_settings": ["game/safe_gaming_settings.rpy", 5], "safe_gaming_settings_available": ["game/safe_gaming_settings.rpy", 220], "BearWithUs": ["game/separate games/BearWithUs/BearWithUs.rpy", 5], "player": ["game/separate games/netcode/netcode_complete.rpy", 8], "narrator": ["game/separate games/netcode/netcode_complete.rpy", 69], "guide": ["game/separate games/Demo Game/DemoGame.rpy", 13], "mysterious_voice": ["game/separate games/Demo Game/DemoGame.rpy", 14], "demo_choice": ["game/separate games/Demo Game/DemoGame.rpy", 17], "demo_score": ["game/separate games/Demo Game/DemoGame.rpy", 18], "demo_ending": ["game/separate games/Demo Game/DemoGame.rpy", 19], "Lumetric": ["game/separate games/Lumrtric/lumetric_game.rpy", 5], "povname": ["game/separate games/Lumrtric/lumetric_game.rpy", 9], "sally": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 10], "angela": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 11], "enok": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 12], "vivian": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 13], "frankie": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 14], "craig": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 15], "imp": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 18], "poon": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 19], "spike": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 20], "skrit": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 21], "slime_diva": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 24], "lord_zuulneruda": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 25], "lord_kaluuz": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 26], "colossus": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 27], "alshar": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 28], "stezza": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 29], "wisp": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 32], "geist": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 33], "carbuncle": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 34], "monolith": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 35], "atlyss_chosen_love_interests": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 38], "atlyss_relationship_points": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 39], "atlyss_current_chapter": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 45], "atlyss_player_class": ["game/separate games/adventures of atlyss/adventures of atlyss.rpy", 46], "austin_the_wickerbeast": ["game/separate games/austin the wickerbeast/austin the wickerbeast.rpy", 8], "austin_friendship_level": ["game/separate games/austin the wickerbeast/austin the wickerbeast.rpy", 11], "austin_current_chapter": ["game/separate games/austin the wickerbeast/austin the wickerbeast.rpy", 12], "austin_relationship_level": ["game/separate games/austin the wickerbeast/austin the wickerbeast.rpy", 13], "goddess_luxury": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 5], "lumetric_lua": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 6], "delta_lola": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 7], "luma_ama": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 8], "loma_star": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 9], "rice_rose": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 10], "blada_data": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 11], "ruthless_ace": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 12], "bikini_reda": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 13], "hydra_gujia": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 14], "cigar_scar": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 15], "diva": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 16], "aradara": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 17], "hopkin_witha": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 18], "saraura": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 19], "cigar_horizon": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 20], "moma": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 21], "mona_horizon": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 22], "hopkin_beacch": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 23], "rise_buey": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 24], "bita": ["game/separate games/hopkin girls combat team/hopkin_girls_combat.rpy", 25], "Netcode": ["game/separate games/netcode/netcode_complete.rpy", 9], "whiskers": ["game/separate games/netcode/netcode_complete.rpy", 12], "magnus": ["game/separate games/netcode/netcode_complete.rpy", 13], "luna": ["game/separate games/netcode/netcode_complete.rpy", 14], "cipher": ["game/separate games/netcode/netcode_complete.rpy", 15], "vertex": ["game/separate games/netcode/netcode_complete.rpy", 16], "meadow": ["game/separate games/netcode/netcode_complete.rpy", 17], "bramble": ["game/separate games/netcode/netcode_complete.rpy", 18], "talon": ["game/separate games/netcode/netcode_complete.rpy", 19], "quantum": ["game/separate games/netcode/netcode_complete.rpy", 22], "nexus": ["game/separate games/netcode/netcode_complete.rpy", 23], "cipher_prime": ["game/separate games/netcode/netcode_complete.rpy", 24], "vector": ["game/separate games/netcode/netcode_complete.rpy", 25], "matrix": ["game/separate games/netcode/netcode_complete.rpy", 26], "pulse": ["game/separate games/netcode/netcode_complete.rpy", 27], "flux": ["game/separate games/netcode/netcode_complete.rpy", 28], "nova": ["game/separate games/netcode/netcode_complete.rpy", 29], "echo": ["game/separate games/netcode/netcode_complete.rpy", 30], "prism": ["game/separate games/netcode/netcode_complete.rpy", 31], "frost": ["game/separate games/netcode/netcode_complete.rpy", 34], "aurora": ["game/separate games/netcode/netcode_complete.rpy", 35], "glacier": ["game/separate games/netcode/netcode_complete.rpy", 36], "crystal": ["game/separate games/netcode/netcode_complete.rpy", 37], "arctic": ["game/separate games/netcode/netcode_complete.rpy", 38], "blizzard": ["game/separate games/netcode/netcode_complete.rpy", 39], "ice_shard": ["game/separate games/netcode/netcode_complete.rpy", 40], "winter": ["game/separate games/netcode/netcode_complete.rpy", 41], "snow_drift": ["game/separate games/netcode/netcode_complete.rpy", 42], "polar": ["game/separate games/netcode/netcode_complete.rpy", 43], "helix": ["game/separate games/netcode/netcode_complete.rpy", 46], "genome": ["game/separate games/netcode/netcode_complete.rpy", 47], "bio_flux": ["game/separate games/netcode/netcode_complete.rpy", 48], "neural_web": ["game/separate games/netcode/netcode_complete.rpy", 49], "symbiosis": ["game/separate games/netcode/netcode_complete.rpy", 50], "evolution": ["game/separate games/netcode/netcode_complete.rpy", 51], "adaptation": ["game/separate games/netcode/netcode_complete.rpy", 52], "metamorphosis": ["game/separate games/netcode/netcode_complete.rpy", 53], "synthesis": ["game/separate games/netcode/netcode_complete.rpy", 54], "organic_prime": ["game/separate games/netcode/netcode_complete.rpy", 55], "mystery_character": ["game/separate games/netcode/netcode_complete.rpy", 58], "RahNight_Bluelight": ["game/separate games/netcode/netcode_complete.rpy", 59], "dr_synthesis": ["game/separate games/netcode/netcode_complete.rpy", 62], "Dr_Temporal_Sage": ["game/separate games/netcode/netcode_complete.rpy", 63], "Dr_Nexus_Weaver": ["game/separate games/netcode/netcode_complete.rpy", 64], "Professor_Echo_Resonance": ["game/separate games/netcode/netcode_complete.rpy", 65], "Dr_Quantum_Flux": ["game/separate games/netcode/netcode_complete.rpy", 66], "bedroom": ["game/separate games/netcode/netcode_complete.rpy", 72], "cozy_burrow_exterior": ["game/separate games/netcode/netcode_complete.rpy", 73], "coffee_shop_interior": ["game/separate games/netcode/netcode_complete.rpy", 74], "binary_bistro_interior": ["game/separate games/netcode/netcode_complete.rpy", 75], "starry_sky": ["game/separate games/netcode/netcode_complete.rpy", 76], "netcode": ["game/separate games/netcode/netcode_complete.rpy", 77], "Anime_Hall_Background_10": ["game/separate games/netcode/netcode_complete.rpy", 78], "coffee_shop_exterior": ["game/separate games/netcode/netcode_complete.rpy", 79], "tavern_exterior": ["game/separate games/netcode/netcode_complete.rpy", 80], "tavern_interior": ["game/separate games/netcode/netcode_complete.rpy", 81], "binary_bistro_exterior": ["game/separate games/netcode/netcode_complete.rpy", 82], "hostess_aria": ["game/separate games/netcode/netcode_complete.rpy", 2659], "server_sage": ["game/separate games/netcode/netcode_complete.rpy", 2685], "café_owner_bloom": ["game/separate games/netcode/netcode_complete.rpy", 3490]}, "screen": {"_exception_actions": ["renpy/common/_errorhandling.rpym", 754], "_exception": ["renpy/common/_errorhandling.rpym", 782], "_parse_errors": ["renpy/common/_errorhandling.rpym", 871], "director_move_button": ["renpy/common/00director.rpy", 1508], "director_lines": ["renpy/common/00director.rpy", 1523], "director_statement": ["renpy/common/00director.rpy", 1590], "director_with_statement": ["renpy/common/00director.rpy", 1618], "director_audio_statement": ["renpy/common/00director.rpy", 1630], "director_footer": ["renpy/common/00director.rpy", 1648], "director_choices": ["renpy/common/00director.rpy", 1676], "director_kind": ["renpy/common/00director.rpy", 1698], "director_tag": ["renpy/common/00director.rpy", 1719], "director_attributes": ["renpy/common/00director.rpy", 1735], "director_transform": ["renpy/common/00director.rpy", 1758], "director_behind": ["renpy/common/00director.rpy", 1782], "director_with": ["renpy/common/00director.rpy", 1805], "director_channel": ["renpy/common/00director.rpy", 1828], "director_audio": ["renpy/common/00director.rpy", 1851], "director": ["renpy/common/00director.rpy", 1872], "notify": ["game/screens.rpy", 1861], "_gallery": ["renpy/common/00gallery.rpy", 672], "gallery_navigation": ["renpy/common/00gallery.rpy", 689], "_choose_renderer": ["renpy/common/00gltest.rpy", 49], "_performance_warning": ["renpy/common/00gltest.rpy", 199], "_self_voicing": ["renpy/common/00preferences.rpy", 718], "_touch_keyboard": ["renpy/common/00touchkeyboard.rpy", 105], "updater": ["renpy/common/00updater.rpy", 2016], "downloader": ["renpy/common/00updater.rpy", 2075], "sync_confirm": ["renpy/common/00sync.rpy", 407], "sync_prompt": ["renpy/common/00sync.rpy", 439], "sync_success": ["renpy/common/00sync.rpy", 480], "sync_error": ["renpy/common/00sync.rpy", 518], "gallery": ["game/screens.rpy", 79], "say": ["game/screens.rpy", 200], "input": ["game/screens.rpy", 277], "choice": ["game/screens.rpy", 310], "quick_menu": ["game/screens.rpy", 2111], "navigation": ["game/screens.rpy", 393], "main_menu": ["game/screens.rpy", 480], "game_selection": ["game/game_router.rpy", 688], "escape_menu": ["game/screens.rpy", 861], "game_menu": ["game/screens.rpy", 1022], "about": ["game/screens.rpy", 1152], "save": ["game/screens.rpy", 1191], "load": ["game/screens.rpy", 1198], "file_slots": ["game/screens.rpy", 1205], "preferences": ["game/screens.rpy", 1335], "history": ["game/screens.rpy", 1486], "help": ["game/screens.rpy", 1574], "keyboard_help": ["game/screens.rpy", 1603], "mouse_help": ["game/screens.rpy", 1654], "gamepad_help": ["game/screens.rpy", 1677], "confirm": ["game/screens.rpy", 1742], "skip_indicator": ["game/screens.rpy", 1805], "nvl": ["game/screens.rpy", 1900], "nvl_dialogue": ["game/screens.rpy", 1932], "bubble": ["game/screens.rpy", 2020], "_accessibility_audio": ["renpy/common/00accessibility.rpy", 126], "_accessibility_text": ["renpy/common/00accessibility.rpy", 190], "_accessibility": ["renpy/common/00accessibility.rpy", 258], "_console": ["renpy/common/00console.rpy", 1066], "_trace_screen": ["renpy/common/00console.rpy", 1156], "_gamepad_select": ["renpy/common/00gamepad.rpy", 22], "_gamepad_control": ["renpy/common/00gamepad.rpy", 45], "iconbutton": ["renpy/common/00iconbutton.rpy", 32], "_progress": ["renpy/common/00keymap.rpy", 414], "_ctc": ["renpy/common/00library.rpy", 488], "_performance": ["renpy/common/00performance.rpy", 29], "_bubble_editor": ["renpy/common/00speechbubble.rpy", 397], "_bubble_window_area_editor": ["renpy/common/00speechbubble.rpy", 470], "_translation_info": ["renpy/common/00translation.rpy", 23], "_auto_voice": ["renpy/common/00voice.rpy", 574], "safe_anticheat_status": ["game/anticheat_safe_integration.rpy", 86], "safe_ban_screen": ["game/anticheat_safe_integration.rpy", 131], "safe_security_status": ["game/anticheat_safe_integration.rpy", 297], "game_selection_screen": ["game/game_router.rpy", 635], "mobile_game_list": ["game/game_router.rpy", 1345], "mobile_game_preview": ["game/game_router.rpy", 1543], "mobile_game_details": ["game/game_router.rpy", 1642], "vr_room": ["game/game_router.rpy", 2025], "vr_controller_left": ["game/game_router.rpy", 2059], "vr_controller_right": ["game/game_router.rpy", 2063], "gaming_settings": ["game/gaming_settings_integration.rpy", 77], "loading_screen": ["game/loading_screen.rpy", 11], "custom_loading_screen": ["game/loading_screen.rpy", 201], "loading_error_screen": ["game/loading_screen.rpy", 244], "mobile_haptic_flash": ["game/mobile_styles.rpy", 119], "project_linker_simple": ["game/project_linker_integration.rpy", 65], "simple_project_browser": ["game/project_linker_integration.rpy", 112], "safe_gaming_settings": ["game/safe_gaming_settings.rpy", 17], "gaming_settings_safe": ["game/safe_gaming_settings.rpy", 156], "simple_settings": ["game/safe_gaming_settings.rpy", 197], "_developer": ["renpy/common/_developer/developer.rpym", 27], "_image_attributes": ["renpy/common/_developer/developer.rpym", 98], "_variable_viewer": ["renpy/common/_developer/developer.rpym", 174], "_missing_images": ["renpy/common/_developer/developer.rpym", 302], "_image_location_picker_image": ["renpy/common/_developer/developer.rpym", 395], "_image_location_picker": ["renpy/common/_developer/developer.rpym", 425], "_image_load_log": ["renpy/common/_developer/developer.rpym", 524], "_translation_identifier": ["renpy/common/_developer/developer.rpym", 585], "_filename_and_line": ["renpy/common/_developer/developer.rpym", 589], "_inspector": ["renpy/common/_developer/inspector.rpym", 27], "_style_inspector": ["renpy/common/_developer/inspector.rpym", 110]}, "transform": {"_notify_transform": ["renpy/common/00action_other.rpy", 995], "_touch_keyboard": ["renpy/common/00touchkeyboard.rpy", 89], "reset": ["renpy/common/00definitions.rpy", 29], "left": ["game/images_definitions.rpy", 74], "right": ["game/images_definitions.rpy", 78], "center": ["game/images_definitions.rpy", 70], "truecenter": ["renpy/common/00definitions.rpy", 43], "topleft": ["renpy/common/00definitions.rpy", 46], "topright": ["renpy/common/00definitions.rpy", 49], "top": ["renpy/common/00definitions.rpy", 52], "offscreenleft": ["renpy/common/00definitions.rpy", 58], "offscreenright": ["renpy/common/00definitions.rpy", 61], "default": ["renpy/common/00definitions.rpy", 64], "_moveleft": ["renpy/common/00definitions.rpy", 71], "_moveright": ["renpy/common/00definitions.rpy", 74], "_movetop": ["renpy/common/00definitions.rpy", 77], "_movebottom": ["renpy/common/00definitions.rpy", 80], "Swing": ["renpy/common/00definitions.rpy", 370], "delayed_blink": ["game/screens.rpy", 1823], "notify_appear": ["game/screens.rpy", 1872], "vr_ui_panel": ["game/game_router.rpy", 2068], "vr_ui_hover": ["game/game_router.rpy", 2073], "mobile_slide_in": ["game/mobile_styles.rpy", 89], "mobile_slide_out": ["game/mobile_styles.rpy", 93], "mobile_bounce": ["game/mobile_styles.rpy", 97], "mobile_fade_in": ["game/mobile_styles.rpy", 102]}, "callable": {"_": ["renpy/common/00obsolete.rpy", 47], "predict_say": ["renpy/common/00library.rpy", 321], "say": ["renpy/common/00library.rpy", 328], "_error_hyperlink_styler": ["renpy/common/_errorhandling.rpym", 293], "_error_hyperlink_function": ["renpy/common/_errorhandling.rpym", 296], "_ExceptionPrintContext.__init__": ["renpy/common/_errorhandling.rpym", 500], "_ExceptionPrintContext._escape_limit": ["renpy/common/_errorhandling.rpym", 510], "_ExceptionPrintContext._color": ["renpy/common/_errorhandling.rpym", 517], "_ExceptionPrintContext.location": ["renpy/common/_errorhandling.rpym", 520], "_ExceptionPrintContext.source_carets": ["renpy/common/_errorhandling.rpym", 526], "_ExceptionPrintContext.string": ["renpy/common/_errorhandling.rpym", 548], "_ExceptionPrintContext.final_exception_line": ["renpy/common/_errorhandling.rpym", 551], "_m1__errorhandling__simple_traceback": ["renpy/common/_errorhandling.rpym", 560], "_m1__errorhandling__full_traceback": ["renpy/common/_errorhandling.rpym", 564], "_m1__errorhandling__format_parse_errors": ["renpy/common/_errorhandling.rpym", 568], "_EditFile.__init__": ["renpy/common/_errorhandling.rpym", 633], "_EditFile.__call__": ["renpy/common/_errorhandling.rpym", 637], "_CopyFile.__init__": ["renpy/common/_errorhandling.rpym", 652], "_CopyFile.__call__": ["renpy/common/_errorhandling.rpym", 656], "_m1__errorhandling__can_open_traceback": ["renpy/common/_errorhandling.rpym", 668], "_m1__errorhandling__TooltipAction.__init__": ["renpy/common/_errorhandling.rpym", 673], "_m1__errorhandling__TooltipAction.__call__": ["renpy/common/_errorhandling.rpym", 677], "_m1__errorhandling__TooltipAction.unhovered": ["renpy/common/_errorhandling.rpym", 682], "_m1__errorhandling__Tooltip.__init__": ["renpy/common/_errorhandling.rpym", 688], "_m1__errorhandling__Tooltip.action": ["renpy/common/_errorhandling.rpym", 692], "_m1__errorhandling__XScrollValue.__init__": ["renpy/common/_errorhandling.rpym", 696], "_m1__errorhandling__XScrollValue.get_adjustment": ["renpy/common/_errorhandling.rpym", 699], "_m1__errorhandling__XScrollValue.get_style": ["renpy/common/_errorhandling.rpym", 706], "_m1__errorhandling__YScrollValue.__init__": ["renpy/common/_errorhandling.rpym", 710], "_m1__errorhandling__YScrollValue.get_adjustment": ["renpy/common/_errorhandling.rpym", 713], "_m1__errorhandling__YScrollValue.get_style": ["renpy/common/_errorhandling.rpym", 720], "_m1__errorhandling__ErrorQuit.__call__": ["renpy/common/_errorhandling.rpym", 728], "_m1__errorhandling__EnterConsole.__call__": ["renpy/common/_errorhandling.rpym", 738], "_scan_audio_directory": ["renpy/common/00audio.rpy", 25], "_scan_images_directory": ["renpy/common/00images.rpy", 28], "_default_with_callback": ["renpy/common/00obsolete.rpy", 128], "_enter_menu": ["renpy/common/00gamemenu.rpy", 80], "_invoke_game_menu": ["renpy/common/00gamemenu.rpy", 120], "DictEquality.__eq__": ["renpy/common/00library.rpy", 41], "DictEquality.__ne__": ["renpy/common/00library.rpy", 58], "DictEquality.__hash__": ["renpy/common/00library.rpy", 61], "FieldEquality.__eq__": ["renpy/common/00library.rpy", 79], "FieldEquality.__ne__": ["renpy/common/00library.rpy", 106], "FieldEquality.__hash__": ["renpy/common/00library.rpy", 109], "_default_empty_window": ["renpy/common/00library.rpy", 139], "_Extend.get_who": ["renpy/common/00library.rpy", 192], "_Extend.__call__": ["renpy/common/00library.rpy", 204], "_Extend.get_extend_text": ["renpy/common/00library.rpy", 222], "_skip_indicator": ["renpy/common/00library.rpy", 240], "_predict_statements": ["renpy/common/00library.rpy", 279], "_predict_screens": ["renpy/common/00library.rpy", 292], "_side_per_interact": ["renpy/common/00sideimage.rpy", 50], "SideImage": ["renpy/common/00sideimage.rpy", 89], "HasSideImage": ["renpy/common/00sideimage.rpy", 106], "_get_field": ["renpy/common/00action_data.rpy", 26], "_set_field": ["renpy/common/00action_data.rpy", 40], "AddToSet.__init__": ["renpy/common/00action_data.rpy", 369], "AddToSet.get_sensitive": ["renpy/common/00action_data.rpy", 373], "AddToSet.__call__": ["renpy/common/00action_data.rpy", 376], "RemoveFromSet.__init__": ["renpy/common/00action_data.rpy", 400], "RemoveFromSet.get_sensitive": ["renpy/common/00action_data.rpy", 404], "RemoveFromSet.__call__": ["renpy/common/00action_data.rpy", 407], "ToggleSetMembership.__init__": ["renpy/common/00action_data.rpy", 434], "ToggleSetMembership.get_selected": ["renpy/common/00action_data.rpy", 438], "ToggleSetMembership.__call__": ["renpy/common/00action_data.rpy", 441], "_keymap_list.remove": ["renpy/common/00keymap.rpy", 26], "_keymap_toggle_afm": ["renpy/common/00keymap.rpy", 227], "_toggle_skipping": ["renpy/common/00keymap.rpy", 233], "toggle_skipping": ["renpy/common/00keymap.rpy", 233], "_keymap_toggle_skipping": ["renpy/common/00keymap.rpy", 253], "_help": ["renpy/common/00keymap.rpy", 263], "_screenshot_core": ["renpy/common/00keymap.rpy", 296], "_screenshot": ["renpy/common/00keymap.rpy", 339], "_screenshot_callback": ["renpy/common/00keymap.rpy", 345], "_fast_skip": ["renpy/common/00keymap.rpy", 351], "_reload_game": ["renpy/common/00keymap.rpy", 357], "_launch_editor": ["renpy/common/00keymap.rpy", 372], "_developer": ["renpy/common/00keymap.rpy", 379], "_profile_once": ["renpy/common/00keymap.rpy", 387], "_memory_profile": ["renpy/common/00keymap.rpy", 395], "_progress_screen": ["renpy/common/00keymap.rpy", 403], "_init_language": ["renpy/common/00start.rpy", 73], "_InputValueAction.__init__": ["renpy/common/00inputvalues.rpy", 29], "_InputValueAction.__call__": ["renpy/common/00inputvalues.rpy", 33], "_InputValueAction.get_selected": ["renpy/common/00inputvalues.rpy", 55], "DisableAllInputValues.__call__": ["renpy/common/00inputvalues.rpy", 76], "InputValue.get_text": ["renpy/common/00inputvalues.rpy", 90], "InputValue.set_text": ["renpy/common/00inputvalues.rpy", 93], "InputValue.enter": ["renpy/common/00inputvalues.rpy", 96], "InputValue.Enable": ["renpy/common/00inputvalues.rpy", 102], "InputValue.Disable": ["renpy/common/00inputvalues.rpy", 108], "InputValue.Toggle": ["renpy/common/00inputvalues.rpy", 114], "_m1_00inputvalues__GenericInputValue.__init__": ["renpy/common/00inputvalues.rpy", 128], "ScreenVariableInputValue.__init__": ["renpy/common/00inputvalues.rpy", 151], "ScreenVariableInputValue.get_text": ["renpy/common/00inputvalues.rpy", 156], "ScreenVariableInputValue.set_text": ["renpy/common/00inputvalues.rpy", 160], "FieldInputValue.__init__": ["renpy/common/00inputvalues.rpy", 182], "FieldInputValue.get_text": ["renpy/common/00inputvalues.rpy", 187], "FieldInputValue.set_text": ["renpy/common/00inputvalues.rpy", 190], "VariableInputValue.after_upgrade": ["renpy/common/00inputvalues.rpy", 214], "VariableInputValue.__init__": ["renpy/common/00inputvalues.rpy", 220], "DictInputValue.__init__": ["renpy/common/00inputvalues.rpy", 238], "DictInputValue.get_text": ["renpy/common/00inputvalues.rpy", 243], "DictInputValue.set_text": ["renpy/common/00inputvalues.rpy", 246], "LocalVariableInputValue.__init__": ["renpy/common/00inputvalues.rpy", 270], "LocalVariableInputValue.get_text": ["renpy/common/00inputvalues.rpy", 273], "alt": ["renpy/common/00accessibility.rpy", 46], "sv": ["renpy/common/00accessibility.rpy", 46], "_font_transform": ["renpy/common/00accessibility.rpy", 78], "Play.__init__": ["renpy/common/00action_audio.rpy", 52], "Play.__call__": ["renpy/common/00action_audio.rpy", 63], "Play.get_selected": ["renpy/common/00action_audio.rpy", 67], "Play.periodic": ["renpy/common/00action_audio.rpy", 75], "Queue.__init__": ["renpy/common/00action_audio.rpy", 100], "Queue.__call__": ["renpy/common/00action_audio.rpy", 105], "Stop.__init__": ["renpy/common/00action_audio.rpy", 122], "Stop.__call__": ["renpy/common/00action_audio.rpy", 126], "SetMixer.__init__": ["renpy/common/00action_audio.rpy", 146], "SetMixer.__call__": ["renpy/common/00action_audio.rpy", 150], "SetMixer.get_selected": ["renpy/common/00action_audio.rpy", 154], "GetMixer": ["renpy/common/00action_audio.rpy", 157], "SetMute.__init__": ["renpy/common/00action_audio.rpy", 194], "SetMute.__call__": ["renpy/common/00action_audio.rpy", 201], "SetMute.get_selected": ["renpy/common/00action_audio.rpy", 207], "ToggleMute.__init__": ["renpy/common/00action_audio.rpy", 227], "ToggleMute.__call__": ["renpy/common/00action_audio.rpy", 233], "ToggleMute.get_selected": ["renpy/common/00action_audio.rpy", 241], "PauseAudio.__init__": ["renpy/common/00action_audio.rpy", 261], "PauseAudio.__call__": ["renpy/common/00action_audio.rpy", 265], "PauseAudio.get_selected": ["renpy/common/00action_audio.rpy", 273], "NullAction.__call__": ["renpy/common/00action_control.rpy", 35], "Return.__init__": ["renpy/common/00action_control.rpy", 53], "Return.__call__": ["renpy/common/00action_control.rpy", 56], "Jump.__init__": ["renpy/common/00action_control.rpy", 75], "Jump.__call__": ["renpy/common/00action_control.rpy", 78], "Call.__init__": ["renpy/common/00action_control.rpy", 93], "Call.__call__": ["renpy/common/00action_control.rpy", 98], "Show.__init__": ["renpy/common/00action_control.rpy", 121], "Show.predict": ["renpy/common/00action_control.rpy", 127], "Show.__call__": ["renpy/common/00action_control.rpy", 130], "Show.get_selected": ["renpy/common/00action_control.rpy", 138], "ToggleScreen.__init__": ["renpy/common/00action_control.rpy", 159], "ToggleScreen.predict": ["renpy/common/00action_control.rpy", 165], "ToggleScreen.__call__": ["renpy/common/00action_control.rpy", 168], "ToggleScreen.get_selected": ["renpy/common/00action_control.rpy", 179], "ShowTransient": ["renpy/common/00action_control.rpy", 183], "Hide.__init__": ["renpy/common/00action_control.rpy", 226], "Hide.__call__": ["renpy/common/00action_control.rpy", 232], "_strftime": ["renpy/common/00action_file.rpy", 78], "_m1_00action_file__slotname": ["renpy/common/00action_file.rpy", 134], "_m1_00action_file__newest_slot": ["renpy/common/00action_file.rpy", 164], "_m1_00action_file__unused_slot_name": ["renpy/common/00action_file.rpy", 171], "FileCurrentPage": ["renpy/common/00action_file.rpy", 187], "FileUsedSlots": ["renpy/common/00action_file.rpy", 196], "FileLoadable": ["renpy/common/00action_file.rpy", 226], "FileScreenshot": ["renpy/common/00action_file.rpy", 236], "FileTime": ["renpy/common/00action_file.rpy", 258], "FileJson": ["renpy/common/00action_file.rpy", 279], "FileSaveName": ["renpy/common/00action_file.rpy", 324], "FileNewest": ["renpy/common/00action_file.rpy", 334], "FileSave.__init__": ["renpy/common/00action_file.rpy", 383], "FileSave.__call__": ["renpy/common/00action_file.rpy", 399], "FileSave.get_sensitive": ["renpy/common/00action_file.rpy", 421], "FileSave.get_selected": ["renpy/common/00action_file.rpy", 433], "FileLoad.__init__": ["renpy/common/00action_file.rpy", 469], "FileLoad.__call__": ["renpy/common/00action_file.rpy", 485], "FileLoad.get_sensitive": ["renpy/common/00action_file.rpy", 501], "FileLoad.get_selected": ["renpy/common/00action_file.rpy", 507], "FileDelete.__init__": ["renpy/common/00action_file.rpy", 537], "FileDelete.__call__": ["renpy/common/00action_file.rpy", 543], "FileDelete.get_sensitive": ["renpy/common/00action_file.rpy", 556], "FileAction": ["renpy/common/00action_file.rpy", 560], "_predict_file_page": ["renpy/common/00action_file.rpy", 584], "FilePage.__init__": ["renpy/common/00action_file.rpy", 609], "FilePage.__call__": ["renpy/common/00action_file.rpy", 619], "FilePage.get_selected": ["renpy/common/00action_file.rpy", 626], "FilePage.predict": ["renpy/common/00action_file.rpy", 629], "FilePageName": ["renpy/common/00action_file.rpy", 632], "FilePageNameInputValue.__init__": ["renpy/common/00action_file.rpy", 675], "FilePageNameInputValue.get_page": ["renpy/common/00action_file.rpy", 685], "FilePageNameInputValue.get_text": ["renpy/common/00action_file.rpy", 702], "FilePageNameInputValue.set_text": ["renpy/common/00action_file.rpy", 724], "FilePageNameInputValue.enter": ["renpy/common/00action_file.rpy", 742], "FileSlotName": ["renpy/common/00action_file.rpy", 747], "FilePageNext.__init__": ["renpy/common/00action_file.rpy", 818], "FilePageNext.__call__": ["renpy/common/00action_file.rpy", 851], "FilePageNext.get_sensitive": ["renpy/common/00action_file.rpy", 858], "FilePageNext.predict": ["renpy/common/00action_file.rpy", 861], "FilePagePrevious.__init__": ["renpy/common/00action_file.rpy", 890], "FilePagePrevious.__call__": ["renpy/common/00action_file.rpy", 921], "FilePagePrevious.get_sensitive": ["renpy/common/00action_file.rpy", 928], "FilePagePrevious.predict": ["renpy/common/00action_file.rpy", 931], "FileTakeScreenshot.__call__": ["renpy/common/00action_file.rpy", 944], "QuickSave": ["renpy/common/00action_file.rpy", 948], "QuickLoad": ["renpy/common/00action_file.rpy", 971], "ShowMenu.__init__": ["renpy/common/00action_menu.rpy", 73], "ShowMenu.predict": ["renpy/common/00action_menu.rpy", 79], "ShowMenu.__call__": ["renpy/common/00action_menu.rpy", 83], "ShowMenu.get_selected": ["renpy/common/00action_menu.rpy", 122], "ShowMenu.get_sensitive": ["renpy/common/00action_menu.rpy", 130], "Continue.__init__": ["renpy/common/00action_menu.rpy", 162], "Continue.__call__": ["renpy/common/00action_menu.rpy", 166], "Continue.get_sensitive": ["renpy/common/00action_menu.rpy", 176], "Start.__init__": ["renpy/common/00action_menu.rpy", 195], "Start.__call__": ["renpy/common/00action_menu.rpy", 198], "MainMenu.__init__": ["renpy/common/00action_menu.rpy", 222], "MainMenu.__call__": ["renpy/common/00action_menu.rpy", 226], "MainMenu.get_sensitive": ["renpy/common/00action_menu.rpy", 239], "Quit.__init__": ["renpy/common/00action_menu.rpy", 257], "Quit.__call__": ["renpy/common/00action_menu.rpy", 260], "Skip.__init__": ["renpy/common/00action_menu.rpy", 295], "Skip.__call__": ["renpy/common/00action_menu.rpy", 299], "Skip.get_selected": ["renpy/common/00action_menu.rpy", 332], "Skip.get_sensitive": ["renpy/common/00action_menu.rpy", 338], "Help.__init__": ["renpy/common/00action_menu.rpy", 381], "Help.__call__": ["renpy/common/00action_menu.rpy", 384], "InvertSelected.__init__": ["renpy/common/00action_other.rpy", 33], "InvertSelected.__call__": ["renpy/common/00action_other.rpy", 41], "InvertSelected.get_selected": ["renpy/common/00action_other.rpy", 44], "InvertSelected.get_sensitive": ["renpy/common/00action_other.rpy", 47], "InvertSelected.periodic": ["renpy/common/00action_other.rpy", 50], "InvertSelected.predict": ["renpy/common/00action_other.rpy", 53], "If": ["renpy/common/00action_other.rpy", 56], "_ActionList.__init__": ["renpy/common/00action_other.rpy", 81], "_ActionList.get_selected": ["renpy/common/00action_other.rpy", 84], "_ActionList.get_sensitive": ["renpy/common/00action_other.rpy", 87], "_ActionList.get_tooltip": ["renpy/common/00action_other.rpy", 90], "_ActionList.periodic": ["renpy/common/00action_other.rpy", 93], "_ActionList.unhovered": ["renpy/common/00action_other.rpy", 96], "_ActionList.__call__": ["renpy/common/00action_other.rpy", 99], "SelectedIf.__init__": ["renpy/common/00action_other.rpy", 122], "SelectedIf.__call__": ["renpy/common/00action_other.rpy", 132], "SelectedIf.get_selected": ["renpy/common/00action_other.rpy", 137], "SensitiveIf.__init__": ["renpy/common/00action_other.rpy", 159], "SensitiveIf.__call__": ["renpy/common/00action_other.rpy", 169], "SensitiveIf.get_sensitive": ["renpy/common/00action_other.rpy", 174], "Screenshot.__call__": ["renpy/common/00action_other.rpy", 185], "HideInterface.__call__": ["renpy/common/00action_other.rpy", 197], "OpenURL.__init__": ["renpy/common/00action_other.rpy", 209], "OpenURL.__call__": ["renpy/common/00action_other.rpy", 212], "With.__init__": ["renpy/common/00action_other.rpy", 222], "With.__call__": ["renpy/common/00action_other.rpy", 225], "Notify.__init__": ["renpy/common/00action_other.rpy", 237], "Notify.predict": ["renpy/common/00action_other.rpy", 240], "Notify.__call__": ["renpy/common/00action_other.rpy", 243], "Rollback.__init__": ["renpy/common/00action_other.rpy", 262], "Rollback.__call__": ["renpy/common/00action_other.rpy", 267], "Rollback.get_sensitive": ["renpy/common/00action_other.rpy", 270], "RollbackToIdentifier.__init__": ["renpy/common/00action_other.rpy", 281], "RollbackToIdentifier.__call__": ["renpy/common/00action_other.rpy", 284], "RollbackToIdentifier.get_sensitive": ["renpy/common/00action_other.rpy", 290], "RestartStatement.__call__": ["renpy/common/00action_other.rpy", 307], "RollForward.__call__": ["renpy/common/00action_other.rpy", 319], "RollForward.get_sensitive": ["renpy/common/00action_other.rpy", 322], "GetTooltip": ["renpy/common/00action_other.rpy", 328], "_m1_00action_other__TooltipAction.__init__": ["renpy/common/00action_other.rpy", 352], "_m1_00action_other__TooltipAction.__call__": ["renpy/common/00action_other.rpy", 356], "_m1_00action_other__TooltipAction.unhovered": ["renpy/common/00action_other.rpy", 361], "_m1_00screen__TooltipAction.__init__": ["renpy/common/00action_other.rpy", 352], "_m1_00screen__TooltipAction.__call__": ["renpy/common/00action_other.rpy", 356], "_m1_00screen__TooltipAction.unhovered": ["renpy/common/00action_other.rpy", 361], "Tooltip.__init__": ["renpy/common/00action_other.rpy", 382], "Tooltip.Action": ["renpy/common/00action_other.rpy", 386], "Tooltip.action": ["renpy/common/00action_other.rpy", 386], "Language.__init__": ["renpy/common/00action_other.rpy", 418], "Language.__call__": ["renpy/common/00action_other.rpy", 421], "Language.get_selected": ["renpy/common/00action_other.rpy", 424], "Language.get_sensitive": ["renpy/common/00action_other.rpy", 427], "Replay.__init__": ["renpy/common/00action_other.rpy", 457], "Replay.__call__": ["renpy/common/00action_other.rpy", 462], "Replay.get_sensitive": ["renpy/common/00action_other.rpy", 475], "EndReplay.__init__": ["renpy/common/00action_other.rpy", 492], "EndReplay.__call__": ["renpy/common/00action_other.rpy", 495], "EndReplay.get_sensitive": ["renpy/common/00action_other.rpy", 505], "MouseMove.__init__": ["renpy/common/00action_other.rpy", 525], "MouseMove.__call__": ["renpy/common/00action_other.rpy", 530], "QueueEvent.__init__": ["renpy/common/00action_other.rpy", 543], "QueueEvent.__call__": ["renpy/common/00action_other.rpy", 547], "Function.__eq__": ["renpy/common/00action_other.rpy", 583], "Function.__init__": ["renpy/common/00action_other.rpy", 609], "Function.__call__": ["renpy/common/00action_other.rpy", 617], "Confirm.__init__": ["renpy/common/00action_other.rpy", 654], "Confirm.__call__": ["renpy/common/00action_other.rpy", 661], "Confirm.get_sensitive": ["renpy/common/00action_other.rpy", 667], "Confirm.get_selected": ["renpy/common/00action_other.rpy", 673], "Confirm.get_tooltip": ["renpy/common/00action_other.rpy", 676], "Scroll.__init__": ["renpy/common/00action_other.rpy", 704], "Scroll.get_adjustment_and_delta": ["renpy/common/00action_other.rpy", 711], "Scroll.get_sensitive": ["renpy/common/00action_other.rpy", 740], "Scroll.__call__": ["renpy/common/00action_other.rpy", 756], "OpenDirectory.__init__": ["renpy/common/00action_other.rpy", 788], "OpenDirectory.get_sensitive": ["renpy/common/00action_other.rpy", 796], "OpenDirectory.__call__": ["renpy/common/00action_other.rpy", 801], "CaptureFocus.__init__": ["renpy/common/00action_other.rpy", 837], "CaptureFocus.__call__": ["renpy/common/00action_other.rpy", 840], "ToggleFocus.__init__": ["renpy/common/00action_other.rpy", 857], "ToggleFocus.__call__": ["renpy/common/00action_other.rpy", 860], "ClearFocus.__init__": ["renpy/common/00action_other.rpy", 880], "ClearFocus.__call__": ["renpy/common/00action_other.rpy", 883], "GetFocusRect": ["renpy/common/00action_other.rpy", 887], "ExecJS.__init__": ["renpy/common/00action_other.rpy", 917], "ExecJS.__call__": ["renpy/common/00action_other.rpy", 920], "CurrentScreenName": ["renpy/common/00action_other.rpy", 927], "CopyToClipboard.__init__": ["renpy/common/00action_other.rpy", 953], "CopyToClipboard.__call__": ["renpy/common/00action_other.rpy", 956], "EditFile.__init__": ["renpy/common/00action_other.rpy", 976], "EditFile.__call__": ["renpy/common/00action_other.rpy", 980], "StaticValue.__init__": ["renpy/common/00barvalues.rpy", 38], "StaticValue.get_adjustment": ["renpy/common/00barvalues.rpy", 42], "AnimatedValue.__init__": ["renpy/common/00barvalues.rpy", 69], "AnimatedValue.get_adjustment": ["renpy/common/00barvalues.rpy", 81], "AnimatedValue.periodic": ["renpy/common/00barvalues.rpy", 85], "AnimatedValue.replaces": ["renpy/common/00barvalues.rpy", 103], "_m1_00barvalues__GenericValue.__init__": ["renpy/common/00barvalues.rpy", 126], "_m1_00barvalues__GenericValue.changed": ["renpy/common/00barvalues.rpy", 147], "_m1_00barvalues__GenericValue.get_adjustment": ["renpy/common/00barvalues.rpy", 162], "_m1_00barvalues__GenericValue.get_style": ["renpy/common/00barvalues.rpy", 181], "DictValue.__init__": ["renpy/common/00barvalues.rpy", 204], "DictValue.get_value": ["renpy/common/00barvalues.rpy", 209], "DictValue.set_value": ["renpy/common/00barvalues.rpy", 214], "FieldValue.__init__": ["renpy/common/00barvalues.rpy", 240], "FieldValue.get_value": ["renpy/common/00barvalues.rpy", 245], "FieldValue.set_value": ["renpy/common/00barvalues.rpy", 250], "VariableValue.__init__": ["renpy/common/00barvalues.rpy", 270], "ScreenVariableValue.__init__": ["renpy/common/00barvalues.rpy", 295], "ScreenVariableValue.get_value": ["renpy/common/00barvalues.rpy", 299], "ScreenVariableValue.set_value": ["renpy/common/00barvalues.rpy", 311], "LocalVariableValue.__init__": ["renpy/common/00barvalues.rpy", 338], "MixerValue.__init__": ["renpy/common/00barvalues.rpy", 419], "MixerValue.get_volume": ["renpy/common/00barvalues.rpy", 424], "MixerValue.set_volume": ["renpy/common/00barvalues.rpy", 427], "MixerValue.set_mixer": ["renpy/common/00barvalues.rpy", 430], "MixerValue.get_mixer": ["renpy/common/00barvalues.rpy", 445], "MixerValue.get_adjustment": ["renpy/common/00barvalues.rpy", 460], "MixerValue.get_style": ["renpy/common/00barvalues.rpy", 477], "_CharacterVolumeValue.__init__": ["renpy/common/00barvalues.rpy", 483], "_CharacterVolumeValue.get_volume": ["renpy/common/00barvalues.rpy", 486], "_CharacterVolumeValue.set_volume": ["renpy/common/00barvalues.rpy", 489], "XScrollValue.__init__": ["renpy/common/00barvalues.rpy", 504], "XScrollValue.get_adjustment": ["renpy/common/00barvalues.rpy", 507], "XScrollValue.get_style": ["renpy/common/00barvalues.rpy", 514], "YScrollValue.__init__": ["renpy/common/00barvalues.rpy", 528], "YScrollValue.get_adjustment": ["renpy/common/00barvalues.rpy", 531], "YScrollValue.get_style": ["renpy/common/00barvalues.rpy", 539], "AudioPositionValue.__init__": ["renpy/common/00barvalues.rpy", 555], "AudioPositionValue.get_pos_duration": ["renpy/common/00barvalues.rpy", 561], "AudioPositionValue.get_adjustment": ["renpy/common/00barvalues.rpy", 567], "AudioPositionValue.periodic": ["renpy/common/00barvalues.rpy", 572], "_vol": ["renpy/common/00defaults.rpy", 65], "_apply_default_preferences": ["renpy/common/00defaults.rpy", 75], "_locale_to_language_function": ["renpy/common/00defaults.rpy", 147], "_imagemap_auto_function": ["renpy/common/00defaults.rpy", 163], "hyperlink_styler": ["renpy/common/00defaults.rpy", 211], "hyperlink_function": ["renpy/common/00defaults.rpy", 214], "hyperlink_sensitive": ["renpy/common/00defaults.rpy", 226], "_m1_00gallery__GalleryAllPriorCondition.check": ["renpy/common/00gallery.rpy", 26], "_m1_00gallery__GalleryArbitraryCondition.__init__": ["renpy/common/00gallery.rpy", 31], "_m1_00gallery__GalleryArbitraryCondition.check": ["renpy/common/00gallery.rpy", 34], "_m1_00gallery__GalleryUnlockCondition.__init__": ["renpy/common/00gallery.rpy", 39], "_m1_00gallery__GalleryUnlockCondition.check": ["renpy/common/00gallery.rpy", 42], "_m1_00gallery__GalleryImage.__init__": ["renpy/common/00gallery.rpy", 56], "_m1_00gallery__GalleryImage.check_unlock": ["renpy/common/00gallery.rpy", 75], "_m1_00gallery__GalleryImage.show": ["renpy/common/00gallery.rpy", 86], "_m1_00gallery__GalleryButton.__init__": ["renpy/common/00gallery.rpy", 124], "_m1_00gallery__GalleryButton.check_unlock": ["renpy/common/00gallery.rpy", 130], "_m1_00gallery__GalleryToggleSlideshow.__init__": ["renpy/common/00gallery.rpy", 147], "_m1_00gallery__GalleryToggleSlideshow.__call__": ["renpy/common/00gallery.rpy", 150], "_m1_00gallery__GalleryToggleSlideshow.get_selected": ["renpy/common/00gallery.rpy", 154], "_m1_00gallery__GalleryAction.__init__": ["renpy/common/00gallery.rpy", 164], "_m1_00gallery__GalleryAction.__call__": ["renpy/common/00gallery.rpy", 168], "Gallery.__init__": ["renpy/common/00gallery.rpy", 265], "Gallery.button": ["renpy/common/00gallery.rpy", 290], "Gallery.image": ["renpy/common/00gallery.rpy", 307], "Gallery.display": ["renpy/common/00gallery.rpy", 307], "Gallery.transform": ["renpy/common/00gallery.rpy", 333], "Gallery.unlock": ["renpy/common/00gallery.rpy", 347], "Gallery.condition": ["renpy/common/00gallery.rpy", 358], "Gallery.allprior": ["renpy/common/00gallery.rpy", 373], "Gallery.unlock_image": ["renpy/common/00gallery.rpy", 383], "Gallery.Action": ["renpy/common/00gallery.rpy", 398], "Gallery.make_button": ["renpy/common/00gallery.rpy", 416], "Gallery.get_fraction": ["renpy/common/00gallery.rpy", 474], "Gallery.show": ["renpy/common/00gallery.rpy", 515], "Gallery.Return": ["renpy/common/00gallery.rpy", 610], "Gallery.Next": ["renpy/common/00gallery.rpy", 619], "Gallery.Previous": ["renpy/common/00gallery.rpy", 634], "Gallery.ToggleSlideshow": ["renpy/common/00gallery.rpy", 649], "_SetRenderer.__init__": ["renpy/common/00gltest.rpy", 33], "_SetRenderer.__call__": ["renpy/common/00gltest.rpy", 36], "_SetRenderer.get_selected": ["renpy/common/00gltest.rpy", 40], "_m1_00gltest__gl_test": ["renpy/common/00gltest.rpy", 263], "_gl_performance_test": ["renpy/common/00gltest.rpy", 284], "_BaseMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 36], "_BaseMatrix.__call__": ["renpy/common/00matrixcolor.rpy", 39], "_BaseMatrix.__mul__": ["renpy/common/00matrixcolor.rpy", 50], "SplineMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 89], "SplineMatrix.__call__": ["renpy/common/00matrixcolor.rpy", 93], "_MultiplyMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 123], "_MultiplyMatrix.__call__": ["renpy/common/00matrixcolor.rpy", 128], "IdentityMatrix.get": ["renpy/common/00matrixcolor.rpy", 144], "SaturationMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 173], "SaturationMatrix.get": ["renpy/common/00matrixcolor.rpy", 177], "TintMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 202], "TintMatrix.__call__": ["renpy/common/00matrixcolor.rpy", 205], "BrightnessMatrix.get": ["renpy/common/00matrixcolor.rpy", 253], "OpacityMatrix.get": ["renpy/common/00matrixcolor.rpy", 272], "ContrastMatrix.get": ["renpy/common/00matrixcolor.rpy", 294], "ColorizeMatrix.__init__": ["renpy/common/00matrixcolor.rpy", 323], "ColorizeMatrix.__call__": ["renpy/common/00matrixcolor.rpy", 328], "HueMatrix.get": ["renpy/common/00matrixcolor.rpy", 365], "InvertMatrix.get": ["renpy/common/00matrixcolor.rpy", 395], "SepiaMatrix": ["renpy/common/00matrixcolor.rpy", 404], "MouseDisplayable.__init__": ["renpy/common/00mousedisplayable.rpy", 41], "MouseDisplayable.add": ["renpy/common/00mousedisplayable.rpy", 50], "MouseDisplayable.render": ["renpy/common/00mousedisplayable.rpy", 61], "MouseDisplayable.visit": ["renpy/common/00mousedisplayable.rpy", 99], "_m1_00musicroom__MusicRoomPlay.__init__": ["renpy/common/00musicroom.rpy", 38], "_m1_00musicroom__MusicRoomPlay.__call__": ["renpy/common/00musicroom.rpy", 43], "_m1_00musicroom__MusicRoomPlay.get_sensitive": ["renpy/common/00musicroom.rpy", 54], "_m1_00musicroom__MusicRoomPlay.get_selected": ["renpy/common/00musicroom.rpy", 57], "_m1_00musicroom__MusicRoomPlay.periodic": ["renpy/common/00musicroom.rpy", 60], "_m1_00musicroom__MusicRoomRandomPlay.__init__": ["renpy/common/00musicroom.rpy", 77], "_m1_00musicroom__MusicRoomRandomPlay.__call__": ["renpy/common/00musicroom.rpy", 80], "_m1_00musicroom__MusicRoomTogglePlay.__init__": ["renpy/common/00musicroom.rpy", 100], "_m1_00musicroom__MusicRoomTogglePlay.__call__": ["renpy/common/00musicroom.rpy", 103], "_m1_00musicroom__MusicRoomTogglePlay.get_selected": ["renpy/common/00musicroom.rpy", 113], "_m1_00musicroom__MusicRoomTogglePause.__init__": ["renpy/common/00musicroom.rpy", 124], "_m1_00musicroom__MusicRoomTogglePause.__call__": ["renpy/common/00musicroom.rpy", 127], "_m1_00musicroom__MusicRoomTogglePause.get_selected": ["renpy/common/00musicroom.rpy", 136], "_m1_00musicroom__MusicRoomTogglePause.get_sensitive": ["renpy/common/00musicroom.rpy", 139], "_m1_00musicroom__MusicRoomTogglePause.periodic": ["renpy/common/00musicroom.rpy", 142], "_m1_00musicroom__MusicRoomStop.__init__": ["renpy/common/00musicroom.rpy", 156], "_m1_00musicroom__MusicRoomStop.__call__": ["renpy/common/00musicroom.rpy", 160], "_m1_00musicroom__MusicRoomStop.get_selected": ["renpy/common/00musicroom.rpy", 163], "_m1_00musicroom__MusicRoomStop.periodic": ["renpy/common/00musicroom.rpy", 166], "MusicRoom.__init__": ["renpy/common/00musicroom.rpy", 189], "MusicRoom.periodic": ["renpy/common/00musicroom.rpy", 270], "MusicRoom.add": ["renpy/common/00musicroom.rpy", 289], "MusicRoom.is_unlocked": ["renpy/common/00musicroom.rpy", 319], "MusicRoom.unlocked_playlist": ["renpy/common/00musicroom.rpy", 332], "MusicRoom.play": ["renpy/common/00musicroom.rpy", 357], "MusicRoom.queue_if_playing": ["renpy/common/00musicroom.rpy", 400], "MusicRoom.stop": ["renpy/common/00musicroom.rpy", 418], "MusicRoom.next": ["renpy/common/00musicroom.rpy", 425], "MusicRoom.previous": ["renpy/common/00musicroom.rpy", 436], "MusicRoom.Play": ["renpy/common/00musicroom.rpy", 443], "MusicRoom.RandomPlay": ["renpy/common/00musicroom.rpy", 464], "MusicRoom.TogglePlay": ["renpy/common/00musicroom.rpy", 474], "MusicRoom.TogglePause": ["renpy/common/00musicroom.rpy", 485], "MusicRoom.Stop": ["renpy/common/00musicroom.rpy", 497], "MusicRoom.Next": ["renpy/common/00musicroom.rpy", 506], "MusicRoom.Previous": ["renpy/common/00musicroom.rpy", 516], "MusicRoom.SetLoop": ["renpy/common/00musicroom.rpy", 526], "MusicRoom.SetSingleTrack": ["renpy/common/00musicroom.rpy", 535], "MusicRoom.SetShuffle": ["renpy/common/00musicroom.rpy", 548], "MusicRoom.ToggleLoop": ["renpy/common/00musicroom.rpy", 560], "MusicRoom.ToggleSingleTrack": ["renpy/common/00musicroom.rpy", 570], "MusicRoom.ToggleShuffle": ["renpy/common/00musicroom.rpy", 579], "_m1_00nvl_mode__s": ["renpy/common/00nvl_mode.rpy", 107], "_m1_00nvl_mode__nvl_screen_dialogue": ["renpy/common/00nvl_mode.rpy", 119], "_m1_00nvl_mode__nvl_show_screen": ["renpy/common/00nvl_mode.rpy", 190], "nvl_show_core": ["renpy/common/00nvl_mode.rpy", 203], "nvl_window": ["renpy/common/00nvl_mode.rpy", 236], "nvl_show": ["renpy/common/00nvl_mode.rpy", 239], "nvl_hide": ["renpy/common/00nvl_mode.rpy", 257], "nvl_erase": ["renpy/common/00nvl_mode.rpy", 280], "nvl_clear_next": ["renpy/common/00nvl_mode.rpy", 285], "NVLCharacter.__init__": ["renpy/common/00nvl_mode.rpy", 307], "NVLCharacter.push_nvl_list": ["renpy/common/00nvl_mode.rpy", 328], "NVLCharacter.pop_nvl_list": ["renpy/common/00nvl_mode.rpy", 350], "NVLCharacter.do_add": ["renpy/common/00nvl_mode.rpy", 354], "NVLCharacter.do_display": ["renpy/common/00nvl_mode.rpy", 366], "NVLCharacter.do_done": ["renpy/common/00nvl_mode.rpy", 405], "NVLCharacter.do_extend": ["renpy/common/00nvl_mode.rpy", 428], "nvl_clear": ["renpy/common/00nvl_mode.rpy", 454], "nvl_menu": ["renpy/common/00nvl_mode.rpy", 467], "NVLSpeaker.__init__": ["renpy/common/00nvl_mode.rpy", 307], "NVLSpeaker.push_nvl_list": ["renpy/common/00nvl_mode.rpy", 328], "NVLSpeaker.pop_nvl_list": ["renpy/common/00nvl_mode.rpy", 350], "NVLSpeaker.do_add": ["renpy/common/00nvl_mode.rpy", 354], "NVLSpeaker.do_display": ["renpy/common/00nvl_mode.rpy", 366], "NVLSpeaker.do_done": ["renpy/common/00nvl_mode.rpy", 405], "NVLSpeaker.do_extend": ["renpy/common/00nvl_mode.rpy", 428], "_nvl_adv_callback": ["renpy/common/00nvl_mode.rpy", 553], "Placeholder.after_setstate": ["renpy/common/00placeholder.rpy", 34], "Placeholder.__init__": ["renpy/common/00placeholder.rpy", 37], "Placeholder.guess_base": ["renpy/common/00placeholder.rpy", 92], "Placeholder.get_child": ["renpy/common/00placeholder.rpy", 134], "Placeholder._duplicate": ["renpy/common/00placeholder.rpy", 203], "Placeholder.visit": ["renpy/common/00placeholder.rpy", 215], "Placeholder.render": ["renpy/common/00placeholder.rpy", 218], "_m1_00preferences__DisplayAction.__init__": ["renpy/common/00preferences.rpy", 29], "_m1_00preferences__DisplayAction.get_size": ["renpy/common/00preferences.rpy", 32], "_m1_00preferences__DisplayAction.__call__": ["renpy/common/00preferences.rpy", 49], "_m1_00preferences__DisplayAction.get_sensitive": ["renpy/common/00preferences.rpy", 53], "_m1_00preferences__DisplayAction.get_selected": ["renpy/common/00preferences.rpy", 59], "_m1_00screen__DisplayAction.__init__": ["renpy/common/00preferences.rpy", 29], "_m1_00screen__DisplayAction.get_size": ["renpy/common/00preferences.rpy", 32], "_m1_00screen__DisplayAction.__call__": ["renpy/common/00preferences.rpy", 49], "_m1_00screen__DisplayAction.get_sensitive": ["renpy/common/00preferences.rpy", 53], "_m1_00screen__DisplayAction.get_selected": ["renpy/common/00preferences.rpy", 59], "_m1_00preferences__ResetPreferences.__call__": ["renpy/common/00preferences.rpy", 71], "_DisplayReset.__call__": ["renpy/common/00preferences.rpy", 89], "Preference": ["renpy/common/00preferences.rpy", 93], "_m1_00preferences__show_self_voicing": ["renpy/common/00preferences.rpy", 700], "_SplineInterpolator.__init__": ["renpy/common/00splines.rpy", 15], "_SplineInterpolator.init_values": ["renpy/common/00splines.rpy", 62], "_SplineInterpolator.__call__": ["renpy/common/00splines.rpy", 79], "SplineMotion": ["renpy/common/00splines.rpy", 126], "_m1_00stylepreferences__register_style_preference": ["renpy/common/00stylepreferences.rpy", 44], "_m1_00stylepreferences__init": ["renpy/common/00stylepreferences.rpy", 79], "_m1_00stylepreferences__update": ["renpy/common/00stylepreferences.rpy", 91], "_m1_00stylepreferences__apply_styles": ["renpy/common/00stylepreferences.rpy", 103], "_m1_00stylepreferences__check": ["renpy/common/00stylepreferences.rpy", 116], "_m1_00stylepreferences__change_language": ["renpy/common/00stylepreferences.rpy", 125], "_m1_00stylepreferences__set_style_preference": ["renpy/common/00stylepreferences.rpy", 129], "_m1_00stylepreferences__get_style_preference": ["renpy/common/00stylepreferences.rpy", 152], "StylePreference.__init__": ["renpy/common/00stylepreferences.rpy", 183], "StylePreference.__call__": ["renpy/common/00stylepreferences.rpy", 190], "StylePreference.get_selected": ["renpy/common/00stylepreferences.rpy", 193], "_TouchKeyboardTextInput.__init__": ["renpy/common/00touchkeyboard.rpy", 32], "_TouchKeyboardTextInput.__call__": ["renpy/common/00touchkeyboard.rpy", 35], "_TouchKeyboardBackspace.__call__": ["renpy/common/00touchkeyboard.rpy", 47], "_TouchKeyboardReturn.__call__": ["renpy/common/00touchkeyboard.rpy", 67], "_invoke_voice_callbacks": ["renpy/common/00voice.rpy", 74], "voice": ["renpy/common/00voice.rpy", 87], "voice_sustain": ["renpy/common/00voice.rpy", 120], "voice_replay": ["renpy/common/00voice.rpy", 133], "voice_can_replay": ["renpy/common/00voice.rpy", 144], "SetVoiceMute.__init__": ["renpy/common/00voice.rpy", 163], "SetVoiceMute.get_selected": ["renpy/common/00voice.rpy", 167], "SetVoiceMute.__call__": ["renpy/common/00voice.rpy", 173], "SetCharacterVolume": ["renpy/common/00voice.rpy", 181], "GetCharacterVolume": ["renpy/common/00voice.rpy", 203], "PlayCharacterVoice.__init__": ["renpy/common/00voice.rpy", 236], "PlayCharacterVoice.__call__": ["renpy/common/00voice.rpy", 242], "PlayCharacterVoice.get_selected": ["renpy/common/00voice.rpy", 256], "PlayCharacterVoice.periodic": ["renpy/common/00voice.rpy", 263], "ToggleVoiceMute.__init__": ["renpy/common/00voice.rpy", 287], "ToggleVoiceMute.get_selected": ["renpy/common/00voice.rpy", 292], "ToggleVoiceMute.__call__": ["renpy/common/00voice.rpy", 300], "VoiceReplay.__call__": ["renpy/common/00voice.rpy", 316], "VoiceReplay.get_sensitive": ["renpy/common/00voice.rpy", 319], "VoiceInfo.__init__": ["renpy/common/00voice.rpy", 328], "_get_voice_info": ["renpy/common/00voice.rpy", 383], "_voice_history_callback": ["renpy/common/00voice.rpy", 423], "_intra_jumps_core": ["renpy/common/00layout.rpy", 48], "_button_menu": ["renpy/common/00layout.rpy", 278], "TransformMatrix.__init__": ["renpy/common/00matrixtransform.rpy", 37], "TransformMatrix.__call__": ["renpy/common/00matrixtransform.rpy", 44], "RoundRect": ["renpy/common/00themes.rpy", 35], "_audio_eval": ["renpy/common/000statements.rpy", 38], "_try_eval": ["renpy/common/000statements.rpy", 41], "_window_show": ["renpy/common/000window.rpy", 46], "_window_hide": ["renpy/common/000window.rpy", 83], "_window_auto_callback": ["renpy/common/000window.rpy", 120], "_init_window": ["renpy/common/000window.rpy", 139], "_JSONDBDict.__init__": ["renpy/common/00db_ren.py", 32], "_JSONDBDict.check": ["renpy/common/00db_ren.py", 45], "_JSONDBDict.__setitem__": ["renpy/common/00db_ren.py", 56], "_JSONDBDict.__delitem__": ["renpy/common/00db_ren.py", 64], "_JSONDBDict.clear": ["renpy/common/00db_ren.py", 71], "_JSONDBDict.setdefault": ["renpy/common/00db_ren.py", 77], "_JSONDBDict.update": ["renpy/common/00db_ren.py", 86], "_JSONDBDict.__ior__": ["renpy/common/00db_ren.py", 97], "JSONDB.__init__": ["renpy/common/00db_ren.py", 171], "JSONDB.save": ["renpy/common/00db_ren.py", 213], "JSONDB.__getitem__": ["renpy/common/00db_ren.py", 233], "JSONDB.__delitem__": ["renpy/common/00db_ren.py", 240], "JSONDB.__setitem__": ["renpy/common/00db_ren.py", 245], "JSONDB.__iter__": ["renpy/common/00db_ren.py", 248], "JSONDB.__reversed__": ["renpy/common/00db_ren.py", 251], "JSONDB.values": ["renpy/common/00db_ren.py", 254], "JSONDB.keys": ["renpy/common/00db_ren.py", 257], "JSONDB.items": ["renpy/common/00db_ren.py", 260], "JSONDB.__len__": ["renpy/common/00db_ren.py", 263], "GamepadExists": ["renpy/common/00gamepad.rpy", 254], "GamepadCalibrate": ["renpy/common/00gamepad.rpy", 271], "_scene_show_hide_transition_callback": ["renpy/common/00sshtransition_ren.py", 47], "_m1_00themes__AWTBox": ["renpy/common/00themes.rpy", 751], "_m1_00themes__AWTButton": ["renpy/common/00themes.rpy", 764], "_m1_00themes__AWTBullet": ["renpy/common/00themes.rpy", 790], "_compat_versions": ["renpy/common/00compat.rpy", 24], "_set_script_version": ["renpy/common/00compat.rpy", 36], "UploadSync.__call__": ["renpy/common/00sync.rpy", 54], "UploadSync.get_sensitive": ["renpy/common/00sync.rpy", 57], "DownloadSync.__call__": ["renpy/common/00sync.rpy", 68], "DownloadSync.get_sensitive": ["renpy/common/00sync.rpy", 72], "BuildConfig.__init__": ["game/build_config.rpy", 12], "BuildConfig.configure_build": ["game/build_config.rpy", 18], "BuildConfig._configure_anticheat_build": ["game/build_config.rpy", 38], "BuildConfig._configure_asset_compression": ["game/build_config.rpy", 49], "BuildConfig.validate_build_compatibility": ["game/build_config.rpy", 62], "safe_rollback_handler": ["game/compatibility_fix.rpy", 15], "compatibility_after_load": ["game/compatibility_fix.rpy", 32], "compatibility_before_save": ["game/compatibility_fix.rpy", 39], "handle_rollback_error": ["game/compatibility_fix.rpy", 55], "detect_platform": ["game/game_router.rpy", 9], "set_game_context": ["game/conflict_resolver.rpy", 9], "get_game_context": ["game/conflict_resolver.rpy", 15], "launch_game_with_loading": ["game/game_router.rpy", 403], "launch_game_direct": ["game/game_router.rpy", 413], "mobile_game_click": ["game/game_router.rpy", 435], "mobile_game_tap": ["game/game_router.rpy", 479], "pc_game_click": ["game/game_router.rpy", 507], "check_game_exists": ["game/game_router.rpy", 534], "get_available_games": ["game/game_router.rpy", 560], "get_new_ui_games": ["game/game_router.rpy", 576], "route_to_game": ["game/game_router.rpy", 594], "register_external_content": ["game/external_content_integrator.rpy", 12], "validate_renpy_syntax": ["game/external_content_integrator.rpy", 21], "safe_integrate_external_content": ["game/external_content_integrator.rpy", 42], "resolve_label_namespace": ["game/label_namespace_resolver.rpy", 57], "get_label_source": ["game/label_namespace_resolver.rpy", 67], "label_exists_in_namespace": ["game/label_namespace_resolver.rpy", 80], "_navigation": ["renpy/common/_layout/classic_navigation.rpym", 39], "_joystick_select_binding": ["renpy/common/_layout/classic_joystick_preferences.rpym", 65], "_joystick_get_binding": ["renpy/common/_layout/classic_joystick_preferences.rpym", 74], "_joystick_take_binding": ["renpy/common/_layout/classic_joystick_preferences.rpym", 78], "_joystick_preferences": ["renpy/common/_layout/classic_joystick_preferences.rpym", 87], "touch": ["game/gui.rpy", 424], "small": ["game/gui.rpy", 431], "fix_rollback_compatibility": ["game/screens.rpy", 463], "show_escape_menu": ["game/screens.rpy", 920], "escape_menu_action": ["game/screens.rpy", 927], "_clear_performance": ["renpy/common/00performance.rpy", 26], "initialize_basic_protection": ["game/anticheat_safe_integration.rpy", 63], "check_safe_ban_status": ["game/anticheat_safe_integration.rpy", 115], "safe_record_user_action": ["game/anticheat_safe_integration.rpy", 259], "startup_compatibility_check": ["game/compatibility_fix.rpy", 95], "switch_to_mobile": ["game/game_router.rpy", 136], "switch_to_desktop": ["game/game_router.rpy", 142], "switch_to_vr": ["game/game_router.rpy", 147], "switch_to_steamvr": ["game/game_router.rpy", 153], "get_current_platform": ["game/platform_detection_integration.rpy", 55], "enable_steamvr": ["game/game_router.rpy", 194], "detect_steamvr": ["game/game_router.rpy", 198], "init_advanced_vr_features": ["game/game_router.rpy", 202], "is_vr_mode": ["game/game_router.rpy", 206], "enable_vr_mode": ["game/game_router.rpy", 210], "disable_vr_mode": ["game/game_router.rpy", 225], "register_label_mapping": ["game/game_router.rpy", 615], "resolve_label_conflict": ["game/game_router.rpy", 619], "safe_record_game_route": ["game/game_router.rpy", 1934], "apply_graphics_settings": ["game/gaming_settings_integration.rpy", 32], "detect_hardware": ["game/gaming_settings_integration.rpy", 55], "open_advanced_gaming_settings": ["game/gaming_settings_integration.rpy", 212], "reset_gaming_settings": ["game/gaming_settings_integration.rpy", 223], "register_label_conflict": ["game/label_namespace_resolver.rpy", 90], "get_conflict_warnings": ["game/label_namespace_resolver.rpy", 100], "safe_call_label": ["game/label_namespace_resolver.rpy", 106], "auto_resolve_game_selection": ["game/label_namespace_resolver.rpy", 217], "mobile_swipe_left": ["game/mobile_styles.rpy", 60], "mobile_swipe_right": ["game/mobile_styles.rpy", 70], "mobile_double_tap": ["game/mobile_styles.rpy", 80], "check_mobile_orientation": ["game/mobile_styles.rpy", 127], "get_platform_info_for_about": ["game/platform_detection_integration.rpy", 32], "show_platform_detection": ["game/platform_detection_integration.rpy", 48], "is_platform_secure": ["game/platform_detection_integration.rpy", 65], "platform_specific_setup": ["game/platform_detection_integration.rpy", 77], "update_platform_variables": ["game/platform_detection_integration.rpy", 137], "integrate_platform_with_anticheat": ["game/platform_detection_integration.rpy", 199], "integrate_platform_with_encryption": ["game/platform_detection_integration.rpy", 224], "unlock_platform_achievement": ["game/platform_detection_integration.rpy", 255], "save_to_platform_cloud": ["game/platform_detection_integration.rpy", 289], "open_project_linker_safe": ["game/project_linker_integration.rpy", 165], "load_project_linker": ["game/platform_detection_integration.rpy", 340], "open_project_linker": ["game/project_linker_integration.rpy", 15], "scan_project_files": ["game/project_linker_integration.rpy", 29], "open_full_project_linker": ["game/project_linker_integration.rpy", 152], "apply_safe_settings": ["game/safe_gaming_settings.rpy", 103], "reset_safe_settings": ["game/safe_gaming_settings.rpy", 125], "open_safe_gaming_settings": ["game/safe_gaming_settings.rpy", 147], "emergency_gaming_settings": ["game/safe_gaming_settings.rpy", 161], "sync_safe_settings_with_preferences": ["game/safe_gaming_settings.rpy", 171], "validate_system_startup": ["game/startup_validation.rpy", 6], "_m1_developer__ImageLocationPickerData.__init__": ["renpy/common/_developer/developer.rpym", 346], "_m1_developer__ImageLocationPickerData.set_position": ["renpy/common/_developer/developer.rpym", 351], "_m1_developer__ImageLocationPickerData.set_rectangle": ["renpy/common/_developer/developer.rpym", 355], "_m1_developer__ImageLocationPickerData.finish_rectangle": ["renpy/common/_developer/developer.rpym", 359], "_m1_developer__ImageLocationPickerData.get_text": ["renpy/common/_developer/developer.rpym", 375], "_m1_developer__missing_show_callback": ["renpy/common/_developer/developer.rpym", 309], "_m1_developer__missing_hide_callback": ["renpy/common/_developer/developer.rpym", 320], "_m1_developer__missing_scene_callback": ["renpy/common/_developer/developer.rpym", 326], "_m1_inspector__format_style": ["renpy/common/_developer/inspector.rpym", 164], "_m1_inspector__format_location": ["renpy/common/_developer/inspector.rpym", 167], "_m1_inspector__safe_repr": ["renpy/common/_developer/inspector.rpym", 179], "_m1_inspector__inspect": ["renpy/common/_developer/inspector.rpym", 189], "_inspector_repr": ["renpy/common/_developer/inspector.rpym", 198], "gui._scale": ["renpy/common/_errorhandling.rpym", 51], "gui.init": ["renpy/common/00gui.rpy", 32], "gui.variant": ["renpy/common/00gui.rpy", 84], "gui.rebuild": ["renpy/common/00gui.rpy", 111], "gui.preference": ["renpy/common/00gui.rpy", 142], "gui.SetPreference.__init__": ["renpy/common/00gui.rpy", 191], "gui.SetPreference.__call__": ["renpy/common/00gui.rpy", 196], "gui.SetPreference.get_selected": ["renpy/common/00gui.rpy", 204], "gui.TogglePreference.__init__": ["renpy/common/00gui.rpy", 227], "gui.TogglePreference.__call__": ["renpy/common/00gui.rpy", 233], "gui.TogglePreference.get_selected": ["renpy/common/00gui.rpy", 244], "gui.button_properties": ["renpy/common/00gui.rpy", 257], "gui.text_properties": ["renpy/common/00gui.rpy", 337], "gui.button_text_properties": ["renpy/common/00gui.rpy", 337], "gui._gui_images": ["renpy/common/00gui.rpy", 467], "_warper.linear": ["renpy/common/000atl.rpy", 40], "_warper.easeout": ["renpy/common/000atl.rpy", 44], "_warper.easein": ["renpy/common/000atl.rpy", 49], "_warper.ease": ["renpy/common/000atl.rpy", 54], "_warper.easeout_quad": ["renpy/common/000atl.rpy", 65], "_warper.easein_quad": ["renpy/common/000atl.rpy", 69], "_warper.ease_quad": ["renpy/common/000atl.rpy", 73], "_warper.easeout_cubic": ["renpy/common/000atl.rpy", 82], "_warper.easein_cubic": ["renpy/common/000atl.rpy", 86], "_warper.ease_cubic": ["renpy/common/000atl.rpy", 90], "_warper.easeout_quart": ["renpy/common/000atl.rpy", 99], "_warper.easein_quart": ["renpy/common/000atl.rpy", 103], "_warper.ease_quart": ["renpy/common/000atl.rpy", 107], "_warper.easeout_quint": ["renpy/common/000atl.rpy", 116], "_warper.easein_quint": ["renpy/common/000atl.rpy", 120], "_warper.ease_quint": ["renpy/common/000atl.rpy", 124], "_warper.easeout_expo": ["renpy/common/000atl.rpy", 133], "_warper.easein_expo": ["renpy/common/000atl.rpy", 137], "_warper.ease_expo": ["renpy/common/000atl.rpy", 141], "_warper.easeout_circ": ["renpy/common/000atl.rpy", 150], "_warper.easein_circ": ["renpy/common/000atl.rpy", 155], "_warper.ease_circ": ["renpy/common/000atl.rpy", 159], "_warper.easeout_back": ["renpy/common/000atl.rpy", 168], "_warper.easein_back": ["renpy/common/000atl.rpy", 173], "_warper.ease_back": ["renpy/common/000atl.rpy", 177], "_warper.easein_elastic": ["renpy/common/000atl.rpy", 186], "_warper.easeout_elastic": ["renpy/common/000atl.rpy", 192], "_warper.ease_elastic": ["renpy/common/000atl.rpy", 196], "_warper.easein_bounce": ["renpy/common/000atl.rpy", 205], "_warper.easeout_bounce": ["renpy/common/000atl.rpy", 218], "_warper.ease_bounce": ["renpy/common/000atl.rpy", 222], "achievement.Backend.register": ["renpy/common/00achievement.rpy", 39], "achievement.Backend.grant": ["renpy/common/00achievement.rpy", 44], "achievement.Backend.clear": ["renpy/common/00achievement.rpy", 50], "achievement.Backend.clear_all": ["renpy/common/00achievement.rpy", 55], "achievement.Backend.progress": ["renpy/common/00achievement.rpy", 60], "achievement.Backend.has": ["renpy/common/00achievement.rpy", 65], "achievement.PersistentBackend.__init__": ["renpy/common/00achievement.rpy", 77], "achievement.PersistentBackend.register": ["renpy/common/00achievement.rpy", 90], "achievement.PersistentBackend.grant": ["renpy/common/00achievement.rpy", 94], "achievement.PersistentBackend.clear": ["renpy/common/00achievement.rpy", 97], "achievement.PersistentBackend.clear_all": ["renpy/common/00achievement.rpy", 102], "achievement.PersistentBackend.has": ["renpy/common/00achievement.rpy", 106], "achievement.PersistentBackend.progress": ["renpy/common/00achievement.rpy", 109], "achievement.merge": ["renpy/common/00achievement.rpy", 126], "achievement.merge_progress": ["renpy/common/00achievement.rpy", 135], "achievement.register": ["renpy/common/00achievement.rpy", 160], "achievement.grant": ["renpy/common/00achievement.rpy", 193], "achievement.clear": ["renpy/common/00achievement.rpy", 209], "achievement.clear_all": ["renpy/common/00achievement.rpy", 223], "achievement.get_progress": ["renpy/common/00achievement.rpy", 233], "achievement.progress": ["renpy/common/00achievement.rpy", 248], "achievement.grant_progress": ["renpy/common/00achievement.rpy", 276], "achievement.has": ["renpy/common/00achievement.rpy", 279], "achievement.sync": ["renpy/common/00achievement.rpy", 293], "achievement.Sync.__call__": ["renpy/common/00achievement.rpy", 314], "achievement.Sync.get_sensitive": ["renpy/common/00achievement.rpy", 318], "achievement.SteamBackend.__init__": ["renpy/common/00steam.rpy", 859], "achievement.SteamBackend.register": ["renpy/common/00steam.rpy", 867], "achievement.SteamBackend.grant": ["renpy/common/00steam.rpy", 873], "achievement.SteamBackend.clear": ["renpy/common/00steam.rpy", 880], "achievement.SteamBackend.clear_all": ["renpy/common/00steam.rpy", 886], "achievement.SteamBackend.progress": ["renpy/common/00steam.rpy", 892], "achievement.SteamBackend.has": ["renpy/common/00steam.rpy", 923], "achievement.steam_preinit": ["renpy/common/00steam.rpy", 928], "achievement.steam_init": ["renpy/common/00steam.rpy", 959], "build.make_file_lists": ["renpy/common/00build.rpy", 33], "build.pattern_list": ["renpy/common/00build.rpy", 51], "build.classify_renpy": ["renpy/common/00build.rpy", 140], "build.classify": ["renpy/common/00build.rpy", 228], "build.clear": ["renpy/common/00build.rpy", 241], "build.remove": ["renpy/common/00build.rpy", 250], "build.archive": ["renpy/common/00build.rpy", 261], "build.documentation": ["renpy/common/00build.rpy", 292], "build.executable": ["renpy/common/00build.rpy", 312], "build.package": ["renpy/common/00build.rpy", 326], "build.dump": ["renpy/common/00build.rpy", 529], "_console.PrettyRepr._repr_bytes": ["renpy/common/00console.rpy", 144], "_console.PrettyRepr._repr_string": ["renpy/common/00console.rpy", 151], "_console.PrettyRepr.repr_bytes": ["renpy/common/00console.rpy", 144], "_console.PrettyRepr.repr_str": ["renpy/common/00console.rpy", 151], "_console.PrettyRepr.repr_tuple": ["renpy/common/00console.rpy", 165], "_console.PrettyRepr.repr_list": ["renpy/common/00console.rpy", 181], "_console.PrettyRepr.repr_RevertableList": ["renpy/common/00console.rpy", 181], "_console.PrettyRepr.repr_set": ["renpy/common/00console.rpy", 191], "_console.PrettyRepr.repr_RevertableSet": ["renpy/common/00console.rpy", 191], "_console.PrettyRepr.repr_frozenset": ["renpy/common/00console.rpy", 201], "_console.PrettyRepr.repr_dict": ["renpy/common/00console.rpy", 209], "_console.PrettyRepr.repr_RevertableDict": ["renpy/common/00console.rpy", 209], "_console.PrettyRepr.repr_defaultdict": ["renpy/common/00console.rpy", 220], "_console.PrettyRepr.repr_OrderedDict": ["renpy/common/00console.rpy", 233], "_console.PrettyRepr.repr_dict_keys": ["renpy/common/00console.rpy", 242], "_console.PrettyRepr.repr_dict_values": ["renpy/common/00console.rpy", 250], "_console.PrettyRepr.repr_dict_items": ["renpy/common/00console.rpy", 258], "_console.PrettyRepr.repr__PrettyDictItem": ["renpy/common/00console.rpy", 276], "_console.PrettyRepr._make_pretty_items": ["renpy/common/00console.rpy", 285], "_console.PrettyRepr._repr_iterable": ["renpy/common/00console.rpy", 299], "_console.PrettyRepr._to_shorted_list": ["renpy/common/00console.rpy", 334], "_console.PrettyRepr.repr_Matrix": ["renpy/common/00console.rpy", 363], "_console.BoundedList.__init__": ["renpy/common/00console.rpy", 411], "_console.BoundedList.append": ["renpy/common/00console.rpy", 415], "_console.BoundedList.clear": ["renpy/common/00console.rpy", 425], "_console.ConsoleHistoryEntry.__init__": ["renpy/common/00console.rpy", 435], "_console.ConsoleHistoryEntry.update_lines": ["renpy/common/00console.rpy", 440], "_console.HistoryEntry.__init__": ["renpy/common/00console.rpy", 435], "_console.HistoryEntry.update_lines": ["renpy/common/00console.rpy", 440], "_console._strip_ansi": ["renpy/common/00console.rpy", 461], "_console.stdout_line": ["renpy/common/00console.rpy", 477], "_console.stderr_line": ["renpy/common/00console.rpy", 486], "_console.ScriptErrorHandler.__init__": ["renpy/common/00console.rpy", 505], "_console.ScriptErrorHandler.__call__": ["renpy/common/00console.rpy", 508], "_console.DebugConsole.__init__": ["renpy/common/00console.rpy", 521], "_console.DebugConsole.backup": ["renpy/common/00console.rpy", 541], "_console.DebugConsole.start": ["renpy/common/00console.rpy", 546], "_console.DebugConsole.reset": ["renpy/common/00console.rpy", 564], "_console.DebugConsole.recall_line": ["renpy/common/00console.rpy", 571], "_console.DebugConsole.older": ["renpy/common/00console.rpy", 588], "_console.DebugConsole.newer": ["renpy/common/00console.rpy", 591], "_console.DebugConsole.interact": ["renpy/common/00console.rpy", 594], "_console.DebugConsole.show_stdio": ["renpy/common/00console.rpy", 651], "_console.DebugConsole.can_renpy": ["renpy/common/00console.rpy", 677], "_console.DebugConsole.format_exception_only": ["renpy/common/00console.rpy", 684], "_console.DebugConsole.format_exception": ["renpy/common/00console.rpy", 687], "_console.DebugConsole.run": ["renpy/common/00console.rpy", 690], "_console.enter": ["renpy/common/00console.rpy", 774], "_console.command": ["renpy/common/00console.rpy", 802], "_console.help": ["renpy/common/00console.rpy", 810], "_console.halp": ["renpy/common/00console.rpy", 859], "_console.clear": ["renpy/common/00console.rpy", 863], "_console.exit": ["renpy/common/00console.rpy", 867], "_console.quit": ["renpy/common/00console.rpy", 871], "_console.stack": ["renpy/common/00console.rpy", 875], "_console.load": ["renpy/common/00console.rpy", 897], "_console.save": ["renpy/common/00console.rpy", 910], "_console.reload": ["renpy/common/00console.rpy", 921], "_console.R": ["renpy/common/00console.rpy", 925], "_console.watch": ["renpy/common/00console.rpy", 929], "_console.renpy_watch": ["renpy/common/00console.rpy", 949], "_console.unwatch": ["renpy/common/00console.rpy", 966], "_console.watch_after_load": ["renpy/common/00console.rpy", 986], "_console.renpy_unwatch": ["renpy/common/00console.rpy", 995], "_console.unwatchall": ["renpy/common/00console.rpy", 1012], "_console.renpy_unwatchall": ["renpy/common/00console.rpy", 1021], "_console.jump": ["renpy/common/00console.rpy", 1033], "_console.short": ["renpy/common/00console.rpy", 1049], "_console.long": ["renpy/common/00console.rpy", 1053], "_console.escape": ["renpy/common/00console.rpy", 1057], "_console.unescape": ["renpy/common/00console.rpy", 1061], "director.audio_code_to_filename": ["renpy/common/00director.rpy", 100], "director.audio_filename_to_code": ["renpy/common/00director.rpy", 103], "director.audio_filename_to_display": ["renpy/common/00director.rpy", 106], "director.is_play": ["renpy/common/00director.rpy", 109], "director.is_queue": ["renpy/common/00director.rpy", 112], "director.is_stop": ["renpy/common/00director.rpy", 115], "director.is_voice": ["renpy/common/00director.rpy", 118], "director.is_interesting": ["renpy/common/00director.rpy", 122], "director.interact_base": ["renpy/common/00director.rpy", 208], "director.after_load": ["renpy/common/00director.rpy", 286], "director.interact": ["renpy/common/00director.rpy", 291], "director.line_log_callback": ["renpy/common/00director.rpy", 307], "director.init": ["renpy/common/00director.rpy", 314], "director.command": ["renpy/common/00director.rpy", 329], "director.get_scene_show_hide_statement": ["renpy/common/00director.rpy", 343], "director.quote_audio": ["renpy/common/00director.rpy", 382], "director.get_play_queue_statement": ["renpy/common/00director.rpy", 398], "director.get_stop_statement": ["renpy/common/00director.rpy", 407], "director.get_voice_statement": ["renpy/common/00director.rpy", 413], "director.get_statement": ["renpy/common/00director.rpy", 419], "director.update_ast": ["renpy/common/00director.rpy", 445], "director.dump_script": ["renpy/common/00director.rpy", 471], "director.pick_tag": ["renpy/common/00director.rpy", 477], "director.find_statement": ["renpy/common/00director.rpy", 498], "director.needs_space": ["renpy/common/00director.rpy", 518], "director.is_spacing": ["renpy/common/00director.rpy", 553], "director.adjust_spacing_before": ["renpy/common/00director.rpy", 563], "director.add_spacing": ["renpy/common/00director.rpy", 604], "director.remove_spacing": ["renpy/common/00director.rpy", 608], "director.get_tags": ["renpy/common/00director.rpy", 614], "director.get_attributes": ["renpy/common/00director.rpy", 634], "director.get_transforms": ["renpy/common/00director.rpy", 644], "director.get_image_attributes": ["renpy/common/00director.rpy", 651], "director.get_ordered_attributes": ["renpy/common/00director.rpy", 662], "director.get_behind_tags": ["renpy/common/00director.rpy", 676], "director.Start.__call__": ["renpy/common/00director.rpy", 701], "director.Start.get_sensitive": ["renpy/common/00director.rpy", 735], "director.Stop.__call__": ["renpy/common/00director.rpy", 743], "director.AddStatement.__init__": ["renpy/common/00director.rpy", 757], "director.AddStatement.get_sensitive": ["renpy/common/00director.rpy", 762], "director.AddStatement.__call__": ["renpy/common/00director.rpy", 765], "director.is_scene_show_hide_editable": ["renpy/common/00director.rpy", 801], "director.ChangeStatement.__init__": ["renpy/common/00director.rpy", 836], "director.ChangeStatement.get_sensitive": ["renpy/common/00director.rpy", 948], "director.ChangeStatement.__call__": ["renpy/common/00director.rpy", 951], "director.SetKind.__init__": ["renpy/common/00director.rpy", 1004], "director.SetKind.__call__": ["renpy/common/00director.rpy", 1007], "director.SetTag.__init__": ["renpy/common/00director.rpy", 1036], "director.SetTag.__call__": ["renpy/common/00director.rpy", 1039], "director.SetTag.get_selected": ["renpy/common/00director.rpy", 1051], "director.ToggleAttribute.__init__": ["renpy/common/00director.rpy", 1064], "director.ToggleAttribute.__call__": ["renpy/common/00director.rpy", 1067], "director.ToggleAttribute.get_selected": ["renpy/common/00director.rpy", 1086], "director.ToggleNegativeAttribute.__init__": ["renpy/common/00director.rpy", 1095], "director.ToggleNegativeAttribute.__call__": ["renpy/common/00director.rpy", 1100], "director.SetList.__init__": ["renpy/common/00director.rpy", 1118], "director.SetList.__call__": ["renpy/common/00director.rpy", 1122], "director.SetList.get_selected": ["renpy/common/00director.rpy", 1130], "director.ToggleList.__init__": ["renpy/common/00director.rpy", 1140], "director.ToggleList.__call__": ["renpy/common/00director.rpy", 1144], "director.ToggleList.get_selected": ["renpy/common/00director.rpy", 1152], "director.SetTransition.__init__": ["renpy/common/00director.rpy", 1161], "director.SetTransition.__call__": ["renpy/common/00director.rpy", 1164], "director.SetTransition.get_selected": ["renpy/common/00director.rpy", 1169], "director.SetChannel.__init__": ["renpy/common/00director.rpy", 1178], "director.SetChannel.__call__": ["renpy/common/00director.rpy", 1181], "director.SetChannel.get_selected": ["renpy/common/00director.rpy", 1189], "director.SetAudio.__init__": ["renpy/common/00director.rpy", 1198], "director.SetAudio.__call__": ["renpy/common/00director.rpy", 1201], "director.SetAudio.get_selected": ["renpy/common/00director.rpy", 1206], "director.Commit.__call__": ["renpy/common/00director.rpy", 1215], "director.Commit.get_sensitive": ["renpy/common/00director.rpy", 1237], "director.Reset.__call__": ["renpy/common/00director.rpy", 1246], "director.Cancel.__call__": ["renpy/common/00director.rpy", 1266], "director.Remove.__call__": ["renpy/common/00director.rpy", 1287], "director.SemiModal.__init__": ["renpy/common/00director.rpy", 1307], "director.SemiModal.per_interact": ["renpy/common/00director.rpy", 1314], "director.SemiModal.render": ["renpy/common/00director.rpy", 1317], "director.SemiModal.event": ["renpy/common/00director.rpy", 1333], "director.SemiModal.get_placement": ["renpy/common/00director.rpy", 1357], "director.SemiModal.visit": ["renpy/common/00director.rpy", 1360], "director.component_key": ["renpy/common/00director.rpy", 1367], "_gamepad.EventWatcher.__init__": ["renpy/common/00gamepad.rpy", 88], "_gamepad.EventWatcher.render": ["renpy/common/00gamepad.rpy", 93], "_gamepad.EventWatcher.event": ["renpy/common/00gamepad.rpy", 96], "_gamepad.calibrate": ["renpy/common/00gamepad.rpy", 120], "iap.Product.__init__": ["renpy/common/00iap.rpy", 36], "iap.NoneBackend.get_store_name": ["renpy/common/00iap.rpy", 54], "iap.NoneBackend.purchase": ["renpy/common/00iap.rpy", 62], "iap.NoneBackend.restore_purchases": ["renpy/common/00iap.rpy", 70], "iap.NoneBackend.has_purchased": ["renpy/common/00iap.rpy", 79], "iap.NoneBackend.consume": ["renpy/common/00iap.rpy", 86], "iap.NoneBackend.is_deferred": ["renpy/common/00iap.rpy", 94], "iap.NoneBackend.get_price": ["renpy/common/00iap.rpy", 101], "iap.NoneBackend.init": ["renpy/common/00iap.rpy", 109], "iap.NoneBackend.request_review": ["renpy/common/00iap.rpy", 116], "iap.AndroidBackend.__init__": ["renpy/common/00iap.rpy", 128], "iap.AndroidBackend.get_store_name": ["renpy/common/00iap.rpy", 139], "iap.AndroidBackend.identifier": ["renpy/common/00iap.rpy", 142], "iap.AndroidBackend.wait_for_result": ["renpy/common/00iap.rpy", 152], "iap.AndroidBackend.purchase": ["renpy/common/00iap.rpy", 167], "iap.AndroidBackend.restore_purchases": ["renpy/common/00iap.rpy", 172], "iap.AndroidBackend.has_purchased": ["renpy/common/00iap.rpy", 179], "iap.AndroidBackend.consume": ["renpy/common/00iap.rpy", 183], "iap.AndroidBackend.is_deferred": ["renpy/common/00iap.rpy", 186], "iap.AndroidBackend.get_price": ["renpy/common/00iap.rpy", 189], "iap.AndroidBackend.request_review": ["renpy/common/00iap.rpy", 193], "iap.AndroidBackend.init": ["renpy/common/00iap.rpy", 196], "iap.IOSBackend.__init__": ["renpy/common/00iap.rpy", 208], "iap.IOSBackend.get_store_name": ["renpy/common/00iap.rpy", 220], "iap.IOSBackend.set_title": ["renpy/common/00iap.rpy", 230], "iap.IOSBackend.identifier": ["renpy/common/00iap.rpy", 233], "iap.IOSBackend.wait_for_result": ["renpy/common/00iap.rpy", 240], "iap.IOSBackend.validate_products": ["renpy/common/00iap.rpy", 257], "iap.IOSBackend.purchase": ["renpy/common/00iap.rpy", 266], "iap.IOSBackend.restore_purchases": ["renpy/common/00iap.rpy", 274], "iap.IOSBackend.has_purchased": ["renpy/common/00iap.rpy", 281], "iap.IOSBackend.consume": ["renpy/common/00iap.rpy", 285], "iap.IOSBackend.is_deferred": ["renpy/common/00iap.rpy", 289], "iap.IOSBackend.get_price": ["renpy/common/00iap.rpy", 293], "iap.IOSBackend.request_review": ["renpy/common/00iap.rpy", 308], "iap.IOSBackend.init": ["renpy/common/00iap.rpy", 312], "iap.register": ["renpy/common/00iap.rpy", 322], "iap.with_background": ["renpy/common/00iap.rpy", 368], "iap.restore": ["renpy/common/00iap.rpy", 381], "iap.Restore.__call__": ["renpy/common/00iap.rpy", 404], "iap.Restore.get_sensitive": ["renpy/common/00iap.rpy", 407], "iap.get_product": ["renpy/common/00iap.rpy", 410], "iap.purchase": ["renpy/common/00iap.rpy", 417], "iap.Purchase.__init__": ["renpy/common/00iap.rpy", 463], "iap.Purchase.__call__": ["renpy/common/00iap.rpy", 468], "iap.Purchase.should_be_sensitive": ["renpy/common/00iap.rpy", 476], "iap.Purchase.get_sensitive": ["renpy/common/00iap.rpy", 489], "iap.Purchase.periodic": ["renpy/common/00iap.rpy", 493], "iap.has_purchased": ["renpy/common/00iap.rpy", 499], "iap.is_deferred": ["renpy/common/00iap.rpy", 518], "iap.get_price": ["renpy/common/00iap.rpy", 533], "iap.get_store_name": ["renpy/common/00iap.rpy", 549], "iap.request_review": ["renpy/common/00iap.rpy", 560], "iap.missing_products": ["renpy/common/00iap.rpy", 575], "iap.init_android": ["renpy/common/00iap.rpy", 586], "iap.init": ["renpy/common/00iap.rpy", 602], "icon.Icon.__init__": ["renpy/common/00icon.rpy", 31], "icon.Icon.find_image": ["renpy/common/00icon.rpy", 36], "icon.Icon.render": ["renpy/common/00icon.rpy", 57], "icon.Icon.visit": ["renpy/common/00icon.rpy", 78], "layeredimage.format_function": ["renpy/common/00layeredimage_ren.py", 47], "layeredimage.resolve_image": ["renpy/common/00layeredimage_ren.py", 120], "layeredimage.resolve_at": ["renpy/common/00layeredimage_ren.py", 128], "layeredimage.IfAttr.__init__": ["renpy/common/00layeredimage_ren.py", 145], "layeredimage.IfAttr.check": ["renpy/common/00layeredimage_ren.py", 148], "layeredimage.IfOr.__init__": ["renpy/common/00layeredimage_ren.py", 161], "layeredimage.IfOr.check": ["renpy/common/00layeredimage_ren.py", 165], "layeredimage.IfAnd.__init__": ["renpy/common/00layeredimage_ren.py", 178], "layeredimage.IfAnd.check": ["renpy/common/00layeredimage_ren.py", 182], "layeredimage.IfNot.__init__": ["renpy/common/00layeredimage_ren.py", 195], "layeredimage.IfNot.check": ["renpy/common/00layeredimage_ren.py", 198], "layeredimage.IfAttribute.__init__": ["renpy/common/00layeredimage_ren.py", 211], "layeredimage.IfAttribute.check": ["renpy/common/00layeredimage_ren.py", 214], "layeredimage.Layer.__init__": ["renpy/common/00layeredimage_ren.py", 235], "layeredimage.Layer.check": ["renpy/common/00layeredimage_ren.py", 248], "layeredimage.Layer.wrap": ["renpy/common/00layeredimage_ren.py", 270], "layeredimage.Layer.apply_format": ["renpy/common/00layeredimage_ren.py", 288], "layeredimage.Layer.get_displayable": ["renpy/common/00layeredimage_ren.py", 295], "layeredimage.Attribute.__init__": ["renpy/common/00layeredimage_ren.py", 353], "layeredimage.Attribute.apply_format": ["renpy/common/00layeredimage_ren.py", 367], "layeredimage.Attribute.get_displayable": ["renpy/common/00layeredimage_ren.py", 376], "layeredimage.Condition.__init__": ["renpy/common/00layeredimage_ren.py", 423], "layeredimage.Condition.apply_format": ["renpy/common/00layeredimage_ren.py", 429], "layeredimage.Condition.get_displayable": ["renpy/common/00layeredimage_ren.py", 435], "layeredimage.ConditionGroup.__init__": ["renpy/common/00layeredimage_ren.py", 457], "layeredimage.ConditionGroup.apply_format": ["renpy/common/00layeredimage_ren.py", 462], "layeredimage.ConditionGroup.get_displayable": ["renpy/common/00layeredimage_ren.py", 466], "layeredimage.Always.__init__": ["renpy/common/00layeredimage_ren.py", 505], "layeredimage.Always.apply_format": ["renpy/common/00layeredimage_ren.py", 510], "layeredimage.Always.get_displayable": ["renpy/common/00layeredimage_ren.py", 516], "layeredimage.LayeredImage.__init__": ["renpy/common/00layeredimage_ren.py", 592], "layeredimage.LayeredImage.format": ["renpy/common/00layeredimage_ren.py", 616], "layeredimage.LayeredImage.add": ["renpy/common/00layeredimage_ren.py", 627], "layeredimage.LayeredImage.get_banned": ["renpy/common/00layeredimage_ren.py", 652], "layeredimage.LayeredImage._duplicate": ["renpy/common/00layeredimage_ren.py", 667], "layeredimage.LayeredImage._list_attributes": ["renpy/common/00layeredimage_ren.py", 737], "layeredimage.LayeredImage._choose_attributes": ["renpy/common/00layeredimage_ren.py", 765], "layeredimage.parse_property": ["renpy/common/00layeredimage_ren.py", 789], "layeredimage.parse_displayable": ["renpy/common/00layeredimage_ren.py", 838], "layeredimage.RawAttribute.after_upgrade": ["renpy/common/00layeredimage_ren.py", 857], "layeredimage.RawAttribute.__init__": ["renpy/common/00layeredimage_ren.py", 862], "layeredimage.RawAttribute.execute": ["renpy/common/00layeredimage_ren.py", 868], "layeredimage.parse_attribute": ["renpy/common/00layeredimage_ren.py", 882], "layeredimage.RawAttributeGroup.after_upgrade": ["renpy/common/00layeredimage_ren.py", 954], "layeredimage.RawAttributeGroup.__init__": ["renpy/common/00layeredimage_ren.py", 961], "layeredimage.RawAttributeGroup.execute": ["renpy/common/00layeredimage_ren.py", 968], "layeredimage.parse_group": ["renpy/common/00layeredimage_ren.py", 1006], "layeredimage.RawCondition.after_upgrade": ["renpy/common/00layeredimage_ren.py", 1082], "layeredimage.RawCondition.__init__": ["renpy/common/00layeredimage_ren.py", 1087], "layeredimage.RawCondition.execute": ["renpy/common/00layeredimage_ren.py", 1093], "layeredimage.parse_condition": ["renpy/common/00layeredimage_ren.py", 1097], "layeredimage.RawConditionGroup.__init__": ["renpy/common/00layeredimage_ren.py", 1157], "layeredimage.RawConditionGroup.execute": ["renpy/common/00layeredimage_ren.py", 1160], "layeredimage.parse_conditions": ["renpy/common/00layeredimage_ren.py", 1167], "layeredimage.RawAlways.after_upgrade": ["renpy/common/00layeredimage_ren.py", 1188], "layeredimage.RawAlways.__init__": ["renpy/common/00layeredimage_ren.py", 1193], "layeredimage.RawAlways.execute": ["renpy/common/00layeredimage_ren.py", 1198], "layeredimage.parse_always": ["renpy/common/00layeredimage_ren.py", 1203], "layeredimage.RawLayeredImage.after_upgrade": ["renpy/common/00layeredimage_ren.py", 1273], "layeredimage.RawLayeredImage.__init__": ["renpy/common/00layeredimage_ren.py", 1278], "layeredimage.RawLayeredImage.execute": ["renpy/common/00layeredimage_ren.py", 1284], "layeredimage.execute_layeredimage": ["renpy/common/00layeredimage_ren.py", 1296], "layeredimage.parse_layeredimage": ["renpy/common/00layeredimage_ren.py", 1299], "layeredimage.lint_layeredimage": ["renpy/common/00layeredimage_ren.py", 1347], "layeredimage.LayeredImageProxy.__init__": ["renpy/common/00layeredimage_ren.py", 1406], "layeredimage.LayeredImageProxy._duplicate": ["renpy/common/00layeredimage_ren.py", 1435], "layeredimage.LayeredImageProxy.filter_attributes": ["renpy/common/00layeredimage_ren.py", 1444], "layeredimage.LayeredImageProxy._choose_attributes": ["renpy/common/00layeredimage_ren.py", 1458], "layeredimage.LayeredImageProxy._list_attributes": ["renpy/common/00layeredimage_ren.py", 1461], "bubble.ToggleShown.__call__": ["renpy/common/00speechbubble.rpy", 93], "bubble.ToggleShown.get_selected": ["renpy/common/00speechbubble.rpy", 101], "bubble.scene_callback": ["renpy/common/00speechbubble.rpy", 104], "bubble.character_callback": ["renpy/common/00speechbubble.rpy", 111], "bubble.BubbleCharacter.__init__": ["renpy/common/00speechbubble.rpy", 120], "bubble.BubbleCharacter.bubble_default_properties": ["renpy/common/00speechbubble.rpy", 151], "bubble.BubbleCharacter.expand_area": ["renpy/common/00speechbubble.rpy", 185], "bubble.BubbleCharacter.do_add": ["renpy/common/00speechbubble.rpy", 207], "bubble.BubbleCharacter.do_show": ["renpy/common/00speechbubble.rpy", 216], "bubble.CycleBubbleProperty.__init__": ["renpy/common/00speechbubble.rpy", 256], "bubble.CycleBubbleProperty.get_selected": ["renpy/common/00speechbubble.rpy", 260], "bubble.CycleBubbleProperty.__call__": ["renpy/common/00speechbubble.rpy", 263], "bubble.CycleBubbleProperty.alternate": ["renpy/common/00speechbubble.rpy", 282], "bubble.ToggleClearRetain.__init__": ["renpy/common/00speechbubble.rpy", 293], "bubble.ToggleClearRetain.get_selected": ["renpy/common/00speechbubble.rpy", 296], "bubble.ToggleClearRetain.__call__": ["renpy/common/00speechbubble.rpy", 299], "bubble.ToggleClearRetain.alternate": ["renpy/common/00speechbubble.rpy", 303], "bubble.SetWindowArea.__init__": ["renpy/common/00speechbubble.rpy", 312], "bubble.SetWindowArea.__call__": ["renpy/common/00speechbubble.rpy", 316], "bubble.SetWindowArea.get_selected": ["renpy/common/00speechbubble.rpy", 320], "bubble.SetWindowArea.finished": ["renpy/common/00speechbubble.rpy", 323], "bubble.SetWindowArea.alternate": ["renpy/common/00speechbubble.rpy", 328], "bubble.GetCurrentDialogue": ["renpy/common/00speechbubble.rpy", 333], "bubble.statement_callback": ["renpy/common/00speechbubble.rpy", 370], "_renpysteam.retrieve_stats": ["renpy/common/00steam.rpy", 42], "_renpysteam.store_stats": ["renpy/common/00steam.rpy", 56], "_renpysteam.list_achievements": ["renpy/common/00steam.rpy", 66], "_renpysteam.get_achievement": ["renpy/common/00steam.rpy", 83], "_renpysteam.grant_achievement": ["renpy/common/00steam.rpy", 102], "_renpysteam.clear_achievement": ["renpy/common/00steam.rpy", 113], "_renpysteam.indicate_achievement_progress": ["renpy/common/00steam.rpy", 124], "_renpysteam.get_float_stat": ["renpy/common/00steam.rpy", 135], "_renpysteam.set_float_stat": ["renpy/common/00steam.rpy", 153], "_renpysteam.get_int_stat": ["renpy/common/00steam.rpy", 165], "_renpysteam.set_int_stat": ["renpy/common/00steam.rpy", 182], "_renpysteam.is_subscribed_app": ["renpy/common/00steam.rpy", 196], "_renpysteam.get_current_game_language": ["renpy/common/00steam.rpy", 206], "_renpysteam.get_steam_ui_language": ["renpy/common/00steam.rpy", 216], "_renpysteam.get_current_beta_name": ["renpy/common/00steam.rpy", 226], "_renpysteam.dlc_installed": ["renpy/common/00steam.rpy", 243], "_renpysteam.install_dlc": ["renpy/common/00steam.rpy", 253], "_renpysteam.uninstall_dlc": ["renpy/common/00steam.rpy", 263], "_renpysteam.dlc_progress": ["renpy/common/00steam.rpy", 273], "_renpysteam.get_app_build_id": ["renpy/common/00steam.rpy", 292], "_renpysteam.is_overlay_enabled": ["renpy/common/00steam.rpy", 304], "_renpysteam.overlay_needs_present": ["renpy/common/00steam.rpy", 317], "_renpysteam.set_overlay_notification_position": ["renpy/common/00steam.rpy", 339], "_renpysteam.activate_overlay": ["renpy/common/00steam.rpy", 351], "_renpysteam.activate_overlay_to_web_page": ["renpy/common/00steam.rpy", 365], "_renpysteam.activate_overlay_to_store": ["renpy/common/00steam.rpy", 374], "_renpysteam.get_persona_name": ["renpy/common/00steam.rpy", 394], "_renpysteam.get_csteam_id": ["renpy/common/00steam.rpy", 404], "_renpysteam.get_account_id": ["renpy/common/00steam.rpy", 417], "_renpysteam.get_session_ticket": ["renpy/common/00steam.rpy", 427], "_renpysteam.cancel_ticket": ["renpy/common/00steam.rpy", 454], "_renpysteam.get_game_badge_level": ["renpy/common/00steam.rpy", 470], "_renpysteam.get_subscribed_items": ["renpy/common/00steam.rpy", 482], "_renpysteam.get_subscribed_item_path": ["renpy/common/00steam.rpy", 505], "_renpysteam.set_timeline_state_description": ["renpy/common/00steam.rpy", 529], "_renpysteam.clear_timeline_state_description": ["renpy/common/00steam.rpy", 544], "_renpysteam.add_timeline_event": ["renpy/common/00steam.rpy", 553], "_renpysteam.set_timeline_game_mode": ["renpy/common/00steam.rpy", 596], "_renpysteam.import_api": ["renpy/common/00steam.rpy", 613], "_renpysteam.periodic": ["renpy/common/00steam.rpy", 656], "_renpysteam.prime_keyboard": ["renpy/common/00steam.rpy", 716], "_renpysteam.keyboard_periodic": ["renpy/common/00steam.rpy", 722], "_renpysteam.keyboard_dismissed": ["renpy/common/00steam.rpy", 778], "_renpysteam._KeyboardShift.__init__": ["renpy/common/00steam.rpy", 804], "_renpysteam._KeyboardShift.render": ["renpy/common/00steam.rpy", 810], "_sync.check_sync_id": ["renpy/common/00sync.rpy", 95], "_sync.get_sync_id": ["renpy/common/00sync.rpy", 112], "_sync.hash_game": ["renpy/common/00sync.rpy", 138], "_sync.key_and_hash": ["renpy/common/00sync.rpy", 152], "_sync.verbose_error": ["renpy/common/00sync.rpy", 174], "_sync.upload_content": ["renpy/common/00sync.rpy", 190], "_sync.download_content": ["renpy/common/00sync.rpy", 198], "_sync.report_error": ["renpy/common/00sync.rpy", 208], "_sync.upload": ["renpy/common/00sync.rpy", 211], "_sync.download": ["renpy/common/00sync.rpy", 298], "textshader.adjust_duration": ["renpy/common/00textshader_ren.py", 34], "updater.urlopen": ["renpy/common/00updater.rpy", 45], "updater.urlretrieve": ["renpy/common/00updater.rpy", 49], "updater.process_deferred_line": ["renpy/common/00updater.rpy", 82], "updater.process_deferred": ["renpy/common/00updater.rpy", 106], "updater.process_deleted": ["renpy/common/00updater.rpy", 148], "updater.zsync_path": ["renpy/common/00updater.rpy", 161], "updater.Updater.__init__": ["renpy/common/00updater.rpy", 258], "updater.Updater.run": ["renpy/common/00updater.rpy", 393], "updater.Updater.update": ["renpy/common/00updater.rpy", 441], "updater.Updater.prompt_confirm": ["renpy/common/00updater.rpy", 507], "updater.Updater.fetch_files_rpu": ["renpy/common/00updater.rpy", 538], "updater.Updater.rpu_copy_fields": ["renpy/common/00updater.rpy", 563], "updater.Updater.rpu_progress": ["renpy/common/00updater.rpy", 577], "updater.Updater.rpu_update": ["renpy/common/00updater.rpy", 592], "updater.Updater.zsync_update": ["renpy/common/00updater.rpy", 685], "updater.Updater.simulation": ["renpy/common/00updater.rpy", 745], "updater.Updater.periodic": ["renpy/common/00updater.rpy", 828], "updater.Updater.proceed": ["renpy/common/00updater.rpy", 839], "updater.Updater.cancel": ["renpy/common/00updater.rpy", 864], "updater.Updater.unlink": ["renpy/common/00updater.rpy", 878], "updater.Updater.rename": ["renpy/common/00updater.rpy", 901], "updater.Updater.path": ["renpy/common/00updater.rpy", 921], "updater.Updater.load_state": ["renpy/common/00updater.rpy", 940], "updater.Updater.test_write": ["renpy/common/00updater.rpy", 957], "updater.Updater.check_updates": ["renpy/common/00updater.rpy", 971], "updater.Updater.add_dlc_state": ["renpy/common/00updater.rpy", 1066], "updater.Updater.check_versions": ["renpy/common/00updater.rpy", 1090], "updater.Updater.update_filename": ["renpy/common/00updater.rpy", 1129], "updater.Updater.prepare": ["renpy/common/00updater.rpy", 1140], "updater.Updater.split_inputs": ["renpy/common/00updater.rpy", 1200], "updater.Updater.download": ["renpy/common/00updater.rpy", 1236], "updater.Updater.download_direct": ["renpy/common/00updater.rpy", 1405], "updater.Updater.unpack": ["renpy/common/00updater.rpy", 1501], "updater.Updater.move_files": ["renpy/common/00updater.rpy", 1572], "updater.Updater.delete_obsolete": ["renpy/common/00updater.rpy", 1594], "updater.Updater.save_state": ["renpy/common/00updater.rpy", 1639], "updater.Updater.clean": ["renpy/common/00updater.rpy", 1649], "updater.Updater.clean_old": ["renpy/common/00updater.rpy", 1661], "updater.Updater.clean_new": ["renpy/common/00updater.rpy", 1665], "updater.get_installed_state": ["renpy/common/00updater.rpy", 1672], "updater.get_installed_packages": ["renpy/common/00updater.rpy", 1702], "updater.can_update": ["renpy/common/00updater.rpy", 1722], "updater.update": ["renpy/common/00updater.rpy", 1745], "updater.Update.__init__": ["renpy/common/00updater.rpy", 1854], "updater.Update.__call__": ["renpy/common/00updater.rpy", 1858], "updater.UpdateVersion": ["renpy/common/00updater.rpy", 1865], "updater.update_command": ["renpy/common/00updater.rpy", 1907], "updater.start_game_download": ["renpy/common/00updater.rpy", 1956], "updater.continue_game_download": ["renpy/common/00updater.rpy", 1986]}}, "build": {"directory_name": "netcode_protogen_game-**********.123v", "executable_name": "netcode_protogen_game", "include_update": false, "packages": [{"name": "gameonly", "formats": ["null"], "file_lists": ["all"], "description": "Game-Only Update for Mobile", "update": true, "dlc": false, "hidden": true}, {"name": "pc", "formats": ["zip"], "file_lists": ["windows", "linux", "renpy", "all"], "description": "PC: Windows and Linux", "update": true, "dlc": false, "hidden": false}, {"name": "linux", "formats": ["tar.bz2"], "file_lists": ["linux", "linux_arm", "renpy", "all"], "description": "Linux", "update": true, "dlc": false, "hidden": false}, {"name": "mac", "formats": ["app-zip", "app-dmg"], "file_lists": ["mac", "renpy", "all"], "description": "Macintosh", "update": true, "dlc": false, "hidden": false}, {"name": "win", "formats": ["zip"], "file_lists": ["windows", "renpy", "all"], "description": "Windows", "update": true, "dlc": false, "hidden": false}, {"name": "market", "formats": ["bare-zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "Windows, Mac, Linux for Markets", "update": true, "dlc": false, "hidden": false}, {"name": "steam", "formats": ["zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "steam", "update": true, "dlc": false, "hidden": true}, {"name": "android", "formats": ["directory"], "file_lists": ["android", "all"], "description": "android", "update": false, "dlc": true, "hidden": true}, {"name": "ios", "formats": ["directory"], "file_lists": ["ios", "all"], "description": "ios", "update": false, "dlc": true, "hidden": true}, {"name": "web", "formats": ["zip"], "file_lists": ["web", "renpy", "all"], "description": "web", "update": false, "dlc": true, "hidden": true}], "archives": [["archive", ["all"]]], "documentation_patterns": ["*.html", "*.txt"], "base_patterns": [["*.py", null], ["*.sh", null], ["*.app/", null], ["*.dll", null], ["*.manifest", null], ["*.keystore", null], ["**.rpe.py", null], ["update.pem", null], ["lib/", null], ["renpy/", null], ["update/", null], ["common/", null], ["update/", null], ["old-game/", null], ["base/", null], ["icon.ico", null], ["icon.icns", null], ["project.json", null], ["log.txt", null], ["errors.txt", null], ["traceback.txt", null], ["image_cache.txt", null], ["text_overflow.txt", null], ["dialogue.txt", null], ["dialogue.tab", null], ["profile_screen.txt", null], ["files.txt", null], ["memory.txt", null], ["tmp/", null], ["game/saves/", null], ["game/bytecode.rpyb", null], ["archived/", null], ["launcherinfo.py", null], ["android.txt", null], ["game/presplash*.*", ["all"]], ["android.json", ["android"]], [".android.json", ["android"]], ["android-*.png", ["android"]], ["android-*.jpg", ["android"]], ["ouya_icon.png", null], ["ios-presplash.*", ["ios"]], ["ios-launchimage.png", null], ["ios-icon.png", null], ["web-presplash.png", ["web"]], ["web-presplash.jpg", ["web"]], ["web-presplash.webp", ["web"]], ["web-icon.png", ["web"]], ["progressive_download.txt", ["web"]], ["steam_appid.txt", null], ["game/cache/bytecode-312.rpyb", ["all"]], ["game/cache/bytecode-*.rpyb", null], ["game/cache/build_info.json", null], ["game/cache/build_time.txt", null], ["**~", null], ["**.bak", null], ["**/.**", null], ["**/#**", null], ["**/thumbs.db", null], ["anti-cheat/**.rpy", ["all"]], ["anti-cheat/**.rpyc", ["all"]], ["game/game_router.rpy", ["all"]], ["game/game_router.rpyc", ["all"]], ["game/**.rpy", ["archive"]], ["anti-cheat/**.rpy", ["archive"]], [".*", null], ["**", ["all"]]], "renpy_patterns": [["renpy/common/_compat/**", null], ["renpy/common/_roundrect/**", null], ["renpy/common/_outline/**", null], ["renpy/common/_theme**", null], ["renpy/**__pycache__/**.cpython-312.pyc", ["all"]], ["renpy/**__pycache__", ["all"]], ["**~", null], ["**/#*", null], ["**/.*", null], ["**.old", null], ["**.new", null], ["**.rpa", null], ["**.rpe", null], ["**.rpe.py", null], ["**/steam_appid.txt", null], ["renpy.py", ["all"]], ["renpy/", ["all"]], ["renpy/**.py", ["renpy"]], ["renpy/**.pxd", null], ["renpy/**.pxi", null], ["renpy/**.pyx", null], ["renpy/**.pyc", null], ["renpy/**.pyo", null], ["renpy/common/", ["all"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**.rpy", ["renpy"]], ["renpy/common/**.rpym", ["renpy"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**", ["all"]], ["renpy/**", ["all"]], ["lib/*/renpy", null], ["lib/*/renpy.exe", null], ["lib/*/pythonw.exe", null], ["lib/py2-*/", null], ["lib/py*-windows-i686/**", ["windows_i686"]], ["lib/py*-windows-x86_64/**", ["windows"]], ["lib/py*-linux-i686/**", ["linux_i686"]], ["lib/py*-linux-aarch64/**", ["linux_arm"]], ["lib/py*-linux-armv7l/**", ["linux_arm"]], ["lib/py*-linux-*/**", ["linux"]], ["lib/py*-mac-*/**", ["mac"]], ["lib/python2.*/**", null], ["lib/**", ["windows", "linux", "mac", "android", "ios"]], ["renpy.sh", ["linux", "mac"]]], "xbit_patterns": ["**.sh", "lib/py*-linux-*/*", "lib/py*-mac-*/*", "**.app/Contents/MacOS/*"], "version": "**********.123v", "display_name": "netcode the protogen and more", "exclude_empty_directories": true, "allow_integrated_gpu": true, "renpy": false, "script_version": true, "destination": "netcode_protogen_game-**********.123v-dists", "itch_channels": {"*-all.zip": "win-osx-linux", "*-market.zip": "win-osx-linux", "*-pc.zip": "win-linux", "*-win.zip": "win", "*-mac.zip": "osx", "*-linux.tar.bz2": "linux", "*-release.apk": "android"}, "mac_info_plist": {}, "merge": [["linux_i686", "linux"], ["windows_i686", "windows"]], "include_i686": true, "change_icon_i686": true, "android_permissions": [], "_sdk_fonts": false, "update_formats": ["rpu"], "info": {"info": {}, "time": 1751979227.681415, "name": "netcode the protogen and more", "version": "**********.123v"}}}