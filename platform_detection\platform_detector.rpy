## Platform Detection System
## Detects game distribution platforms and handles their requirements

init -100 python:
    import platform
    import os
    import subprocess
    import json
    import hashlib
    import time
    import re
    from datetime import datetime

    # Platform detection state
    platform_state = {
        'detected_platform': 'unknown',
        'platform_verified': False,
        'distribution_source': 'unknown',
        'launcher_detected': False,
        'platform_features': {},
        'compliance_status': {},
        'detection_timestamp': None,
        'platform_data': {}
    }

    # Platform detection configuration
    PLATFORM_CONFIG = {
        'enabled': True,
        'strict_mode': True,
        'auto_detect': True,
        'create_source_file': True,
        'validate_platform': True,
        'enforce_terms': True,
        'report_violations': True,
        'detection_timeout': 30,
        'cache_results': True
    }

    class PlatformDetector:
        def __init__(self):
            self.detected_platforms = []
            self.primary_platform = None
            self.platform_processes = {}
            self.platform_apis = {}
            self.detection_cache = {}
            
        def detect_all_platforms(self):
            """Detect all available platforms"""
            try:
                renpy.log("Starting comprehensive platform detection...")
                
                # Clear previous detection
                self.detected_platforms.clear()
                platform_state['detection_timestamp'] = datetime.now().isoformat()
                
                # Detect PC platforms
                self._detect_steam()
                self._detect_epic_games()
                self._detect_itch_io()
                self._detect_gamejolt()
                self._detect_amazon_games()
                
                # Detect mobile platforms
                self._detect_google_play()
                self._detect_apple_store()
                
                # Detect direct download
                self._detect_direct_download()
                
                # Determine primary platform
                self._determine_primary_platform()
                
                # Update platform state
                platform_state['detected_platform'] = self.primary_platform or 'unknown'
                platform_state['platform_verified'] = len(self.detected_platforms) > 0
                
                renpy.log(f"Platform detection complete. Primary: {self.primary_platform}")
                renpy.log(f"All detected platforms: {self.detected_platforms}")
                
                return self.detected_platforms
                
            except Exception as e:
                renpy.log(f"Platform detection error: {str(e)}")
                return []
        
        def _detect_steam(self):
            """Detect Steam platform"""
            try:
                steam_detected = False
                steam_data = {}
                
                # Check for Steam API
                try:
                    import steam
                    steam_data['api_available'] = True
                    steam_detected = True
                except ImportError:
                    steam_data['api_available'] = False
                
                # Check for Steam processes
                if platform.system() == "Windows":
                    steam_processes = ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']
                else:
                    steam_processes = ['steam', 'steam-runtime']
                
                for process in steam_processes:
                    if self._is_process_running(process):
                        steam_data['process_detected'] = process
                        steam_detected = True
                        break
                
                # Check for Steam environment variables
                steam_env_vars = ['STEAM_COMPAT_DATA_PATH', 'STEAM_RUNTIME', 'SteamAppId']
                for env_var in steam_env_vars:
                    if os.environ.get(env_var):
                        steam_data['environment'] = env_var
                        steam_detected = True
                        break
                
                # Check for Steam overlay
                if os.environ.get('SteamOverlayGameId'):
                    steam_data['overlay_detected'] = True
                    steam_detected = True
                
                # Check for Steam directories
                steam_paths = [
                    os.path.expanduser("~/.steam"),
                    os.path.expanduser("~/.local/share/Steam"),
                    "C:\\Program Files (x86)\\Steam",
                    "C:\\Program Files\\Steam"
                ]
                
                for path in steam_paths:
                    if os.path.exists(path):
                        steam_data['installation_path'] = path
                        steam_detected = True
                        break
                
                if steam_detected:
                    self.detected_platforms.append('steam')
                    self.platform_processes['steam'] = steam_data
                    renpy.log("Steam platform detected")
                
            except Exception as e:
                renpy.log(f"Steam detection error: {str(e)}")
        
        def _detect_epic_games(self):
            """Detect Epic Games Store platform"""
            try:
                epic_detected = False
                epic_data = {}
                
                # Check for Epic Games Launcher processes
                if platform.system() == "Windows":
                    epic_processes = ['EpicGamesLauncher.exe', 'UnrealEngineLauncher.exe', 'EpicWebHelper.exe']
                else:
                    epic_processes = ['epic-games-launcher', 'unreal-engine']
                
                for process in epic_processes:
                    if self._is_process_running(process):
                        epic_data['process_detected'] = process
                        epic_detected = True
                        break
                
                # Check for Epic Online Services
                try:
                    # This would be where you'd check for EOS SDK
                    # For now, we'll check for common EOS files
                    eos_files = ['EOSSDK-Win64-Shipping.dll', 'libEOSSDK-Linux-Shipping.so']
                    for eos_file in eos_files:
                        if os.path.exists(eos_file):
                            epic_data['eos_detected'] = True
                            epic_detected = True
                            break
                except:
                    pass
                
                # Check for Epic environment variables
                epic_env_vars = ['EPIC_USERNAME', 'EPIC_USERID', 'EOS_OVERLAY_ENABLED']
                for env_var in epic_env_vars:
                    if os.environ.get(env_var):
                        epic_data['environment'] = env_var
                        epic_detected = True
                        break
                
                # Check for Epic installation paths
                epic_paths = [
                    os.path.expanduser("~/Library/Application Support/Epic"),
                    "C:\\ProgramData\\Epic",
                    "C:\\Users\\<USER>\\Epic Games"
                ]
                
                for path in epic_paths:
                    if os.path.exists(path):
                        epic_data['installation_path'] = path
                        epic_detected = True
                        break
                
                if epic_detected:
                    self.detected_platforms.append('epic')
                    self.platform_processes['epic'] = epic_data
                    renpy.log("Epic Games Store platform detected")
                
            except Exception as e:
                renpy.log(f"Epic Games detection error: {str(e)}")
        
        def _detect_itch_io(self):
            """Detect itch.io platform"""
            try:
                itch_detected = False
                itch_data = {}
                
                # Check for itch.io app processes
                itch_processes = ['itch.exe', 'itch', 'butler.exe', 'butler']
                
                for process in itch_processes:
                    if self._is_process_running(process):
                        itch_data['process_detected'] = process
                        itch_detected = True
                        break
                
                # Check for itch.io directories
                itch_paths = [
                    os.path.expanduser("~/.config/itch"),
                    os.path.expanduser("~/Library/Application Support/itch"),
                    os.path.expanduser("~/.itch"),
                    os.path.join(os.environ.get('APPDATA', ''), 'itch') if os.environ.get('APPDATA') else None
                ]
                
                for path in itch_paths:
                    if path and os.path.exists(path):
                        itch_data['installation_path'] = path
                        itch_detected = True
                        break
                
                # Check for itch.io environment variables
                if os.environ.get('ITCH_GAME_URL') or os.environ.get('ITCH_API_KEY'):
                    itch_data['environment_detected'] = True
                    itch_detected = True
                
                # Check for .itch.toml file (itch.io game metadata)
                itch_config_files = ['.itch.toml', 'itch.toml', '.itch/config.json']
                for config_file in itch_config_files:
                    if os.path.exists(config_file):
                        itch_data['config_file'] = config_file
                        itch_detected = True
                        break
                
                if itch_detected:
                    self.detected_platforms.append('itch')
                    self.platform_processes['itch'] = itch_data
                    renpy.log("itch.io platform detected")
                
            except Exception as e:
                renpy.log(f"itch.io detection error: {str(e)}")
        
        def _detect_gamejolt(self):
            """Detect GameJolt platform"""
            try:
                gamejolt_detected = False
                gamejolt_data = {}
                
                # Check for GameJolt client processes
                gamejolt_processes = ['gamejolt-client.exe', 'gamejolt-client', 'GameJolt Client.exe']
                
                for process in gamejolt_processes:
                    if self._is_process_running(process):
                        gamejolt_data['process_detected'] = process
                        gamejolt_detected = True
                        break
                
                # Check for GameJolt API integration
                try:
                    # Look for GameJolt API files or configuration
                    gamejolt_files = ['gamejolt_api.py', 'gamejolt.json', '.gamejolt']
                    for gj_file in gamejolt_files:
                        if os.path.exists(gj_file):
                            gamejolt_data['api_file'] = gj_file
                            gamejolt_detected = True
                            break
                except:
                    pass
                
                # Check for GameJolt environment variables
                gj_env_vars = ['GAMEJOLT_GAME_ID', 'GAMEJOLT_PRIVATE_KEY', 'GAMEJOLT_USERNAME']
                for env_var in gj_env_vars:
                    if os.environ.get(env_var):
                        gamejolt_data['environment'] = env_var
                        gamejolt_detected = True
                        break
                
                if gamejolt_detected:
                    self.detected_platforms.append('gamejolt')
                    self.platform_processes['gamejolt'] = gamejolt_data
                    renpy.log("GameJolt platform detected")
                
            except Exception as e:
                renpy.log(f"GameJolt detection error: {str(e)}")
        
        def _detect_amazon_games(self):
            """Detect Amazon Games platform"""
            try:
                amazon_detected = False
                amazon_data = {}
                
                # Check for Amazon Games processes
                amazon_processes = ['Amazon Games.exe', 'amazon-games', 'AmazonGamesLauncher.exe']
                
                for process in amazon_processes:
                    if self._is_process_running(process):
                        amazon_data['process_detected'] = process
                        amazon_detected = True
                        break
                
                # Check for Amazon GameLift or other Amazon gaming services
                amazon_env_vars = ['AWS_GAMELIFT_REGION', 'AMAZON_GAMES_USER', 'AWS_COGNITO_IDENTITY_POOL_ID']
                for env_var in amazon_env_vars:
                    if os.environ.get(env_var):
                        amazon_data['environment'] = env_var
                        amazon_detected = True
                        break
                
                # Check for Amazon Games installation paths
                amazon_paths = [
                    "C:\\Users\\<USER>\\Amazon Games",
                    os.path.expanduser("~/Amazon Games"),
                    "/Applications/Amazon Games.app"
                ]
                
                for path in amazon_paths:
                    if os.path.exists(path):
                        amazon_data['installation_path'] = path
                        amazon_detected = True
                        break
                
                if amazon_detected:
                    self.detected_platforms.append('amazon')
                    self.platform_processes['amazon'] = amazon_data
                    renpy.log("Amazon Games platform detected")
                
            except Exception as e:
                renpy.log(f"Amazon Games detection error: {str(e)}")

        def _detect_google_play(self):
            """Detect Google Play Store platform (Android)"""
            try:
                play_detected = False
                play_data = {}

                # Check if running on Android
                if hasattr(renpy, 'android') or platform.system().lower() == 'android':
                    play_data['platform'] = 'android'
                    play_detected = True

                    # Check for Google Play Services
                    try:
                        # This would require Android-specific imports
                        # For now, we'll check for common indicators
                        if os.path.exists('/system/framework/com.google.android.gms.jar'):
                            play_data['play_services'] = True
                            play_detected = True
                    except:
                        pass

                    # Check for Google Play environment
                    play_env_vars = ['ANDROID_DATA', 'GOOGLE_PLAY_SERVICES_VERSION']
                    for env_var in play_env_vars:
                        if os.environ.get(env_var):
                            play_data['environment'] = env_var
                            break

                if play_detected:
                    self.detected_platforms.append('googleplay')
                    self.platform_processes['googleplay'] = play_data
                    renpy.log("Google Play Store platform detected")

            except Exception as e:
                renpy.log(f"Google Play detection error: {str(e)}")

        def _detect_apple_store(self):
            """Detect Apple App Store platform (iOS)"""
            try:
                appstore_detected = False
                appstore_data = {}

                # Check if running on iOS
                if hasattr(renpy, 'ios') or platform.system().lower() == 'darwin':
                    # Check for iOS-specific indicators
                    if hasattr(renpy, 'ios'):
                        appstore_data['platform'] = 'ios'
                        appstore_detected = True

                        # Check for App Store receipt
                        receipt_path = '/var/mobile/Applications/StoreKit/receipt'
                        if os.path.exists(receipt_path):
                            appstore_data['receipt_found'] = True

                    # Check for macOS App Store (for Mac games)
                    elif platform.system() == 'Darwin':
                        mac_appstore_paths = [
                            '/Applications/App Store.app',
                            '~/Library/Preferences/com.apple.appstore.plist'
                        ]

                        for path in mac_appstore_paths:
                            expanded_path = os.path.expanduser(path)
                            if os.path.exists(expanded_path):
                                appstore_data['mac_appstore'] = True
                                appstore_detected = True
                                break

                if appstore_detected:
                    self.detected_platforms.append('appstore')
                    self.platform_processes['appstore'] = appstore_data
                    renpy.log("Apple App Store platform detected")

            except Exception as e:
                renpy.log(f"Apple App Store detection error: {str(e)}")

        def _detect_direct_download(self):
            """Detect direct download (no platform launcher)"""
            try:
                # If no other platforms detected, assume direct download
                if not self.detected_platforms:
                    direct_data = {
                        'method': 'direct_download',
                        'detected_by': 'elimination',
                        'timestamp': datetime.now().isoformat()
                    }

                    # Check for download metadata files
                    download_indicators = [
                        'download_info.txt',
                        'source.txt',
                        '.download_source',
                        'distribution.json'
                    ]

                    for indicator in download_indicators:
                        if os.path.exists(indicator):
                            direct_data['metadata_file'] = indicator
                            break

                    self.detected_platforms.append('direct')
                    self.platform_processes['direct'] = direct_data
                    renpy.log("Direct download detected")

            except Exception as e:
                renpy.log(f"Direct download detection error: {str(e)}")

        def _determine_primary_platform(self):
            """Determine the primary platform from detected platforms"""
            try:
                if not self.detected_platforms:
                    self.primary_platform = 'unknown'
                    return

                # Priority order for platforms
                platform_priority = [
                    'steam',      # Highest priority
                    'epic',
                    'googleplay',
                    'appstore',
                    'itch',
                    'gamejolt',
                    'amazon',
                    'direct'      # Lowest priority
                ]

                # Find the highest priority platform
                for platform in platform_priority:
                    if platform in self.detected_platforms:
                        self.primary_platform = platform
                        platform_state['distribution_source'] = platform
                        break

                if not self.primary_platform and self.detected_platforms:
                    self.primary_platform = self.detected_platforms[0]

            except Exception as e:
                renpy.log(f"Primary platform determination error: {str(e)}")
                self.primary_platform = 'unknown'

        def _is_process_running(self, process_name):
            """Check if a process is currently running"""
            try:
                if platform.system() == "Windows":
                    # Use tasklist on Windows
                    result = subprocess.run(['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
                                          capture_output=True, text=True, timeout=5)
                    return process_name.lower() in result.stdout.lower()
                else:
                    # Use ps on Unix-like systems
                    result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=5)
                    return process_name.lower() in result.stdout.lower()
            except:
                return False

        def get_platform_info(self, platform_name=None):
            """Get detailed information about a specific platform or all platforms"""
            if platform_name:
                return self.platform_processes.get(platform_name, {})
            return self.platform_processes

        def is_platform_detected(self, platform_name):
            """Check if a specific platform is detected"""
            return platform_name in self.detected_platforms

        def get_primary_platform(self):
            """Get the primary detected platform"""
            return self.primary_platform

        def create_detection_report(self):
            """Create a comprehensive detection report"""
            report = {
                'detection_timestamp': platform_state.get('detection_timestamp'),
                'detected_platforms': self.detected_platforms,
                'primary_platform': self.primary_platform,
                'platform_details': self.platform_processes,
                'system_info': {
                    'os': platform.system(),
                    'architecture': platform.machine(),
                    'python_version': platform.python_version()
                }
            }
            return report

    # Initialize platform detector
    platform_detector = PlatformDetector()

# Auto-detect platforms on startup
init python:
    if PLATFORM_CONFIG.get('enabled', True) and PLATFORM_CONFIG.get('auto_detect', True):
        try:
            platform_detector.detect_all_platforms()

            # Update global platform state
            platform_state['detected_platform'] = platform_detector.get_primary_platform()
            platform_state['platform_verified'] = len(platform_detector.detected_platforms) > 0
            platform_state['platform_data'] = platform_detector.get_platform_info()

        except Exception as e:
            renpy.log(f"Auto platform detection failed: {str(e)}")

# Platform detection functions for use in game
define platform_detection_enabled = PLATFORM_CONFIG.get('enabled', True)
define detected_platform = platform_state.get('detected_platform', 'unknown')
define platform_verified = platform_state.get('platform_verified', False)
