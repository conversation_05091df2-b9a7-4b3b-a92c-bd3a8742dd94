{"platform_name": "googleplay", "display_name": "Google Play Store", "detection_config": {"platform_check": "android", "environment_variables": ["ANDROID_DATA", "GOOGLE_PLAY_SERVICES_VERSION"], "system_files": ["/system/framework/com.google.android.gms.jar"], "api_files": ["google_play_services.jar"]}, "features": {"play_services": {"enabled": true, "required": true}, "achievements": {"enabled": true, "config_file": "google_play_achievements.json"}, "leaderboards": {"enabled": true, "play_services_required": true}, "cloud_save": {"enabled": true, "play_services_required": true}, "in_app_purchases": {"enabled": true, "google_play_billing": true}}, "terms_of_service": {"content_rating_required": true, "iarc_rating": true, "age_verification": {"required_for_mature": true, "minimum_age": 13}, "content_restrictions": {"no_hate_speech": true, "no_violence_against_real_entities": true, "appropriate_metadata": true}}, "technical_requirements": {"target_api_level": 30, "permissions_justified": true, "privacy_policy_required": true, "64_bit_support": true}, "monetization": {"google_play_billing": true, "subscription_rules": true, "regional_pricing": true}, "security": {"play_protect": true, "app_signing": true, "violation_reporting": true}}