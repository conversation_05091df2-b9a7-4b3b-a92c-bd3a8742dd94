# Universal Game Loading Screen System
# Shows when transitioning between games in the launcher

# Loading screen variables
default loading_progress = 0
default loading_game_name = ""
default loading_game_description = ""
default loading_complete = False

# Loading screen with animated progress bar and game info
screen loading_screen(game_info):
    
    # Full screen background
    add "#001122"
    
    # Animated background elements
    add "images/gui/overlay/main_menu.png" alpha 0.3
    
    # Main loading container
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 500
        background "#003366"
        padding (40, 40)
        
        vbox:
            spacing 30
            xalign 0.5
            
            # Game title and logo area
            frame:
                xsize 720
                ysize 120
                background "#004488"
                padding (20, 20)
                
                hbox:
                    spacing 20
                    yalign 0.5
                    
                    # Game icon/logo
                    if game_info.get("image"):
                        add game_info["image"] size (80, 80) yalign 0.5
                    else:
                        text "🎮" size 60 color "#00b4d8" yalign 0.5
                    
                    # Game info
                    vbox:
                        spacing 5
                        yalign 0.5
                        
                        text "[game_info.get('display_name', game_info.get('name', 'Unknown Game'))]" size 28 color "#caf0f8" text_align 0.0
                        text "[game_info.get('description title', game_info.get('description', 'Loading game...'))]" size 16 color "#90e0ef" text_align 0.0
            
            # Loading progress section
            frame:
                xsize 720
                ysize 200
                background "#002244"
                padding (30, 30)
                
                vbox:
                    spacing 20
                    xalign 0.5
                    
                    text "Loading Game..." size 24 color "#00b4d8" xalign 0.5
                    
                    # Animated progress bar
                    frame:
                        xsize 600
                        ysize 30
                        background "#001133"
                        padding (5, 5)
                        
                        # Progress bar background
                        frame:
                            xsize 590
                            ysize 20
                            background "#003366"
                            
                            # Progress bar fill (animated)
                            frame:
                                xsize int(590 * (loading_progress / 100.0))
                                ysize 20
                                background "#00b4d8"
                    
                    # Progress percentage
                    text "[loading_progress]%" size 18 color "#90e0ef" xalign 0.5
                    
                    # Loading status text
                    if loading_progress < 25:
                        text "Initializing game systems..." size 14 color "#90e0ef" xalign 0.5
                    elif loading_progress < 50:
                        text "Loading game assets..." size 14 color "#90e0ef" xalign 0.5
                    elif loading_progress < 75:
                        text "Preparing game world..." size 14 color "#90e0ef" xalign 0.5
                    elif loading_progress < 95:
                        text "Finalizing setup..." size 14 color "#90e0ef" xalign 0.5
                    else:
                        text "Ready to play!" size 14 color "#00ff88" xalign 0.5
            
            # Game description area
            frame:
                xsize 720
                ysize 100
                background "#001133"
                padding (20, 15)
                
                viewport:
                    xsize 680
                    ysize 70
                    scrollbars "vertical"

                    text "[game_info.get('description full', game_info.get('description_full', 'Preparing to launch your selected game...'))]" size 12 color "#90e0ef" text_align 0.0

# Loading screen animation and logic
label show_loading_screen(game_info):
    
    # Initialize loading variables
    $ loading_progress = 0
    $ loading_game_name = game_info.get("display_name", game_info.get("name", "Unknown Game"))
    $ loading_game_description = game_info.get("description title", game_info.get("description", "Loading game..."))
    $ loading_complete = False
    
    # Show loading screen
    show screen loading_screen(game_info)
    
    # Play loading sound effect
    play sound "audio/click.ogg"
    
    # Animated loading sequence
    python:
        import time
        
        # Simulate loading with progress updates (much slower)
        loading_stages = [
            (10, 1.5),   # Initialize - 10% in 1.5 seconds
            (25, 2.0),   # Load assets - 25% in 2.0 seconds
            (45, 2.5),   # Prepare world - 45% in 2.5 seconds
            (65, 2.0),   # Setup systems - 65% in 2.0 seconds
            (80, 1.5),   # Finalize - 80% in 1.5 seconds
            (95, 1.0),   # Almost ready - 95% in 1.0 seconds
            (100, 1.0)   # Complete - 100% in 1.0 seconds
        ]
        
        for target_progress, duration in loading_stages:
            start_progress = loading_progress
            start_time = time.time()
            
            while loading_progress < target_progress:
                elapsed = time.time() - start_time
                progress_ratio = min(elapsed / duration, 1.0)
                loading_progress = int(start_progress + (target_progress - start_progress) * progress_ratio)
                
                # Update screen
                renpy.restart_interaction()
                time.sleep(0.05)  # Small delay for smooth animation
                
                if progress_ratio >= 1.0:
                    break
    
    # Brief pause at 100% to show completion
    $ renpy.pause(2.0)

    # Mark loading as complete
    $ loading_complete = True

    # Hide loading screen
    hide screen loading_screen

    # Transition effect
    scene black
    with fade

    # Loading complete - return to caller
    # The game launcher will handle jumping to the actual game
    return

# Quick loading screen for faster transitions (optional)
label show_quick_loading_screen(game_info):
    
    # Show simplified loading screen
    scene black
    with dissolve
    
    # Simple loading message
    centered "{size=24}{color=#00b4d8}Loading [game_info.get('display_name', game_info.get('name', 'Game'))]...{/color}{/size}"
    
    # Brief pause
    $ renpy.pause(1.0)
    
    # Clear screen
    scene black
    with fade
    
    return

# Loading screen with custom background (if game has specific loading image)
screen custom_loading_screen(game_info, background_image=None):
    
    # Custom background if provided
    if background_image:
        add background_image
    else:
        add "#001122"
    
    # Semi-transparent overlay
    add "#000000" alpha 0.6
    
    # Loading content (same as main loading screen but with custom background)
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 300
        background "#003366"
        padding (30, 30)
        
        vbox:
            spacing 20
            xalign 0.5
            
            text "Loading [game_info.get('display_name', game_info.get('name', 'Game'))]" size 24 color "#caf0f8" xalign 0.5
            
            # Simple progress indicator
            text "●●●●●" size 20 color "#00b4d8" xalign 0.5
            
            text "Please wait..." size 14 color "#90e0ef" xalign 0.5

# Error handling for loading screen
label loading_error(error_message="Unknown error occurred"):

    scene black
    with fade

    # Show error screen
    call screen loading_error_screen(error_message)

    return

# Error screen
screen loading_error_screen(error_message):

    # Error display
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 200
        background "#660000"
        padding (20, 20)

        vbox:
            spacing 15
            xalign 0.5

            text "Loading Error" size 20 color "#ff6666" xalign 0.5
            text "[error_message]" size 14 color "#ffcccc" xalign 0.5 text_align 0.5

            textbutton "Return to Game Selection":
                action Return("return_to_launcher")
                xalign 0.5
                text_size 14
                text_color "#ffffff"
                background "#003366"
                hover_background "#004488"
