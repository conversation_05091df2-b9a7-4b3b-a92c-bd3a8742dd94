## Hardcore Anti-Cheat System - Riot Games Level Protection
## Enhanced Developer Recognition and Full Access Control
## Multi-layered security system for visual novel protection

init -100 python:
    import hashlib
    import os
    import time
    import threading
    import json
    import random
    import struct
    
    # Anti-cheat configuration
    ANTICHEAT_CONFIG = {
        'enabled': True,
        'strict_mode': True,
        'memory_protection': True,
        'process_monitoring': True,
        'file_integrity': True,
        'behavioral_analysis': True,
        'network_validation': True,
        'kernel_level_checks': True,
        'nsfw_detection': True,  # NSFW content detection
        'auto_adult_mode': True,  # Automatically enable adult mode
        'legal_protection': True,  # Legal document protection
        'ban_duration': 86400 * 30,  # 30 days
        'violation_threshold': 3
    }
    
    # Global anti-cheat state
    anticheat_state = {
        'initialized': False,
        'violations': 0,
        'last_check': 0,
        'session_id': None,
        'integrity_hash': None,
        'protected_memory': {},
        'monitored_processes': set(),
        'behavioral_score': 100,
        'banned': False,
        'ban_reason': None,
        'developer_authenticated': False,
        'full_access_granted': False,
        'developer_privileges': [],
        'master_override_active': False
    }

class AntiCheatCore:
    """Core anti-cheat system with multiple protection layers"""
    
    def __init__(self):
        self.session_start = time.time()
        self.check_interval = 1.0  # Check every second
        self.memory_regions = {}
        self.file_hashes = {}
        self.process_whitelist = set()
        self.violation_log = []
        self.encryption_key = self._generate_session_key()
        
    def _generate_session_key(self):
        """Generate unique session encryption key"""
        import random
        import time
        seed = str(time.time()) + str(random.randint(1000000, 9999999))
        return hashlib.sha256(seed.encode()).hexdigest()[:32]
    
    def initialize(self):
        """Initialize anti-cheat system"""
        try:
            if anticheat_state['banned']:
                self._handle_banned_user()
                return False
            
            # Generate session ID
            anticheat_state['session_id'] = self._generate_session_key()
            
            # Initialize file integrity checking
            self._initialize_file_integrity()
            
            # Start memory protection
            self._initialize_memory_protection()
            
            # Begin process monitoring
            self._start_process_monitoring()
            
            # Initialize behavioral analysis
            self._initialize_behavioral_analysis()

            # Initialize NSFW detection if enabled
            if ANTICHEAT_CONFIG.get('nsfw_detection', True):
                self._initialize_nsfw_detection()

            # Authenticate developer access first
            self._authenticate_developer_access()

            # Initialize legal document protection
            if ANTICHEAT_CONFIG.get('legal_protection', True):
                self._initialize_legal_protection()

            # Start continuous monitoring thread
            self._start_monitoring_thread()

            anticheat_state['initialized'] = True
            renpy.log("Anti-cheat system initialized successfully")
            return True
            
        except Exception as e:
            renpy.log("Anti-cheat initialization failed: {}".format(str(e)))
            self._report_violation("INIT_FAILURE", str(e))
            return False
    
    def _initialize_file_integrity(self):
        """Initialize file integrity checking system"""
        critical_files = [
            'game/script.rpy',
            'game/options.rpy',
            'game/screens.rpy',
            'game/anticheat_core.rpy'
        ]
        
        for file_path in critical_files:
            try:
                if renpy.loadable(file_path):
                    with renpy.open_file(file_path, 'rb') as f:
                        content = f.read()
                        file_hash = hashlib.sha256(content).hexdigest()
                        self.file_hashes[file_path] = file_hash
            except Exception as e:
                self._report_violation("FILE_INTEGRITY_INIT", "Failed to hash {}".format(file_path))
    
    def _initialize_memory_protection(self):
        """Initialize memory protection system"""
        # Protect critical game variables
        protected_vars = [
            'persistent',
            'config',
            'anticheat_state',
            'ANTICHEAT_CONFIG'
        ]
        
        for var_name in protected_vars:
            try:
                if hasattr(renpy.store, var_name):
                    var_value = getattr(renpy.store, var_name)
                    var_hash = hashlib.sha256(str(var_value).encode()).hexdigest()
                    self.memory_regions[var_name] = {
                        'hash': var_hash,
                        'last_check': time.time(),
                        'access_count': 0
                    }
            except Exception as e:
                self._report_violation("MEMORY_PROTECTION_INIT", str(e))
    
    def _start_process_monitoring(self):
        """Start monitoring running processes for cheating tools"""
        # Known cheat engine signatures
        self.blacklisted_processes = {
            'cheatengine',
            'artmoney',
            'gameguardian',
            'speedhack',
            'memoryeditor',
            'processhacker',
            'ollydbg',
            'x64dbg',
            'ida',
            'ghidra',
            'wireshark',
            'fiddler',
            'charles',
            'autohotkey',
            'autoit'
        }
        
        # Whitelist legitimate processes
        self.process_whitelist = {
            'explorer.exe',
            'dwm.exe',
            'winlogon.exe',
            'csrss.exe',
            'lsass.exe',
            'services.exe',
            'svchost.exe',
            'chrome.exe',
            'firefox.exe',
            'discord.exe',
            'steam.exe'
        }
    
    def _initialize_behavioral_analysis(self):
        """Initialize behavioral analysis system"""
        self.behavioral_patterns = {
            'click_speed': [],
            'input_timing': [],
            'menu_navigation': [],
            'save_load_frequency': 0,
            'skip_patterns': [],
            'unusual_actions': 0
        }

    def _initialize_nsfw_detection(self):
        """Initialize NSFW content detection system"""
        try:
            # Perform initial NSFW content scan
            if hasattr(renpy.store, 'nsfw_detector'):
                renpy.log("Running initial NSFW content scan...")
                scan_results = nsfw_detector.perform_full_scan()

                if scan_results['nsfw_detected']:
                    renpy.log("NSFW content detected during initialization")

                    # Auto-enable adult mode if configured
                    if ANTICHEAT_CONFIG.get('auto_adult_mode', True):
                        nsfw_detector.enable_adult_mode("Initial scan detected NSFW content")

                        # Report to anti-cheat system
                        self._report_violation("NSFW_CONTENT_DETECTED",
                            "Adult content detected - Confidence: {}%".format(scan_results['confidence']))

                    # Store scan results
                    anticheat_state['nsfw_scan_results'] = scan_results
                else:
                    renpy.log("No NSFW content detected during initial scan")
            else:
                renpy.log("NSFW detector not available")

        except Exception as e:
            renpy.log("NSFW detection initialization failed: {}".format(str(e)))

    def _initialize_legal_protection(self):
        """Initialize legal document protection system"""
        try:
            # Initialize legal protection if available
            if hasattr(renpy.store, 'legal_protector'):
                renpy.log("Initializing legal document protection...")

                success = legal_protector.initialize_legal_protection()

                if success:
                    renpy.log("Legal document protection initialized successfully")

                    # Check if terms acceptance is required
                    if legal_protector.require_terms_acceptance():
                        anticheat_state['require_terms_acceptance'] = True
                        renpy.log("Terms acceptance required")

                    # Store legal protection status
                    anticheat_state['legal_protection_active'] = True
                else:
                    renpy.log("Legal document protection initialization failed")

                    # Report initialization failure
                    self._report_violation("LEGAL_PROTECTION_FAILED",
                        "Legal document protection could not be initialized")
            else:
                renpy.log("Legal protector not available")

        except Exception as e:
            renpy.log("Legal protection initialization error: {}".format(str(e)))

    def _authenticate_developer_access(self):
        """Comprehensive developer authentication and access control"""
        try:
            renpy.log("Performing developer authentication...")

            # Multiple authentication methods
            auth_methods = [
                self._check_hardware_fingerprint(),
                self._check_user_account(),
                self._check_system_signature(),
                self._check_developer_files(),
                self._check_encryption_keys()
            ]

            # Count successful authentications
            successful_auths = sum(auth_methods)

            # Require at least 2 successful authentications
            if successful_auths >= 2:
                self._grant_full_developer_access()
                return True
            else:
                renpy.log("Developer authentication failed: {}/5 methods successful".format(successful_auths))
                return False

        except Exception as e:
            renpy.log("Developer authentication error: {}".format(str(e)))
            return False

    def _check_hardware_fingerprint(self):
        """Check hardware fingerprint for developer recognition"""
        try:
            # Get current hardware signature
            if hasattr(renpy.store, 'legal_protector'):
                current_signature = legal_protector._generate_developer_signature()

                # Known developer signatures (your computers)
                known_signatures = [
                    # Primary developer computer
                    "your_primary_signature_here",
                    # Secondary/old computers
                    "your_old_computer_signature_here",
                    # Add more signatures as needed
                ]

                if current_signature in known_signatures:
                    renpy.log("Hardware fingerprint authenticated")
                    return True

            return False

        except Exception as e:
            renpy.log("Hardware fingerprint check failed: {}".format(str(e)))
            return False

    def _check_user_account(self):
        """Check user account information"""
        try:
            import getpass
            import os

            # Get current user information
            current_user = getpass.getuser().lower()
            computer_name = os.environ.get('COMPUTERNAME', '').lower()

            # Known developer accounts (your usernames)
            known_users = [
                'your_username',
                'your_old_username',
                'administrator',
                'dev',
                'developer'
            ]

            # Known computer names
            known_computers = [
                'your_computer_name',
                'your_old_computer',
                'dev-pc',
                'gaming-pc'
            ]

            if current_user in known_users or computer_name in known_computers:
                renpy.log("User account authenticated: {}@{}".format(current_user, computer_name))
                return True

            return False

        except Exception as e:
            renpy.log("User account check failed: {}".format(str(e)))
            return False

    def _check_system_signature(self):
        """Check system-specific signatures"""
        try:
            import platform
            import hashlib

            # Create system signature
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'system': platform.system()
            }

            signature = hashlib.sha256(str(system_info).encode()).hexdigest()

            # Known system signatures
            known_systems = [
                "your_system_signature_here",
                # Add more system signatures
            ]

            if signature in known_systems:
                renpy.log("System signature authenticated")
                return True

            return False

        except Exception as e:
            renpy.log("System signature check failed: {}".format(str(e)))
            return False

    def _check_developer_files(self):
        """Check for developer-specific files"""
        try:
            # Developer file indicators
            developer_files = [
                '.vscode/encryption_core.py',
                'anti-cheat/setup_developer.py',
                'anti-cheat/developer_config.json',
                '.git/config',
                'project.godot',
                'requirements.txt'
            ]

            found_files = 0
            for file_path in developer_files:
                if os.path.exists(os.path.join(config.basedir, file_path)):
                    found_files += 1

            # Require at least 2 developer files
            if found_files >= 2:
                renpy.log("Developer files authenticated: {}/{}".format(found_files, len(developer_files)))
                return True

            return False

        except Exception as e:
            renpy.log("Developer files check failed: {}".format(str(e)))
            return False

    def _check_encryption_keys(self):
        """Check for encryption system access"""
        try:
            # Check if encryption system is available
            if hasattr(renpy.store, 'encryption_engine'):
                # Try to generate a project key (developer privilege)
                test_key = encryption_engine.generate_project_key("test")

                if test_key:
                    renpy.log("Encryption keys authenticated")
                    return True

            return False

        except Exception as e:
            renpy.log("Encryption keys check failed: {}".format(str(e)))
            return False

    def _grant_full_developer_access(self):
        """Grant full developer access and privileges"""
        try:
            # Set developer authentication flags
            anticheat_state['developer_authenticated'] = True
            anticheat_state['full_access_granted'] = True
            anticheat_state['master_override_active'] = True

            # Grant all developer privileges
            developer_privileges = [
                'ANTI_CHEAT_CONTROL',
                'ENCRYPTION_ACCESS',
                'LEGAL_DOCUMENT_OVERRIDE',
                'NSFW_CONFIGURATION',
                'SYSTEM_MONITORING',
                'BAN_OVERRIDE',
                'DEBUG_ACCESS',
                'CONFIGURATION_EDIT',
                'FILE_SYSTEM_ACCESS',
                'MEMORY_ACCESS',
                'PROCESS_CONTROL',
                'NETWORK_OVERRIDE'
            ]

            anticheat_state['developer_privileges'] = developer_privileges

            # Set persistent developer status
            persistent.developer_authenticated = True
            persistent.developer_access_granted = time.time()
            persistent.developer_privileges = developer_privileges

            # Enable all developer features
            config.developer = True
            config.console = True

            # Disable restrictions for developer
            ANTICHEAT_CONFIG['strict_mode'] = False
            LEGAL_CONFIG['violation_ban'] = False

            # Grant encryption access
            if hasattr(renpy.store, 'ENCRYPTION_CONFIG'):
                ENCRYPTION_CONFIG['developer_mode'] = True
                ENCRYPTION_CONFIG['bypass_restrictions'] = True

            # Log successful authentication
            renpy.log("FULL DEVELOPER ACCESS GRANTED")
            renpy.log("Privileges: {}".format(", ".join(developer_privileges)))

            # Show developer welcome message
            renpy.notify("🔓 DEVELOPER ACCESS GRANTED - Full Control Enabled")

            return True

        except Exception as e:
            renpy.log("Error granting developer access: {}".format(str(e)))
            return False

    def has_developer_privilege(self, privilege):
        """Check if developer has specific privilege"""
        return (anticheat_state.get('developer_authenticated', False) and
                privilege in anticheat_state.get('developer_privileges', []))

    def override_ban(self, reason="Developer override"):
        """Override any bans (developer only)"""
        if self.has_developer_privilege('BAN_OVERRIDE'):
            anticheat_state['banned'] = False
            anticheat_state['ban_reason'] = None
            persistent.anticheat_banned = False
            persistent.anticheat_ban_reason = None
            persistent.anticheat_ban_timestamp = None

            renpy.log("Ban overridden by developer: {}".format(reason))
            renpy.notify("🔓 Ban Override Applied")
            return True

        return False

    def access_encryption_system(self):
        """Access encryption system (developer only)"""
        if self.has_developer_privilege('ENCRYPTION_ACCESS'):
            return True
        return False

    def modify_anticheat_config(self, setting, value):
        """Modify anti-cheat configuration (developer only)"""
        if self.has_developer_privilege('CONFIGURATION_EDIT'):
            ANTICHEAT_CONFIG[setting] = value
            renpy.log("Anti-cheat config modified: {} = {}".format(setting, value))
            return True
        return False
    
    def _start_monitoring_thread(self):
        """Start continuous monitoring in background thread"""
        def monitor_loop():
            while anticheat_state['initialized'] and not anticheat_state['banned']:
                try:
                    self._perform_security_checks()
                    time.sleep(self.check_interval)
                except Exception as e:
                    self._report_violation("MONITOR_THREAD_ERROR", str(e))
                    break
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def _perform_security_checks(self):
        """Perform comprehensive security checks"""
        current_time = time.time()
        
        # File integrity check
        if current_time - anticheat_state.get('last_file_check', 0) > 30:
            self._check_file_integrity()
            anticheat_state['last_file_check'] = current_time
        
        # Memory protection check
        if current_time - anticheat_state.get('last_memory_check', 0) > 5:
            self._check_memory_integrity()
            anticheat_state['last_memory_check'] = current_time
        
        # Process monitoring
        if current_time - anticheat_state.get('last_process_check', 0) > 10:
            self._check_running_processes()
            anticheat_state['last_process_check'] = current_time
        
        # Behavioral analysis
        self._analyze_behavior()

        # Anti-debugging checks
        self._check_debugging_tools()

        # NSFW content monitoring (periodic check)
        if ANTICHEAT_CONFIG.get('nsfw_detection', True) and current_time - anticheat_state.get('last_nsfw_check', 0) > 300:  # Every 5 minutes
            self._monitor_nsfw_content()
            anticheat_state['last_nsfw_check'] = current_time

        # Legal document monitoring (periodic check)
        if ANTICHEAT_CONFIG.get('legal_protection', True) and current_time - anticheat_state.get('last_legal_check', 0) > 60:  # Every minute
            self._monitor_legal_documents()
            anticheat_state['last_legal_check'] = current_time

        anticheat_state['last_check'] = current_time
    
    def _check_file_integrity(self):
        """Check integrity of critical game files"""
        for file_path, expected_hash in self.file_hashes.items():
            try:
                if renpy.loadable(file_path):
                    with renpy.open_file(file_path, 'rb') as f:
                        content = f.read()
                        current_hash = hashlib.sha256(content).hexdigest()
                        
                        if current_hash != expected_hash:
                            self._report_violation("FILE_TAMPERING", 
                                "File {} has been modified".format(file_path))
                            return False
            except Exception as e:
                self._report_violation("FILE_CHECK_ERROR", str(e))
        return True
    
    def _check_memory_integrity(self):
        """Check integrity of protected memory regions"""
        for var_name, protection_data in self.memory_regions.items():
            try:
                if hasattr(renpy.store, var_name):
                    current_value = getattr(renpy.store, var_name)
                    current_hash = hashlib.sha256(str(current_value).encode()).hexdigest()
                    
                    if current_hash != protection_data['hash']:
                        # Allow legitimate changes to some variables
                        if var_name not in ['persistent']:
                            self._report_violation("MEMORY_TAMPERING", 
                                "Protected variable {} has been modified".format(var_name))
                            return False
                        else:
                            # Update hash for legitimate persistent changes
                            protection_data['hash'] = current_hash
                    
                    protection_data['access_count'] += 1
                    protection_data['last_check'] = time.time()
            except Exception as e:
                self._report_violation("MEMORY_CHECK_ERROR", str(e))
        return True
    
    def _check_running_processes(self):
        """Check for blacklisted processes"""
        try:
            import psutil
            running_processes = [proc.name().lower() for proc in psutil.process_iter(['name'])]
            
            for process in running_processes:
                process_clean = process.replace('.exe', '').replace('.dll', '')
                if any(blacklisted in process_clean for blacklisted in self.blacklisted_processes):
                    self._report_violation("BLACKLISTED_PROCESS", 
                        "Detected cheating tool: {}".format(process))
                    return False
        except ImportError:
            # psutil not available, use alternative method
            pass
        except Exception as e:
            self._report_violation("PROCESS_CHECK_ERROR", str(e))
        
        return True
    
    def _check_debugging_tools(self):
        """Check for debugging and reverse engineering tools"""
        try:
            # Check for common debugger indicators
            import sys
            
            # Check if being debugged
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                self._report_violation("DEBUGGER_DETECTED", "Python debugger detected")
                return False
            
            # Check for timing anomalies (debugger stepping)
            start_time = time.time()
            for i in range(1000):
                pass
            end_time = time.time()
            
            if end_time - start_time > 0.1:  # Should be much faster
                self._report_violation("TIMING_ANOMALY", "Execution timing suggests debugging")
                return False
                
        except Exception as e:
            self._report_violation("DEBUG_CHECK_ERROR", str(e))
        
        return True
    
    def _analyze_behavior(self):
        """Analyze user behavior for suspicious patterns"""
        # This would be called from game events
        current_time = time.time()
        
        # Check for impossible input speeds
        if len(self.behavioral_patterns['click_speed']) > 10:
            avg_speed = sum(self.behavioral_patterns['click_speed'][-10:]) / 10
            if avg_speed < 0.05:  # Impossibly fast clicking
                self._report_violation("IMPOSSIBLE_INPUT_SPEED", 
                    "Input speed too fast: {}ms".format(avg_speed * 1000))
        
        # Check save/load abuse
        if self.behavioral_patterns['save_load_frequency'] > 100:
            self._report_violation("SAVE_LOAD_ABUSE",
                "Excessive save/load operations")

    def _monitor_nsfw_content(self):
        """Monitor for new NSFW content during gameplay"""
        try:
            if hasattr(renpy.store, 'nsfw_detector'):
                # Quick scan for new content
                image_results = nsfw_detector.scan_image_files()
                audio_results = nsfw_detector.scan_audio_files()

                if image_results['nsfw_detected'] or audio_results['nsfw_detected']:
                    scan_results = {
                        'nsfw_detected': True,
                        'confidence': 75,  # Medium confidence for runtime detection
                        'text_results': {'nsfw_detected': False, 'triggers': []},
                        'image_results': image_results,
                        'audio_results': audio_results
                    }

                    # Handle detection
                    nsfw_detector.handle_nsfw_detection(scan_results)

                    # Report to anti-cheat
                    self._report_violation("RUNTIME_NSFW_DETECTED",
                        "New NSFW content detected during gameplay")

        except Exception as e:
            renpy.log("NSFW monitoring error: {}".format(str(e)))

    def _monitor_legal_documents(self):
        """Monitor legal documents for tampering"""
        try:
            if hasattr(renpy.store, 'legal_protector'):
                # Check document integrity
                if not legal_protector.verify_document_integrity():
                    # Legal violations detected
                    violations = legal_state.get('violations_detected', 0)
                    missing_docs = legal_state.get('missing_documents', [])
                    tampered_docs = legal_state.get('tampered_documents', [])

                    # Report violations
                    if missing_docs:
                        self._report_violation("LEGAL_DOCUMENTS_MISSING",
                            "Missing legal documents: {}".format(", ".join(missing_docs)))

                    if tampered_docs:
                        self._report_violation("LEGAL_DOCUMENTS_TAMPERED",
                            "Tampered legal documents: {}".format(", ".join(tampered_docs)))

                    # Trigger ban for serious violations
                    if violations >= 3:  # Multiple violations
                        self._trigger_ban("LEGAL_VIOLATION",
                            "Multiple legal document violations detected")

        except Exception as e:
            renpy.log("Legal document monitoring error: {}".format(str(e)))
    
    def _report_violation(self, violation_type, details):
        """Report a security violation"""
        violation = {
            'type': violation_type,
            'details': details,
            'timestamp': time.time(),
            'session_id': anticheat_state.get('session_id', 'unknown')
        }
        
        self.violation_log.append(violation)
        anticheat_state['violations'] += 1
        
        renpy.log("ANTICHEAT VIOLATION: {} - {}".format(violation_type, details))
        
        # Reduce behavioral score
        anticheat_state['behavioral_score'] -= 10
        
        # Check if ban threshold reached
        if (anticheat_state['violations'] >= ANTICHEAT_CONFIG['violation_threshold'] or 
            anticheat_state['behavioral_score'] <= 0):
            self._trigger_ban(violation_type, details)
    
    def _trigger_ban(self, violation_type, details):
        """Trigger anti-cheat ban"""
        anticheat_state['banned'] = True
        anticheat_state['ban_reason'] = "{}: {}".format(violation_type, details)
        anticheat_state['ban_timestamp'] = time.time()
        
        # Save ban to persistent data
        persistent.anticheat_banned = True
        persistent.anticheat_ban_reason = anticheat_state['ban_reason']
        persistent.anticheat_ban_timestamp = anticheat_state['ban_timestamp']
        
        renpy.log("ANTICHEAT BAN TRIGGERED: {}".format(anticheat_state['ban_reason']))
        
        # Force quit game
        renpy.quit()
    
    def _handle_banned_user(self):
        """Handle banned user attempting to play"""
        ban_time = persistent.anticheat_ban_timestamp
        current_time = time.time()
        
        if current_time - ban_time < ANTICHEAT_CONFIG['ban_duration']:
            remaining_time = ANTICHEAT_CONFIG['ban_duration'] - (current_time - ban_time)
            days_remaining = int(remaining_time / 86400)
            
            renpy.call_screen("anticheat_ban_screen", 
                reason=persistent.anticheat_ban_reason,
                days_remaining=days_remaining)
        else:
            # Ban expired, reset
            persistent.anticheat_banned = False
            persistent.anticheat_ban_reason = None
            persistent.anticheat_ban_timestamp = None
            anticheat_state['banned'] = False
    
    def record_user_action(self, action_type, timing=None):
        """Record user action for behavioral analysis"""
        if not anticheat_state['initialized']:
            return
        
        current_time = time.time()
        
        if action_type == "click":
            if timing:
                self.behavioral_patterns['click_speed'].append(timing)
                if len(self.behavioral_patterns['click_speed']) > 50:
                    self.behavioral_patterns['click_speed'].pop(0)
        
        elif action_type == "save":
            self.behavioral_patterns['save_load_frequency'] += 1
        
        elif action_type == "load":
            self.behavioral_patterns['save_load_frequency'] += 1
        
        elif action_type == "skip":
            self.behavioral_patterns['skip_patterns'].append(current_time)

# Initialize anti-cheat system
anticheat_core = AntiCheatCore()

# Default persistent values
default persistent.anticheat_banned = False
default persistent.anticheat_ban_reason = None
default persistent.anticheat_ban_timestamp = None
default persistent.developer_authenticated = False
default persistent.developer_access_granted = None
default persistent.developer_privileges = []
