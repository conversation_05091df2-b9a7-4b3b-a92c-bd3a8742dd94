## Dedicated GPU Detection and Configuration System
## Supports NVIDIA, AMD, and Intel GPUs with performance caps
## NVIDIA Cap: Cannot exceed GTX 1080 performance
## AMD Cap: Cannot go below AMD Radeon RX 560 performance

init python:
    import os
    import sys
    import subprocess
    import re
    import platform

    class DedicatedGPUDetector:
        """
        Comprehensive dedicated GPU detection and configuration system
        Supports NVIDIA, AMD, and Intel GPUs with performance limitations
        """

        def __init__(self):
            self.detected_gpu = None
            self.gpu_vendor = None
            self.gpu_model = None
            self.gpu_info = {}
            self.performance_tier = None
            self.is_within_caps = False
            self.recommended_settings = {}

        def detect_and_configure_gpu(self):
            """
            Main function to detect GPU and apply configuration
            """
            print("=== DEDICATED GPU DETECTION AND CONFIGURATION ===")

            try:
                # Detect GPU hardware
                self.gpu_info = self._get_gpu_info()

                # Parse GPU information
                if self._parse_gpu_info():
                    # Check performance caps
                    self._check_performance_caps()

                    # Configure Ren'Py based on detected GPU
                    self._configure_renpy_for_gpu()

                    # Generate detailed report
                    self._generate_gpu_report()

                    return True
                else:
                    print("❌ Failed to detect compatible GPU")
                    return False

            except Exception as e:
                print(f"Error in GPU detection: {e}")
                return False

        def _get_gpu_info(self):
            """
            Get GPU information from system
            """
            gpu_info = {}

            try:
                if platform.system() == "Windows":
                    # Use wmic to get GPU information
                    result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name,adapterram,driverversion'],
                                          capture_output=True, text=True, timeout=15)
                    gpu_info['raw_output'] = result.stdout

                    # Parse GPU names
                    lines = result.stdout.strip().split('\n')
                    gpu_names = []
                    for line in lines[1:]:  # Skip header
                        if line.strip() and 'name' not in line.lower():
                            gpu_names.append(line.strip())

                    gpu_info['gpu_names'] = gpu_names

                elif platform.system() == "Linux":
                    # Use lspci to get GPU information
                    result = subprocess.run(['lspci', '-v'], capture_output=True, text=True, timeout=10)
                    gpu_info['raw_output'] = result.stdout

                    # Parse GPU information
                    gpu_names = []
                    for line in result.stdout.split('\n'):
                        if 'VGA compatible controller' in line or 'Display controller' in line:
                            gpu_names.append(line.split(': ')[1] if ': ' in line else line)

                    gpu_info['gpu_names'] = gpu_names

                print(f"Detected GPUs: {gpu_info.get('gpu_names', ['Unknown'])}")
                return gpu_info

            except Exception as e:
                print(f"Error getting GPU info: {e}")
                return {}

        def _parse_gpu_info(self):
            """
            Parse GPU information to identify vendor and model
            """
            gpu_names = self.gpu_info.get('gpu_names', [])

            for gpu_name in gpu_names:
                gpu_name_lower = gpu_name.lower()

                # Skip integrated graphics (we want dedicated GPUs)
                if any(term in gpu_name_lower for term in ['intel(r) uhd', 'intel(r) iris', 'amd radeon(tm) graphics', 'vega']):
                    continue

                # NVIDIA GPU detection
                if 'nvidia' in gpu_name_lower or 'geforce' in gpu_name_lower or 'gtx' in gpu_name_lower or 'rtx' in gpu_name_lower:
                    self.gpu_vendor = 'NVIDIA'
                    self.gpu_model = gpu_name
                    self.detected_gpu = gpu_name
                    print(f"✅ NVIDIA GPU detected: {gpu_name}")
                    return True

                # AMD GPU detection
                elif 'amd' in gpu_name_lower or 'radeon' in gpu_name_lower or 'rx ' in gpu_name_lower:
                    # Skip integrated Vega graphics
                    if 'vega' not in gpu_name_lower or 'rx' in gpu_name_lower:
                        self.gpu_vendor = 'AMD'
                        self.gpu_model = gpu_name
                        self.detected_gpu = gpu_name
                        print(f"✅ AMD GPU detected: {gpu_name}")
                        return True

                # Intel dedicated GPU detection (Arc series)
                elif 'intel' in gpu_name_lower and ('arc' in gpu_name_lower or 'xe' in gpu_name_lower):
                    # Only dedicated Intel GPUs (Arc series)
                    if 'arc' in gpu_name_lower:
                        self.gpu_vendor = 'Intel'
                        self.gpu_model = gpu_name
                        self.detected_gpu = gpu_name
                        print(f"✅ Intel dedicated GPU detected: {gpu_name}")
                        return True

            print("⚠️  No compatible dedicated GPU found")
            return False

        def _check_performance_caps(self):
            """
            Check if GPU meets performance requirements and caps
            NVIDIA: Cannot exceed GTX 1080 performance
            AMD: Cannot go below RX 560 performance
            """
            print(f"Checking performance caps for {self.gpu_vendor} GPU...")

            if self.gpu_vendor == 'NVIDIA':
                self._check_nvidia_caps()
            elif self.gpu_vendor == 'AMD':
                self._check_amd_caps()
            elif self.gpu_vendor == 'Intel':
                self._check_intel_caps()

        def _check_nvidia_caps(self):
            """
            Check NVIDIA GPU against GTX 1080 performance cap
            """
            gpu_model_lower = self.gpu_model.lower()

            # NVIDIA GPU performance hierarchy (relative to GTX 1080 = 100%)
            nvidia_performance_db = {
                # RTX 40 Series (Above cap - will be limited)
                'rtx 4090': 200, 'rtx 4080': 170, 'rtx 4070 ti': 140, 'rtx 4070': 120, 'rtx 4060 ti': 105,
                'rtx 4060': 90,

                # RTX 30 Series (Above cap - will be limited)
                'rtx 3090 ti': 180, 'rtx 3090': 170, 'rtx 3080 ti': 150, 'rtx 3080': 140, 'rtx 3070 ti': 120,
                'rtx 3070': 110, 'rtx 3060 ti': 105, 'rtx 3060': 85,

                # RTX 20 Series
                'rtx 2080 ti': 130, 'rtx 2080 super': 110, 'rtx 2080': 105, 'rtx 2070 super': 100,
                'rtx 2070': 95, 'rtx 2060 super': 85, 'rtx 2060': 75,

                # GTX 16 Series
                'gtx 1660 ti': 70, 'gtx 1660 super': 68, 'gtx 1660': 60, 'gtx 1650 super': 50, 'gtx 1650': 40,

                # GTX 10 Series (Reference point)
                'gtx 1080 ti': 130, 'gtx 1080': 100, 'gtx 1070 ti': 85, 'gtx 1070': 80, 'gtx 1060': 60,
                'gtx 1050 ti': 40, 'gtx 1050': 30
            }

            # Find matching GPU
            detected_performance = 0
            for gpu_key, performance in nvidia_performance_db.items():
                if gpu_key in gpu_model_lower:
                    detected_performance = performance
                    break

            if detected_performance == 0:
                print(f"⚠️  Unknown NVIDIA GPU model, assuming GTX 1080 equivalent")
                detected_performance = 100

            # Apply GTX 1080 cap (100% max)
            if detected_performance > 100:
                print(f"🔒 GPU performance ({detected_performance}%) exceeds GTX 1080 cap, limiting to GTX 1080 level")
                self.performance_tier = 'capped_high'
                self.is_within_caps = True
            else:
                print(f"✅ GPU performance ({detected_performance}%) within acceptable range")
                self.performance_tier = 'acceptable'
                self.is_within_caps = True

        def _check_amd_caps(self):
            """
            Check AMD GPU against RX 560 minimum performance requirement
            """
            gpu_model_lower = self.gpu_model.lower()

            # AMD GPU performance hierarchy (relative to RX 560 = 100%)
            amd_performance_db = {
                # RX 7000 Series (RDNA 3)
                'rx 7900 xtx': 400, 'rx 7900 xt': 350, 'rx 7800 xt': 280, 'rx 7700 xt': 220, 'rx 7600': 150,

                # RX 6000 Series (RDNA 2)
                'rx 6950 xt': 320, 'rx 6900 xt': 300, 'rx 6800 xt': 280, 'rx 6800': 250, 'rx 6750 xt': 220,
                'rx 6700 xt': 200, 'rx 6650 xt': 180, 'rx 6600 xt': 160, 'rx 6600': 140, 'rx 6500 xt': 80,
                'rx 6400': 60,

                # RX 5000 Series (RDNA 1)
                'rx 5700 xt': 180, 'rx 5700': 160, 'rx 5600 xt': 140, 'rx 5500 xt': 110,

                # RX 500 Series (Polaris)
                'rx 590': 130, 'rx 580': 120, 'rx 570': 110, 'rx 560': 100, 'rx 550': 70
            }

            # Find matching GPU
            detected_performance = 0
            for gpu_key, performance in amd_performance_db.items():
                if gpu_key in gpu_model_lower:
                    detected_performance = performance
                    break

            if detected_performance == 0:
                print(f"⚠️  Unknown AMD GPU model, assuming RX 560 equivalent")
                detected_performance = 100

            # Apply RX 560 minimum requirement (100% min)
            if detected_performance < 100:
                print(f"❌ GPU performance ({detected_performance}%) below RX 560 minimum requirement")
                self.performance_tier = 'below_minimum'
                self.is_within_caps = False
            else:
                print(f"✅ GPU performance ({detected_performance}%) meets RX 560 minimum requirement")
                self.performance_tier = 'acceptable'
                self.is_within_caps = True

        def _check_intel_caps(self):
            """
            Check Intel dedicated GPU performance (Arc series)
            """
            gpu_model_lower = self.gpu_model.lower()

            # Intel Arc GPU performance hierarchy (relative to RX 560 = 100%)
            intel_performance_db = {
                # Arc A-Series
                'arc a770': 180, 'arc a750': 160, 'arc a580': 130, 'arc a380': 110, 'arc a310': 90
            }

            # Find matching GPU
            detected_performance = 0
            for gpu_key, performance in intel_performance_db.items():
                if gpu_key in gpu_model_lower:
                    detected_performance = performance
                    break

            if detected_performance == 0:
                print(f"⚠️  Unknown Intel GPU model, assuming Arc A380 equivalent")
                detected_performance = 110

            # Intel Arc GPUs are generally acceptable
            if detected_performance >= 100:
                print(f"✅ Intel GPU performance ({detected_performance}%) acceptable")
                self.performance_tier = 'acceptable'
                self.is_within_caps = True
            else:
                print(f"⚠️  Intel GPU performance ({detected_performance}%) may be limited")
                self.performance_tier = 'limited'
                self.is_within_caps = True

        def _configure_renpy_for_gpu(self):
            """
            Configure Ren'Py settings based on detected GPU and performance tier
            """
            if not self.is_within_caps:
                print("❌ GPU does not meet minimum requirements, using safe fallback settings")
                self._apply_fallback_settings()
                return

            print(f"Configuring Ren'Py for {self.gpu_vendor} GPU...")

            if self.gpu_vendor == 'NVIDIA':
                self._configure_nvidia_settings()
            elif self.gpu_vendor == 'AMD':
                self._configure_amd_settings()
            elif self.gpu_vendor == 'Intel':
                self._configure_intel_settings()

        def _configure_nvidia_settings(self):
            """
            Configure optimal settings for NVIDIA GPUs
            """
            try:
                if self.performance_tier == 'capped_high':
                    # High-end GPU capped to GTX 1080 level
                    settings = {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 4096,
                        'anisotropic_filtering': True,
                        'framerate_limit': 60,
                        'quality_preset': 'high_capped'
                    }
                    print("🔒 Applying GTX 1080-capped settings for high-end NVIDIA GPU")

                else:
                    # Standard NVIDIA settings
                    settings = {
                        'renderer': 'gl2',
                        'vsync': True,
                        'texture_scaling': 'linear',
                        'max_texture_size': 2048,
                        'anisotropic_filtering': True,
                        'framerate_limit': 60,
                        'quality_preset': 'standard'
                    }
                    print("✅ Applying standard NVIDIA GPU settings")

                self._apply_renpy_settings(settings)

                # NVIDIA-specific optimizations
                renpy.config.gl_vendor = "NVIDIA"
                renpy.config.gl_debug = False
                renpy.config.gl_framerate = settings['framerate_limit']

            except Exception as e:
                print(f"Error configuring NVIDIA settings: {e}")

        def _configure_amd_settings(self):
            """
            Configure optimal settings for AMD GPUs
            """
            try:
                settings = {
                    'renderer': 'gl2',
                    'vsync': True,
                    'texture_scaling': 'linear',
                    'max_texture_size': 2048,
                    'anisotropic_filtering': True,
                    'framerate_limit': 60,
                    'quality_preset': 'standard'
                }

                print("✅ Applying standard AMD GPU settings")
                self._apply_renpy_settings(settings)

                # AMD-specific optimizations
                renpy.config.gl_vendor = "AMD"
                renpy.config.gl_debug = False
                renpy.config.gl_framerate = settings['framerate_limit']

            except Exception as e:
                print(f"Error configuring AMD settings: {e}")

        def _configure_intel_settings(self):
            """
            Configure optimal settings for Intel Arc GPUs
            """
            try:
                settings = {
                    'renderer': 'gl2',
                    'vsync': True,
                    'texture_scaling': 'linear',
                    'max_texture_size': 2048,
                    'anisotropic_filtering': False,  # Intel Arc may have issues
                    'framerate_limit': 60,
                    'quality_preset': 'conservative'
                }

                print("✅ Applying Intel Arc GPU settings")
                self._apply_renpy_settings(settings)

                # Intel-specific optimizations
                renpy.config.gl_vendor = "Intel"
                renpy.config.gl_debug = True  # Enable debugging for Arc
                renpy.config.gl_framerate = settings['framerate_limit']

            except Exception as e:
                print(f"Error configuring Intel settings: {e}")

        def _apply_fallback_settings(self):
            """
            Apply safe fallback settings for unsupported GPUs
            """
            try:
                settings = {
                    'renderer': 'gl',
                    'vsync': True,
                    'texture_scaling': 'nearest',
                    'max_texture_size': 1024,
                    'anisotropic_filtering': False,
                    'framerate_limit': 30,
                    'quality_preset': 'minimum'
                }

                print("⚠️  Applying minimum fallback settings")
                self._apply_renpy_settings(settings)

            except Exception as e:
                print(f"Error applying fallback settings: {e}")

        def _apply_renpy_settings(self, settings):
            """
            Apply settings to Ren'Py configuration
            """
            try:
                # Core renderer settings
                renpy.config.renderer = settings.get('renderer', 'gl')
                renpy.config.gl_vsync = settings.get('vsync', True)

                # Texture settings
                if settings.get('texture_scaling') == 'linear':
                    renpy.config.gl_texture_scaling = True
                else:
                    renpy.config.gl_texture_scaling = False

                renpy.config.gl_maximum_texture_size = settings.get('max_texture_size', 1024)

                # Quality settings
                renpy.config.gl_anisotropic = settings.get('anisotropic_filtering', False)
                renpy.config.gl_framerate = settings.get('framerate_limit', 60)

                # Performance optimizations
                renpy.config.gl_resize = True
                renpy.config.gl_tearing = False

                self.recommended_settings = settings
                print(f"✅ Applied {settings['quality_preset']} quality settings")

            except Exception as e:
                print(f"Error applying Ren'Py settings: {e}")

        def _generate_gpu_report(self):
            """
            Generate comprehensive GPU detection and configuration report
            """
            print(f"\n{'='*70}")
            print("DEDICATED GPU DETECTION AND CONFIGURATION REPORT")
            print(f"{'='*70}")

            # GPU Information
            print(f"Detected GPU: {self.detected_gpu}")
            print(f"GPU Vendor: {self.gpu_vendor}")
            print(f"Performance Tier: {self.performance_tier}")
            print(f"Within Performance Caps: {'✅ Yes' if self.is_within_caps else '❌ No'}")

            # Performance Caps Information
            if self.gpu_vendor == 'NVIDIA':
                print(f"NVIDIA Performance Cap: GTX 1080 maximum")
                if self.performance_tier == 'capped_high':
                    print("🔒 GPU performance limited to GTX 1080 level")
            elif self.gpu_vendor == 'AMD':
                print(f"AMD Performance Requirement: RX 560 minimum")
                if not self.is_within_caps:
                    print("❌ GPU does not meet RX 560 minimum requirement")
            elif self.gpu_vendor == 'Intel':
                print(f"Intel Arc GPU: Generally acceptable performance")

            # Applied Settings
            if self.recommended_settings:
                print(f"\nApplied Ren'Py Settings:")
                for key, value in self.recommended_settings.items():
                    print(f"  {key}: {value}")

            # Recommendations
            print(f"\nRecommendations:")
            if self.performance_tier == 'capped_high':
                print("  • GPU performance is artificially limited to GTX 1080 level")
                print("  • Consider using a GTX 1080 or lower for optimal experience")
            elif self.performance_tier == 'below_minimum':
                print("  • GPU does not meet minimum requirements")
                print("  • Consider upgrading to AMD RX 560 or better")
                print("  • Performance may be severely limited")
            elif self.performance_tier == 'acceptable':
                print("  • GPU meets all requirements")
                print("  • Optimal performance expected")
            elif self.performance_tier == 'limited':
                print("  • GPU performance may be limited")
                print("  • Monitor performance and adjust settings if needed")

            print(f"{'='*70}")

    # Initialize GPU detector
    gpu_detector = DedicatedGPUDetector()

    def detect_and_configure_dedicated_gpu():
        """
        Main function to detect and configure dedicated GPU
        """
        return gpu_detector.detect_and_configure_gpu()

    def get_gpu_performance_info():
        """
        Get current GPU performance information
        """
        return {
            'gpu_vendor': gpu_detector.gpu_vendor,
            'gpu_model': gpu_detector.gpu_model,
            'performance_tier': gpu_detector.performance_tier,
            'is_within_caps': gpu_detector.is_within_caps,
            'recommended_settings': gpu_detector.recommended_settings
        }

# Automatically run GPU detection at startup
init:
    python:
        try:
            print("Starting dedicated GPU detection and configuration...")
            detect_and_configure_dedicated_gpu()
        except Exception as e:
            print(f"Error in dedicated GPU detection: {e}")

# Manual GPU detection label for testing
label test_dedicated_gpu_detection:
    "Testing dedicated GPU detection and configuration..."

    python:
        detect_and_configure_dedicated_gpu()

    "Check the console for detailed GPU detection results!"
    return

# GPU performance check label
label check_gpu_performance:
    "Checking current GPU performance configuration..."

    python:
        gpu_info = get_gpu_performance_info()

        if gpu_info['gpu_vendor']:
            narrator(f"GPU Vendor: {gpu_info['gpu_vendor']}")
            narrator(f"GPU Model: {gpu_info['gpu_model']}")
            narrator(f"Performance Tier: {gpu_info['performance_tier']}")
            narrator(f"Within Caps: {'Yes' if gpu_info['is_within_caps'] else 'No'}")

            if gpu_info['recommended_settings']:
                narrator("Applied Settings:")
                for key, value in gpu_info['recommended_settings'].items():
                    narrator(f"  {key}: {value}")
        else:
            narrator("No dedicated GPU detected or configured")

    "GPU performance check complete!"
    return