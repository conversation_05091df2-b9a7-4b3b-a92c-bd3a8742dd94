﻿# The script of the game goes in this file.

# The game starts here.

label start:

    # First engage anti-cheat with custom UI (safe initialization)
    call initialize_anticheat_safe

    # Check if user is banned
    if getattr(persistent, 'anticheat_banned', False):
        $ ban_time_remaining = int((getattr(persistent, 'anticheat_ban_timestamp', 0) + ANTICHEAT_CONFIG['ban_duration'] - time.time()) / 86400)
        call screen anticheat_ban_screen(reason=getattr(persistent, 'anticheat_ban_reason', 'Unknown violation'), days_remaining=ban_time_remaining)
        return

    # Check legal compliance (safe)
    call check_legal_compliance_safe

    # Show anti-cheat status in developer mode (safe)
    if config.developer:
        show screen safe_anticheat_status

    # Enter the game engine interface with enhanced game selection (safe)
    jump enhanced_game_selection_safe

label main_story:

    # add a file (named either "starry sky.jpg") to the
    # images directory to show it.

    scene starry sky

    # This shows a character sprite. A placeholder is used, but you can
    # replace it by adding a file named "netcode.png" to the images
    # directory.

    hide netcode

    # These display lines of dialogue.

    "{i}Congratulation! demo patch is installed!{/i}"

    "welcome to the vn"

    # Enhanced game selection with anti-cheat integration (line 56 area)
    # This section is now handled by the enhanced_game_selection label
    # which provides the game engine UI with anti-cheat notifications

    # Redirect to Universal Game Router for game selection
    "🎮 Welcome to the visual novel experience!"
    "Use the Universal Game Router to select and play games."

    # Jump to the Universal Game Router
    jump safe_game_launcher

    # This ends the game.

    return
