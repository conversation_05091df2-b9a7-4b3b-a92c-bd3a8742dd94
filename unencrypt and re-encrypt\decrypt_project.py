#!/usr/bin/env python3
"""
Project Decryption Script
Decrypts all encrypted game files for development or distribution
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path
from encryption_core import encryption_engine
from resource_manager import SecureResourceManager

class ProjectDecryptor:
    """Handles complete project decryption"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.config_path = self.project_path / '.vscode' / 'encryption_config.json'
        self.config = {}
        
    def load_config(self):
        """Load encryption configuration"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    self.config = json.load(f)
                return True
            else:
                print("No encryption configuration found")
                return False
                
        except Exception as e:
            print(f"Failed to load configuration: {e}")
            return False
    
    def generate_project_password(self):
        """Generate the same project password used for encryption"""
        project_name = self.config.get('project_name', 'netcode_protogen')
        return encryption_engine.generate_project_key(project_name)
    
    def verify_password(self, password):
        """Verify password against stored hash"""
        import hashlib
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        stored_hash = self.config.get('password_hash')
        
        if stored_hash and password_hash == stored_hash:
            return True
        else:
            print("Password verification failed")
            return False
    
    def decrypt_file_category(self, category_files, password):
        """Decrypt files of a specific category"""
        decrypted_files = []
        
        for file_info in category_files:
            encrypted_path = self.project_path / file_info['encrypted']
            
            if not encrypted_path.exists():
                print(f"  Missing encrypted file: {file_info['encrypted']}")
                continue
            
            try:
                print(f"  Decrypting: {file_info['encrypted']}")
                
                # Determine output path (remove .enc extension)
                if encrypted_path.name.endswith('.enc'):
                    output_path = encrypted_path.with_suffix('')
                else:
                    output_path = encrypted_path.with_suffix('.dec')
                
                decrypted_path = encryption_engine.decrypt_file(
                    str(encrypted_path),
                    password,
                    str(output_path)
                )
                
                if decrypted_path:
                    decrypted_files.append({
                        'encrypted': file_info['encrypted'],
                        'decrypted': str(Path(decrypted_path).relative_to(self.project_path)),
                        'category': file_info['category'],
                        'timestamp': time.time()
                    })
                
            except Exception as e:
                print(f"    Error decrypting {encrypted_path}: {e}")
        
        return decrypted_files
    
    def decrypt_project(self, keep_encrypted=False, password=None):
        """Decrypt entire project"""
        try:
            print("Starting project decryption...")
            print(f"Project: {self.project_path}")
            
            # Load configuration
            if not self.load_config():
                return False
            
            # Generate or use provided password
            if not password:
                password = self.generate_project_password()
            
            # Verify password
            if not self.verify_password(password):
                return False
            
            print(f"Using password: {password[:8]}...")
            
            # Get encrypted files from config
            encrypted_files = self.config.get('files_encrypted', [])
            
            if not encrypted_files:
                print("No encrypted files found in configuration")
                return False
            
            # Group files by category
            files_by_category = {}
            for file_info in encrypted_files:
                category = file_info.get('category', 'unknown')
                if category not in files_by_category:
                    files_by_category[category] = []
                files_by_category[category].append(file_info)
            
            # Decrypt files by category
            all_decrypted_files = []
            
            for category, category_files in files_by_category.items():
                print(f"Decrypting {category} files...")
                decrypted_files = self.decrypt_file_category(category_files, password)
                all_decrypted_files.extend(decrypted_files)
                
                print(f"  {category}: {len(decrypted_files)} files decrypted")
            
            # Remove encrypted files if requested
            if not keep_encrypted:
                print("Removing encrypted files...")
                for file_info in encrypted_files:
                    encrypted_path = self.project_path / file_info['encrypted']
                    if encrypted_path.exists():
                        encrypted_path.unlink()
                        print(f"  Removed: {file_info['encrypted']}")
            
            print(f"\nDecryption completed successfully!")
            print(f"Files decrypted: {len(all_decrypted_files)}")
            
            return True
            
        except Exception as e:
            print(f"Project decryption failed: {e}")
            return False
    
    def decrypt_specific_files(self, file_patterns, password=None):
        """Decrypt specific files matching patterns"""
        try:
            if not self.load_config():
                return False
            
            if not password:
                password = self.generate_project_password()
            
            if not self.verify_password(password):
                return False
            
            encrypted_files = self.config.get('files_encrypted', [])
            matching_files = []
            
            # Find matching files
            for pattern in file_patterns:
                for file_info in encrypted_files:
                    if pattern in file_info['encrypted'] or pattern in file_info['original']:
                        matching_files.append(file_info)
            
            if not matching_files:
                print("No matching files found")
                return False
            
            print(f"Decrypting {len(matching_files)} matching files...")
            
            decrypted_files = self.decrypt_file_category(matching_files, password)
            
            print(f"Successfully decrypted {len(decrypted_files)} files")
            return True
            
        except Exception as e:
            print(f"Specific file decryption failed: {e}")
            return False
    
    def list_encrypted_files(self):
        """List all encrypted files"""
        try:
            if not self.load_config():
                return False
            
            encrypted_files = self.config.get('files_encrypted', [])
            
            if not encrypted_files:
                print("No encrypted files found")
                return True
            
            print(f"Encrypted files ({len(encrypted_files)} total):")
            print("-" * 60)
            
            # Group by category
            files_by_category = {}
            for file_info in encrypted_files:
                category = file_info.get('category', 'unknown')
                if category not in files_by_category:
                    files_by_category[category] = []
                files_by_category[category].append(file_info)
            
            for category, category_files in files_by_category.items():
                print(f"\n{category.upper()} ({len(category_files)} files):")
                for file_info in category_files:
                    encrypted_path = self.project_path / file_info['encrypted']
                    status = "✓" if encrypted_path.exists() else "✗"
                    size_kb = file_info.get('size', 0) // 1024
                    print(f"  {status} {file_info['encrypted']} ({size_kb} KB)")
            
            return True
            
        except Exception as e:
            print(f"Failed to list encrypted files: {e}")
            return False
    
    def verify_encrypted_files(self):
        """Verify all encrypted files exist and are valid"""
        try:
            if not self.load_config():
                return False
            
            encrypted_files = self.config.get('files_encrypted', [])
            
            print("Verifying encrypted files...")
            
            valid_count = 0
            missing_count = 0
            invalid_count = 0
            
            for file_info in encrypted_files:
                encrypted_path = self.project_path / file_info['encrypted']
                
                if not encrypted_path.exists():
                    missing_count += 1
                    print(f"  ✗ Missing: {file_info['encrypted']}")
                    continue
                
                # Try to read the file header to verify it's encrypted
                try:
                    with open(encrypted_path, 'r') as f:
                        content = f.read(100)
                        if content.startswith('eyJ') or content.startswith('{'):  # Base64 or JSON
                            valid_count += 1
                            print(f"  ✓ Valid: {file_info['encrypted']}")
                        else:
                            invalid_count += 1
                            print(f"  ? Invalid format: {file_info['encrypted']}")
                except:
                    invalid_count += 1
                    print(f"  ✗ Unreadable: {file_info['encrypted']}")
            
            print(f"\nVerification results:")
            print(f"  Valid: {valid_count}")
            print(f"  Missing: {missing_count}")
            print(f"  Invalid: {invalid_count}")
            print(f"  Total: {len(encrypted_files)}")
            
            return missing_count == 0 and invalid_count == 0
            
        except Exception as e:
            print(f"Verification failed: {e}")
            return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Decrypt Visual Novel Project')
    parser.add_argument('project_path', help='Path to project directory')
    parser.add_argument('--keep-encrypted', action='store_true', 
                       help='Keep encrypted files after decryption')
    parser.add_argument('--password', help='Custom decryption password')
    parser.add_argument('--files', nargs='+', help='Decrypt specific files matching patterns')
    parser.add_argument('--list', action='store_true', help='List encrypted files')
    parser.add_argument('--verify', action='store_true', help='Verify encrypted files')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"Project path does not exist: {args.project_path}")
        sys.exit(1)
    
    decryptor = ProjectDecryptor(args.project_path)
    
    if args.list:
        success = decryptor.list_encrypted_files()
    elif args.verify:
        success = decryptor.verify_encrypted_files()
    elif args.files:
        success = decryptor.decrypt_specific_files(args.files, args.password)
    else:
        success = decryptor.decrypt_project(args.keep_encrypted, args.password)
    
    if success:
        print("Operation completed successfully")
        sys.exit(0)
    else:
        print("Operation failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
