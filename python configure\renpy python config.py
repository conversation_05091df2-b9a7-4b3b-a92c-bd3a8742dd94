#!/usr/bin/env python3
"""
Ren'Py Python Configuration Tool with Version Compatibility Bridge
Configures Python environment for Ren'Py without modifying existing game files
Updated to support newer Python versions (3.10+, 3.11+, 3.12+) while maintaining
compatibility with the original Python 3.9.10 engine
"""

import os
import sys
import json
import platform
import subprocess
import re
import shutil
from pathlib import Path

# Import compatibility modules
try:
    from compatibility_bridge import compatibility_bridge, compatibility_warning
    from version_adapter import RenPyVersionAdapter
    COMPATIBILITY_AVAILABLE = True
except ImportError:
    COMPATIBILITY_AVAILABLE = False
    print("Warning: Compatibility modules not available, running in basic mode")

class RenPyPythonConfigurator:
    """Handles Python configuration for Ren'Py projects"""

    def __init__(self, project_dir=None):
        self.project_dir = Path(project_dir) if project_dir else Path.cwd().parent
        self.game_dir = self.project_dir / "game"
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "python_config.json"
        self.supported_versions = ["3.12", "3.11", "3.10", "3.9", "3.8"]

    def detect_python_installations(self):
        """Detect available Python installations"""
        python_installations = []

        # Common Python executable names to check
        python_names = ["python", "python3", "py"]

        # Add version-specific names
        for version in self.supported_versions:
            python_names.extend([f"python{version}", f"python{version.replace('.', '')}"])

        for python_name in python_names:
            try:
                # Try to find the executable
                python_path = shutil.which(python_name)
                if python_path:
                    # Get version info
                    result = subprocess.run([python_path, "--version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version_info = result.stdout.strip()
                        python_installations.append({
                            "name": python_name,
                            "path": python_path,
                            "version": version_info
                        })
            except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                continue

        # Remove duplicates based on path
        unique_installations = []
        seen_paths = set()
        for install in python_installations:
            if install["path"] not in seen_paths:
                unique_installations.append(install)
                seen_paths.add(install["path"])

        return unique_installations
        
    def detect_renpy_environment(self):
        """Detect Ren'Py environment and Python version"""
        print("Detecting Ren'Py environment...")
        
        # Check if we're in a Ren'Py project
        if not self.game_dir.exists():
            print(f"Warning: Game directory not found at {self.game_dir}")
        
        # Try to detect Ren'Py version
        try:
            # Look for version info in common locations
            version_file = self.project_dir / "renpy" / "version.py"
            if version_file.exists():
                with open(version_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    version_match = re.search(r'version\s*=\s*[\'"](.+?)[\'"]', content)
                    if version_match:
                        return version_match.group(1)
            
            # Alternative method - check game/options.rpy
            options_file = self.game_dir / "options.rpy"
            if options_file.exists():
                with open(options_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Look for config.version
                    version_match = re.search(r'config\.version\s*=\s*[\'"](.+?)[\'"]', content)
                    if version_match:
                        return f"Ren'Py (config.version: {version_match.group(1)})"
            
            return "Ren'Py (version unknown)"
        except Exception as e:
            print(f"Error detecting Ren'Py version: {e}")
            return "Ren'Py (detection failed)"
    
    def save_configuration(self, config_data):
        """Save configuration to JSON file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)
            print(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def load_configuration(self):
        """Load configuration from JSON file"""
        if not self.config_file.exists():
            return self.create_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return self.create_default_config()
    
    def create_default_config(self):
        """Create default configuration"""
        python_installations = self.detect_python_installations()
        current_python = {
            "version": platform.python_version(),
            "executable": sys.executable,
            "version_info": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        }

        return {
            "project_name": "netcode_protogen_game",
            "python_version": platform.python_version(),
            "current_python": current_python,
            "available_python_installations": python_installations,
            "renpy_detected": self.detect_renpy_environment(),
            "system_info": {
                "os": platform.system(),
                "platform": platform.platform(),
                "processor": platform.processor(),
                "architecture": platform.architecture()[0]
            },
            "python_paths": {
                "site_packages": self.get_site_packages_path(),
                "executable": sys.executable,
                "python_path": sys.path[:5]  # First 5 paths for brevity
            },
            "modules_available": self.check_required_modules(),
            "compatibility": self.check_version_compatibility(),
            "last_updated": platform.python_version()
        }
    
    def get_site_packages_path(self):
        """Get site-packages directory path"""
        for path in sys.path:
            if "site-packages" in path:
                return path
        return "Not found"
    
    def check_required_modules(self):
        """Check if required modules are available"""
        required_modules = [
            "cryptography", "pillow", "requests",
            "numpy", "pygame", "future", "ssl", "json", "urllib"
        ]

        # Additional modules for newer Python versions
        newer_modules = [
            "asyncio", "pathlib", "typing", "dataclasses"
        ]

        available_modules = {}

        # Check required modules
        for module in required_modules:
            try:
                __import__(module)
                available_modules[module] = {"available": True, "required": True}
            except ImportError:
                available_modules[module] = {"available": False, "required": True}

        # Check newer modules
        for module in newer_modules:
            try:
                __import__(module)
                available_modules[module] = {"available": True, "required": False}
            except ImportError:
                available_modules[module] = {"available": False, "required": False}

        return available_modules

    def check_version_compatibility(self):
        """Check Python version compatibility with Ren'Py"""
        current_version = sys.version_info
        version_str = f"{current_version.major}.{current_version.minor}"

        compatibility_info = {
            "current_version": version_str,
            "is_supported": version_str in self.supported_versions,
            "recommendations": []
        }

        if current_version.major < 3:
            compatibility_info["recommendations"].append("Python 2 is not supported. Please upgrade to Python 3.8+")
        elif current_version.minor < 8:
            compatibility_info["recommendations"].append("Python 3.7 and below may have compatibility issues. Consider upgrading to 3.8+")
        elif current_version.minor >= 12:
            compatibility_info["recommendations"].append("Python 3.12+ is supported but may require updated dependencies")

        return compatibility_info
    
    def configure_python(self):
        """Configure Python for Ren'Py project"""
        print("Configuring Python for Ren'Py project...")
        print(f"Current Python version: {platform.python_version()}")

        # Detect available Python installations
        installations = self.detect_python_installations()
        print(f"Found {len(installations)} Python installation(s)")
        for install in installations:
            print(f"  - {install['name']}: {install['version']} at {install['path']}")

        # Load or create configuration
        config = self.load_configuration()

        # Update configuration with current info
        config["python_version"] = platform.python_version()
        config["current_python"] = {
            "version": platform.python_version(),
            "executable": sys.executable,
            "version_info": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        }
        config["available_python_installations"] = installations
        config["renpy_detected"] = self.detect_renpy_environment()
        config["system_info"]["os"] = platform.system()
        config["system_info"]["platform"] = platform.platform()
        config["system_info"]["architecture"] = platform.architecture()[0]
        config["modules_available"] = self.check_required_modules()
        config["compatibility"] = self.check_version_compatibility()
        config["last_updated"] = platform.python_version()

        # Save updated configuration
        self.save_configuration(config)

        # Create Python path configuration file
        self.create_path_config()

        # Print compatibility information
        compatibility = config["compatibility"]
        print(f"\nCompatibility Check:")
        print(f"Current version: {compatibility['current_version']}")
        print(f"Supported: {'Yes' if compatibility['is_supported'] else 'No'}")
        if compatibility['recommendations']:
            print("Recommendations:")
            for rec in compatibility['recommendations']:
                print(f"  - {rec}")

        print("\nPython configuration complete!")
        return True
    
    def create_path_config(self):
        """Create Python path configuration file"""
        path_config = self.config_dir / "python_path.py"

        content = f"""# Python Path Configuration for Ren'Py
# Generated by RenPyPythonConfigurator
# Compatible with Python 3.8+ including 3.10, 3.11, 3.12+

import sys
import os
from pathlib import Path

# Ensure compatibility with newer Python versions
try:
    # For Python 3.8+
    from typing import List, Optional
except ImportError:
    # Fallback for older versions
    pass

def add_project_paths():
    \"\"\"Add project-specific paths to sys.path\"\"\"
    try:
        # Get base directory (works with both older and newer Ren'Py versions)
        if 'config' in globals() and hasattr(config, 'basedir'):
            base_dir = config.basedir
        else:
            # Fallback method
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Define custom paths
        custom_paths = [
            os.path.join(base_dir, "python configure"),
            os.path.join(base_dir, ".vscode"),
            os.path.join(base_dir, "anti-cheat"),
            os.path.join(base_dir, "platform_detection"),
            os.path.join(base_dir, "options"),
            os.path.join(base_dir, "PROJECT_LINKER")
        ]

        # Add paths to sys.path if they exist
        for path in custom_paths:
            if os.path.exists(path) and path not in sys.path:
                sys.path.insert(0, path)

        return True
    except Exception as e:
        print(f"Warning: Could not configure Python paths: {{e}}")
        return False

# Auto-configure paths when imported
add_project_paths()

# Version compatibility check
def check_python_compatibility():
    \"\"\"Check if current Python version is compatible\"\"\"
    version_info = sys.version_info
    if version_info.major >= 3 and version_info.minor >= 8:
        return True
    else:
        print(f"Warning: Python {{version_info.major}}.{{version_info.minor}} may have compatibility issues")
        return False

# Run compatibility check
check_python_compatibility()
"""

        try:
            with open(path_config, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Python path configuration created: {path_config}")
            return True
        except Exception as e:
            print(f"Error creating path configuration: {e}")
            return False

def main():
    """Main function with compatibility bridge support"""
    print("=" * 70)
    print("Ren'Py Python Configuration Tool with Compatibility Bridge")
    print("Bridges Python 3.9.10 (engine) with newer versions (3.10+, 3.11+, 3.12+)")
    print("=" * 70)

    # Get project directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(f"Project directory: {project_dir}")

    # Initialize compatibility if available
    if COMPATIBILITY_AVAILABLE:
        print("\n🔗 Compatibility Bridge: Active")
        version_adapter = RenPyVersionAdapter(project_dir)

        # Generate and display compatibility report
        print("\n" + "=" * 50)
        print("COMPATIBILITY ANALYSIS")
        print("=" * 50)
        report = version_adapter.generate_compatibility_report()
        print(report)

        # Save adapted configuration
        version_adapter.save_adapted_config()
    else:
        print("\n⚠️  Compatibility Bridge: Not available (basic mode)")

    # Create configurator
    configurator = RenPyPythonConfigurator(project_dir)

    # Configure Python
    success = configurator.configure_python()

    if success:
        print("\n" + "=" * 70)
        print("CONFIGURATION COMPLETE!")
        print("=" * 70)

        if COMPATIBILITY_AVAILABLE:
            print("🔗 COMPATIBILITY BRIDGE INTEGRATION:")
            print("Add this to your game/script.rpy file:")
            print()
            bridge_code = version_adapter.create_bridge_integration()
            print(bridge_code)
        else:
            print("📝 BASIC INTEGRATION:")
            print("Add this to your game/script.rpy file:")
            print()
            print("   init -999 python:")
            print("       import sys")
            print("       sys.path.insert(0, config.basedir + '/python configure')")
            print("       try:")
            print("           import python_path")
            print("       except ImportError:")
            print("           pass")

        print("\n📋 ADDITIONAL NOTES:")
        print("- Check 'python_config_adapted.json' for detailed compatibility info")
        print("- The bridge maintains backward compatibility with Python 3.9.10")
        print("- Newer Python features are enabled automatically when available")
        print("- Your Ren'Py engine will continue to work as before")
        print("=" * 70)
    else:
        print("\n❌ Configuration failed. Please check the error messages above.")
        return 1

    return 0

if __name__ == "__main__":
    main()