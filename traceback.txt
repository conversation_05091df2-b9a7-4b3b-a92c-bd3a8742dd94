﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/game_router.rpy", line 1723, in script call
    call screen game_selection(available_games)
  File "game/separate games/adventures of atlyss/adventures of atlyss.rpy", line 415, in script
    if chosen_love_interest == "sally":
  File "game/separate games/adventures of atlyss/adventures of atlyss.rpy", line 415, in <module>
    if chosen_love_interest == "sally":
       ^^^^^^^^^^^^^^^^^^^^            
NameError: name 'chosen_love_interest' is not defined. Did you mean: 'atlyss_chosen_love_interest'?

-- Full Traceback ------------------------------------------------------------

Traceback (most recent call last):
  File "game/game_router.rpy", line 1723, in script call
    call screen game_selection(available_games)
  File "game/separate games/adventures of atlyss/adventures of atlyss.rpy", line 415, in script
    if chosen_love_interest == "sally":
  File "renpy/ast.py", line 2162, in execute
    if renpy.python.py_eval(condition):
       ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^ 
  File "renpy/python.py", line 1383, in py_eval
    return py_eval_bytecode(code, globals, locals)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "renpy/python.py", line 1375, in py_eval_bytecode
    return eval(bytecode, globals, locals)
           ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "game/separate games/adventures of atlyss/adventures of atlyss.rpy", line 415, in <module>
    if chosen_love_interest == "sally":
       ^^^^^^^^^^^^^^^^^^^^            
NameError: name 'chosen_love_interest' is not defined. Did you mean: 'atlyss_chosen_love_interest'?

Windows-11-10.0.26100-SP0 AMD64
Ren'Py 8.4.0.25062210
netcode the protogen and more **********.123v
Fri Jul  4 00:33:10 2025
