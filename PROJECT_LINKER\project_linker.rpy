## Project Auto-Linker System
## Automatically maintains connections when files are moved or edited

init -100 python:
    import os
    import re
    import json
    import time
    import shutil
    from collections import defaultdict
    
    # Project linker configuration
    project_linker = {
        'enabled': True,
        'auto_scan': True,
        'backup_enabled': True,
        'link_map': {},
        'file_registry': {},
        'dependency_graph': defaultdict(set),
        'last_scan': 0,
        'scan_interval': 5.0,  # seconds
        'project_root': config.basedir,
        'game_dir': os.path.join(config.basedir, 'game')
    }
    
    # File type patterns and their link types
    link_patterns = {
        'images': {
            'extensions': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp'],
            'references': [r'image\s+(\w+)\s*=\s*["\']([^"\']+)["\']', r'scene\s+([^\s]+)', r'show\s+([^\s]+)'],
            'directories': ['images', 'gui']
        },
        'audio': {
            'extensions': ['.ogg', '.mp3', '.wav', '.opus'],
            'references': [r'play\s+music\s+["\']([^"\']+)["\']', r'play\s+sound\s+["\']([^"\']+)["\']', r'define\s+config\.main_menu_music\s*=\s*["\']([^"\']+)["\']'],
            'directories': ['audio', 'music']
        },
        'scripts': {
            'extensions': ['.rpy', '.rpyc'],
            'references': [r'jump\s+([^\s]+)', r'call\s+([^\s]+)', r'include\s+["\']([^"\']+)["\']'],
            'directories': ['game']
        },
        'fonts': {
            'extensions': ['.ttf', '.otf', '.woff'],
            'references': [r'define\s+gui\..*font\s*=\s*["\']([^"\']+)["\']'],
            'directories': ['fonts', 'gui']
        },
        'videos': {
            'extensions': ['.webm', '.mp4', '.ogv'],
            'references': [r'play\s+movie\s+["\']([^"\']+)["\']'],
            'directories': ['videos']
        }
    }

class ProjectLinker:
    """Manages automatic file linking and dependency tracking"""
    
    def __init__(self):
        self.enabled = True
        self.file_watchers = {}
        self.link_cache = {}
        self.backup_dir = os.path.join(project_linker['project_root'], '.project_backups')
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """Ensure backup directory exists"""
        try:
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
        except Exception as e:
            renpy.log("Failed to create backup directory: {}".format(str(e)))
    
    def scan_project_files(self):
        """Scan all project files and build dependency graph"""
        try:
            renpy.log("Starting project file scan...")
            
            # Clear existing data
            project_linker['file_registry'].clear()
            project_linker['dependency_graph'].clear()
            
            # Scan game directory
            for root, dirs, files in os.walk(project_linker['game_dir']):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, project_linker['project_root'])
                    
                    # Register file
                    self.register_file(relative_path, file_path)
                    
                    # Scan for dependencies if it's a script file
                    if file.endswith('.rpy'):
                        self.scan_file_dependencies(file_path, relative_path)
            
            project_linker['last_scan'] = time.time()
            renpy.log("Project scan completed. Found {} files".format(len(project_linker['file_registry'])))
            
        except Exception as e:
            renpy.log("Project scan failed: {}".format(str(e)))
    
    def register_file(self, relative_path, full_path):
        """Register a file in the project registry"""
        try:
            file_info = {
                'full_path': full_path,
                'relative_path': relative_path,
                'last_modified': os.path.getmtime(full_path) if os.path.exists(full_path) else 0,
                'file_type': self.detect_file_type(relative_path),
                'dependencies': set(),
                'dependents': set()
            }
            
            project_linker['file_registry'][relative_path] = file_info
            
        except Exception as e:
            renpy.log("Failed to register file {}: {}".format(relative_path, str(e)))
    
    def detect_file_type(self, file_path):
        """Detect file type based on extension and location"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        for file_type, config in link_patterns.items():
            if file_ext in config['extensions']:
                return file_type
        
        return 'other'
    
    def scan_file_dependencies(self, file_path, relative_path):
        """Scan a file for dependencies on other files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            dependencies = set()
            
            # Scan for each type of reference
            for file_type, config in link_patterns.items():
                for pattern in config['references']:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[-1]  # Take the last group (usually the file path)
                        
                        # Try to resolve the file path
                        resolved_path = self.resolve_file_reference(match, file_type)
                        if resolved_path:
                            dependencies.add(resolved_path)
            
            # Update registry
            if relative_path in project_linker['file_registry']:
                project_linker['file_registry'][relative_path]['dependencies'] = dependencies
            
            # Update dependency graph
            project_linker['dependency_graph'][relative_path] = dependencies
            
            # Update reverse dependencies
            for dep in dependencies:
                if dep in project_linker['file_registry']:
                    project_linker['file_registry'][dep]['dependents'].add(relative_path)
            
        except Exception as e:
            renpy.log("Failed to scan dependencies for {}: {}".format(file_path, str(e)))
    
    def resolve_file_reference(self, reference, file_type):
        """Resolve a file reference to an actual file path"""
        try:
            # Remove quotes and clean up the reference
            reference = reference.strip('\'"')
            
            # If it's already a relative path, use it
            if reference.startswith('game/') or reference.startswith('./'):
                potential_path = reference
            else:
                # Try to find the file in appropriate directories
                config = link_patterns.get(file_type, {})
                directories = config.get('directories', ['game'])
                
                for directory in directories:
                    potential_path = os.path.join('game', directory, reference)
                    if os.path.exists(os.path.join(project_linker['project_root'], potential_path)):
                        return potential_path
                
                # Try without extension
                for directory in directories:
                    for ext in config.get('extensions', []):
                        potential_path = os.path.join('game', directory, reference + ext)
                        if os.path.exists(os.path.join(project_linker['project_root'], potential_path)):
                            return potential_path
                
                # Try direct reference in game directory
                potential_path = os.path.join('game', reference)
            
            # Check if file exists
            full_path = os.path.join(project_linker['project_root'], potential_path)
            if os.path.exists(full_path):
                return potential_path
            
            return None
            
        except Exception as e:
            renpy.log("Failed to resolve reference {}: {}".format(reference, str(e)))
            return None
    
    def move_file(self, old_path, new_path):
        """Move a file and update all references"""
        try:
            old_relative = os.path.relpath(old_path, project_linker['project_root'])
            new_relative = os.path.relpath(new_path, project_linker['project_root'])
            
            renpy.log("Moving file: {} -> {}".format(old_relative, new_relative))
            
            # Create backup
            if project_linker['backup_enabled']:
                self.create_backup(old_path)
            
            # Move the actual file
            os.makedirs(os.path.dirname(new_path), exist_ok=True)
            shutil.move(old_path, new_path)
            
            # Update all references to this file
            self.update_file_references(old_relative, new_relative)
            
            # Update registry
            if old_relative in project_linker['file_registry']:
                file_info = project_linker['file_registry'][old_relative]
                file_info['full_path'] = new_path
                file_info['relative_path'] = new_relative
                project_linker['file_registry'][new_relative] = file_info
                del project_linker['file_registry'][old_relative]
            
            renpy.log("File moved successfully: {} -> {}".format(old_relative, new_relative))
            return True
            
        except Exception as e:
            renpy.log("Failed to move file {}: {}".format(old_path, str(e)))
            return False
    
    def update_file_references(self, old_path, new_path):
        """Update all references to a moved file"""
        try:
            # Find all files that reference the moved file
            referencing_files = []
            
            for file_path, file_info in project_linker['file_registry'].items():
                if old_path in file_info.get('dependencies', set()):
                    referencing_files.append(file_path)
            
            # Update each referencing file
            for ref_file in referencing_files:
                self.update_references_in_file(ref_file, old_path, new_path)
            
        except Exception as e:
            renpy.log("Failed to update file references: {}".format(str(e)))
    
    def update_references_in_file(self, file_path, old_ref, new_ref):
        """Update references within a specific file"""
        try:
            full_path = os.path.join(project_linker['project_root'], file_path)
            
            if not os.path.exists(full_path):
                return
            
            # Read file content
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create backup
            if project_linker['backup_enabled']:
                self.create_backup(full_path)
            
            # Update references
            old_name = os.path.splitext(os.path.basename(old_ref))[0]
            new_name = os.path.splitext(os.path.basename(new_ref))[0]
            
            # Replace various reference patterns
            patterns_to_replace = [
                (r'(["\'])' + re.escape(old_ref) + r'(["\'])', r'\1' + new_ref + r'\2'),
                (r'(["\'])' + re.escape(old_name) + r'(["\'])', r'\1' + new_name + r'\2'),
                (r'\b' + re.escape(old_name) + r'\b', new_name)
            ]
            
            modified = False
            for pattern, replacement in patterns_to_replace:
                new_content = re.sub(pattern, replacement, content)
                if new_content != content:
                    content = new_content
                    modified = True
            
            # Write updated content
            if modified:
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                renpy.log("Updated references in: {}".format(file_path))
            
        except Exception as e:
            renpy.log("Failed to update references in {}: {}".format(file_path, str(e)))
    
    def create_backup(self, file_path):
        """Create a backup of a file before modification"""
        try:
            if not project_linker['backup_enabled']:
                return
            
            relative_path = os.path.relpath(file_path, project_linker['project_root'])
            backup_path = os.path.join(self.backup_dir, relative_path + '.backup')
            
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            shutil.copy2(file_path, backup_path)
            
        except Exception as e:
            renpy.log("Failed to create backup for {}: {}".format(file_path, str(e)))
    
    def auto_link_new_file(self, file_path):
        """Automatically link a new file to the project"""
        try:
            relative_path = os.path.relpath(file_path, project_linker['project_root'])
            
            # Register the new file
            self.register_file(relative_path, file_path)
            
            # If it's a script file, scan for dependencies
            if file_path.endswith('.rpy'):
                self.scan_file_dependencies(file_path, relative_path)
            
            renpy.log("Auto-linked new file: {}".format(relative_path))
            
        except Exception as e:
            renpy.log("Failed to auto-link file {}: {}".format(file_path, str(e)))
    
    def validate_links(self):
        """Validate all file links and report broken references"""
        broken_links = []
        
        try:
            for file_path, file_info in project_linker['file_registry'].items():
                for dependency in file_info.get('dependencies', set()):
                    dep_full_path = os.path.join(project_linker['project_root'], dependency)
                    if not os.path.exists(dep_full_path):
                        broken_links.append((file_path, dependency))
            
            if broken_links:
                renpy.log("Found {} broken links".format(len(broken_links)))
                for source, target in broken_links:
                    renpy.log("Broken link: {} -> {}".format(source, target))
            
            return broken_links
            
        except Exception as e:
            renpy.log("Link validation failed: {}".format(str(e)))
            return []

# Initialize project linker
project_linker_instance = ProjectLinker()

# Auto-scan on startup
init python:
    if project_linker['auto_scan']:
        project_linker_instance.scan_project_files()

# Periodic monitoring
init python:
    def monitor_project_files():
        """Monitor project files for changes"""
        try:
            current_time = time.time()
            if current_time - project_linker['last_scan'] > project_linker['scan_interval']:
                # Quick check for new or modified files
                for root, dirs, files in os.walk(project_linker['game_dir']):
                    for file in files:
                        file_path = os.path.join(root, file)
                        relative_path = os.path.relpath(file_path, project_linker['project_root'])
                        
                        if relative_path not in project_linker['file_registry']:
                            # New file detected
                            project_linker_instance.auto_link_new_file(file_path)
                        else:
                            # Check if file was modified
                            file_info = project_linker['file_registry'][relative_path]
                            current_mtime = os.path.getmtime(file_path)
                            if current_mtime > file_info['last_modified']:
                                # File was modified, rescan dependencies
                                if file_path.endswith('.rpy'):
                                    project_linker_instance.scan_file_dependencies(file_path, relative_path)
                                file_info['last_modified'] = current_mtime
                
                project_linker['last_scan'] = current_time
        
        except Exception as e:
            renpy.log("Project monitoring error: {}".format(str(e)))
    
    # Add to periodic callbacks if enabled
    if project_linker['auto_scan']:
        config.periodic_callbacks.append(monitor_project_files)

# Project Linker Management Interface
screen project_linker_manager():

    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600

        vbox:
            spacing 20

            text "🔗 Project Auto-Linker Manager" size 24 xalign 0.5 color "#00ff00"

            # Status information
            frame:
                background "#2e2e2e"
                padding (20, 15)

                vbox:
                    spacing 10

                    text "System Status" size 18 color "#ffaa00"

                    hbox:
                        spacing 20

                        vbox:
                            spacing 5
                            text "Auto-Linking: {}".format("Enabled" if project_linker['enabled'] else "Disabled") size 14
                            text "Auto-Scan: {}".format("Enabled" if project_linker['auto_scan'] else "Disabled") size 14
                            text "Backups: {}".format("Enabled" if project_linker['backup_enabled'] else "Disabled") size 14

                        vbox:
                            spacing 5
                            text "Files Tracked: {}".format(len(project_linker['file_registry'])) size 14
                            text "Dependencies: {}".format(sum(len(deps) for deps in project_linker['dependency_graph'].values())) size 14
                            text "Last Scan: {}".format(time.strftime("%H:%M:%S", time.localtime(project_linker['last_scan']))) size 14

            # Control buttons
            hbox:
                spacing 15
                xalign 0.5

                textbutton "🔄 Rescan Project" action Function(project_linker_instance.scan_project_files) text_size 14
                textbutton "🔍 Validate Links" action Function(show_link_validation) text_size 14
                textbutton "📁 Move File" action Call("file_mover_interface") text_size 14
                textbutton "⚙️ Settings" action Call("linker_settings") text_size 14

            # File registry view
            frame:
                background "#1a1a1a"
                padding (15, 10)
                xfill True
                yfill True

                vbox:
                    spacing 10

                    text "File Registry" size 16 color "#00aaff"

                    viewport:
                        scrollbars "vertical"
                        mousewheel True

                        vbox:
                            spacing 5

                            for file_path, file_info in sorted(project_linker['file_registry'].items()):
                                hbox:
                                    spacing 10

                                    # File type icon
                                    if file_info['file_type'] == 'images':
                                        text "🖼️" size 16
                                    elif file_info['file_type'] == 'audio':
                                        text "🔊" size 16
                                    elif file_info['file_type'] == 'scripts':
                                        text "📜" size 16
                                    elif file_info['file_type'] == 'fonts':
                                        text "🔤" size 16
                                    elif file_info['file_type'] == 'videos':
                                        text "🎬" size 16
                                    else:
                                        text "📄" size 16

                                    vbox:
                                        spacing 2
                                        text file_path size 12 color "#ffffff"
                                        text "Dependencies: {} | Dependents: {}".format(
                                            len(file_info.get('dependencies', set())),
                                            len(file_info.get('dependents', set()))
                                        ) size 10 color "#888888"

            textbutton "Close" action Return() xalign 0.5

# File mover interface
label file_mover_interface:

    call screen file_mover

    return

screen file_mover():

    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 500

        vbox:
            spacing 20

            text "📁 Smart File Mover" size 20 xalign 0.5 color "#00ff00"

            text "Select a file to move and all references will be automatically updated:" size 14 xalign 0.5

            # File selection
            frame:
                background "#2e2e2e"
                padding (15, 10)

                vbox:
                    spacing 10

                    text "Select File to Move:" size 16 color "#ffaa00"

                    viewport:
                        scrollbars "vertical"
                        mousewheel True
                        xsize 550
                        ysize 200

                        vbox:
                            spacing 5

                            for file_path in sorted(project_linker['file_registry'].keys()):
                                textbutton file_path action SetScreenVariable("selected_file", file_path) text_size 12

            # Move controls
            if selected_file:
                frame:
                    background "#1a1a1a"
                    padding (15, 10)

                    vbox:
                        spacing 10

                        text "Selected: {}".format(selected_file) size 14 color "#ffffff"

                        hbox:
                            spacing 10

                            text "New Path:" size 14
                            input default selected_file value ScreenVariableInputValue("new_file_path") length 50

                        hbox:
                            spacing 15

                            textbutton "Move File" action Function(move_selected_file) text_size 14
                            textbutton "Preview Changes" action Function(preview_move_changes) text_size 14

            hbox:
                spacing 15
                xalign 0.5

                textbutton "Cancel" action Return() text_size 14

# Linker settings
label linker_settings:

    call screen linker_settings_screen

    return

screen linker_settings_screen():

    modal True

    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 400

        vbox:
            spacing 20

            text "⚙️ Project Linker Settings" size 20 xalign 0.5 color "#00ff00"

            vbox:
                spacing 15

                # Toggle settings
                vbox:
                    style_prefix "check"

                    textbutton "Enable Auto-Linking" action ToggleDict(project_linker, 'enabled')
                    textbutton "Enable Auto-Scan" action ToggleDict(project_linker, 'auto_scan')
                    textbutton "Enable Backups" action ToggleDict(project_linker, 'backup_enabled')

                # Scan interval
                vbox:
                    spacing 5

                    text "Scan Interval: {:.1f} seconds".format(project_linker['scan_interval']) size 14
                    bar value DictValue(project_linker, 'scan_interval', 30.0, offset=1.0) xsize 300

                # Backup management
                vbox:
                    spacing 10

                    text "Backup Management" size 16 color "#ffaa00"

                    hbox:
                        spacing 15

                        textbutton "Clear Backups" action Function(clear_backups) text_size 12
                        textbutton "Restore from Backup" action Call("backup_restore_interface") text_size 12

            hbox:
                spacing 15
                xalign 0.5

                textbutton "Save Settings" action Function(save_linker_settings) text_size 14
                textbutton "Close" action Return() text_size 14

# Helper functions for the interface
init python:

    selected_file = ""
    new_file_path = ""

    def show_link_validation():
        """Show link validation results"""
        broken_links = project_linker_instance.validate_links()
        if broken_links:
            renpy.notify("Found {} broken links - check log".format(len(broken_links)))
        else:
            renpy.notify("All links are valid!")

    def move_selected_file():
        """Move the selected file"""
        global selected_file, new_file_path

        if not selected_file or not new_file_path:
            renpy.notify("Please select a file and enter a new path")
            return

        old_full_path = os.path.join(project_linker['project_root'], selected_file)
        new_full_path = os.path.join(project_linker['project_root'], new_file_path)

        if project_linker_instance.move_file(old_full_path, new_full_path):
            renpy.notify("File moved successfully!")
            selected_file = ""
            new_file_path = ""
        else:
            renpy.notify("Failed to move file - check log")

    def preview_move_changes():
        """Preview what changes would be made"""
        global selected_file, new_file_path

        if not selected_file:
            renpy.notify("Please select a file first")
            return

        # Find all files that would be affected
        affected_files = []
        for file_path, file_info in project_linker['file_registry'].items():
            if selected_file in file_info.get('dependencies', set()):
                affected_files.append(file_path)

        if affected_files:
            renpy.notify("Would update {} files".format(len(affected_files)))
        else:
            renpy.notify("No files would be affected")

    def clear_backups():
        """Clear all backup files"""
        try:
            backup_dir = project_linker_instance.backup_dir
            if os.path.exists(backup_dir):
                shutil.rmtree(backup_dir)
                project_linker_instance.ensure_backup_dir()
            renpy.notify("Backups cleared successfully")
        except Exception as e:
            renpy.notify("Failed to clear backups")

    def save_linker_settings():
        """Save linker settings to persistent data"""
        try:
            persistent.project_linker_settings = {
                'enabled': project_linker['enabled'],
                'auto_scan': project_linker['auto_scan'],
                'backup_enabled': project_linker['backup_enabled'],
                'scan_interval': project_linker['scan_interval']
            }
            renpy.notify("Settings saved successfully")
        except Exception as e:
            renpy.notify("Failed to save settings")

    def load_linker_settings():
        """Load linker settings from persistent data"""
        try:
            if hasattr(persistent, 'project_linker_settings') and persistent.project_linker_settings:
                settings = persistent.project_linker_settings
                project_linker.update(settings)
        except Exception as e:
            renpy.log("Failed to load linker settings: {}".format(str(e)))

    # Load settings on startup
    load_linker_settings()

# Add project linker to developer tools
init python:
    def open_project_linker():
        """Open the project linker manager"""
        renpy.call_screen("project_linker_manager")

# Default persistent values
default persistent.project_linker_settings = None

# Default screen variables
default selected_file = ""
default new_file_path = ""
