#!/usr/bin/env python3
"""
Dynamic Resource Manager with On-Demand Decryption
Loads encrypted resources, decrypts temporarily, and re-encrypts after use
"""

import os
import sys
import time
import threading
import tempfile
import shutil
import hashlib
import json
from pathlib import Path
from encryption_core import encryption_engine

class SecureResourceManager:
    """Manages encrypted resources with temporary decryption"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.temp_dir = None
        self.active_resources = {}
        self.resource_cache = {}
        self.access_log = []
        self.cleanup_thread = None
        self.running = True
        
        # Security settings
        self.config = {
            'max_cache_time': 300,      # 5 minutes max cache
            'cleanup_interval': 60,     # Cleanup every minute
            'max_temp_files': 50,       # Max temporary files
            'secure_delete': True,      # Secure file deletion
            'access_logging': True,     # Log all access
            'auto_reencrypt': True      # Auto re-encrypt after use
        }
        
        self._initialize_temp_directory()
        self._start_cleanup_thread()
    
    def _initialize_temp_directory(self):
        """Initialize secure temporary directory"""
        self.temp_dir = tempfile.mkdtemp(prefix='secure_resources_')
        
        # Set restrictive permissions (owner only)
        os.chmod(self.temp_dir, 0o700)
        
        print(f"Initialized secure temp directory: {self.temp_dir}")
    
    def _start_cleanup_thread(self):
        """Start background cleanup thread"""
        def cleanup_loop():
            while self.running:
                try:
                    self._cleanup_expired_resources()
                    time.sleep(self.config['cleanup_interval'])
                except Exception as e:
                    print(f"Cleanup thread error: {e}")
        
        self.cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def _cleanup_expired_resources(self):
        """Clean up expired temporary resources"""
        current_time = time.time()
        expired_resources = []
        
        for resource_id, resource_info in self.active_resources.items():
            if current_time - resource_info['access_time'] > self.config['max_cache_time']:
                expired_resources.append(resource_id)
        
        for resource_id in expired_resources:
            self._secure_cleanup_resource(resource_id)
    
    def _secure_cleanup_resource(self, resource_id):
        """Securely clean up a temporary resource"""
        if resource_id not in self.active_resources:
            return
        
        resource_info = self.active_resources[resource_id]
        temp_path = resource_info['temp_path']
        
        try:
            # Secure delete the temporary file
            if os.path.exists(temp_path):
                if self.config['secure_delete']:
                    encryption_engine.secure_delete(temp_path)
                else:
                    os.remove(temp_path)
            
            # Remove from active resources
            del self.active_resources[resource_id]
            
            # Log cleanup
            if self.config['access_logging']:
                self.access_log.append({
                    'action': 'cleanup',
                    'resource_id': resource_id,
                    'timestamp': time.time()
                })
            
            print(f"Cleaned up resource: {resource_id}")
            
        except Exception as e:
            print(f"Error cleaning up resource {resource_id}: {e}")
    
    def _generate_resource_id(self, file_path):
        """Generate unique resource ID"""
        path_str = str(file_path)
        timestamp = str(time.time())
        combined = f"{path_str}:{timestamp}"
        return hashlib.sha256(combined.encode()).hexdigest()[:16]
    
    def load_encrypted_resource(self, encrypted_path, password=None):
        """Load and temporarily decrypt a resource"""
        try:
            encrypted_path = Path(encrypted_path)
            
            if not encrypted_path.exists():
                print(f"Encrypted resource not found: {encrypted_path}")
                return None
            
            # Generate resource ID
            resource_id = self._generate_resource_id(encrypted_path)
            
            # Check if already loaded
            if resource_id in self.active_resources:
                resource_info = self.active_resources[resource_id]
                resource_info['access_time'] = time.time()
                resource_info['access_count'] += 1
                return resource_info['temp_path']
            
            # Decrypt to temporary file
            temp_filename = f"resource_{resource_id}"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            decrypted_path = encryption_engine.decrypt_file(
                str(encrypted_path), 
                password, 
                temp_path
            )
            
            if not decrypted_path:
                print(f"Failed to decrypt resource: {encrypted_path}")
                return None
            
            # Store resource info
            self.active_resources[resource_id] = {
                'encrypted_path': str(encrypted_path),
                'temp_path': decrypted_path,
                'load_time': time.time(),
                'access_time': time.time(),
                'access_count': 1,
                'password': password
            }
            
            # Log access
            if self.config['access_logging']:
                self.access_log.append({
                    'action': 'load',
                    'resource_id': resource_id,
                    'encrypted_path': str(encrypted_path),
                    'timestamp': time.time()
                })
            
            print(f"Loaded encrypted resource: {encrypted_path} -> {resource_id}")
            return decrypted_path
            
        except Exception as e:
            print(f"Error loading encrypted resource: {e}")
            return None
    
    def release_resource(self, resource_id_or_path):
        """Manually release a resource"""
        # Find resource by ID or path
        resource_id = None
        
        if resource_id_or_path in self.active_resources:
            resource_id = resource_id_or_path
        else:
            # Search by temp path
            for rid, info in self.active_resources.items():
                if info['temp_path'] == resource_id_or_path:
                    resource_id = rid
                    break
        
        if resource_id:
            self._secure_cleanup_resource(resource_id)
            return True
        
        return False
    
    def get_resource_info(self, resource_id):
        """Get information about a loaded resource"""
        return self.active_resources.get(resource_id, None)
    
    def list_active_resources(self):
        """List all currently active resources"""
        return list(self.active_resources.keys())
    
    def get_access_log(self, limit=100):
        """Get recent access log entries"""
        return self.access_log[-limit:]
    
    def encrypt_and_store_resource(self, source_path, password=None, delete_original=True):
        """Encrypt a resource and store it"""
        try:
            source_path = Path(source_path)
            
            if not source_path.exists():
                print(f"Source file not found: {source_path}")
                return None
            
            # Encrypt the file
            encrypted_path = encryption_engine.encrypt_file(
                str(source_path), 
                password, 
                delete_original
            )
            
            if encrypted_path:
                print(f"Encrypted and stored: {source_path} -> {encrypted_path}")
                return encrypted_path
            else:
                print(f"Failed to encrypt: {source_path}")
                return None
                
        except Exception as e:
            print(f"Error encrypting resource: {e}")
            return None
    
    def batch_encrypt_directory(self, directory_path, password=None, file_extensions=None):
        """Encrypt all files in a directory"""
        if file_extensions is None:
            file_extensions = ['.rpy', '.py', '.png', '.jpg', '.jpeg', '.gif', 
                             '.webp', '.ogg', '.mp3', '.wav', '.m4a']
        
        directory_path = Path(directory_path)
        encrypted_files = []
        
        for file_path in directory_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in file_extensions:
                # Skip already encrypted files
                if file_path.suffix == '.enc':
                    continue
                
                encrypted_path = self.encrypt_and_store_resource(
                    str(file_path), 
                    password, 
                    delete_original=True
                )
                
                if encrypted_path:
                    encrypted_files.append(encrypted_path)
        
        return encrypted_files
    
    def batch_decrypt_directory(self, directory_path, password=None):
        """Decrypt all encrypted files in a directory"""
        directory_path = Path(directory_path)
        decrypted_files = []
        
        for file_path in directory_path.rglob('*.enc'):
            decrypted_path = encryption_engine.decrypt_file(
                str(file_path), 
                password
            )
            
            if decrypted_path:
                decrypted_files.append(decrypted_path)
        
        return decrypted_files
    
    def create_resource_manifest(self, directory_path, output_path=None):
        """Create a manifest of all encrypted resources"""
        directory_path = Path(directory_path)
        
        if output_path is None:
            output_path = directory_path / 'resource_manifest.json'
        
        manifest = {
            'created': time.time(),
            'directory': str(directory_path),
            'resources': []
        }
        
        for file_path in directory_path.rglob('*.enc'):
            relative_path = file_path.relative_to(directory_path)
            
            # Get file info
            file_stat = file_path.stat()
            file_hash = hashlib.sha256(file_path.read_bytes()).hexdigest()
            
            resource_info = {
                'path': str(relative_path),
                'size': file_stat.st_size,
                'modified': file_stat.st_mtime,
                'hash': file_hash,
                'original_name': str(relative_path)[:-4] if str(relative_path).endswith('.enc') else str(relative_path)
            }
            
            manifest['resources'].append(resource_info)
        
        # Write manifest
        with open(output_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print(f"Created resource manifest: {output_path}")
        return output_path
    
    def verify_resource_integrity(self, manifest_path):
        """Verify integrity of encrypted resources using manifest"""
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        directory_path = Path(manifest['directory'])
        verification_results = []
        
        for resource in manifest['resources']:
            resource_path = directory_path / resource['path']
            
            if not resource_path.exists():
                verification_results.append({
                    'path': resource['path'],
                    'status': 'missing',
                    'expected_hash': resource['hash'],
                    'actual_hash': None
                })
                continue
            
            # Calculate current hash
            current_hash = hashlib.sha256(resource_path.read_bytes()).hexdigest()
            
            if current_hash == resource['hash']:
                status = 'valid'
            else:
                status = 'corrupted'
            
            verification_results.append({
                'path': resource['path'],
                'status': status,
                'expected_hash': resource['hash'],
                'actual_hash': current_hash
            })
        
        return verification_results
    
    def shutdown(self):
        """Shutdown the resource manager and clean up"""
        self.running = False
        
        # Clean up all active resources
        for resource_id in list(self.active_resources.keys()):
            self._secure_cleanup_resource(resource_id)
        
        # Remove temporary directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        print("Resource manager shutdown complete")

# Global resource manager instance
resource_manager = None

def initialize_resource_manager(project_path):
    """Initialize the global resource manager"""
    global resource_manager
    resource_manager = SecureResourceManager(project_path)
    return resource_manager

def get_resource_manager():
    """Get the global resource manager instance"""
    return resource_manager

if __name__ == "__main__":
    # Command line interface
    if len(sys.argv) < 3:
        print("Usage: python resource_manager.py <command> <path> [password]")
        print("Commands: encrypt_dir, decrypt_dir, create_manifest, verify")
        sys.exit(1)
    
    command = sys.argv[1]
    path = sys.argv[2]
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    rm = SecureResourceManager(os.getcwd())
    
    try:
        if command == "encrypt_dir":
            results = rm.batch_encrypt_directory(path, password)
            print(f"Encrypted {len(results)} files")
        
        elif command == "decrypt_dir":
            results = rm.batch_decrypt_directory(path, password)
            print(f"Decrypted {len(results)} files")
        
        elif command == "create_manifest":
            manifest_path = rm.create_resource_manifest(path)
            print(f"Created manifest: {manifest_path}")
        
        elif command == "verify":
            results = rm.verify_resource_integrity(path)
            for result in results:
                print(f"{result['path']}: {result['status']}")
        
        else:
            print(f"Unknown command: {command}")
    
    finally:
        rm.shutdown()
