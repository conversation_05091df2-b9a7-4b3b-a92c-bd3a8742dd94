Identifier	Character	Dialogue	Filename	Line Number	Ren'<PERSON><PERSON>
		netcode the protogen and more	game/options.rpy	15
		Back	game/screens.rpy	252
		History	game/screens.rpy	253
		Skip	game/screens.rpy	254
		Auto	game/screens.rpy	255
		Save	game/screens.rpy	256
		Q.Save	game/screens.rpy	257
		Q.Load	game/screens.rpy	258
		Prefs	game/screens.rpy	259
		Start	game/screens.rpy	300
		Load	game/screens.rpy	308
		Preferences	game/screens.rpy	310
		End Replay	game/screens.rpy	314
		Main Menu	game/screens.rpy	318
		About	game/screens.rpy	320
		Help	game/screens.rpy	325
		Version [config.version!t]\n	game/screens.rpy	560
		Made with Ren'Py [renpy.version_only].\n\n[renpy.license!t]	game/screens.rpy	566
		%A, %B %d %Y, %H:%M	game/screens.rpy	644
		empty slot	game/screens.rpy	644
		<	game/screens.rpy	664
		A	game/screens.rpy	668
		Q	game/screens.rpy	671
		>	game/screens.rpy	677
		Upload Sync	game/screens.rpy	682
		Download Sync	game/screens.rpy	686
		Display	game/screens.rpy	745
		Window	game/screens.rpy	746
		Unseen Text	game/screens.rpy	752
		After Choices	game/screens.rpy	753
		Transitions	game/screens.rpy	754
		Text Speed	game/screens.rpy	767
		Auto-Forward Time	game/screens.rpy	771
		Music Volume	game/screens.rpy	778
		Sound Volume	game/screens.rpy	785
		Test	game/screens.rpy	791
		Mute All	game/screens.rpy	806
		The dialogue history is empty.	game/screens.rpy	925
		Keyboard	game/screens.rpy	993
		Mouse	game/screens.rpy	994
		Enter	game/screens.rpy	1010
		Advances dialogue and activates the interface.	game/screens.rpy	1011
		Space	game/screens.rpy	1014
		Advances dialogue without selecting choices.	game/screens.rpy	1015
		Arrow Keys	game/screens.rpy	1018
		Navigate the interface.	game/screens.rpy	1019
		Escape	game/screens.rpy	1022
		Accesses the game menu.	game/screens.rpy	1023
		Ctrl	game/screens.rpy	1026
		Skips dialogue while held down.	game/screens.rpy	1027
		Tab	game/screens.rpy	1030
		Toggles dialogue skipping.	game/screens.rpy	1031
		Page Up	game/screens.rpy	1034
		Rolls back to earlier dialogue.	game/screens.rpy	1035
		Page Down	game/screens.rpy	1038
		Rolls forward to later dialogue.	game/screens.rpy	1039
		Hides the user interface.	game/screens.rpy	1043
		Takes a screenshot.	game/screens.rpy	1047
		Toggles assistive self-voicing.	game/screens.rpy	1051
		Opens the accessibility menu.	game/screens.rpy	1055
		Left Click	game/screens.rpy	1061
		Middle Click	game/screens.rpy	1065
		Right Click	game/screens.rpy	1069
		Mouse Wheel Up	game/screens.rpy	1073
		Mouse Wheel Down	game/screens.rpy	1077
		Right Trigger\nA/Bottom Button	game/screens.rpy	1084
		Left Trigger\nLeft Shoulder	game/screens.rpy	1088
		Right Shoulder	game/screens.rpy	1092
		D-Pad, Sticks	game/screens.rpy	1096
		Start, Guide, B/Right Button	game/screens.rpy	1100
		Y/Top Button	game/screens.rpy	1104
		Skipping	game/screens.rpy	1219
		Menu	game/screens.rpy	1531
start_a170b500	e	You've created a new Ren'Py game.	game/script.rpy	27	e "[what]"
start_f41f55d7	e	Once you add a story, pictures, and music, you can release it to the world!	game/script.rpy	29	e "[what]"
