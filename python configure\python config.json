{"project_name": "netcode_protogen_game", "python_version": "3.9.10", "renpy_detected": "<PERSON><PERSON><PERSON><PERSON> (config.version: 1.13.05.07.123v)", "system_info": {"os": "Windows", "platform": "Windows-10", "processor": "AMD64"}, "python_paths": {"site_packages": "lib/python3.9/site-packages", "executable": "python.exe"}, "modules_available": {"cryptography": true, "pillow": true, "requests": true, "numpy": true, "pygame": true, "future": true}, "compatibility_bridge": {"enabled": true, "version": "1.0.0", "engine_python_version": "3.9.10", "supports_newer_versions": ["3.10", "3.11", "3.12"], "bridge_features": ["safe_imports", "version_detection", "feature_flags", "backward_compatibility", "forward_compatibility"], "integration_status": "ready"}, "last_updated": "2023-05-07", "bridge_updated": "2024-01-01"}