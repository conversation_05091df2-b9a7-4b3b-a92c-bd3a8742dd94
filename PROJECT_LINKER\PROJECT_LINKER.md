# Project Auto-Linker System

## Overview
The Project Auto-Linker is a comprehensive file management system that automatically maintains connections between files in your Ren'Py project. When you move, rename, or reorganize files, the system automatically updates all references to ensure nothing breaks.

## 🚀 **Key Features**

### **Automatic Link Maintenance**
- **Smart File Tracking**: Automatically tracks all files in your project
- **Reference Detection**: Finds all references between files (images, audio, scripts, etc.)
- **Auto-Update**: When you move a file, all references are automatically updated
- **Dependency Mapping**: Builds a complete map of file dependencies

### **File Movement & Organization**
- **Drag & Drop Interface**: Visual file moving with automatic link updates
- **Batch Operations**: Move multiple files at once
- **Smart Renaming**: Rename files with pattern matching
- **Type-Based Organization**: Automatically organize files by type

### **Safety & Backup**
- **Automatic Backups**: Creates backups before any file operations
- **Link Validation**: Checks for broken links and missing files
- **Rollback Support**: Restore from backups if needed
- **Preview Mode**: See what changes will be made before executing

## 📁 **Supported File Types**

### **Images**
- **Extensions**: .png, .jpg, .jpeg, .gif, .webp, .bmp
- **Detects**: `image` definitions, `scene`, `show` commands
- **Directories**: `images/`, `gui/`

### **Audio**
- **Extensions**: .ogg, .mp3, .wav, .opus
- **Detects**: `play music`, `play sound`, music definitions
- **Directories**: `audio/`, `music/`

### **Scripts**
- **Extensions**: .rpy, .rpyc
- **Detects**: `jump`, `call`, `include` statements
- **Directories**: `game/`

### **Fonts**
- **Extensions**: .ttf, .otf, .woff
- **Detects**: Font definitions in GUI
- **Directories**: `fonts/`, `gui/`

### **Videos**
- **Extensions**: .webm, .mp4, .ogv
- **Detects**: `play movie` commands
- **Directories**: `videos/`

## 🎮 **How to Use**

### **Accessing the Project Linker**

**Main Menu Access**:
- Go to main menu
- Click "Project Linker" (appears in developer mode)
- Or use hotkey: `Shift+K+L`

**Quick Access Overlay**:
- Shows current status in corner of screen
- Quick buttons for common operations
- Real-time file count and link status

### **Moving Files**

**Method 1: Smart File Mover**
1. Open Project Linker Manager
2. Click "Move File"
3. Select source file from list
4. Enter new path
5. Click "Move File" - all references update automatically

**Method 2: Quick Move**
1. Use hotkey `Shift+K+M`
2. Enter source and destination paths
3. Execute move with automatic link updates

**Method 3: Drag & Drop** (Visual Interface)
1. Open Project Linker Manager
2. Use drag & drop interface
3. Drop source file and destination
4. Execute move with visual feedback

### **Batch Operations**

**Batch Move**:
- Select multiple files
- Set destination folder
- Move all at once with link updates

**Pattern Rename**:
- Find and replace patterns in filenames
- Preview changes before executing
- All references updated automatically

**Organize by Type**:
- Automatically sort files into type-based folders
- Images → `game/images/`
- Audio → `game/audio/`
- Scripts → `game/scripts/`

## ⚙️ **Configuration**

### **Settings**
- **Auto-Linking**: Enable/disable automatic link maintenance
- **Auto-Scan**: Automatically scan for new files
- **Backups**: Enable/disable automatic backups
- **Scan Interval**: How often to check for changes (1-30 seconds)

### **Hotkeys**
- `Shift+K+L`: Open Project Linker Manager
- `Shift+K+M`: Quick Move dialog
- `Shift+K+V`: Validate all links
- `Shift+K+R`: Rescan project files

## 🔍 **Link Validation**

### **Automatic Validation**
- Continuously monitors file integrity
- Detects broken links and missing files
- Reports issues in real-time
- Suggests fixes for common problems

### **Manual Validation**
- Click "Validate Links" to check all connections
- Shows detailed report of any issues
- Identifies orphaned files
- Highlights missing dependencies

## 🛡️ **Safety Features**

### **Backup System**
- **Automatic Backups**: Created before any file operation
- **Timestamped**: Each backup includes timestamp
- **Selective Restore**: Restore individual files or entire project
- **Cleanup Tools**: Remove old backups to save space

### **Preview Mode**
- **Preview Changes**: See what will be modified before executing
- **Impact Analysis**: Shows which files will be affected
- **Dependency Preview**: View the dependency chain
- **Safe Execution**: Only proceed after confirmation

### **Error Handling**
- **Graceful Failures**: Operations fail safely without corruption
- **Detailed Logging**: Complete log of all operations
- **Rollback Support**: Undo operations if problems occur
- **Validation Checks**: Verify operations completed successfully

## 📊 **Project Analysis**

### **Dependency Graph**
- **Visual Mapping**: See how files connect to each other
- **Dependency Chains**: Trace file relationships
- **Orphan Detection**: Find unused files
- **Critical Path Analysis**: Identify important file connections

### **File Registry**
- **Complete Inventory**: Every file tracked with metadata
- **Type Classification**: Files categorized by type and usage
- **Modification Tracking**: Monitor when files change
- **Usage Statistics**: See which files are most referenced

## 🔧 **Advanced Features**

### **Custom Patterns**
- **Add New File Types**: Extend support for custom file types
- **Custom Reference Patterns**: Define how files reference each other
- **Directory Mapping**: Set up custom organization schemes
- **Rule-Based Processing**: Create automated file handling rules

### **Integration**
- **Anti-Cheat Integration**: Works with your security system
- **Version Control**: Compatible with Git and other VCS
- **Build System**: Integrates with Ren'Py build process
- **External Tools**: API for external file management tools

## 📋 **Best Practices**

### **Project Organization**
- **Consistent Naming**: Use clear, consistent file naming
- **Logical Structure**: Organize files in logical directory structures
- **Regular Validation**: Run link validation regularly
- **Backup Management**: Keep backups but clean up old ones

### **File Operations**
- **Use the Linker**: Always use the project linker for file operations
- **Preview First**: Preview changes before executing
- **Batch When Possible**: Use batch operations for efficiency
- **Validate After**: Check links after major reorganizations

### **Maintenance**
- **Regular Scans**: Let the system scan regularly for changes
- **Monitor Status**: Keep an eye on the status overlay
- **Clean Backups**: Periodically clean up old backup files
- **Update Patterns**: Add new file types as your project grows

## 🚨 **Troubleshooting**

### **Common Issues**

**"File not found" errors**:
- Run link validation to find broken references
- Check if files were moved outside the linker
- Verify file paths are correct
- Restore from backup if needed

**"Links not updating"**:
- Ensure auto-linking is enabled
- Check if files are in tracked directories
- Verify file types are supported
- Rescan project to refresh registry

**"Backup failures"**:
- Check disk space availability
- Verify write permissions
- Clear old backups to free space
- Check backup directory settings

### **Recovery Procedures**

**Restore from Backup**:
1. Open Project Linker Manager
2. Go to Settings
3. Click "Restore from Backup"
4. Select backup timestamp
5. Choose files to restore

**Reset Project Registry**:
1. Open Project Linker Manager
2. Click "Rescan Project"
3. Wait for complete scan
4. Validate all links
5. Fix any reported issues

## 🎯 **Quick Reference**

### **Essential Commands**
- **Move File**: Select file → Enter new path → Move
- **Validate Links**: Click "Validate Links" button
- **Batch Rename**: Pattern find/replace → Preview → Execute
- **Organize Files**: Select "Organize by Type" → Execute

### **Status Indicators**
- **Green Dot**: System active and healthy
- **Red Dot**: System disabled or errors
- **File Count**: Number of tracked files
- **Link Count**: Number of dependencies tracked

---

**The Project Auto-Linker ensures your Ren'Py project stays organized and functional no matter how you move or reorganize files. It's like having an intelligent assistant that keeps track of everything and fixes connections automatically!**
