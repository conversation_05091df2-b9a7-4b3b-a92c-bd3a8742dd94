## Project Linker Integration
## Simplified integration of PROJECT_LINKER functionality

init python:
    import os
    import json
    
    # Project linker state
    project_linker_state = {
        'available': False,
        'files_found': [],
        'last_scan': None
    }

    def open_project_linker():
        """Open the project linker manager"""
        try:
            # Check if the full project linker is available
            if os.path.exists("PROJECT_LINKER/project_linker.rpy"):
                # Try to load and call the full project linker
                renpy.call_screen("project_linker_simple")
            else:
                # Show a simple file browser instead
                renpy.call_screen("simple_project_browser")
        except Exception as e:
            renpy.notify(f"Project Linker error: {str(e)}")
            renpy.log(f"Project Linker error: {str(e)}")

    def scan_project_files():
        """Scan for project files"""
        try:
            project_files = []
            
            # Scan main directories
            directories_to_scan = [
                "game",
                "separate games",
                "anti-cheat",
                "platform_detection",
                "Vulkan",
                "music player",
                "options",
                "PROJECT_LINKER",
                "unencrypt and re-encrypt"
            ]
            
            for directory in directories_to_scan:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        for file in files:
                            if file.endswith(('.rpy', '.py', '.json', '.md')):
                                file_path = os.path.join(root, file)
                                project_files.append(file_path)
            
            project_linker_state['files_found'] = project_files
            project_linker_state['last_scan'] = renpy.get_game_runtime()
            
            return project_files
            
        except Exception as e:
            renpy.log(f"Project file scan error: {str(e)}")
            return []

# Simple project linker screen
screen project_linker_simple():
    tag menu
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        
        vbox:
            spacing 20
            
            text "Project Linker - Simple Mode" size 30 xalign 0.5
            
            text "This is a simplified version of the Project Linker." size 16
            text "The full Project Linker is located in the PROJECT_LINKER directory." size 16
            
            frame:
                xfill True
                yfill True
                
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        text "Available Project Files:" size 20
                        
                        $ project_files = scan_project_files()
                        
                        for file_path in project_files[:50]:  # Show first 50 files
                            textbutton file_path:
                                action Function(renpy.notify, f"File: {file_path}")
                                text_size 14
            
            hbox:
                spacing 20
                xalign 0.5
                
                textbutton "Refresh Files" action Function(scan_project_files)
                textbutton "Open PROJECT_LINKER" action Function(open_full_project_linker)
                textbutton "Close" action Return()

# Simple project browser screen
screen simple_project_browser():
    tag menu
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        
        vbox:
            spacing 20
            
            text "Simple Project Browser" size 24 xalign 0.5
            
            text "PROJECT_LINKER directory not found." size 16
            text "Here are the main project directories:" size 16
            
            frame:
                xfill True
                yfill True
                
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        $ main_dirs = ["game", "separate games", "anti-cheat", "platform_detection", "Vulkan", "music player", "options", "unencrypt and re-encrypt"]
                        
                        for directory in main_dirs:
                            if os.path.exists(directory):
                                textbutton f"📁 {directory}" action Function(renpy.notify, f"Directory: {directory}")
                            else:
                                text f"📁 {directory} (not found)" color "#888"
            
            textbutton "Close" action Return() xalign 0.5

init python:
    def open_full_project_linker():
        """Try to open the full project linker"""
        try:
            if os.path.exists("PROJECT_LINKER/project_linker.rpy"):
                renpy.notify("Full Project Linker found in PROJECT_LINKER directory")
                renpy.notify("Please run the game from the PROJECT_LINKER directory for full functionality")
            else:
                renpy.notify("Full Project Linker not found")
        except Exception as e:
            renpy.notify(f"Error: {str(e)}")

# Alternative safe function that always works
init python:
    def open_project_linker_safe():
        """Safe wrapper for opening project linker"""
        try:
            open_project_linker()
        except Exception as e:
            renpy.notify("Project Linker unavailable")
            renpy.log(f"Project Linker error: {str(e)}")

# Check project linker availability on startup
init python:
    try:
        if os.path.exists("PROJECT_LINKER/project_linker.rpy"):
            project_linker_state['available'] = True
            renpy.log("PROJECT_LINKER directory found")
        else:
            project_linker_state['available'] = False
            renpy.log("PROJECT_LINKER directory not found - using simple mode")
    except Exception as e:
        renpy.log(f"Project Linker availability check error: {str(e)}")
        project_linker_state['available'] = False
