## Game-Specific Lighting Functions
## Easy-to-use lighting functions for visual novel developers
## Provides high-level lighting control for common game scenarios

# Convenient lighting functions for game developers
init python:
    
    def quick_lighting(scenario):
        """Quick lighting setup for common scenarios"""
        scenario_map = {
            'day': 'afternoon',
            'morning': 'morning', 
            'evening': 'sunset',
            'night': 'night',
            'indoor': 'indoor_warm',
            'outdoor': 'afternoon',
            'romantic': 'romantic',
            'dramatic': 'dramatic'
        }
        
        actual_scenario = scenario_map.get(scenario, scenario)
        return set_scene_lighting(actual_scenario, 1.0)
    
    def fade_to_lighting(scenario, duration=3.0):
        """Fade to a new lighting scenario"""
        return set_scene_lighting(scenario, duration)
    
    def add_candle_light(x=0, y=1, z=0, flicker=True):
        """Add a flickering candle light"""
        light_id = add_scene_light(x, y, z, 1.0, 0.7, 0.3, 1.5, 2.0, f"candle_{int(time.time()*1000)}")
        
        if flicker and light_id:
            # Add flickering animation
            import random
            base_intensity = 1.5
            flicker_amount = 0.4
            
            def flicker_candle():
                if 'integrated_lighting' in globals():
                    new_intensity = base_intensity + random.uniform(-flicker_amount, flicker_amount)
                    animate_scene_light(light_id, 'intensity', new_intensity, 0.1 + random.uniform(0, 0.2))
                
                # Schedule next flicker
                renpy.timeout(0.1 + random.uniform(0, 0.3))
                return flicker_candle
            
            # Start flickering
            renpy.timeout(0.1)
            flicker_candle()
        
        return light_id
    
    def add_window_light(direction='left', intensity=1.0, color_temp='warm'):
        """Add window lighting from a specific direction"""
        color_temps = {
            'warm': [1.0, 0.9, 0.7],
            'cool': [0.8, 0.9, 1.0],
            'neutral': [1.0, 1.0, 1.0],
            'sunset': [1.0, 0.6, 0.3],
            'moonlight': [0.7, 0.8, 1.0]
        }
        
        positions = {
            'left': [-3, 1, 0],
            'right': [3, 1, 0],
            'front': [0, 1, 2],
            'back': [0, 1, -2]
        }
        
        color = color_temps.get(color_temp, [1.0, 1.0, 1.0])
        position = positions.get(direction, [0, 1, 0])
        
        return add_scene_light(
            position[0], position[1], position[2],
            color[0], color[1], color[2],
            intensity, 6.0, f"window_{direction}"
        )
    
    def lightning_flash(duration=0.2, intensity=3.0):
        """Create a lightning flash effect"""
        if 'integrated_lighting' in globals():
            # Store original ambient
            original_ambient = integrated_lighting.current_scene_lighting['ambient'].copy()
            
            # Flash to bright white
            flash_ambient = {'r': 0.9, 'g': 0.9, 'b': 1.0, 'intensity': intensity}
            integrated_lighting.current_scene_lighting['ambient'] = flash_ambient
            integrated_lighting._update_shader_uniforms()
            
            # Schedule return to original
            def restore_lighting():
                integrated_lighting.current_scene_lighting['ambient'] = original_ambient
                integrated_lighting._update_shader_uniforms()
            
            renpy.timeout(duration)
            restore_lighting()
            
            print(f"⚡ Lightning flash ({duration}s)")
            return True
        
        return False
    
    def fire_glow_effect(x=0, y=0, z=0, size='medium'):
        """Create a fire glow effect with multiple lights"""
        sizes = {
            'small': {'count': 2, 'intensity': 1.0, 'radius': 1.5},
            'medium': {'count': 3, 'intensity': 1.5, 'radius': 2.0},
            'large': {'count': 4, 'intensity': 2.0, 'radius': 3.0}
        }
        
        config = sizes.get(size, sizes['medium'])
        fire_lights = []
        
        for i in range(config['count']):
            import random
            offset_x = random.uniform(-0.3, 0.3)
            offset_y = random.uniform(-0.1, 0.2)
            offset_z = random.uniform(-0.3, 0.3)
            
            light_id = add_scene_light(
                x + offset_x, y + offset_y, z + offset_z,
                1.0, 0.6 + random.uniform(-0.1, 0.1), 0.2 + random.uniform(-0.1, 0.1),
                config['intensity'] * random.uniform(0.8, 1.2),
                config['radius'],
                f"fire_{i}_{int(time.time()*1000)}"
            )
            
            if light_id:
                fire_lights.append(light_id)
        
        print(f"🔥 Created {size} fire effect with {len(fire_lights)} lights")
        return fire_lights

# Scene-specific lighting functions
def scene_school_classroom():
    """Set up classroom lighting"""
    return set_location('school_classroom', 'afternoon', 'clear', 1.0)

def scene_school_hallway():
    """Set up hallway lighting"""
    return set_location('school_hallway', 'afternoon', 'clear', 1.0)

def scene_home_bedroom():
    """Set up bedroom lighting"""
    return set_location('home_bedroom', 'evening', 'clear', 1.0)

def scene_home_kitchen():
    """Set up kitchen lighting"""
    return set_location('home_kitchen', 'morning', 'clear', 1.0)

def scene_park_day():
    """Set up daytime park lighting"""
    return set_location('park_day', 'afternoon', 'clear', 1.0)

def scene_park_evening():
    """Set up evening park lighting"""
    return set_location('park_evening', 'evening', 'clear', 1.0)

def scene_cafe():
    """Set up cafe lighting"""
    return set_location('cafe_interior', 'afternoon', 'clear', 1.0)

def scene_rooftop_night():
    """Set up rooftop night lighting"""
    return set_location('rooftop_night', 'night', 'clear', 1.0)

# Character lighting functions
def light_protagonist(position=None):
    """Add lighting for protagonist"""
    return set_character_light('protagonist', 'protagonist', position or [0, 0, 0])

def light_love_interest(position=None):
    """Add lighting for love interest"""
    return set_character_light('love_interest', 'love_interest', position or [1, 0, 0])

def light_antagonist(position=None):
    """Add lighting for antagonist"""
    return set_character_light('antagonist', 'antagonist', position or [-1, 0, 0])

def light_mysterious_character(position=None):
    """Add lighting for mysterious character"""
    return set_character_light('mysterious', 'mysterious', position or [0, 0, -1])

def light_cheerful_character(position=None):
    """Add lighting for cheerful character"""
    return set_character_light('cheerful', 'cheerful', position or [0.5, 0, 0.5])

# Mood and atmosphere functions
def mood_happy():
    """Set happy/cheerful mood lighting"""
    return set_mood('happy', 1.2, 2.0)

def mood_sad():
    """Set sad/melancholic mood lighting"""
    return set_mood('sad', 0.7, 2.0)

def mood_romantic():
    """Set romantic mood lighting"""
    return set_mood('romantic', 1.0, 3.0)

def mood_dramatic():
    """Set dramatic/intense mood lighting"""
    return set_mood('dramatic', 1.1, 1.5)

def mood_mysterious():
    """Set mysterious/suspenseful mood lighting"""
    return set_mood('mysterious', 0.8, 2.5)

def mood_energetic():
    """Set energetic/exciting mood lighting"""
    return set_mood('energetic', 1.3, 1.0)

def mood_calm():
    """Set calm/peaceful mood lighting"""
    return set_mood('calm', 0.9, 3.0)

def mood_tense():
    """Set tense/anxious mood lighting"""
    return set_mood('tense', 0.8, 1.0)

# Weather and time functions
def set_time_morning():
    """Set morning lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, 'morning', renpy_lighting.weather_condition, 2.0)
    return quick_lighting('morning')

def set_time_afternoon():
    """Set afternoon lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, 'afternoon', renpy_lighting.weather_condition, 2.0)
    return quick_lighting('afternoon')

def set_time_evening():
    """Set evening lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, 'evening', renpy_lighting.weather_condition, 2.0)
    return quick_lighting('evening')

def set_time_night():
    """Set night lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, 'night', renpy_lighting.weather_condition, 2.0)
    return quick_lighting('night')

def set_weather_clear():
    """Set clear weather lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, renpy_lighting.current_time_of_day, 'clear', 1.5)
    return True

def set_weather_cloudy():
    """Set cloudy weather lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, renpy_lighting.current_time_of_day, 'cloudy', 1.5)
    return True

def set_weather_rainy():
    """Set rainy weather lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, renpy_lighting.current_time_of_day, 'rainy', 1.5)
    return True

def set_weather_stormy():
    """Set stormy weather lighting"""
    if 'renpy_lighting' in globals() and renpy_lighting.current_location:
        return set_location(renpy_lighting.current_location, renpy_lighting.current_time_of_day, 'stormy', 1.5)
    return True

# Special effect functions
def sunrise_transition(duration=10.0):
    """Animate a sunrise transition"""
    if 'integrated_lighting' in globals():
        # Start with night lighting
        integrated_lighting.set_lighting_scenario('night', 0.0)
        
        # Animate to morning
        integrated_lighting.set_lighting_scenario('morning', duration)
        
        print(f"🌅 Sunrise transition started ({duration}s)")
        return True
    return False

def sunset_transition(duration=8.0):
    """Animate a sunset transition"""
    if 'integrated_lighting' in globals():
        # Start with afternoon lighting
        integrated_lighting.set_lighting_scenario('afternoon', 0.0)
        
        # Animate to sunset
        integrated_lighting.set_lighting_scenario('sunset', duration)
        
        print(f"🌇 Sunset transition started ({duration}s)")
        return True
    return False

def day_night_cycle(day_duration=30.0, night_duration=20.0):
    """Start an automatic day/night cycle"""
    def cycle():
        # Day phase
        sunrise_transition(3.0)
        renpy.timeout(day_duration - 6.0)  # Account for transition times
        
        # Night phase  
        sunset_transition(3.0)
        renpy.timeout(night_duration - 3.0)
        
        # Repeat cycle
        return cycle
    
    print(f"🔄 Started day/night cycle (day: {day_duration}s, night: {night_duration}s)")
    renpy.timeout(0.1)
    return cycle()

# Utility functions
def save_lighting_state(state_name):
    """Save current lighting state"""
    if 'integrated_lighting' in globals():
        state = integrated_lighting._copy_lighting_state()
        integrated_lighting.scene_lighting_cache[state_name] = state
        print(f"💾 Saved lighting state: {state_name}")
        return True
    return False

def load_lighting_state(state_name, transition_time=1.0):
    """Load saved lighting state"""
    if 'integrated_lighting' in globals() and state_name in integrated_lighting.scene_lighting_cache:
        state = integrated_lighting.scene_lighting_cache[state_name]
        
        if transition_time > 0:
            integrated_lighting._animate_lighting_transition(state, transition_time)
        else:
            integrated_lighting._apply_lighting_scenario(state)
        
        print(f"📁 Loaded lighting state: {state_name}")
        return True
    return False

def reset_lighting():
    """Reset to default lighting"""
    return quick_lighting('afternoon')

def get_lighting_status():
    """Get current lighting status"""
    if 'integrated_lighting' in globals():
        return integrated_lighting.get_lighting_info()
    return None

# Test and demo functions
label test_game_lighting:
    "Testing Game Lighting Functions..."
    
    "Let's test different lighting scenarios:"
    
    python:
        scene_school_classroom()
    
    "School classroom lighting applied."
    
    python:
        mood_romantic()
    
    "Romantic mood lighting applied."
    
    python:
        add_candle_light(-1, 1, 0)
        add_candle_light(1, 1, 0)
    
    "Added flickering candles."
    
    python:
        lightning_flash(0.3, 4.0)
    
    "Lightning flash!"
    
    python:
        fire_glow_effect(0, 0, 1, 'large')
    
    "Fire glow effect added."
    
    "Game lighting test complete!"
    return

label demo_lighting_scenarios:
    "Lighting Scenarios Demo"
    
    "Morning in the classroom..."
    python:
        scene_school_classroom()
        set_time_morning()
    
    "Afternoon in the park..."
    python:
        scene_park_day()
    
    "Evening at the cafe..."
    python:
        scene_cafe()
        set_time_evening()
    
    "Night on the rooftop..."
    python:
        scene_rooftop_night()
    
    "Romantic bedroom scene..."
    python:
        scene_home_bedroom()
        mood_romantic()
    
    "Dramatic confrontation..."
    python:
        mood_dramatic()
    
    "Demo complete!"
    return
