# Dynamic Image Definition System
# Automatically scans and defines character images and background images
# Supports gif, jpg, png formats with automatic detection

init python:
    import os
    import re
    
    # Image file extensions to scan for
    IMAGE_EXTENSIONS = ['.gif', '.jpg', '.jpeg', '.png']

    # Directories to scan for images
    PICTURES_DIRS = ["file for pictures", "game/images"]
    
    def scan_and_define_images():
        """
        Scans multiple directories and automatically defines images
        Based on naming conventions:
        - Characters: charactername_expression.ext or charactername_pose.ext
        - Backgrounds: bg_backgroundname.ext or background_backgroundname.ext
        """

        character_images = {}
        background_images = {}

        # Scan all specified directories
        for pictures_dir in PICTURES_DIRS:
            if not os.path.exists(pictures_dir):
                print(f"Pictures directory '{pictures_dir}' not found, skipping...")
                continue

            print(f"Scanning directory: {pictures_dir}")

            # Scan all files in the current directory
            for root, dirs, files in os.walk(pictures_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_name, file_ext = os.path.splitext(file)

                    # Check if it's a supported image format
                    if file_ext.lower() in IMAGE_EXTENSIONS:
                        relative_path = os.path.relpath(file_path, ".").replace("\\", "/")

                        # Check if it's a background image
                        if file_name.lower().startswith('bg_') or file_name.lower().startswith('background_'):
                            bg_name = file_name.lower().replace('bg_', '').replace('background_', '')
                            background_images[bg_name] = relative_path
                            print(f"Found background: {bg_name} -> {relative_path}")

                        # Check if it's a character image
                        else:
                            # Parse character name and expression/pose
                            # Format: charactername_expression or charactername_pose or charactername_naked
                            parts = file_name.split('_')
                            if len(parts) >= 2:
                                character_name = parts[0].lower()
                                expression_or_pose = '_'.join(parts[1:]).lower()

                                if character_name not in character_images:
                                    character_images[character_name] = {}

                                character_images[character_name][expression_or_pose] = relative_path
                                print(f"Found character image: {character_name} {expression_or_pose} -> {relative_path}")

                            # Single name files (default pose)
                            else:
                                character_name = file_name.lower()
                                if character_name not in character_images:
                                    character_images[character_name] = {}
                                character_images[character_name]['default'] = relative_path
                                print(f"Found character image: {character_name} default -> {relative_path}")

        return character_images, background_images

    def parse_hashtag_comments():
        """
         Also handles comments inside triple-quoted strings:
        '''
        # add a file (named either "netcode.png") to the images directory
        # add a file (named either "bedroom.jpg") to the images directory
        '''
        """

        hashtag_images = {}
        script_files = []

        # Find all .rpy files in the project
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith('.rpy'):
                    script_files.append(os.path.join(root, file))

        print(f"Scanning {len(script_files)} script files for hashtag image references...")

        for script_file in script_files:
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.splitlines()

                # Track if we're inside a triple-quoted string
                in_triple_quote = False
                triple_quote_type = None

                for line_num, line in enumerate(lines, 1):
                    original_line = line
                    line = line.strip()

                    # Check for triple quotes
                    if '"""' in line or "'''" in line:
                        if '"""' in line:
                            quote_type = '"""'
                        else:
                            quote_type = "'''"

                        # Count occurrences to handle multiple quotes on same line
                        quote_count = line.count(quote_type)

                        if not in_triple_quote:
                            if quote_count % 2 == 1:  # Odd number means we're entering
                                in_triple_quote = True
                                triple_quote_type = quote_type
                        else:
                            if quote_type == triple_quote_type and quote_count % 2 == 1:
                                in_triple_quote = False
                                triple_quote_type = None

                    # Look for hashtag comments with image references
                    should_process = False

                    # Process if it's a regular hashtag comment
                    if line.startswith('#') and ('add a file' in line.lower() or 'named either' in line.lower()):
                        should_process = True

                    # Process if we're inside triple quotes and line contains hashtag pattern
                    elif in_triple_quote and '#' in line and ('add a file' in line.lower() or 'named either' in line.lower()):
                        should_process = True

                    # Also look for other patterns that might indicate image references
                    elif (line.startswith('#') and
                          ('replace it by adding' in line.lower() or
                           'to the images directory' in line.lower() or
                           'images directory to show' in line.lower())):
                        should_process = True

                    # Handle patterns inside docstrings too
                    elif (in_triple_quote and '#' in line and
                          ('replace it by adding' in line.lower() or
                           'to the images directory' in line.lower() or
                           'images directory to show' in line.lower())):
                        should_process = True

                    if should_process:
                        # Extract image filename from quotes
                        import re
                        matches = re.findall(r'"([^"]*\.(png|jpg|jpeg|gif))"', line, re.IGNORECASE)

                        for match in matches:
                            image_filename = match[0]
                            file_name, file_ext = os.path.splitext(image_filename)

                            # Check if this is a background or character image
                            if ('bedroom' in file_name.lower() or 'bg_' in file_name.lower() or
                                'background' in file_name.lower() or 'scene' in file_name.lower()):
                                bg_name = file_name.lower().replace('bg_', '').replace('background_', '')
                                hashtag_images[f"bg_{bg_name}"] = {
                                    'type': 'background',
                                    'filename': image_filename,
                                    'source_file': script_file,
                                    'line_number': line_num,
                                    'in_docstring': in_triple_quote
                                }
                                context = "docstring" if in_triple_quote else "comment"
                                print(f"Found hashtag background reference in {context}: {bg_name} -> {image_filename} ({script_file}:{line_num})")
                            else:
                                # Treat as character image
                                char_name = file_name.lower()
                                hashtag_images[char_name] = {
                                    'type': 'character',
                                    'filename': image_filename,
                                    'source_file': script_file,
                                    'line_number': line_num,
                                    'in_docstring': in_triple_quote
                                }
                                context = "docstring" if in_triple_quote else "comment"
                                print(f"Found hashtag character reference in {context}: {char_name} -> {image_filename} ({script_file}:{line_num})")

            except Exception as e:
                print(f"Error reading script file {script_file}: {e}")

        return hashtag_images

    def parse_define_character_image():
        """
        Parse 'define character image' statements from script files
        Looks for patterns like:
        # define character image Netcode = "netcode.jpg"
        # define character image Austin = "austin_happy.png"
        """

        define_images = {}
        script_files = []

        # Find all .rpy files in the project
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith('.rpy'):
                    script_files.append(os.path.join(root, file))

        print(f"Scanning {len(script_files)} script files for 'define character image' statements...")

        for script_file in script_files:
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line_num, line in enumerate(lines, 1):
                    line = line.strip()

                    # Look for '# define character image' pattern (commented out to avoid Ren'Py errors)
                    if line.startswith('# define character image') or line.startswith('#define character image'):
                        # Parse the pattern: # define character image CharacterName = "filename.ext"
                        import re
                        match = re.match(r'#\s*define\s+character\s+image\s+(\w+)\s*=\s*["\']([^"\']+)["\']', line, re.IGNORECASE)

                        if match:
                            character_name = match.group(1)
                            image_filename = match.group(2)

                            define_images[character_name.lower()] = {
                                'character_name': character_name,
                                'filename': image_filename,
                                'source_file': script_file,
                                'line_number': line_num
                            }

                            print(f"Found 'define character image': {character_name} -> {image_filename} ({script_file}:{line_num})")
                        else:
                            print(f"WARNING: Could not parse 'define character image' line: {line} ({script_file}:{line_num})")

            except Exception as e:
                print(f"Error reading script file {script_file}: {e}")

        return define_images

    def find_images_for_define_statements(define_images):
        """
        Find actual image files that match 'define character image' statements
        """
        found_images = {}

        for char_key, info in define_images.items():
            filename = info['filename']
            character_name = info['character_name']
            file_found = False

            # Search in all image directories
            for pictures_dir in PICTURES_DIRS:
                if not os.path.exists(pictures_dir):
                    continue

                for root, dirs, files in os.walk(pictures_dir):
                    for file in files:
                        if file.lower() == filename.lower():
                            file_path = os.path.join(root, file)
                            relative_path = os.path.relpath(file_path, ".").replace("\\", "/")

                            found_images[char_key] = {
                                'path': relative_path,
                                'character_name': character_name,
                                'filename': filename,
                                'source_file': info['source_file'],
                                'line_number': info['line_number']
                            }

                            print(f"Found file for 'define character image': {character_name} -> {relative_path}")
                            file_found = True
                            break

                    if file_found:
                        break

                if file_found:
                    break

            if not file_found:
                print(f"WARNING: Could not find file '{filename}' for character '{character_name}' defined in {info['source_file']}:{info['line_number']}")

        return found_images

    def find_images_for_hashtag_references(hashtag_images):
        """
        Find actual image files that match hashtag references
        """
        found_images = {}

        for image_name, info in hashtag_images.items():
            filename = info['filename']
            file_found = False

            # Search in all image directories
            for pictures_dir in PICTURES_DIRS:
                if not os.path.exists(pictures_dir):
                    continue

                for root, dirs, files in os.walk(pictures_dir):
                    for file in files:
                        if file.lower() == filename.lower():
                            file_path = os.path.join(root, file)
                            relative_path = os.path.relpath(file_path, ".").replace("\\", "/")

                            found_images[image_name] = {
                                'path': relative_path,
                                'type': info['type'],
                                'source_file': info['source_file'],
                                'line_number': info['line_number']
                            }

                            print(f"Found file for hashtag reference: {image_name} -> {relative_path}")
                            file_found = True
                            break

                    if file_found:
                        break

                if file_found:
                    break

            if not file_found:
                print(f"WARNING: Could not find file '{filename}' referenced in {info['source_file']}:{info['line_number']}")

        return found_images
    
    def define_character_images(character_images):
        """Define character images in Ren'Py"""
        for character_name, expressions in character_images.items():
            for expression, image_path in expressions.items():
                if expression == 'default':
                    # Define base character image
                    image_tag = character_name
                else:
                    # Define character with expression/pose
                    image_tag = f"{character_name} {expression}"

                # Clean up the image tag (replace spaces and special chars)
                clean_tag = re.sub(r'[^a-zA-Z0-9_\s]', '', image_tag)

                try:
                    # Define the image in Ren'Py
                    renpy.image(clean_tag, image_path)
                    print(f"Defined character image: {clean_tag} -> {image_path}")

                    # Also define with capitalized version for compatibility
                    if clean_tag != clean_tag.capitalize():
                        renpy.image(clean_tag.capitalize(), image_path)
                        print(f"Defined character image: {clean_tag.capitalize()} -> {image_path}")

                except Exception as e:
                    print(f"Error defining character image {clean_tag}: {e}")

    def define_background_images(background_images):
        """Define background images in Ren'Py"""
        for bg_name, image_path in background_images.items():
            try:
                # Define background with 'bg' prefix
                bg_tag = f"bg {bg_name}"
                renpy.image(bg_tag, image_path)
                print(f"Defined background image: {bg_tag}")

                # Also define without 'bg' prefix for direct access
                renpy.image(bg_name, image_path)
                print(f"Defined background image: {bg_name}")

            except Exception as e:
                print(f"Error defining background image {bg_name}: {e}")

    def define_hashtag_images(hashtag_images):
        """Define images found through hashtag references"""
        for image_name, info in hashtag_images.items():
            try:
                image_path = info['path']
                image_type = info['type']

                if image_type == 'background':
                    # Define as background
                    renpy.image(image_name, image_path)
                    print(f"Defined hashtag background: {image_name} -> {image_path}")

                    # Also define without bg_ prefix if it has one
                    if image_name.startswith('bg_'):
                        clean_name = image_name.replace('bg_', '')
                        renpy.image(clean_name, image_path)
                        print(f"Defined hashtag background: {clean_name} -> {image_path}")

                elif image_type == 'character':
                    # Define as character
                    renpy.image(image_name, image_path)
                    print(f"Defined hashtag character: {image_name} -> {image_path}")

            except Exception as e:
                print(f"Error defining hashtag image {image_name}: {e}")

    def define_character_image_statements(define_images):
        """
        Define images from 'define character image' statements
        This replaces placeholder images with actual character images
        """
        for char_key, info in define_images.items():
            try:
                image_path = info['path']
                character_name = info['character_name']

                # Define multiple variations to ensure compatibility
                variations = [
                    character_name.lower(),           # netcode
                    character_name.capitalize(),      # Netcode
                    character_name.upper(),           # NETCODE
                    character_name                    # Original case
                ]

                # Remove duplicates while preserving order
                unique_variations = []
                for var in variations:
                    if var not in unique_variations:
                        unique_variations.append(var)

                for variation in unique_variations:
                    renpy.image(variation, image_path)
                    print(f"Defined character image: {variation} -> {image_path}")

                print(f"Character '{character_name}' placeholder will be replaced with: {image_path}")

            except Exception as e:
                print(f"Error defining character image from statement {character_name}: {e}")

    def replace_placeholder_images():
        """
        Replace existing placeholder images with real images when found
        This ensures that 'show Netcode' commands work with actual images instead of gray placeholders
        If no real image is found, the placeholder remains (gray silhouette character)
        """
        print("Checking for placeholder image replacements...")

        replacements_made = 0

        try:
            # Scan for common character names that might have placeholders
            common_characters = ['netcode', 'Netcode', 'austin', 'Austin', 'player', 'Player',
                               'lumetric', 'Lumetric', 'bearwithus', 'BearWithUs']

            for char_name in common_characters:
                image_found = False

                # Check if we have a real image file for this character
                for pictures_dir in PICTURES_DIRS:
                    if not os.path.exists(pictures_dir):
                        continue

                    for root, dirs, files in os.walk(pictures_dir):
                        for file in files:
                            file_name, file_ext = os.path.splitext(file)

                            # Check for exact match or character_expression pattern
                            if (file_ext.lower() in IMAGE_EXTENSIONS and
                                (file_name.lower() == char_name.lower() or
                                 file_name.lower().startswith(char_name.lower() + '_'))):

                                file_path = os.path.join(root, file)
                                relative_path = os.path.relpath(file_path, ".").replace("\\", "/")

                                # Define the image to replace any existing placeholder
                                try:
                                    # Define multiple case variations to ensure compatibility
                                    renpy.image(char_name.lower(), relative_path)
                                    renpy.image(char_name.capitalize(), relative_path)
                                    if char_name.upper() != char_name.capitalize():
                                        renpy.image(char_name.upper(), relative_path)

                                    print(f"✅ REPLACED placeholder '{char_name}' with actual image: {relative_path}")
                                    replacements_made += 1
                                    image_found = True

                                except Exception as e:
                                    print(f"Error replacing placeholder for {char_name}: {e}")

                                break

                        if image_found:
                            break

                    if image_found:
                        break

                if not image_found:
                    print(f"⚪ No image found for '{char_name}' - placeholder will remain")

            print(f"Placeholder replacement complete: {replacements_made} characters updated")

        except Exception as e:
            print(f"Error in placeholder replacement: {e}")

    def ensure_character_compatibility():
        """
        Ensure character images work with existing script commands like 'show Netcode'
        """
        print("Ensuring character image compatibility...")

        # Common character name patterns to check
        character_patterns = {
            'netcode': ['netcode.jpg', 'netcode.png', 'netcode.gif'],
            'austin': ['austin.jpg', 'austin.png', 'austin.gif', 'austin_happy.png'],
            'player': ['player.jpg', 'player.png', 'player.gif']
        }

        for char_name, possible_files in character_patterns.items():
            for filename in possible_files:
                # Search for the file in our directories
                for pictures_dir in PICTURES_DIRS:
                    if not os.path.exists(pictures_dir):
                        continue

                    for root, dirs, files in os.walk(pictures_dir):
                        if filename in files:
                            file_path = os.path.join(root, filename)
                            relative_path = os.path.relpath(file_path, ".").replace("\\", "/")

                            # Define all case variations
                            try:
                                renpy.image(char_name, relative_path)
                                renpy.image(char_name.capitalize(), relative_path)
                                renpy.image(char_name.upper(), relative_path)
                                print(f"Ensured compatibility: {char_name} -> {relative_path}")
                            except Exception as e:
                                print(f"Error ensuring compatibility for {char_name}: {e}")

                            break

    def scan_scripts_for_character_usage():
        """
        Scan script files to find which characters are being used in 'show' commands
        This helps identify which characters need images vs placeholders
        """
        print("Scanning scripts for character usage...")

        used_characters = set()
        script_files = []

        # Find all .rpy files
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith('.rpy'):
                    script_files.append(os.path.join(root, file))

        for script_file in script_files:
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line_num, line in enumerate(lines, 1):
                    line = line.strip()

                    # Look for 'show' commands
                    if line.startswith('show '):
                        # Extract character name from 'show CharacterName' or 'show character_name expression'
                        import re
                        match = re.match(r'show\s+(\w+)', line)
                        if match:
                            character_name = match.group(1)
                            used_characters.add(character_name)
                            print(f"Found character usage: '{character_name}' in {script_file}:{line_num}")

            except Exception as e:
                print(f"Error scanning script file {script_file}: {e}")

        return used_characters

    def report_placeholder_status():
        """
        Report which characters will show as placeholders vs actual images
        """
        print("\n=== CHARACTER IMAGE STATUS REPORT ===")

        # Get characters used in scripts
        used_characters = scan_scripts_for_character_usage()

        if not used_characters:
            print("No character usage found in scripts")
            return

        for char_name in sorted(used_characters):
            image_found = False
            found_files = []

            # Check if we have actual image files for this character
            for pictures_dir in PICTURES_DIRS:
                if not os.path.exists(pictures_dir):
                    continue

                for root, dirs, files in os.walk(pictures_dir):
                    for file in files:
                        file_name, file_ext = os.path.splitext(file)

                        if (file_ext.lower() in IMAGE_EXTENSIONS and
                            (file_name.lower() == char_name.lower() or
                             file_name.lower().startswith(char_name.lower() + '_'))):

                            relative_path = os.path.relpath(os.path.join(root, file), ".").replace("\\", "/")
                            found_files.append(relative_path)
                            image_found = True

            if image_found:
                print(f"✅ '{char_name}' -> ACTUAL IMAGE: {', '.join(found_files)}")
            else:
                print(f"⚪ '{char_name}' -> PLACEHOLDER (gray silhouette) - no image file found")

        print("=== END REPORT ===\n")

    def auto_define_images():
        """Main function to automatically define all images"""
        print("Starting automatic image definition...")

        # Scan directories for images
        character_images, background_images = scan_and_define_images()

        # Parse hashtag comments for image references
        hashtag_references = parse_hashtag_comments()
        hashtag_images = find_images_for_hashtag_references(hashtag_references)

        # Parse 'define character image' statements
        define_statements = parse_define_character_image()
        define_images = find_images_for_define_statements(define_statements)

        print(f"Found {len(character_images)} characters with images")
        print(f"Found {len(background_images)} background images")
        print(f"Found {len(hashtag_images)} hashtag-referenced images")
        print(f"Found {len(define_images)} 'define character image' statements")

        # Define character images
        define_character_images(character_images)

        # Define background images
        define_background_images(background_images)

        # Define hashtag-referenced images
        define_hashtag_images(hashtag_images)

        # Define images from 'define character image' statements
        define_character_image_statements(define_images)

        # Replace placeholder images with real images
        replace_placeholder_images()

        # Ensure compatibility with existing character commands
        ensure_character_compatibility()

        print("Automatic image definition complete!")

        return character_images, background_images, hashtag_images, define_images

# Automatically run the image definition system at startup
init:
    python:
        # Run the automatic image definition
        try:
            auto_define_images()
        except Exception as e:
            print(f"Error in automatic image definition: {e}")

# Enhanced character definition system
init python:
    def define_characters_from_images(character_images):
        """Define Character objects based on discovered images"""
        for character_name, expressions in character_images.items():
            # Create a proper character name (capitalize first letter)
            display_name = character_name.replace('_', ' ').title()

            # Generate a unique color for each character
            import hashlib
            color_hash = hashlib.md5(character_name.encode()).hexdigest()[:6]
            character_color = f"#{color_hash}"

            try:
                # Define the character object
                char_var_name = f"char_{character_name}"

                # Create character definition string
                char_definition = f'define {char_var_name} = Character("{display_name}", color="{character_color}")'

                # Execute the character definition
                exec(char_definition, globals())
                print(f"Defined character: {char_var_name} = Character('{display_name}', color='{character_color}')")

            except Exception as e:
                print(f"Error defining character {character_name}: {e}")

    def create_image_tags_list():
        """Create a list of all defined image tags for easy reference"""
        character_images, background_images = scan_and_define_images()

        all_tags = []

        # Add character image tags
        for character_name, expressions in character_images.items():
            for expression in expressions.keys():
                if expression == 'default':
                    all_tags.append(character_name)
                else:
                    all_tags.append(f"{character_name} {expression}")

        # Add background image tags
        for bg_name in background_images.keys():
            all_tags.append(f"bg {bg_name}")
            all_tags.append(bg_name)

        return all_tags

    def enhanced_auto_define_images():
        """Enhanced version that also defines characters and handles all reference types"""
        print("Starting enhanced automatic image definition...")

        # Scan directories for images
        character_images, background_images = scan_and_define_images()

        # Parse hashtag comments for image references
        hashtag_references = parse_hashtag_comments()
        hashtag_images = find_images_for_hashtag_references(hashtag_references)

        # Parse 'define character image' statements
        define_statements = parse_define_character_image()
        define_images = find_images_for_define_statements(define_statements)

        print(f"Found {len(character_images)} characters with images")
        print(f"Found {len(background_images)} background images")
        print(f"Found {len(hashtag_images)} hashtag-referenced images")
        print(f"Found {len(define_images)} 'define character image' statements")

        # Define character images
        define_character_images(character_images)

        # Define background images
        define_background_images(background_images)

        # Define hashtag-referenced images
        define_hashtag_images(hashtag_images)

        # Define images from 'define character image' statements
        define_character_image_statements(define_images)

        # Replace placeholder images with real images
        replace_placeholder_images()

        # Ensure compatibility with existing character commands
        ensure_character_compatibility()

        # Generate status report showing which characters will be placeholders vs actual images
        report_placeholder_status()

        # Define character objects
        define_characters_from_images(character_images)

        # Create tags list
        all_tags = create_image_tags_list()
        print(f"Created {len(all_tags)} image tags total")

        print("Enhanced automatic image definition complete!")

        return character_images, background_images, hashtag_images, define_images, all_tags

# Update the initialization to use enhanced version
init:
    python:
        # Run the enhanced automatic image definition
        try:
            enhanced_auto_define_images()
        except Exception as e:
            print(f"Error in enhanced automatic image definition: {e}")

# Manual refresh function for testing
label refresh_images:
    python:
        enhanced_auto_define_images()
    "Images refreshed! Check the console for details."
    return

# Test label to demonstrate usage
label test_dynamic_images:
    "Testing the dynamic image system..."

    python:
        # Get all available image tags
        character_images, background_images, hashtag_images, define_images, all_tags = enhanced_auto_define_images()

        # Show available images
        if character_images:
            narrator("Available characters:")
            for char_name, expressions in character_images.items():
                narrator(f"- {char_name}: {', '.join(expressions.keys())}")

        if background_images:
            narrator("Available backgrounds:")
            for bg_name in background_images.keys():
                narrator(f"- {bg_name}")

        if hashtag_images:
            narrator("Hashtag-referenced images:")
            for img_name, info in hashtag_images.items():
                narrator(f"- {img_name} ({info['type']}) from {info['source_file']}")

        if define_images:
            narrator("'Define character image' statements:")
            for char_key, info in define_images.items():
                narrator(f"- {info['character_name']} -> {info['filename']} from {info['source_file']}")

    "Dynamic image system test complete!"
    return
