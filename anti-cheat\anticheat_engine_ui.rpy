## Anti-Cheat Engine UI Integration
## Custom UI that engages first with game engine integration

init -50 python:
    import platform
    import subprocess
    import re
    
    # Graphics detection system
    graphics_info = {
        'gpu_type': 'unknown',
        'gpu_name': 'Unknown Graphics',
        'is_integrated': False,
        'is_discrete': False,
        'driver_version': 'Unknown',
        'memory': 'Unknown'
    }
    
    def detect_graphics_card():
        """Detect graphics card information"""
        try:
            if platform.system() == "Windows":
                # Use wmic to get GPU information
                result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name,AdapterRAM,DriverVersion'], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            parts = line.strip().split()
                            if len(parts) >= 2:
                                gpu_name = ' '.join(parts[:-2]) if len(parts) > 2 else parts[0]
                                
                                # Detect GPU type
                                gpu_name_lower = gpu_name.lower()
                                
                                if any(x in gpu_name_lower for x in ['intel', 'intel(r) uhd', 'intel(r) hd']):
                                    graphics_info['gpu_type'] = 'intel_integrated'
                                    graphics_info['is_integrated'] = True
                                    graphics_info['gpu_name'] = gpu_name
                                elif any(x in gpu_name_lower for x in ['amd', 'radeon', 'vega']):
                                    if 'integrated' in gpu_name_lower or 'apu' in gpu_name_lower:
                                        graphics_info['gpu_type'] = 'amd_integrated'
                                        graphics_info['is_integrated'] = True
                                    else:
                                        graphics_info['gpu_type'] = 'amd_discrete'
                                        graphics_info['is_discrete'] = True
                                    graphics_info['gpu_name'] = gpu_name
                                elif any(x in gpu_name_lower for x in ['nvidia', 'geforce', 'gtx', 'rtx']):
                                    graphics_info['gpu_type'] = 'nvidia_discrete'
                                    graphics_info['is_discrete'] = True
                                    graphics_info['gpu_name'] = gpu_name
                                
                                break
            
            # Fallback detection
            if graphics_info['gpu_type'] == 'unknown':
                graphics_info['gpu_name'] = 'Generic Graphics Adapter'
                graphics_info['gpu_type'] = 'generic'
                
        except Exception as e:
            renpy.log("Graphics detection failed: {}".format(str(e)))
            graphics_info['gpu_name'] = 'Detection Failed'
            graphics_info['gpu_type'] = 'unknown'
    
    # Detect graphics on startup
    detect_graphics_card()

# Anti-cheat engagement screen
screen anticheat_engagement():
    
    modal True
    
    frame:
        xfill True
        yfill True
        background "#000000"
        
        vbox:
            xalign 0.5
            yalign 0.5
            spacing 30
            
            # Anti-cheat logo/title
            frame:
                background "#1a1a1a"
                padding (40, 20)
                
                vbox:
                    spacing 15
                    xalign 0.5
                    
                    text "🛡️ ANTI-CHEAT SYSTEM" size 36 color "#00ff00" bold True xalign 0.5
                    text "INITIALIZING PROTECTION" size 18 color "#ffffff" xalign 0.5
            
            # System status
            frame:
                background "#2e2e2e"
                padding (30, 25)
                xsize 600
                
                vbox:
                    spacing 12
                    
                    text "System Security Check" size 20 color "#ffaa00" xalign 0.5
                    
                    hbox:
                        spacing 20
                        xalign 0.5
                        
                        vbox:
                            spacing 8
                            
                            text "✓ Memory Protection" size 14 color "#00ff00"
                            text "✓ Process Monitoring" size 14 color "#00ff00"
                            text "✓ File Integrity" size 14 color "#00ff00"
                            text "✓ Network Security" size 14 color "#00ff00"
                        
                        vbox:
                            spacing 8
                            
                            text "✓ Legal Documents" size 14 color "#00ff00"
                            text "✓ Encryption System" size 14 color "#00ff00"
                            text "✓ NSFW Detection" size 14 color "#00ff00"
                            text "✓ Developer Auth" size 14 color "#00ff00" if anticheat_state.get('developer_authenticated', False) else text "○ Developer Auth" size 14 color "#888888"
            
            # Graphics detection
            frame:
                background "#1a1a1a"
                padding (20, 15)
                
                vbox:
                    spacing 10
                    
                    text "Graphics System Detected" size 16 color "#00aaff" xalign 0.5
                    
                    if graphics_info['is_integrated']:
                        text "🔧 Integrated Graphics: {}".format(graphics_info['gpu_name']) size 12 color "#ffaa00" xalign 0.5
                    elif graphics_info['is_discrete']:
                        text "🎮 Discrete Graphics: {}".format(graphics_info['gpu_name']) size 12 color "#00ff00" xalign 0.5
                    else:
                        text "❓ Graphics: {}".format(graphics_info['gpu_name']) size 12 color "#cccccc" xalign 0.5
            
            # Continue button
            textbutton "🚀 ENTER GAME ENGINE" action Return() text_size 20 text_color "#00ff00" xalign 0.5

# Game engine main interface
screen game_engine_interface():
    
    tag menu
    
    # Background
    add "#1a1a1a"
    
    # Main title
    frame:
        xalign 0.5
        ypos 50
        background None
        
        text "NETCODE THE PROTOGEN AND MORE" size 32 color "#00ff00" bold True xalign 0.5
    
    # Game selection area (around line 56 equivalent)
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 400
        background "#2e2e2e"
        
        vbox:
            spacing 20
            xalign 0.5
            yalign 0.5
            
            text "🎮 SELECT VISUAL NOVEL" size 24 color "#ffffff" xalign 0.5
            
            # Game selection buttons (line 56 area)
            vbox:
                spacing 15
                xalign 0.5
                
                textbutton "Visual Novel: Netcode the Protogen" action Jump("Netcode_the_protogen_bedroom_1") text_size 18 xsize 500
                textbutton "Visual Novel: Lumetric" action Jump("Lumetric_1") text_size 18 xsize 500
                textbutton "Visual Novel: BearWithUs" action Jump("BearWithUs_1") text_size 18 xsize 500
                
                null height 20
                
                textbutton "🔧 Game Settings" action ShowMenu("preferences") text_size 16 xsize 300
                textbutton "💾 Load Game" action ShowMenu("load") text_size 16 xsize 300
    
    # Anti-cheat status notification (achievement box style)
    use anticheat_notification_box
    
    # Graphics status notification
    use graphics_status_notification

# Achievement box style anti-cheat notification
screen anticheat_notification_box():
    
    # Sliding notification box (always visible on menu)
    frame:
        xpos 20
        ypos 20
        xsize 300
        ysize 120
        background "#000000dd"
        
        vbox:
            spacing 8
            
            # Header
            hbox:
                spacing 10
                
                text "🛡️" size 20 color "#00ff00"
                text "ANTI-CHEAT ACTIVE" size 14 color "#00ff00" bold True
                
                if anticheat_state.get('developer_authenticated', False):
                    text "👑" size 16 color "#ffd700"
            
            # Status indicators
            vbox:
                spacing 4
                
                hbox:
                    spacing 10
                    text "Status:" size 12 color "#ffffff"
                    if anticheat_state.get('initialized', False):
                        text "PROTECTED" size 12 color "#00ff00" bold True
                    else:
                        text "INITIALIZING" size 12 color "#ffaa00"
                
                hbox:
                    spacing 10
                    text "Violations:" size 12 color "#ffffff"
                    text str(anticheat_state.get('violations', 0)) size 12 color "#ff6666" if anticheat_state.get('violations', 0) > 0 else text "0" size 12 color "#00ff00"
                
                hbox:
                    spacing 10
                    text "Security:" size 12 color "#ffffff"
                    text "MAXIMUM" size 12 color "#00ff00" bold True

# Graphics status notification
screen graphics_status_notification():
    
    # Graphics card status box
    frame:
        xpos 20
        ypos 160
        xsize 300
        ysize 100
        background "#000000dd"
        
        vbox:
            spacing 8
            
            # Header
            hbox:
                spacing 10
                
                if graphics_info['is_integrated']:
                    text "🔧" size 20 color "#ffaa00"
                    text "INTEGRATED GRAPHICS" size 12 color "#ffaa00" bold True
                elif graphics_info['is_discrete']:
                    text "🎮" size 20 color "#00ff00"
                    text "DISCRETE GRAPHICS" size 12 color "#00ff00" bold True
                else:
                    text "❓" size 20 color "#cccccc"
                    text "GRAPHICS DETECTED" size 12 color "#cccccc" bold True
            
            # GPU information
            vbox:
                spacing 4
                
                text "GPU: {}".format(graphics_info['gpu_name'][:25] + "..." if len(graphics_info['gpu_name']) > 25 else graphics_info['gpu_name']) size 10 color "#ffffff"
                
                if graphics_info['is_integrated']:
                    text "⚠️ Performance may vary" size 9 color "#ffaa00"
                elif graphics_info['is_discrete']:
                    text "✓ Optimal performance" size 9 color "#00ff00"
                else:
                    text "ℹ️ Generic driver detected" size 9 color "#cccccc"

# Developer panel access (if authenticated)
screen developer_panel_access():
    
    if anticheat_state.get('developer_authenticated', False):
        frame:
            xpos 20
            ypos 280
            xsize 300
            ysize 80
            background "#000000dd"
            
            vbox:
                spacing 8
                
                hbox:
                    spacing 10
                    text "👑" size 20 color "#ffd700"
                    text "DEVELOPER ACCESS" size 12 color "#ffd700" bold True
                
                textbutton "🔓 Open Control Panel" action Call("open_developer_panel") text_size 10 xsize 250

# Enhanced main menu with anti-cheat integration
screen main_menu():
    
    tag menu
    
    # Use the game engine interface
    use game_engine_interface
    
    # Add developer panel access if authenticated
    use developer_panel_access

# Anti-cheat initialization label
label initialize_anticheat_ui:
    
    # Show anti-cheat engagement screen
    call screen anticheat_engagement
    
    # Initialize all anti-cheat systems
    if hasattr(renpy.store, 'anticheat_core'):
        $ anticheat_core.initialize()
    
    # Initialize legal protection
    if hasattr(renpy.store, 'legal_protector'):
        $ legal_protector.initialize_legal_protection()
    
    # Show success notification
    $ renpy.notify("🛡️ Anti-cheat system engaged")
    
    return

# Game engine main label
label game_engine_main:
    
    # Show the game engine interface
    call screen game_engine_interface
    
    # This should not be reached as the interface handles navigation
    return

# Enhanced game selection with anti-cheat integration
label enhanced_game_selection:
    
    scene bg black
    
    # Show anti-cheat status
    show screen anticheat_notification_box
    show screen graphics_status_notification
    
    # Game selection menu (equivalent to line 56 area)
    menu:
        "🎮 Select your visual novel experience:"
        
        "Visual Novel: Netcode the Protogen":
            # Log game selection
            if hasattr(renpy.store, 'anticheat_core'):
                $ anticheat_core.record_user_action("game_selection", "netcode_protogen")
            
            jump Netcode_the_protogen_bedroom_1
        
        "Visual Novel: Lumetric":
            # Log game selection
            if hasattr(renpy.store, 'anticheat_core'):
                $ anticheat_core.record_user_action("game_selection", "lumetric")
            
            jump Lumetric_1
        
        "Visual Novel: BearWithUs":
            # Log game selection
            if hasattr(renpy.store, 'anticheat_core'):
                $ anticheat_core.record_user_action("game_selection", "bearwithus")
            
            jump BearWithUs_1
        
        "🔧 System Settings" if anticheat_state.get('developer_authenticated', False):
            call screen developer_control_panel
            jump enhanced_game_selection
        
        "❌ Exit":
            return

# Placeholder labels for the games (to prevent errors)
label Netcode_the_protogen_bedroom_1:
    "Starting Netcode the Protogen visual novel..."
    "This is where the Netcode story would begin."
    return

label Lumetric_1:
    "Starting Lumetric visual novel..."
    "This is where the Lumetric story would begin."
    return

label BearWithUs_1:
    "Starting BearWithUs visual novel..."
    "This is where the BearWithUs story would begin."
    return
