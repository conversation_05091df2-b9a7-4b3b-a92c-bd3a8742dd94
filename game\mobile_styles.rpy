# Mobile-specific styles for Universal Game Router
# Optimizes interface for touch devices

init python:
    # Mobile style configuration
    if renpy.variant("mobile") or renpy.variant("android") or renpy.variant("ios"):
        
        # Mobile button styles
        style.mobile_button = Style(style.button)
        style.mobile_button.minimum = (80, 60)
        style.mobile_button.padding = (15, 15, 15, 15)
        style.mobile_button.margin = (5, 5, 5, 5)
        style.mobile_button.background = "#023e8a"
        style.mobile_button.hover_background = "#0077b6"
        # Note: activate_sound is set on individual buttons, not in style
        
        # Mobile text styles
        style.mobile_text = Style(style.default)
        style.mobile_text.size = 16
        style.mobile_text.line_spacing = 4
        
        style.mobile_button_text = Style(style.button_text)
        style.mobile_button_text.size = 14
        style.mobile_button_text.color = "#ffffff"
        style.mobile_button_text.hover_color = "#00b4d8"
        
        # Mobile title styles
        style.mobile_title = Style(style.default)
        style.mobile_title.size = 20
        style.mobile_title.color = "#00b4d8"
        style.mobile_title.bold = True
        
        # Mobile frame styles
        style.mobile_frame = Style(style.frame)
        style.mobile_frame.background = "#001122"
        style.mobile_frame.padding = (10, 10, 10, 10)
        style.mobile_frame.margin = (5, 5, 5, 5)
        
        # Mobile viewport styles
        style.mobile_viewport = Style(style.viewport)
        style.mobile_viewport.xfill = True
        style.mobile_viewport.yfill = True
        
        # Mobile tab styles
        style.mobile_tab = Style(style.button)
        style.mobile_tab.minimum = (80, 40)
        style.mobile_tab.padding = (10, 8, 10, 8)
        style.mobile_tab.background = "#003366"
        style.mobile_tab.hover_background = "#004488"
        # Note: selected states handled in screen logic

        style.mobile_tab_text = Style(style.button_text)
        style.mobile_tab_text.size = 12
        style.mobile_tab_text.color = "#90e0ef"
        style.mobile_tab_text.hover_color = "#caf0f8"
        # Note: selected_color handled in screen logic

# Mobile gesture support
init python:
    def mobile_swipe_left():
        """Handle left swipe gesture"""
        if renpy.get_screen("game_selection"):
            current_tab = getattr(store, 'mobile_tab', 'games')
            tabs = ['games', 'preview', 'details']
            current_index = tabs.index(current_tab) if current_tab in tabs else 0
            next_index = (current_index + 1) % len(tabs)
            store.mobile_tab = tabs[next_index]
            renpy.restart_interaction()
    
    def mobile_swipe_right():
        """Handle right swipe gesture"""
        if renpy.get_screen("game_selection"):
            current_tab = getattr(store, 'mobile_tab', 'games')
            tabs = ['games', 'preview', 'details']
            current_index = tabs.index(current_tab) if current_tab in tabs else 0
            prev_index = (current_index - 1) % len(tabs)
            store.mobile_tab = tabs[prev_index]
            renpy.restart_interaction()
    
    def mobile_double_tap():
        """Handle double tap gesture"""
        # Quick return to main menu
        if renpy.get_screen("game_selection"):
            renpy.return_statement("return")
    
    # Mobile gestures will be handled in screen logic for better compatibility

# Mobile-specific transforms
transform mobile_slide_in:
    alpha 0.0 xoffset 100
    easein 0.3 alpha 1.0 xoffset 0

transform mobile_slide_out:
    alpha 1.0 xoffset 0
    easeout 0.3 alpha 0.0 xoffset -100

transform mobile_bounce:
    zoom 1.0
    easein 0.1 zoom 1.05
    easeout 0.1 zoom 1.0

transform mobile_fade_in:
    alpha 0.0
    easein 0.5 alpha 1.0

# Mobile accessibility features
init python:
    if renpy.variant("mobile") or renpy.variant("android") or renpy.variant("ios"):
        # Larger touch targets (handled in screen definitions)
        
        # Haptic feedback simulation (visual feedback)
        def mobile_haptic_feedback():
            renpy.show_screen("mobile_haptic_flash")
            renpy.restart_interaction()
        
        # Visual feedback for touch (button sounds handled individually)

# Mobile haptic feedback screen
screen mobile_haptic_flash():
    zorder 1000
    timer 0.1 action Hide("mobile_haptic_flash")
    
    add "#ffffff" alpha 0.1

# Mobile orientation support
init python:
    def check_mobile_orientation():
        """Check and adapt to mobile orientation"""
        if renpy.variant("mobile") or renpy.variant("android") or renpy.variant("ios"):
            # Detect orientation based on screen dimensions
            if config.screen_width > config.screen_height:
                # Landscape mode
                store.mobile_orientation = "landscape"
            else:
                # Portrait mode
                store.mobile_orientation = "portrait"
        else:
            store.mobile_orientation = "desktop"
    
    # Check orientation on startup
    config.start_callbacks.append(check_mobile_orientation)

# Initialize orientation
default mobile_orientation = "portrait"

# Mobile performance optimizations
init python:
    if renpy.variant("mobile") or renpy.variant("android") or renpy.variant("ios"):
        # Mobile performance optimizations (using safe config variables only)
        try:
            config.image_cache_size = 8
            config.sound_cache_size = 4
        except:
            pass  # Ignore if config variables don't exist

        # Faster transitions
        config.enter_transition = None
        config.exit_transition = None
        config.intra_transition = None
