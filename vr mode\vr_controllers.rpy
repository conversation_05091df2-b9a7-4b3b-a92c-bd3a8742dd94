# VR Controller Interaction System
# Full controller support for SteamVR with visual novel interaction mechanics

# VR Controller Input Handling
init python:
    import math
    
    # Controller input mapping
    class VRInput:
        def __init__(self):
            self.trigger_threshold = 0.5
            self.grip_threshold = 0.5
            self.touchpad_click_threshold = 0.8
            
        def update_controller_state(self, controller_id, input_data):
            """Update controller state from VR input"""
            if controller_id == 0:  # Left controller
                controller = store.vr_controller_left
            else:  # Right controller
                controller = store.vr_controller_right
            
            # Update position and rotation
            controller.position = input_data.get('position', [0, 0, 0])
            controller.rotation = input_data.get('rotation', [0, 0, 0, 1])
            
            # Update button states
            controller.trigger_pressed = input_data.get('trigger', 0) > self.trigger_threshold
            controller.grip_pressed = input_data.get('grip', 0) > self.grip_threshold
            controller.touchpad_pressed = input_data.get('touchpad_click', False)
            controller.touchpad_position = input_data.get('touchpad_pos', [0, 0])
            controller.menu_pressed = input_data.get('menu', False)
            controller.connected = input_data.get('connected', False)
    
    # Initialize VR input system
    store.vr_input = VRInput()
    
    # VR interaction states
    store.vr_pointing_at = None
    store.vr_selected_element = None
    store.vr_drag_mode = False
    store.vr_laser_visible = True

# VR Ray-casting for UI Interaction
init python:
    def vr_raycast_ui(controller):
        """Cast ray from controller to detect UI elements"""
        # Simplified ray-casting for Ren'Py UI elements
        # In full implementation, this would use proper 3D math
        
        # Get controller position and direction
        pos = controller.position
        rot = controller.rotation
        
        # Calculate ray direction from controller rotation
        # Simplified: assume controller points forward
        ray_direction = [0, 0, -1]  # Forward direction
        
        # Check intersection with UI panels
        for panel_name, panel_pos in store.vr_ui_panels.items():
            distance = calculate_distance(pos, panel_pos)
            if distance < 0.5:  # Within interaction range
                return panel_name
        
        return None
    
    def calculate_distance(pos1, pos2):
        """Calculate 3D distance between two points"""
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1] 
        dz = pos1[2] - pos2[2]
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def vr_interact_with_element(element_name):
        """Interact with VR UI element"""
        if element_name == "main_menu":
            renpy.call_in_new_context("main_menu")
        elif element_name == "game_selection":
            renpy.call_in_new_context("safe_game_launcher")
        elif element_name == "settings":
            renpy.show_screen("vr_settings")

# VR Gesture Recognition
init python:
    class VRGesture:
        def __init__(self):
            self.gesture_history = []
            self.max_history = 30  # 30 frames of gesture data
            
        def add_gesture_point(self, controller_pos):
            """Add controller position to gesture history"""
            self.gesture_history.append(controller_pos)
            if len(self.gesture_history) > self.max_history:
                self.gesture_history.pop(0)
        
        def detect_swipe_left(self):
            """Detect left swipe gesture"""
            if len(self.gesture_history) < 10:
                return False
            
            start_x = self.gesture_history[0][0]
            end_x = self.gesture_history[-1][0]
            
            return (start_x - end_x) > 0.3  # 30cm swipe
        
        def detect_swipe_right(self):
            """Detect right swipe gesture"""
            if len(self.gesture_history) < 10:
                return False
            
            start_x = self.gesture_history[0][0]
            end_x = self.gesture_history[-1][0]
            
            return (end_x - start_x) > 0.3  # 30cm swipe
        
        def detect_point_gesture(self):
            """Detect pointing gesture"""
            # Check if controller is held steady in pointing position
            if len(self.gesture_history) < 5:
                return False
            
            # Check for stability (low movement)
            recent_positions = self.gesture_history[-5:]
            max_variance = 0.05  # 5cm variance allowed
            
            for i in range(1, len(recent_positions)):
                distance = calculate_distance(recent_positions[0], recent_positions[i])
                if distance > max_variance:
                    return False
            
            return True
    
    # Initialize gesture recognition
    store.vr_gesture_left = VRGesture()
    store.vr_gesture_right = VRGesture()

# VR Haptic Feedback
init python:
    def vr_haptic_pulse(controller_id, intensity=0.5, duration=0.1):
        """Send haptic pulse to VR controller"""
        # In full implementation, this would call OpenVR haptic APIs
        # For now, we'll simulate with visual feedback
        
        if controller_id == 0:
            renpy.show_screen("vr_haptic_left", intensity=intensity, duration=duration)
        else:
            renpy.show_screen("vr_haptic_right", intensity=intensity, duration=duration)
    
    def vr_haptic_click():
        """Haptic feedback for button clicks"""
        vr_haptic_pulse(1, 0.7, 0.05)  # Right controller, strong, short
    
    def vr_haptic_hover():
        """Haptic feedback for UI hover"""
        vr_haptic_pulse(1, 0.3, 0.02)  # Right controller, light, very short

# VR Controller Visual Representation
screen vr_controller_left():
    if vr_controller_left.connected:
        # Left controller model
        add "vr_controller_model.png":
            xpos vr_controller_left.position[0] * 100 + 400
            ypos vr_controller_left.position[1] * 100 + 300
            alpha 0.8
            
        # Left controller laser pointer
        if vr_laser_visible and vr_controller_left.trigger_pressed:
            add "#ff0000":
                xpos vr_controller_left.position[0] * 100 + 400
                ypos vr_controller_left.position[1] * 100 + 300
                xsize 3
                ysize 200
                alpha 0.6

screen vr_controller_right():
    if vr_controller_right.connected:
        # Right controller model
        add "vr_controller_model.png":
            xpos vr_controller_right.position[0] * 100 + 400
            ypos vr_controller_right.position[1] * 100 + 300
            alpha 0.8
            
        # Right controller laser pointer
        if vr_laser_visible and vr_controller_right.trigger_pressed:
            add "#00ff00":
                xpos vr_controller_right.position[0] * 100 + 400
                ypos vr_controller_right.position[1] * 100 + 300
                xsize 3
                ysize 200
                alpha 0.6

# VR Haptic Feedback Visual
screen vr_haptic_left(intensity=0.5, duration=0.1):
    timer duration action Hide("vr_haptic_left")
    
    add "#ffffff":
        alpha intensity * 0.3
        xpos vr_controller_left.position[0] * 100 + 390
        ypos vr_controller_left.position[1] * 100 + 290
        xsize 20
        ysize 20

screen vr_haptic_right(intensity=0.5, duration=0.1):
    timer duration action Hide("vr_haptic_right")
    
    add "#ffffff":
        alpha intensity * 0.3
        xpos vr_controller_right.position[0] * 100 + 390
        ypos vr_controller_right.position[1] * 100 + 290
        xsize 20
        ysize 20

# VR Controller Input Processing
label vr_controller_update:
    """Update VR controller states and handle input"""
    
    python:
        # Simulate controller input (in real implementation, this would come from OpenVR)
        if steamvr_mode_active:
            # Update gesture recognition
            if vr_controller_left.connected:
                vr_gesture_left.add_gesture_point(vr_controller_left.position)
            if vr_controller_right.connected:
                vr_gesture_right.add_gesture_point(vr_controller_right.position)
            
            # Check for interactions
            pointing_at = vr_raycast_ui(vr_controller_right)
            if pointing_at != store.vr_pointing_at:
                store.vr_pointing_at = pointing_at
                if pointing_at:
                    vr_haptic_hover()
            
            # Handle trigger press
            if vr_controller_right.trigger_pressed and pointing_at:
                vr_haptic_click()
                vr_interact_with_element(pointing_at)
            
            # Handle gestures
            if vr_gesture_right.detect_swipe_left():
                # Swipe left - previous page/option
                renpy.call_in_new_context("vr_swipe_left")
                vr_gesture_right.gesture_history.clear()
            
            if vr_gesture_right.detect_swipe_right():
                # Swipe right - next page/option
                renpy.call_in_new_context("vr_swipe_right")
                vr_gesture_right.gesture_history.clear()
            
            # Handle menu button
            if vr_controller_right.menu_pressed:
                renpy.show_screen("vr_menu")
    
    return

# VR Gesture Actions
label vr_swipe_left:
    "VR Gesture: Swipe Left detected"
    # Handle left swipe action (e.g., previous page)
    return

label vr_swipe_right:
    "VR Gesture: Swipe Right detected"
    # Handle right swipe action (e.g., next page)
    return

label vr_click_handler:
    "VR Click detected"
    # Handle VR click action
    return

# VR Controller Calibration
screen vr_controller_calibration():
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        background "#001122"
        padding (40, 40)
        
        vbox:
            spacing 30
            xalign 0.5
            
            text "🎮 VR Controller Calibration" style "steamvr_title" xalign 0.5
            
            text "Please follow the instructions to calibrate your VR controllers:" style "steamvr_text"
            
            vbox:
                spacing 20
                
                text "1. Hold both controllers in front of you" style "steamvr_text"
                text "2. Press and hold both triggers" style "steamvr_text"
                text "3. Point at the center of your play area" style "steamvr_text"
                text "4. Release triggers when ready" style "steamvr_text"
            
            hbox:
                spacing 30
                xalign 0.5
                
                textbutton "Start Calibration" style "steamvr_button":
                    action Function(start_vr_calibration)
                
                textbutton "Skip" style "steamvr_button":
                    action Hide("vr_controller_calibration")

# VR Calibration Functions
init python:
    def start_vr_calibration():
        """Start VR controller calibration process"""
        store.vr_calibrating = True
        renpy.show_screen("vr_calibration_progress")
    
    def complete_vr_calibration():
        """Complete VR calibration"""
        store.vr_calibrating = False
        store.vr_calibrated = True
        renpy.hide_screen("vr_calibration_progress")
        renpy.hide_screen("vr_controller_calibration")

# Initialize VR controller variables
default vr_calibrating = False
default vr_calibrated = False
default vr_laser_visible = True
