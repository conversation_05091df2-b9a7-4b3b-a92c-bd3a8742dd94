# Universal Game Router System - Enhanced with Mobile Support
# This system allows the engine to recognize and route to separate game files
# without deleting them or causing label conflicts
# Now includes mobile-responsive interface and touch controls

# Platform detection and configuration with VR support
init -15 python:
    # Detect platform type including VR
    def detect_platform():
        # Check for SteamVR first (PC VR headsets)
        if not renpy.variant("mobile") and not renpy.variant("android"):
            try:
                if 'detect_steamvr' in globals() and detect_steamvr():
                    return "steamvr"
            except:
                pass

        # Check for Quest VR mode (Quest headsets on Android)
        if renpy.variant("android"):
            # Check if running on Quest VR headset
            try:
                import os
                import subprocess

                # Check for Quest-specific indicators
                quest_indicators = [
                    "oculus",
                    "quest",
                    "vr",
                    "meta"
                ]

                # Check system properties for Quest/VR indicators
                try:
                    # Check if we're in VR mode by looking for VR-specific properties
                    result = subprocess.check_output(['getprop', 'ro.product.model'],
                                                   stderr=subprocess.DEVNULL,
                                                   universal_newlines=True).lower()

                    for indicator in quest_indicators:
                        if indicator in result:
                            return "vr"
                except:
                    pass

                # Check for VR environment variables
                vr_env_vars = ['VR_MODE', 'OCULUS_VR', 'QUEST_VR']
                for var in vr_env_vars:
                    if os.environ.get(var):
                        return "vr"

                # If Android but no VR indicators, it's mobile
                return "mobile"

            except:
                # Fallback to mobile if VR detection fails
                return "mobile"
        elif renpy.variant("mobile") or renpy.variant("ios"):
            return "mobile"
        else:
            return "desktop"

    # Store platform detection
    store.current_platform = detect_platform()

# Mobile-specific configuration
init -14 python:
    if store.current_platform == "mobile":
        # Enable touch gestures
        try:
            config.gestures = True
        except:
            pass

        # Mobile-specific preferences
        try:
            preferences.text_cps = 40  # Slower text for mobile reading
            preferences.auto_forward_time = 3.0  # Longer auto-forward time
        except:
            pass

        # Enable drag scrolling
        try:
            config.mouse_hide_time = 0
        except:
            pass

        # Touch-friendly settings
        try:
            config.longpress_duration = 0.5
            config.longpress_radius = 15
        except:
            pass

# VR-specific configuration (Quest headsets)
init -14 python:
    if store.current_platform == "vr":
        # VR mode configuration for Quest headsets
        try:
            # Enable VR-specific settings
            config.gl_resize = True
            config.gl_enable = True

            # VR display settings
            config.screen_width = 2880  # Quest 2/3 resolution width
            config.screen_height = 1700  # Quest 2/3 resolution height

            # VR-optimized performance
            config.image_cache_size = 16  # Higher cache for VR
            config.sound_cache_size = 8

            # VR interaction settings
            config.mouse_hide_time = 0  # Always show cursor in VR
            config.gestures = True  # Enable hand tracking gestures

            # VR-specific preferences
            preferences.text_cps = 30  # Slightly faster text for VR reading
            preferences.auto_forward_time = 4.0  # Longer auto-forward for VR

        except:
            pass

# Desktop-specific configuration
init -13 python:
    if store.current_platform == "desktop":
        # Desktop-specific configuration
        try:
            # Ensure mouse cursor is visible and responsive
            config.mouse_hide_time = 1.0  # Hide mouse after 1 second of inactivity
            config.mouse_displayable = None  # Use default cursor
        except:
            pass

# Platform switching function
init python:
    def switch_to_mobile():
        """Manually switch to mobile interface"""
        store.current_platform = "mobile"
        store.mobile_tab = "games"
        renpy.restart_interaction()

    def switch_to_desktop():
        """Manually switch to desktop interface"""
        store.current_platform = "desktop"
        renpy.restart_interaction()

    def switch_to_vr():
        """Manually switch to VR interface (Quest VR)"""
        store.current_platform = "vr"
        store.vr_mode_active = True
        renpy.restart_interaction()

    def switch_to_steamvr():
        """Manually switch to SteamVR interface (PC VR)"""
        try:
            # Check if SteamVR functions are available
            if 'enable_steamvr' in globals() and enable_steamvr():
                store.current_platform = "steamvr"
                store.steamvr_mode_active = True

                # Initialize advanced VR features if available
                if 'init_advanced_vr_features' in globals():
                    init_advanced_vr_features()

                # Show advanced features notification if any detected
                advanced_features = []
                if hasattr(store, 'eye_tracking_active') and eye_tracking_active:
                    advanced_features.append("Eye Tracking")
                if hasattr(store, 'full_body_tracking_active') and full_body_tracking_active:
                    advanced_features.append("Body Tracking")
                if hasattr(store, 'advanced_haptics_active') and advanced_haptics_active:
                    advanced_features.append("Advanced Haptics")

                if advanced_features:
                    renpy.notify(f"SteamVR + {', '.join(advanced_features)} activated!")
                else:
                    renpy.notify("SteamVR activated!")

                renpy.restart_interaction()
            else:
                renpy.notify("SteamVR not detected or failed to initialize")
        except:
            # Fallback if SteamVR system not available
            store.current_platform = "steamvr"
            store.steamvr_mode_active = True
            renpy.notify("SteamVR mode activated (basic)")
            renpy.restart_interaction()

    def get_current_platform():
        """Get current platform setting"""
        return getattr(store, 'current_platform', 'desktop')

    # Safe VR function stubs (will be overridden by VR modules if loaded)
    def enable_steamvr():
        """Stub function for SteamVR enabling"""
        return False

    def detect_steamvr():
        """Stub function for SteamVR detection"""
        return False

    def init_advanced_vr_features():
        """Stub function for advanced VR features"""
        pass

    def is_vr_mode():
        """Check if currently in VR mode"""
        return get_current_platform() == "vr"

    def enable_vr_mode():
        """Enable VR mode with 3D settings"""
        store.current_platform = "vr"
        store.vr_mode_active = True

        # Apply VR-specific transforms and settings
        try:
            # Enable 3D rendering if available
            config.gl_enable = True
            config.gl_resize = True
        except:
            pass

        renpy.restart_interaction()

    def disable_vr_mode():
        """Disable VR mode and return to mobile"""
        if renpy.variant("android"):
            store.current_platform = "mobile"
        else:
            store.current_platform = "desktop"
        store.vr_mode_active = False
        renpy.restart_interaction()

# Initialize platform-specific variables
default mobile_tab = "games"
default mobile_selected_game = None
default mobile_selected_game_data = None
default mobile_double_tap_time = 0.5  # Time window for double tap detection
default current_platform = "desktop"  # Default fallback
default vr_mode_active = False  # Quest VR mode state
default steamvr_mode_active = False  # SteamVR mode state
default vr_depth_enabled = True  # 3D depth effect in VR
default vr_hand_tracking = True  # Hand tracking support

# Safe VR variable initialization
default eye_tracking_active = False
default full_body_tracking_active = False
default advanced_haptics_active = False
default vr_laser_visible = True

init -10 python:
    # Game routing configuration
    game_routes = {
        "netcode": {
            "path": "separate games/netcode/",
            "main_file": "netcode bedroom 1.rpy",
            "entry_label": "Netcode_the_protogen_bedroom_1",
            "backup_entry": "netcode_complete.rpy",
            "external_integration": "netcode_bedroom_1_external_integration",
            "display_name": "Netcode the Protogen",
            "category": "zwute studio",
            "description title": "Complete deno of netcode the protogen and visual novel with branching storylines",
            "description full": "Dive deep into the world of Netcode the Protogen, where technology meets emotion in an epic visual novel experience. Follow the journey of a unique protogen character as they navigate through complex relationships, technological challenges, and branching storylines that will keep you engaged for hours. With multiple endings and character development paths, every choice you make shapes the narrative in meaningful ways. Experience cutting-edge visual novel storytelling with beautiful artwork, compelling dialogue, and immersive world-building that brings this cybernetic universe to life.",
            "image": "images/logo of the games/netcode_preview.png",
            "ui_visibility": "all"
        },
        "lumetric": {
            "path": "separate games/Lumrtric/",
            "main_file": "Lumrtric bedroom 1.rpy",
            "entry_label": "Lumetric_1",
            "backup_entry": "lumetric_game.rpy",
            "display_name": "Lumetric",
            "category": "streamers furrys",
            "description title": "femboy and streamer story with multiple paths",
            "description full": "Enter the thrilling world of Lumetric, where streaming culture meets unique character development in an unexpected twist of fate. Follow the adventures of a popular streamer who must navigate both the digital world of content creation and personal relationships that develop along the way. With multiple branching paths, your decisions will determine not only the fate of the main character but also their streaming career and relationships with viewers and fellow content creators. Experience the unique blend of modern internet culture and character-driven storytelling in this innovative visual novel.",
            "image": "images/logo of the games/lumetric_preview.png",
            "ui_visibility": "all"
        },
        "bearwithus": {
            "path": "separate games/BearWithUs/",
            "main_file": "BearWithUs.rpy",
            "entry_label": "BearWithUs_1",
            "backup_entry": "bearwithus_game.rpy",
            "display_name": "BearWithUs",
            "category": "streamers bears",
            "description title": "gaming bear of the arcade",
            "description full": "Step into the nostalgic world of BearWithUs, where a lovable gaming bear runs the most popular arcade in town. Experience heartwarming stories of friendship, competition, and the golden age of arcade gaming. Meet colorful characters, participate in gaming tournaments, and discover the deeper connections that form around shared gaming experiences. This charming visual novel celebrates gaming culture while telling touching stories about community, perseverance, and the joy of play. With multiple storylines focusing on different arcade patrons and their personal journeys, every playthrough offers new discoveries.",
            "image": "images/logo of the games/bearwithus_preview.png"
        },
        "demo": {
            "path": "separate games/Demo Game/",
            "main_file": "DemoGame.rpy",
            "entry_label": "demo_game_start",
            "backup_entry": None,
            "display_name": "Demo Game",
            "category": "miscellaneous",
            "description title": "Demonstration of the launcher system capabilities",
            "description full": "This comprehensive demo showcases all the advanced features of the Universal Game Router system. Experience the seamless integration of multiple games within a single launcher, complete with anti-cheat protection, security monitoring, and cross-platform compatibility. The demo includes examples of save system integration, character customization, branching dialogue systems, and all the technical capabilities that make this launcher system unique. Perfect for developers and players who want to understand the full potential of this advanced visual novel framework.",
            "image": "images/logo of the games/demo_preview.png",
            "ui_visibility": "all"
        },

        # Official Furry Community Games - Only for new UI
        "hopkin_girls_combat": {
            "path": "separate games/hopkin girls combat team/",
            "main_file": "hopkin_girls_combat.rpy",
            "entry_label": "hopkin_girls_combat_start",
            "backup_entry": None,
            "display_name": "Hopkin Girls Combat Team",
            "category": "zwute studio",
            "description title": "Epic visual novel featuring 20 hopkin girls with extensive dialogue and character development",
            "description full": "Immerse yourself in the most comprehensive Hopkin Girls Combat Team visual novel ever created, featuring an incredible cast of 20 unique hopkin warriors: Goddess Luxury, Lumetric Lua, Delta Lola, Luma Ama, Loma Star, Rice Rose, Blada Data, Ruthless Ace, Bikini Reda, Hydra Gujia, Cigar Scar, Diva, Aradara, Hopkin Witha, Saraura, Cigar Horizon, Moma, Mona Horizon, Hopkin Beacch, Rise Buey, and Bita. This epic visual novel features extensive dialogue systems, detailed character interactions, dramatic opening sequences, and deep storytelling that brings each hopkin girl to life with their own unique personality, backstory, and combat specialization. Experience rich visual storytelling with branching dialogue trees, character development arcs, and immersive world-building that honors the official hopkin species lore while delivering an unforgettable narrative experience.",
            "image": "images/logo of the games/hopkin_girls_preview.png",
            "ui_visibility": "new_only"
        },
        "hopkin_boys_combat": {
            "path": "separate games/hopkin boy combat team/",
            "main_file": "hopkin_boys_combat.rpy",
            "entry_label": "hopkin_boys_combat_start",
            "backup_entry": None,
            "display_name": "Hopkin Boy Combat Team",
            "category": "zwute studio",
            "description title": "Official Hopkin species male combat team visual novel",
            "description full": "Join the official Hopkin Boy Combat Team in this canonical visual novel featuring the authentic lore of the Hopkin species. Experience the brotherhood, tactical prowess, and unwavering determination of the male Hopkin warriors as they face challenges that test both their combat skills and their bonds. Based on official furry community lore, this story explores the rich cultural heritage, combat traditions, and honor code that defines the Hopkin male warrior class.",
            "image": "images/logo of the games/hopkin_boys_preview.png",
            "ui_visibility": "new_only"
        },
        "nardodragon_combat": {
            "path": "separate games/nardodragon combat team/",
            "main_file": "nardodragon_combat.rpy",
            "entry_label": "nardodragon_combat_start",
            "backup_entry": None,
            "display_name": "Nardodragon Combat Team",
            "category": "zwute studio",
            "description title": "Official Nardodragon species combat visual novel",
            "description full": "Soar into the official world of the Nardodragon species in this authentic visual novel based on established furry community lore. Command the legendary Nardodragon Combat Team as they utilize their unique draconic abilities, ancient wisdom, and fierce loyalty to protect their realm. This story features the official Nardodragon characteristics including their elemental powers, hierarchical society, and the sacred bonds between dragon and rider that define their culture.",
            "image": "images/logo of the games/nardodragon_preview.png",
            "ui_visibility": "new_only"
        },
        "avali_combat": {
            "path": "separate games/avali combat team/",
            "main_file": "avali_combat.rpy",
            "entry_label": "avali_combat_start",
            "backup_entry": None,
            "display_name": "Avali Combat Team",
            "category": "zwute studio",
            "description title": "Official Avali species combat team visual novel",
            "description full": "Experience the official Avali species lore in this canonical visual novel featuring the renowned Avali Combat Team. Navigate the complex pack dynamics, advanced technology, and survival instincts that define the Avali culture. Based on the official furry community canon, this story explores their arctic origins, pack mentality, technological prowess, and the fierce loyalty that binds Avali teams together in the face of any challenge.",
            "image": "images/logo of the games/avali_preview.png",
            "ui_visibility": "new_only"
        },
        "rexouium_combat": {
            "path": "separate games/Rexouium combat team/",
            "main_file": "rexouium_combat.rpy",
            "entry_label": "rexouium_combat_start",
            "backup_entry": None,
            "display_name": "Rexouium Combat Team",
            "category": "zwute studio",
            "description title": "Official Rexouium species combat visual novel",
            "description full": "Dive into the official Rexouium species universe with this authentic visual novel based on established furry community lore. Join the elite Rexouium Combat Team as they showcase their unique bio-mechanical nature, adaptive abilities, and pack coordination. This story features the official Rexouium characteristics including their synthetic-organic hybrid nature, advanced sensory capabilities, and the complex social structures that govern their society.",
            "image": "images/logo of the games/rexouium_preview.png",
            "ui_visibility": "new_only"
        },
        "austin_wickerbeast": {
            "path": "separate games/austin the wickerbeast/",
            "main_file": "austin the wickerbeast.rpy",
            "entry_label": "austin_wickerbeast_start",
            "backup_entry": None,
            "display_name": "Austin the Wickerbeast",
            "category": "zwute studio",
            "description title": "Official Wickerbeast species character visual novel",
            "description full": "Follow Austin in this official Wickerbeast species visual novel based on authentic furry community lore. Experience the unique world of the Wickerbeast through Austin's adventures, exploring their distinctive plant-like characteristics, symbiotic nature, and deep connection to natural environments. This canonical story delves into official Wickerbeast biology, their role as guardians of nature, and the mystical abilities that make them one of the most fascinating species in the furry community.",
            "image": "images/logo of the games/austin_wickerbeast_preview.png",
            "ui_visibility": "new_only"
        },
        "mamanightscale_dragons": {
            "path": "separate games/mamanightscale of the dead & the last dragon/",
            "main_file": "mamanightscale_dragons.rpy",
            "entry_label": "mamanightscale_dragons_start",
            "backup_entry": None,
            "display_name": "Mamanightscale of the Dead & The Last Dragon",
            "category": "zwute studio",
            "description title": "Official dragon species epic visual novel",
            "description full": "Experience the epic tale of Mamanightscale in this official dragon species visual novel based on authentic furry community lore. Journey through a world where ancient dragons face extinction, following the legendary Mamanightscale as she navigates between the realm of the living and the dead. This canonical story explores official dragon mythology, their ancient powers, the sacred duty of dragon guardians, and the mystical connection between life, death, and rebirth in dragon culture.",
            "image": "images/logo of the games/mamanightscale_preview.png",
            "ui_visibility": "new_only"
        },
        "atlyss": {
            "path": "separate games/adventures of atlyss/",
            "main_file": "adventures of atlyss.rpy",
            "entry_label": "adventures_of_atlyss_start",
            "backup_entry": None,
            "display_name": "Adventures of Atlyss",
            "category": "atlyss",
            "description title": "A romantic adventure visual novel based on the Steam game Atlyss",
            "description full": "Embark on an epic romantic adventure in the mystical realm of Atlyss, inspired by the popular Steam game. Choose your adventurer class and meet five unique love interests: Kira the fierce warrior, Zara the mystical mage, Luna the gentle healer, Nova the adventurous rogue, and Sage the wise scholar. Experience typical visual novel dialogue, character selection mechanics, and dating simulation as you explore dungeons, build relationships, and discover love in this fantasy world. With branching storylines based on your choices and class selection, every playthrough offers new romantic possibilities and adventures.",
            "image": "images/logo of the games/atlyss_preview.png",
            "ui_visibility": "all"
        }
    }

    # Loading screen function for game launches
    def launch_game_with_loading(game_info):
        """Launch a game with loading screen animation"""
        try:
            # Show loading screen which will handle the actual game launch
            renpy.call("show_loading_screen", game_info)
        except Exception as e:
            # Fallback - just launch the game directly
            renpy.return_statement(game_info["key"])

    # Direct game launcher function
    def launch_game_direct(game_info):
        """Launch a game directly through the launcher labels"""
        game_key = game_info["key"]
        if game_key == "netcode":
            renpy.call("launch_netcode_game")
        elif game_key == "lumetric":
            renpy.call("launch_lumetric_game")
        elif game_key == "bearwithus":
            renpy.call("launch_bearwithus_game")
        elif game_key == "demo":
            renpy.call("launch_demo_game")
        elif game_key == "hopkin_girls_combat":
            renpy.call("launch_hopkin_girls_game")
        elif game_key == "atlyss":
            renpy.call("launch_atlyss_game")
        elif game_key == "austin_wickerbeast":
            renpy.call("launch_austin_game")
        else:
            # Use the loading function for other games
            launch_game_with_loading(game_info)

    # Mobile-specific click handling function
    def mobile_game_click(game_key):
        """Handle mobile game clicks - single tap shows details, double tap loads game"""
        import time
        current_time = time.time()

        # Check if this is a double tap (within time window and same game)
        if (hasattr(store, 'mobile_last_tap_time') and
            hasattr(store, 'mobile_last_tapped_game') and
            current_time - store.mobile_last_tap_time < store.mobile_double_tap_time and
            store.mobile_last_tapped_game == game_key):

            # Double tap - load the game
            store.mobile_last_tap_time = 0  # Reset to prevent triple tap
            store.mobile_last_tapped_game = None
            return ("load_game", game_key)
        else:
            # Single tap - show game details
            store.mobile_last_tap_time = current_time
            store.mobile_last_tapped_game = game_key
            store.mobile_selected_game = game_key

            # Get game data for preview
            if game_key in game_routes:
                store.mobile_selected_game_data = game_routes[game_key]

            # Switch to preview tab to show the details
            store.mobile_tab = "preview"
            return ("show_details", game_key)

    # Initialize tap tracking variables for both mobile and PC
    if not hasattr(store, 'mobile_last_tap_time'):
        store.mobile_last_tap_time = 0
    if not hasattr(store, 'mobile_last_tapped_game'):
        store.mobile_last_tapped_game = None
    if not hasattr(store, 'pc_last_click_time'):
        store.pc_last_click_time = 0
    if not hasattr(store, 'pc_last_clicked_game'):
        store.pc_last_clicked_game = None
    if not hasattr(store, 'pc_selected_game'):
        store.pc_selected_game = None
    if not hasattr(store, 'pc_selected_game_data'):
        store.pc_selected_game_data = None

    # Mobile game tap handler for the mobile interface
    def mobile_game_tap(game):
        """Handle mobile game taps - single tap shows details in preview tab, double tap loads game"""
        import time
        current_time = time.time()
        game_key = game["key"]

        # Check if this is a double tap (within time window and same game)
        if (hasattr(store, 'mobile_last_tap_time') and
            hasattr(store, 'mobile_last_tapped_game') and
            current_time - store.mobile_last_tap_time < store.mobile_double_tap_time and
            store.mobile_last_tapped_game == game_key):

            # Double tap - load the game
            store.mobile_last_tap_time = 0  # Reset to prevent triple tap
            store.mobile_last_tapped_game = None
            renpy.return_statement(game_key)  # Load the game
        else:
            # Single tap - show game details and switch to preview tab
            store.mobile_last_tap_time = current_time
            store.mobile_last_tapped_game = game_key
            store.mobile_selected_game = game_key
            store.mobile_selected_game_data = game

            # Switch to preview tab so user can see image and scroll through full description
            store.mobile_tab = "preview"
            renpy.restart_interaction()

    # PC game click handler (same behavior as mobile but shows in left preview panel)
    def pc_game_click(game):
        """Handle PC game clicks - single click shows details in left preview panel, double click loads game"""
        import time
        current_time = time.time()
        game_key = game["key"]

        # Check if this is a double click (within time window and same game)
        if (hasattr(store, 'pc_last_click_time') and
            hasattr(store, 'pc_last_clicked_game') and
            current_time - store.pc_last_click_time < store.mobile_double_tap_time and
            store.pc_last_clicked_game == game_key):

            # Double click - load the game
            store.pc_last_click_time = 0  # Reset to prevent triple click
            store.pc_last_clicked_game = None
            renpy.return_statement(game_key)  # Load the game
        else:
            # Single click - show game details in left preview panel
            store.pc_last_click_time = current_time
            store.pc_last_clicked_game = game_key
            store.pc_selected_game = game_key
            store.pc_selected_game_data = game

            # Update the preview panel to show image and scrollable full description
            renpy.restart_interaction()

    # Function to check if a game file exists
    def check_game_exists(game_key):
        if game_key not in game_routes:
            return False
        
        route = game_routes[game_key]
        main_path = route["path"] + route["main_file"]
        
        # Check if main file exists
        try:
            if renpy.loadable(main_path):
                return True
        except:
            pass
        
        # Check backup file if available
        if route["backup_entry"]:
            backup_path = route["path"] + route["backup_entry"]
            try:
                if renpy.loadable(backup_path):
                    return True
            except:
                pass
        
        return False

    # Function to get available games (for old UI - shows all games)
    def get_available_games():
        available = []
        for game_key, route in game_routes.items():
            if check_game_exists(game_key):
                available.append({
                    "key": game_key,
                    "name": route["display_name"],
                    "description": route.get("description title", route.get("description", "No description available")),
                    "description_full": route.get("description full", route.get("description title", "No detailed description available")),
                    "image": route.get("image", "images/logo of the games/default_preview.png"),
                    "category": route.get("category", "miscellaneous"),
                    "entry_label": route["entry_label"]
                })
        return available

    # Function to get available games for new UI (filters by ui_visibility)
    def get_new_ui_games():
        available = []
        for game_key, route in game_routes.items():
            # Only include games with "new_only" or "all" ui_visibility
            ui_visibility = route.get("ui_visibility", "all")
            if ui_visibility in ["new_only", "all"] and check_game_exists(game_key):
                available.append({
                    "key": game_key,
                    "name": route["display_name"],
                    "description": route.get("description title", route.get("description", "No description available")),
                    "description_full": route.get("description full", route.get("description title", "No detailed description available")),
                    "image": route.get("image", "images/logo of the games/default_preview.png"),
                    "category": route.get("category", "miscellaneous"),
                    "entry_label": route["entry_label"]
                })
        return available

    # Function to safely route to a game
    def route_to_game(game_key):
        if game_key not in game_routes:
            return False
        
        route = game_routes[game_key]
        entry_label = route["entry_label"]
        
        # Check if the label exists and is callable
        try:
            if renpy.has_label(entry_label):
                return entry_label
        except:
            pass
        
        return False

# Label conflict resolution system
init python:
    # Store original label mappings to prevent conflicts
    original_labels = {}
    
    def register_label_mapping(original_label, redirect_label):
        """Register a label mapping for conflict resolution"""
        original_labels[original_label] = redirect_label
    
    def resolve_label_conflict(label_name):
        """Resolve label conflicts by returning the correct redirect"""
        if label_name in original_labels:
            return original_labels[label_name]
        return label_name

# Register label mappings to resolve conflicts
init python:
    # Map conflicting labels to their integrated versions
    register_label_mapping("BearWithUs_1_separate", "BearWithUs_1")
    register_label_mapping("Lumetric_1_separate", "Lumetric_1") 
    register_label_mapping("Netcode_the_protogen_bedroom_1_separate", "Netcode_the_protogen_bedroom_1")



# Game selection screen
screen game_selection_screen(games):
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        
        vbox:
            spacing 20
            xalign 0.5
            yalign 0.5
            
            text "Select a Game to Play" size 40 xalign 0.5
            
            viewport:
                scrollbars "vertical"
                mousewheel True
                xsize 750
                ysize 400
                
                vbox:
                    spacing 15
                    
                    for game_name, game_key in games:
                        if game_key != "return":
                            python:
                                game_info = None
                                for g in get_available_games():
                                    if g["key"] == game_key:
                                        game_info = g
                                        break
                            
                            frame:
                                xsize 700
                                
                                hbox:
                                    spacing 20
                                    
                                    vbox:
                                        spacing 5
                                        xsize 500
                                        
                                        text "[game_name]" size 30 color "#ffffff"
                                        if game_info:
                                            text "[game_info['description']]" size 20 color "#cccccc"
                                    
                                    textbutton "Play" action [Return(game_key)] xsize 150 ysize 50
            
            textbutton "Return to Main Menu" action Return("return") xsize 200 ysize 50 xalign 0.5

# Enhanced game selection screen with mobile support
screen game_selection(available_games):
    modal True

    # Ensure proper mouse and keyboard interaction
    key "game_menu" action Return("return")
    key "K_ESCAPE" action Return("return")

    # Platform switching buttons (top-right corner)
    hbox:
        xalign 0.98
        yalign 0.02
        spacing 5

        if get_current_platform() == "desktop":
            textbutton "📱":
                action Function(switch_to_mobile)
                xsize 40
                ysize 30
                text_size 16
                tooltip "Switch to Mobile Interface"
                background "#023e8a"
                hover_background "#0077b6"
            # Show SteamVR option on PC
            if not renpy.variant("mobile"):
                textbutton "🥽":
                    action Function(switch_to_steamvr)
                    xsize 40
                    ysize 30
                    text_size 16
                    tooltip "Switch to SteamVR Mode (PC VR)"
                    background "#023e8a"
                    hover_background "#0077b6"
            # Show Quest VR option if on Android build
            if renpy.variant("android"):
                textbutton "🎮":
                    action Function(switch_to_vr)
                    xsize 40
                    ysize 30
                    text_size 16
                    tooltip "Switch to Quest VR Mode"
                    background "#023e8a"
                    hover_background "#0077b6"
        elif get_current_platform() == "mobile":
            textbutton "🖥️":
                action Function(switch_to_desktop)
                xsize 40
                ysize 30
                text_size 16
                tooltip "Switch to Desktop Interface"
                background "#023e8a"
                hover_background "#0077b6"
            # Show VR option if on Android build
            if renpy.variant("android"):
                textbutton "🥽":
                    action Function(switch_to_vr)
                    xsize 40
                    ysize 30
                    text_size 16
                    tooltip "Switch to VR Mode (Quest)"
                    background "#023e8a"
                    hover_background "#0077b6"
        elif get_current_platform() == "vr":
            textbutton "📱":
                action Function(switch_to_mobile)
                xsize 40
                ysize 30
                text_size 16
                tooltip "Switch to Mobile Interface"
                background "#023e8a"
                hover_background "#0077b6"
            textbutton "🖥️":
                action Function(switch_to_desktop)
                xsize 40
                ysize 30
                text_size 16
                tooltip "Switch to Desktop Interface"
                background "#023e8a"
                hover_background "#0077b6"

    # Multi-layer animated background
    add "#000814"

    # Gradient overlay layers
    add "#001d3d" alpha 0.4

    add "#003566" alpha 0.2

    # Platform detection and responsive variables
    python:
        # Detect current platform
        current_platform_type = get_current_platform()
        is_mobile = (current_platform_type == "mobile")
        is_vr = (current_platform_type == "vr")
        is_steamvr = (current_platform_type == "steamvr")
        is_desktop = (current_platform_type == "desktop")

        # Responsive sizing based on platform
        if is_steamvr:
            # SteamVR layout (optimized for PC VR headsets with controllers)
            button_width = 350
            button_height = 100
            main_width = 1600
            main_height = 1000
            panel_width = 450
            panel_height = 800
            text_size_large = 42
            text_size_medium = 28
            text_size_small = 20
            spacing_large = 50
            spacing_medium = 30
            spacing_small = 20
        elif is_vr:
            # Quest VR layout (optimized for Quest headsets in 3D)
            button_width = 300
            button_height = 80
            main_width = 1400
            main_height = 900
            panel_width = 400
            panel_height = 700
            text_size_large = 36
            text_size_medium = 24
            text_size_small = 18
            spacing_large = 40
            spacing_medium = 25
            spacing_small = 15
        elif is_mobile:
            # Mobile layout
            button_width = 180
            button_height = 60
            main_width = min(config.screen_width - 40, 800)
            main_height = min(config.screen_height - 100, 600)
            panel_width = main_width // 3 - 10
            panel_height = main_height - 80
            text_size_large = 20
            text_size_medium = 14
            text_size_small = 10
            spacing_large = 15
            spacing_medium = 10
            spacing_small = 5
        else:
            # Desktop layout
            button_width = 220
            button_height = 50
            main_width = 1100
            main_height = 520
            panel_width = 280
            panel_height = 460
            text_size_large = 28
            text_size_medium = 16
            text_size_small = 12
            spacing_large = 20
            spacing_medium = 15
            spacing_small = 8

    # Top buttons with platform-responsive styling
    if is_mobile or is_vr:
        # Mobile: Stack buttons vertically or use smaller horizontal layout
        vbox:
            xalign 0.5
            ypos 20
            spacing 10

            # Gaming Settings button (mobile)
            textbutton "🎮 Settings":
                action Return("settings")
                xsize button_width
                ysize button_height
                text_size text_size_medium
                text_color "#00b4d8"
                background "#023e8a"
                hover_background "#0077b6"
                activate_sound "audio/click.ogg"

            # Security Status button (mobile)
            textbutton "🛡️ Security":
                action Return("advanced")
                xsize button_width
                ysize button_height
                text_size text_size_medium
                text_color "#0077b6"
                background "#023e8a"
                hover_background "#0077b6"
                activate_sound "audio/click.ogg"
    else:
        # Desktop: Horizontal layout
        hbox:
            xalign 0.5
            ypos 40
            spacing 20

            # Gaming Settings button (desktop)
            textbutton "🎮 Gaming Settings":
                action Return("settings")
                xsize button_width
                ysize button_height
                text_size text_size_medium
                text_color "#00b4d8"
                background "#023e8a"
                hover_background "#0077b6"

            # Security Status button (desktop)
            textbutton "🛡️ Security Status":
                action Return("advanced")
                xsize button_width
                ysize button_height
                text_size text_size_medium
                text_color "#0077b6"
                background "#023e8a"
                hover_background "#0077b6"

    # Main content area - responsive
    frame:
        xalign 0.5
        yalign 0.5
        xsize main_width
        ysize main_height
        background "#001122"
        padding (20 if not is_mobile else 10, 20 if not is_mobile else 10)

        # Subtle inner glow
        add "#0077b6" alpha 0.1

        if is_steamvr:
            # SteamVR: Full PC VR experience with controller interaction
            vbox:
                spacing spacing_medium
                xalign 0.5
                yalign 0.5

                # SteamVR mode indicator
                text "🥽 STEAMVR MODE - PC VR with Controllers" size text_size_large xalign 0.5 color "#00ff00"

                # VR room visualization (basic implementation)
                frame:
                    xsize main_width - 200
                    ysize 200
                    background "#001122"
                    padding (20, 20)

                    vbox:
                        spacing 10
                        xalign 0.5
                        yalign 0.5

                        text "🥽 VR Room Space" size text_size_medium xalign 0.5 color "#00ccff"
                        text "Room boundaries and tracking area" size text_size_small xalign 0.5 color "#90e0ef"

                        # Simple room boundary visualization
                        frame:
                            xsize main_width - 280
                            ysize 120
                            background "#002244"
                            padding (10, 10)

                            # Room grid
                            for i in range(5):
                                for j in range(3):
                                    add "#333333" alpha 0.3:
                                        xpos i * 40 + 10
                                        ypos j * 30 + 10
                                        xsize 2
                                        ysize 25

                            # Controller indicators
                            add "#00ff00" alpha 0.8 xpos 50 ypos 50 xsize 15 ysize 30  # Left controller
                            add "#ff0000" alpha 0.8 xpos 150 ypos 50 xsize 15 ysize 30  # Right controller

                # SteamVR game selection (3D positioned)
                frame at vr_ui_panel:
                    xsize main_width - 200
                    ysize main_height - 300
                    background "#001122"
                    padding (30, 30)

                    vbox:
                        spacing spacing_large
                        xalign 0.5

                        text "Point and Click with VR Controllers" size text_size_medium xalign 0.5 color "#00ccff"

                        viewport:
                            xsize main_width - 260
                            ysize main_height - 450
                            scrollbars "vertical"
                            mousewheel True

                            vbox:
                                spacing spacing_medium

                                for game in available_games:
                                    textbutton "[game['name']]" style "steamvr_button":
                                        action Return(game["key"])
                                        at vr_ui_hover

                        textbutton "RETURN TO MAIN MENU" style "steamvr_button":
                            action Return("return")
                            xalign 0.5
        elif is_vr:
            # Quest VR: Enhanced 3D layout with depth and larger elements for Quest headsets
            vbox:
                spacing spacing_medium
                xalign 0.5
                yalign 0.5

                # VR mode indicator
                text "🥽 VR MODE - Quest Optimized" size text_size_large xalign 0.5 color "#00ff88"

                # VR tab navigation (larger for VR)
                hbox:
                    xalign 0.5
                    spacing 15

                    textbutton "Games" action SetScreenVariable("mobile_tab", "games") text_size text_size_medium + 4
                    textbutton "Preview" action SetScreenVariable("mobile_tab", "preview") text_size text_size_medium + 4
                    textbutton "Details" action SetScreenVariable("mobile_tab", "details") text_size text_size_medium + 4

                # VR content based on selected tab (using mobile screens but with VR sizing)
                if not hasattr(store, 'mobile_tab'):
                    $ mobile_tab = "games"

                if mobile_tab == "games":
                    use mobile_game_list(available_games, main_width - 80, main_height - 200)
                elif mobile_tab == "preview":
                    use mobile_game_preview(main_width - 80, main_height - 200)
                elif mobile_tab == "details":
                    use mobile_game_details(available_games, main_width - 80, main_height - 200)
        elif is_mobile:
            # Mobile: Vertical layout with tabs
                xalign 0.5
                yalign 0.5

                # Mobile tab navigation
                hbox:
                    xalign 0.5
                    spacing 5

                    textbutton "Games" action SetScreenVariable("mobile_tab", "games") text_size text_size_small
                    textbutton "Preview" action SetScreenVariable("mobile_tab", "preview") text_size text_size_small
                    textbutton "Details" action SetScreenVariable("mobile_tab", "details") text_size text_size_small

                # Mobile content based on selected tab
                if not hasattr(store, 'mobile_tab'):
                    $ mobile_tab = "games"

                if mobile_tab == "games":
                    use mobile_game_list(available_games, main_width - 40, main_height - 120)
                elif mobile_tab == "preview":
                    use mobile_game_preview(main_width - 40, main_height - 120)
                elif mobile_tab == "details":
                    use mobile_game_details(available_games, main_width - 40, main_height - 120)
        else:
            # Desktop: Horizontal three-panel layout
            hbox:
                spacing 30
                xalign 0.5
                yalign 0.5

                # Desktop left panel - Enhanced game preview
                frame:
                    xsize panel_width
                    ysize panel_height
                    background "#003366"
                    padding (15, 15)

                    vbox:
                        spacing spacing_medium
                        xalign 0.5

                        text "Game Preview" size text_size_medium xalign 0.5 color "#90e0ef"

                        # Main preview image
                        frame:
                            xsize panel_width - 30
                            ysize 180
                            background "#023e8a"
                            padding (10, 10)

                            vbox:
                                xalign 0.5
                                yalign 0.5
                                spacing 5

                                if pc_selected_game_data:
                                    # Show selected game image or fallback
                                    python:
                                        image_exists = False
                                        if pc_selected_game_data.get("image"):
                                            try:
                                                renpy.loadable(pc_selected_game_data["image"])
                                                image_exists = True
                                            except:
                                                image_exists = False

                                    if image_exists:
                                        add pc_selected_game_data["image"]:
                                            xalign 0.5
                                            yalign 0.5
                                            fit "contain"
                                            xsize panel_width - 50
                                            ysize 120
                                    else:
                                        text "🎮" size 40 xalign 0.5 color "#00b4d8"

                                    text "[pc_selected_game_data['name']]" size text_size_small + 2 xalign 0.5 color "#caf0f8"

                                    # Play button for selected game
                                    textbutton "PLAY GAME":
                                        action Function(launch_game_direct, pc_selected_game_data)
                                        xsize panel_width - 50
                                        ysize 40
                                        xalign 0.5
                                        text_size text_size_medium
                                        text_color "#ffffff"
                                        text_hover_color "#00ff88"
                                        background "#0077b6"
                                        hover_background "#00b4d8"
                                        activate_sound "audio/click.ogg"
                                else:
                                    text "🎮" size 40 xalign 0.5 color "#666666"
                                    text "Game Preview" size text_size_small xalign 0.5 color "#caf0f8"
                                    text "Click games to preview" size text_size_small - 2 xalign 0.5 color "#90e0ef"

                        text "Description" size text_size_medium xalign 0.5 color "#90e0ef"

                        # Description area
                        frame:
                            xsize panel_width - 30
                            ysize 140
                            background "#023e8a"
                            padding (10, 10)

                            viewport:
                                scrollbars "vertical"
                                mousewheel True

                                if pc_selected_game_data:
                                    text "[pc_selected_game_data.get('description_full', 'No detailed description available.')]" size text_size_small color "#caf0f8" text_align 0.0
                                else:
                                    text "Click a game from the center panel to see its detailed description and features here." size text_size_small color "#caf0f8" text_align 0.5

                        # Close preview button
                        if pc_selected_game_data:
                            textbutton "CLOSE PREVIEW":
                                action [SetVariable("pc_selected_game", None), SetVariable("pc_selected_game_data", None)]
                                xsize panel_width - 50
                                ysize 35
                                xalign 0.5
                                text_size text_size_small + 1
                                text_color "#ffffff"
                                text_hover_color "#ff6666"
                                background "#666666"
                                hover_background "#888888"
                                activate_sound "audio/click.ogg"

                # Desktop center panel - Enhanced game selection
                frame:
                    xsize panel_width + 200
                    ysize panel_height
                    background "#002244"
                    padding (20, 20)

                    vbox:
                        spacing spacing_large
                        xalign 0.5

                        # Title
                        text "Select a Game to Play" size text_size_large xalign 0.5 color "#00b4d8"

                        # Game list with categories as headers
                        python:
                            # Group games by category
                            categories = ["zwute studio", "bronys", "movies", "streamers bears", "streamers furrys", "monster hunter series", "atlyss", "star fox", "miscellaneous"]
                            games_by_category = {}

                            for category in categories:
                                games_by_category[category] = [game for game in available_games if game.get('category', 'miscellaneous') == category]

                        viewport:
                            xsize panel_width + 160
                            ysize panel_height - 150
                            scrollbars "vertical"
                            mousewheel True

                            vbox:
                                spacing spacing_medium

                                for category in categories:
                                    if len(games_by_category[category]) > 0:
                                        # Category header
                                        frame:
                                            xsize panel_width + 140
                                            ysize 40
                                            background "#00b4d8"
                                            padding (15, 8)

                                            text "[category]" size text_size_medium + 2 color "#ffffff" xalign 0.0 yalign 0.5 text_align 0.0

                                        # Games in this category
                                        for game in games_by_category[category]:
                                            if pc_selected_game == game["key"]:
                                                button:
                                                    xsize panel_width + 140
                                                    ysize 80
                                                    background "#004488"
                                                    hover_background "#0055aa"
                                                    action Function(pc_game_click, game)
                                                    activate_sound "audio/click.ogg"
                                                    focus_mask True
                                                    keyboard_focus True

                                                    hbox:
                                                        spacing spacing_medium
                                                        yalign 0.5
                                                        xpos 15

                                                        # Game icon
                                                        text "🎮" size 28 yalign 0.5 color "#00b4d8"

                                                        # Game info
                                                        vbox:
                                                            xsize panel_width + 20
                                                            yalign 0.5
                                                            spacing 5

                                                            text "[game['name']]" size text_size_medium color "#caf0f8" text_align 0.0
                                                            text "[game.get('description', 'Visual novel experience')]" size text_size_small color "#90e0ef" text_align 0.0

                                                        # Play button (separate action for immediate play)
                                                        textbutton "▶":
                                                            action Function(launch_game_direct, game)
                                                            xsize 50
                                                            ysize 50
                                                            text_size 20
                                                            text_color "#ffffff"
                                                            text_hover_color "#00ff88"
                                                            background "#0077b6"
                                                            hover_background "#00b4d8"
                                                            activate_sound "audio/click.ogg"
                                                            focus_mask True
                                                            keyboard_focus True
                                                            tooltip "Play Game"
                                            else:
                                                button:
                                                    xsize panel_width + 140
                                                    ysize 80
                                                    background "#003366"
                                                    hover_background "#0055aa"
                                                    action Function(pc_game_click, game)
                                                    activate_sound "audio/click.ogg"
                                                    focus_mask True
                                                    keyboard_focus True

                                                    hbox:
                                                        spacing spacing_medium
                                                        yalign 0.5
                                                        xpos 15

                                                        # Game icon
                                                        text "🎮" size 28 yalign 0.5 color "#00b4d8"

                                                        # Game info
                                                        vbox:
                                                            xsize panel_width + 20
                                                            yalign 0.5
                                                            spacing 5

                                                            text "[game['name']]" size text_size_medium color "#caf0f8" text_align 0.0
                                                            text "[game.get('description', 'Visual novel experience')]" size text_size_small color "#90e0ef" text_align 0.0

                                                        # Play button (separate action for immediate play)
                                                        textbutton "▶":
                                                            action Function(launch_game_direct, game)
                                                            xsize 50
                                                            ysize 50
                                                            text_size 20
                                                            text_color "#ffffff"
                                                            text_hover_color "#00ff88"
                                                            background "#0077b6"
                                                            hover_background "#00b4d8"
                                                            activate_sound "audio/click.ogg"
                                                            focus_mask True
                                                            keyboard_focus True
                                                            tooltip "Play Game"

                        # Return button
                        textbutton "RETURN BACK TO MAIN MENU":
                            action Return("return")
                            xsize panel_width + 100
                            ysize 55
                            xalign 0.5
                            text_size text_size_medium
                            text_color "#ffffff"
                            text_hover_color "#00b4d8"
                            background "#023e8a"
                            hover_background "#0077b6"
                            activate_sound "audio/click.ogg"

                # Desktop right panel - Enhanced game details
                frame:
                    xsize panel_width
                    ysize panel_height
                    background "#003366"
                    padding (15, 15)

                    vbox:
                        spacing spacing_medium
                        xalign 0.5

                        text "Game Details" size text_size_medium xalign 0.5 color "#90e0ef"

                        # Game details area
                        frame:
                            xsize panel_width - 30
                            ysize panel_height - 80
                            background "#023e8a"
                            padding (15, 15)

                            viewport:
                                scrollbars "vertical"
                                mousewheel True

                                vbox:
                                    spacing spacing_large

                                    # Stats section
                                    vbox:
                                        spacing spacing_small
                                        text "📊 Statistics" size text_size_small + 2 color "#caf0f8"
                                        text "• Available Games: [len(available_games)]" size text_size_small - 1 color "#90e0ef"
                                        text "• System Status: Online" size text_size_small - 1 color "#90e0ef"
                                        text "• Security: Active" size text_size_small - 1 color "#90e0ef"

                                    # Features section
                                    vbox:
                                        spacing spacing_small
                                        text "✨ Features" size text_size_small + 2 color "#caf0f8"
                                        text "• Visual Novel Engine" size text_size_small - 1 color "#90e0ef"
                                        text "• Multiple Storylines" size text_size_small - 1 color "#90e0ef"
                                        text "• Character Development" size text_size_small - 1 color "#90e0ef"
                                        text "• Save System" size text_size_small - 1 color "#90e0ef"

                                    # Tips section
                                    vbox:
                                        spacing spacing_small
                                        text "💡 Tips" size text_size_small + 2 color "#caf0f8"
                                        if is_mobile:
                                            text "Tap games to select" size text_size_small - 1 color "#90e0ef"
                                            text "Use tabs to navigate" size text_size_small - 1 color "#90e0ef"
                                            text "Swipe to scroll lists" size text_size_small - 1 color "#90e0ef"
                                        else:
                                            text "Hover over games for previews" size text_size_small - 1 color "#90e0ef"
                                            text "Use Gaming Settings for options" size text_size_small - 1 color "#90e0ef"
                                            text "Check Security Status regularly" size text_size_small - 1 color "#90e0ef"

# Mobile-specific screens for touch interface

# Mobile game list screen
screen mobile_game_list(available_games, width, height):
    frame:
        xsize width
        ysize height
        background "#002244"
        padding (10, 10)

        vbox:
            spacing 10
            xalign 0.5

            text "Select a Game to Play" size 20 xalign 0.5 color "#00b4d8"

            # Mobile games organized by category
            python:
                # Group games by category for mobile
                mobile_categories = ["zwute studio", "bronys", "movies", "streamers bears", "streamers furrys", "monster hunter series", "atlyss", "star fox", "miscellaneous"]
                mobile_games_by_category = {}

                for category in mobile_categories:
                    mobile_games_by_category[category] = [game for game in available_games if game.get('category', 'miscellaneous') == category]

            viewport:
                xsize width - 20
                ysize height - 100
                scrollbars "vertical"
                mousewheel True
                draggable True

                vbox:
                    spacing 15

                    for category in mobile_categories:
                        if len(mobile_games_by_category[category]) > 0:
                            # Category header for mobile
                            frame:
                                xsize width - 40
                                ysize 45
                                background "#00b4d8"
                                padding (15, 10)

                                text "[category]" size 18 color "#ffffff" xalign 0.0 yalign 0.5 text_align 0.0

                            # Games in this category for mobile
                            for game in mobile_games_by_category[category]:
                                if mobile_selected_game == game["key"]:
                                    button:
                                        xsize width - 40
                                        ysize 90
                                        background "#004488"
                                        hover_background "#0055aa"
                                        action Function(mobile_game_tap, game)
                                        activate_sound "audio/click.ogg"

                                        hbox:
                                            spacing 15
                                            yalign 0.5
                                            xpos 15

                                            text "🎮" size 32 yalign 0.5 color "#00b4d8"

                                            vbox:
                                                xsize width - 140
                                                yalign 0.5
                                                spacing 5

                                                text "[game['name']]" size 18 color "#caf0f8" text_align 0.0
                                                text "[game.get('description', 'Visual novel experience')]" size 14 color "#90e0ef" text_align 0.0

                                            # Quick play button for mobile
                                            textbutton "▶":
                                                action Function(launch_game_direct, game)
                                                xsize 40
                                                ysize 40
                                                text_size 16
                                                text_color "#ffffff"
                                                text_hover_color "#00ff88"
                                                background "#0077b6"
                                                hover_background "#00b4d8"
                                                activate_sound "audio/click.ogg"
                                else:
                                    button:
                                        xsize width - 40
                                        ysize 90
                                        background "#003366"
                                        hover_background "#0055aa"
                                        action Function(mobile_game_tap, game)
                                        activate_sound "audio/click.ogg"

                                        hbox:
                                            spacing 15
                                            yalign 0.5
                                            xpos 15

                                            text "🎮" size 32 yalign 0.5 color "#00b4d8"

                                            vbox:
                                                xsize width - 140
                                                yalign 0.5
                                                spacing 5

                                                text "[game['name']]" size 18 color "#caf0f8" text_align 0.0
                                                text "[game.get('description', 'Visual novel experience')]" size 14 color "#90e0ef" text_align 0.0

                                            # Quick play button for mobile
                                            textbutton "▶":
                                                action Function(launch_game_direct, game)
                                                xsize 40
                                                ysize 40
                                                text_size 16
                                                text_color "#ffffff"
                                                text_hover_color "#00ff88"
                                                background "#0077b6"
                                                hover_background "#00b4d8"
                                                activate_sound "audio/click.ogg"
                        if mobile_selected_game == game["key"]:
                            button:
                                xsize width - 40
                                ysize 90
                                background "#004488"
                                hover_background "#0055aa"
                                action Function(mobile_game_tap, game)
                                activate_sound "audio/click.ogg"

                                hbox:
                                    spacing 15
                                    yalign 0.5
                                    xpos 15

                                    text "🎮" size 32 yalign 0.5 color "#00b4d8"

                                    vbox:
                                        xsize width - 140
                                        yalign 0.5
                                        spacing 5

                                        text "[game['name']]" size 18 color "#caf0f8" text_align 0.0
                                        text "[game.get('description', 'Visual novel experience')]" size 14 color "#90e0ef" text_align 0.0

                                    # Quick play button for mobile
                                    textbutton "▶":
                                        action Function(launch_game_direct, game)
                                        xsize 40
                                        ysize 40
                                        text_size 16
                                        text_color "#ffffff"
                                        text_hover_color "#00ff88"
                                        background "#0077b6"
                                        hover_background "#00b4d8"
                                        activate_sound "audio/click.ogg"
                        else:
                            button:
                                xsize width - 40
                                ysize 90
                                background "#003366"
                                hover_background "#0055aa"
                                action Function(mobile_game_tap, game)
                                activate_sound "audio/click.ogg"

                                hbox:
                                    spacing 15
                                    yalign 0.5
                                    xpos 15

                                    text "🎮" size 32 yalign 0.5 color "#00b4d8"

                                    vbox:
                                        xsize width - 140
                                        yalign 0.5
                                        spacing 5

                                        text "[game['name']]" size 18 color "#caf0f8" text_align 0.0
                                        text "[game.get('description', 'Visual novel experience')]" size 14 color "#90e0ef" text_align 0.0

                                    # Quick play button for mobile
                                    textbutton "▶":
                                        action Function(launch_game_direct, game)
                                        xsize 40
                                        ysize 40
                                        text_size 16
                                        text_color "#ffffff"
                                        text_hover_color "#00ff88"
                                        background "#0077b6"
                                        hover_background "#00b4d8"
                                        activate_sound "audio/click.ogg"

            textbutton "RETURN TO MAIN MENU":
                action Return("return")
                xsize width - 40
                ysize 50
                xalign 0.5
                text_size 14
                text_color "#ffffff"
                background "#023e8a"
                hover_background "#0077b6"
                activate_sound "audio/click.ogg"

# Mobile game preview screen
screen mobile_game_preview(width, height):
    frame:
        xsize width
        ysize height
        background "#003366"
        padding (10, 10)

        vbox:
            spacing 15
            xalign 0.5

            if mobile_selected_game_data:
                text "[mobile_selected_game_data['name']]" size 18 xalign 0.5 color "#90e0ef"
            else:
                text "Game Preview" size 18 xalign 0.5 color "#90e0ef"

            frame:
                xsize width - 40
                ysize height // 2
                background "#023e8a"
                padding (15, 15)

                vbox:
                    xalign 0.5
                    yalign 0.5
                    spacing 10

                    if mobile_selected_game_data:
                        # Show game image or fallback to icon
                        python:
                            mobile_image_exists = False
                            if mobile_selected_game_data.get("image"):
                                try:
                                    renpy.loadable(mobile_selected_game_data["image"])
                                    mobile_image_exists = True
                                except:
                                    mobile_image_exists = False

                        if mobile_image_exists:
                            add mobile_selected_game_data["image"]:
                                xalign 0.5
                                yalign 0.5
                                fit "contain"
                                xsize width - 80
                                ysize 100
                        else:
                            text "🎮" size 60 xalign 0.5 color "#00b4d8"

                        text "[mobile_selected_game_data['name']]" size 18 xalign 0.5 color "#caf0f8"

                        # Play button for mobile
                        textbutton "PLAY GAME":
                            action Function(launch_game_direct, mobile_selected_game_data)
                            xsize width - 80
                            ysize 45
                            xalign 0.5
                            text_size 16
                            text_color "#ffffff"
                            text_hover_color "#00ff88"
                            background "#0077b6"
                            hover_background "#00b4d8"
                            activate_sound "audio/click.ogg"

                        # Close button for mobile
                        textbutton "CLOSE":
                            action [SetVariable("mobile_selected_game", None), SetVariable("mobile_selected_game_data", None), SetVariable("mobile_tab", "games")]
                            xsize width - 80
                            ysize 35
                            xalign 0.5
                            text_size 14
                            text_color "#ffffff"
                            text_hover_color "#ff6666"
                            background "#666666"
                            hover_background "#888888"
                            activate_sound "audio/click.ogg"
                    else:
                        text "🎮" size 60 xalign 0.5 color "#666666"
                        text "Game Preview" size 16 xalign 0.5 color "#caf0f8"
                        text "Tap a game to see preview" size 12 xalign 0.5 color "#90e0ef"

            text "Description" size 16 xalign 0.5 color "#90e0ef"

            frame:
                xsize width - 40
                ysize height // 2 - 60
                background "#023e8a"
                padding (15, 15)

                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    draggable True

                    if mobile_selected_game_data:
                        text "[mobile_selected_game_data.get('description_full', 'No detailed description available.')]" size 12 color "#caf0f8" text_align 0.0
                    else:
                        text "Select a game from the Games tab to see its detailed description and features here." size 12 color "#caf0f8" text_align 0.5

# Mobile game details screen
screen mobile_game_details(available_games, width, height):
    frame:
        xsize width
        ysize height
        background "#003366"
        padding (10, 10)

        vbox:
            spacing 15
            xalign 0.5

            text "Game Details" size 18 xalign 0.5 color "#90e0ef"

            viewport:
                xsize width - 20
                ysize height - 60
                scrollbars "vertical"
                mousewheel True
                draggable True

                vbox:
                    spacing 20
                    xsize width - 40

                    # Stats section
                    frame:
                        xsize width - 40
                        background "#023e8a"
                        padding (15, 15)

                        vbox:
                            spacing 8
                            text "📊 Statistics" size 14 color "#caf0f8"
                            text "• Available Games: [len(available_games)]" size 11 color "#90e0ef"
                            text "• System Status: Online" size 11 color "#90e0ef"
                            text "• Security: Active" size 11 color "#90e0ef"
                            text "• Platform: Mobile" size 11 color "#90e0ef"

                    # Features section
                    frame:
                        xsize width - 40
                        background "#023e8a"
                        padding (15, 15)

                        vbox:
                            spacing 8
                            text "✨ Features" size 14 color "#caf0f8"
                            text "• Touch-Friendly Interface" size 11 color "#90e0ef"
                            text "• Visual Novel Engine" size 11 color "#90e0ef"
                            text "• Multiple Storylines" size 11 color "#90e0ef"
                            text "• Auto-Save System" size 11 color "#90e0ef"
                            text "• Gesture Controls" size 11 color "#90e0ef"

                    # Mobile Tips section
                    frame:
                        xsize width - 40
                        background "#023e8a"
                        padding (15, 15)

                        vbox:
                            spacing 8
                            text "📱 Mobile Tips" size 14 color "#caf0f8"
                            text "• Tap to select games" size 11 color "#90e0ef"
                            text "• Swipe to scroll lists" size 11 color "#90e0ef"
                            text "• Use tabs to navigate" size 11 color "#90e0ef"
                            text "• Pinch to zoom (if supported)" size 11 color "#90e0ef"
                            text "• Double-tap for quick actions" size 11 color "#90e0ef"

# Safe game launcher with conflict resolution - Universal Game Router
label safe_game_launcher:
    # Get available games for new UI (filtered by ui_visibility)
    python:
        available_games = get_new_ui_games()

    # Check if games are available
    if len(available_games) == 0:
        "No games found in the separate games directories."
        "Please check that the game files are properly placed."
        return

    # Directly show the game selection screen like in the image
    call screen game_selection(available_games)

    # Handle the returned selection
    python:
        selected_game = _return
        # Debug: Show what was selected
        if selected_game and selected_game != "return":
            renpy.say(None, f"Selected game: {selected_game}")

    if selected_game == "return":
        return
    elif selected_game == "settings":
        jump router_settings
    elif selected_game == "advanced":
        jump advanced_router_options
    elif selected_game:
        # Launch the selected game
        python:
            entry_label = route_to_game(selected_game)

        if entry_label:
            # Set appropriate game context
            if selected_game == "lumetric":
                $ set_game_context("lumetric")
            elif selected_game == "netcode":
                $ set_game_context("netcode")
            elif selected_game == "bearwithus":
                $ set_game_context("bearwithus")
            else:
                $ set_game_context(selected_game)

            jump expression entry_label
        else:
            "Error: Could not launch the selected game."
            jump safe_game_launcher
    else:
        jump safe_game_launcher

# Advanced router options
label advanced_router_options:

    menu:
        "⚙️ Advanced Options"

        "🎮 Launch Games":
            call universal_game_launcher from _call_universal_game_launcher
            jump safe_game_launcher

        "📊 System Status":
            jump comprehensive_system_status

        "🔙 Back":
            jump safe_game_launcher

# Router settings
label router_settings:

    menu:
        "🔧 Settings"

        "🎮 Gaming Settings":
            call screen safe_gaming_settings
            jump router_settings

        "🛡️ Security Settings":
            call screen safe_anticheat_status
            jump router_settings

        "📋 Preferences":
            call screen preferences
            jump router_settings

        "🔙 Back":
            jump safe_game_launcher

# Original game launcher (moved to Advanced Options)
label universal_game_launcher:
    python:
        available_games = get_available_games()

    if len(available_games) == 0:
        "No games found in the separate games directories."
        "Please check that the game files are properly placed."
        return

    "Universal Game Launcher"
    "Detected [len(available_games)] available games:"
    ""

    python:
        game_menu_items = []
        for game in available_games:
            game_menu_items.append((game["name"], game["key"]))
        game_menu_items.append(("Return to Main Menu", "return"))

    call screen game_selection_screen(game_menu_items)

    # Return the selected game key
    python:
        selected_game = _return

    if selected_game == "return":
        return

    # Check if selected_game is valid
    if selected_game is None:
        "Error: No game was selected."
        "Please try selecting a game again."
        jump universal_game_launcher

    python:
        entry_label = route_to_game(selected_game)

    if entry_label:
        # Check if the game exists in game_routes before accessing it
        python:
            game_display_name = "Unknown Game"
            if selected_game in game_routes:
                game_display_name = game_routes[selected_game]['display_name']
            else:
                game_display_name = selected_game.title()

        "Launching [game_display_name]..."

        # Set appropriate game context to resolve conflicts
        if selected_game == "lumetric":
            $ set_game_context("lumetric")
        elif selected_game == "netcode":
            $ set_game_context("netcode")
        elif selected_game == "bearwithus":
            $ set_game_context("bearwithus")
        else:
            $ set_game_context(selected_game)

        # Record the game launch for anti-cheat
        $ safe_record_user_action("game_launch", selected_game)

        # Jump to the game
        jump expression entry_label
    else:
        # Safe error handling when game routing fails
        python:
            error_game_name = "Unknown Game"
            if selected_game and selected_game in game_routes:
                error_game_name = game_routes[selected_game]['display_name']
            elif selected_game:
                error_game_name = selected_game.title()

        "Error: Could not launch [error_game_name]."
        "The game files may be missing or corrupted."
        "Selected game key: [selected_game]"
        jump universal_game_launcher

# Enhanced game selection that integrates with existing system
label enhanced_game_selection_with_routing:
    menu:
        "Game Selection - Enhanced with Routing"
        
        "Launch games through Universal Router":
            jump safe_game_launcher
        
        "Launch integrated games directly":
            jump enhanced_game_selection_safe
        
        "View game routing status":
            jump display_routing_status
        
        "Return to main menu":
            return

# Display routing status
label display_routing_status:
    "Game Routing System Status"
    ""
    
    python:
        available_games = get_available_games()
        total_games = len(game_routes)
        available_count = len(available_games)
    
    "Total configured games: [total_games]"
    "Available games: [available_count]"
    ""
    
    if available_count > 0:
        "✅ Available Games:"
        python:
            for game in available_games:
                renpy.say(None, f"• {game['name']} - {game['description']}")
    
    python:
        missing_games = []
        for game_key, route in game_routes.items():
            if not check_game_exists(game_key):
                missing_games.append(route["display_name"])
    
    if len(missing_games) > 0:
        ""
        "❌ Missing Games:"
        python:
            for game_name in missing_games:
                renpy.say(None, f"• {game_name}")
    
    ""
    "Routing system allows games to coexist without label conflicts."
    "All separate game files are preserved and accessible."
    
    return

# Integration with existing anti-cheat system
init python:
    def safe_record_game_route(game_key, action):
        """Record game routing actions for anti-cheat system"""
        try:
            if hasattr(renpy.store, 'safe_record_user_action'):
                safe_record_user_action(f"route_{action}", game_key)
        except:
            pass

# Comprehensive system status display
label comprehensive_system_status:
    "🔍 Comprehensive System Status"
    ""

    # Game availability status
    python:
        available_games = get_available_games()
        total_games = len(game_routes)
        available_count = len(available_games)

    "📊 Game Availability:"
    "• Total configured games: [total_games]"
    "• Available games: [available_count]"
    ""

    # Conflict resolution status
    python:
        current_context = get_game_context()

    "🛡️ Conflict Resolution Status:"
    if current_context:
        "• Current game context: [current_context]"
    else:
        "• Current game context: None"

    "• Conflict resolution method: Label redirection"
    "• Original separate game files: Preserved with unique names"

    ""

    # Label conflict status
    "🏷️ Known Label Conflicts:"
    python:
        conflict_labels = ["whisker_and_bean_scene", "howling_hearth_scene", "binary_bistro_scene", "act_4_conclusion"]
        for label in conflict_labels:
            renpy.say(None, f"• {label} - Redirected ✅")

    ""

    # Available launchers
    "🚀 Available Launchers:"
    "• Universal Game Router (Separate games)"

    menu:
        "System Status Options"

        "🔄 Refresh Status":
            jump comprehensive_system_status

        "🎮 Test Game Launching":
            jump test_all_launchers

        "🔧 Test Conflicts":
            jump test_conflict_resolution

        "🔙 Back to Main Menu":
            jump enhanced_game_selection_safe

# Test all launcher systems
label test_all_launchers:
    "🧪 Launcher System Testing"
    ""

    menu:
        "Which launcher would you like to test?"

        "Test Universal Router":
            call safe_game_launcher from _call_safe_game_launcher
            jump test_all_launchers

        "Test Conflict Resolution":
            call test_conflict_resolution from _call_test_conflict_resolution
            jump test_all_launchers

        "Test Namespace Launcher":
            call namespace_game_launcher from _call_namespace_game_launcher
            jump test_all_launchers

        "Back to Status":
            jump comprehensive_system_status

# Basic VR Room Screen (embedded for safety)
screen vr_room():
    # Simple VR room visualization
    if steamvr_mode_active:
        frame:
            xsize 400
            ysize 200
            background "#001122"
            padding (20, 20)
            xalign 0.5
            yalign 0.3

            vbox:
                spacing 10
                xalign 0.5

                text "🥽 VR Room Active" size 20 xalign 0.5 color "#00ccff"

                # Simple room grid
                frame:
                    xsize 360
                    ysize 120
                    background "#002244"
                    padding (10, 10)

                    # Grid lines
                    for i in range(6):
                        add "#333333" alpha 0.4 xpos i * 60 ypos 0 xsize 2 ysize 100
                    for j in range(4):
                        add "#333333" alpha 0.4 xpos 0 ypos j * 25 xsize 340 ysize 2

                    # Player position
                    add "#ffff00" alpha 0.8 xpos 170 ypos 50 xsize 10 ysize 10

# Basic VR Controller Screens (embedded for safety)
screen vr_controller_left():
    if steamvr_mode_active:
        add "#00ff00" alpha 0.6 xpos 300 ypos 400 xsize 20 ysize 60

screen vr_controller_right():
    if steamvr_mode_active:
        add "#ff0000" alpha 0.6 xpos 500 ypos 400 xsize 20 ysize 60

# VR UI Transform (basic implementation)
transform vr_ui_panel:
    anchor (0.5, 0.5)
    zoom 1.0
    alpha 0.95

transform vr_ui_hover:
    on hover:
        easein 0.2 zoom 1.05
    on idle:
        easeout 0.2 zoom 1.0

# Basic SteamVR button style
init python:
    if not renpy.variant("mobile"):
        style.steamvr_button = Style(style.button)
        style.steamvr_button.minimum = (200, 60)
        style.steamvr_button.padding = (20, 15, 20, 15)
        style.steamvr_button.background = "#001a4d"
        style.steamvr_button.hover_background = "#003d99"

        # Create button text style
        style.steamvr_button_text = Style(style.button_text)
        style.steamvr_button_text.color = "#00ccff"
        style.steamvr_button_text.hover_color = "#ffffff"
