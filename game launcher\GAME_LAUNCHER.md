# Universal Game Launcher System

## Overview
The Universal Game Launcher is a comprehensive system that opens before your main game and automatically detects all separate games in your project. Each game in the "separate games" folder becomes a selectable option in the launcher interface.

## 🚀 **Features**

### **Automatic Game Detection**
- **Folder Scanning**: Automatically scans "separate games" folder
- **Game Information Extraction**: Reads game details from .rpy file comments
- **Dynamic Loading**: Updates game list without manual configuration
- **Validation**: Ensures games are properly formatted and launchable

### **Professional Interface**
- **Modern UI**: Clean, dark-themed interface with game selection
- **Game Details**: Shows name, description, version, and author
- **Launch Controls**: Easy one-click game launching
- **Status Monitoring**: Real-time status updates and error handling

### **Anti-Cheat Integration**
- **Security Checks**: Verifies anti-cheat system before launching
- **Developer Recognition**: Automatically detects developer computers
- **Protection Status**: Shows security system status
- **Violation Monitoring**: Integrates with main anti-cheat system

### **Separate Game Support**
- **Independent Games**: Each game runs independently
- **Shared Security**: All games protected by main anti-cheat system
- **Individual Save Files**: Each game has its own save system
- **Return to Launcher**: Easy navigation back to game selection

## 📁 **File Structure**

```
Project Root/
├── game_launcher.py              # Main launcher application
├── Launch Games.bat              # Windows launcher shortcut
├── game/
│   └── launcher_integration.rpy  # Ren'Py integration
└── separate games/
    ├── Demo Game/
    │   └── DemoGame.rpy          # Sample separate game
    ├── Your Game 1/
    │   └── YourGame1.rpy
    └── Your Game 2/
        └── YourGame2.rpy
```

## 🎮 **How to Use**

### **Starting the Launcher**
1. **Double-click** `Launch Games.bat` to start the launcher
2. **Or run** `python game_launcher.py` from command line
3. **Launcher opens** showing all available games

### **Selecting and Playing Games**
1. **Browse games** in the left panel
2. **View details** in the right panel
3. **Click "Launch Game"** to start selected game
4. **Game opens** in Ren'Py with full features

### **Creating Separate Games**
1. **Create folder** in "separate games" directory
2. **Create .rpy file** with your game content
3. **Add game information** in comments at the top
4. **Launcher automatically detects** the new game

## 🔧 **Creating Separate Games**

### **Game Information Format**
Add these comments at the top of your .rpy file:
```python
# title: Your Amazing Game
# description: A fantastic adventure with multiple endings and exciting gameplay.
# version: 1.2
# author: Your Name
```

### **Basic Game Structure**
```python
# Game Information Comments (as above)

# Character definitions
define narrator = Character(None)
define protagonist = Character("Hero")

# Game variables
default game_progress = 0

# Start label (required)
label start:
    "Welcome to your separate game!"
    "This game runs independently but shares the launcher system."
    
    menu:
        "What would you like to do?"
        
        "Start adventure":
            jump adventure_begin
        
        "Return to launcher" if hasattr(renpy.store, 'return_to_launcher'):
            call expression "return_to_launcher"
    
    return

label adventure_begin:
    "Your adventure begins here!"
    # Add your game content
    return
```

### **Advanced Features**
```python
# Access launcher information
if hasattr(renpy.store, 'launcher_state'):
    if launcher_state.get('launcher_detected', False):
        "This game was launched from the Universal Launcher!"

# Return to launcher function
if hasattr(renpy.store, 'return_to_launcher'):
    # Add menu option to return to launcher
    textbutton "Return to Launcher" action Function(return_to_launcher)
```

## ⚙️ **Launcher Configuration**

### **Game Detection Settings**
The launcher automatically detects games based on:
- **File Extension**: Looks for .rpy files
- **Folder Structure**: Scans "separate games" folder
- **Game Information**: Reads comments for details
- **Validation**: Ensures games are properly formatted

### **Launch Settings**
- **Auto-close**: Launcher closes after starting a game (configurable)
- **Anti-cheat Check**: Verifies security before launching
- **Developer Mode**: Additional options for developers
- **Error Handling**: Graceful handling of launch failures

## 🛡️ **Security Integration**

### **Anti-Cheat Protection**
- **System Verification**: Checks anti-cheat files before launching
- **Developer Authentication**: Recognizes developer computers
- **Violation Monitoring**: Integrates with main security system
- **Protection Status**: Shows security system health

### **Separate Game Security**
- **Reduced Strictness**: Separate games have relaxed anti-cheat settings
- **Shared Protection**: All games protected by main security system
- **Violation Reporting**: Security violations reported to main system
- **Developer Override**: Developers can bypass restrictions

## 🔍 **Troubleshooting**

### **Common Issues**

**"No games detected"**
- Check that "separate games" folder exists
- Ensure .rpy files are in individual folders
- Verify game information comments are properly formatted
- Click "Refresh Games" button

**"Game won't launch"**
- Check that Python is installed and in PATH
- Verify Ren'Py is properly installed
- Check game .rpy file for syntax errors
- Review launcher console output for errors

**"Anti-cheat warnings"**
- Ensure anti-cheat files are present
- Run developer setup if you're the developer
- Check that security systems are properly configured
- Contact support if issues persist

**"Launcher crashes"**
- Check Python version (requires 3.7+)
- Verify tkinter is available
- Check file permissions
- Run from command line to see error messages

### **Developer Tools**

**Debug Mode**
- Run with `python game_launcher.py --debug` for verbose output
- Check console for detailed error messages
- Use developer panel for advanced diagnostics

**Manual Game Addition**
- Games are detected automatically
- No manual configuration required
- Refresh button updates game list
- Check folder structure if games don't appear

## 📊 **Technical Details**

### **Game Detection Algorithm**
1. **Scan Folder**: Recursively scan "separate games" folder
2. **Find .rpy Files**: Locate all Ren'Py script files
3. **Extract Info**: Parse comments for game information
4. **Validate**: Ensure games are properly formatted
5. **Display**: Add valid games to launcher interface

### **Launch Process**
1. **Security Check**: Verify anti-cheat system status
2. **Game Validation**: Ensure selected game is launchable
3. **Config Creation**: Generate temporary configuration
4. **Process Launch**: Start Ren'Py with game script
5. **Monitoring**: Track launch success/failure

### **Integration Features**
- **Launcher Detection**: Games can detect if launched from launcher
- **Return Navigation**: Easy return to launcher from games
- **Shared Settings**: Preferences shared between games
- **Security Integration**: Anti-cheat protection for all games

## 🎯 **Best Practices**

### **For Game Creators**
- **Clear Information**: Provide detailed game descriptions
- **Proper Structure**: Follow recommended folder organization
- **Error Handling**: Include proper error handling in games
- **Return Option**: Add return to launcher functionality

### **For Players**
- **Use Launcher**: Always start games through the launcher
- **Check Status**: Monitor security system status
- **Report Issues**: Contact support for any problems
- **Keep Updated**: Ensure launcher and games are current

### **For Developers**
- **Test Thoroughly**: Test all games through launcher
- **Monitor Logs**: Check launcher and game logs regularly
- **Security First**: Ensure anti-cheat integration works properly
- **User Experience**: Focus on smooth, intuitive operation

---

**The Universal Game Launcher provides a professional, secure way to manage and play multiple games within your visual novel project. It automatically detects new games, integrates with your security systems, and provides a seamless user experience.**
