# Label Namespace Resolver
# This system resolves label conflicts between integrated games and separate game files
# without requiring deletion of any files

init -5 python:
    # Namespace mapping for label resolution
    label_namespaces = {
        # Integrated versions (priority)
        "integrated": {
            "BearWithUs_1": "game/bearwithus_game.rpy",
            "Lumetric_1": "game/lumetric_game.rpy",
            "Netcode_the_protogen_bedroom_1": "game/netcode_complete.rpy"
        },

        # Separate game versions (fallback)
        "separate": {
            "BearWithUs_1": "game/separate games/BearWithUs/BearWithUs.rpy",
            "Lumetric_1": "game/separate games/Lumrtric/Lumrtric bedroom 1.rpy",
            "Netcode_the_protogen_bedroom_1": "game/separate games/netcode/netcode bedroom 1.rpy"
        }
    }

    # Conflicting labels that need redirection
    conflicting_labels = {
        # Labels that exist in multiple separate game files
        "whisker_and_bean_scene": {
            "lumetric_version": "game/separate games/Lumrtric/lumetric_game.rpy",
            "netcode_version": "game/separate games/netcode/netcode_complete.rpy",
            "default_route": "lumetric_version"  # Which version to use by default
        },
        "howling_hearth_scene": {
            "lumetric_version": "game/separate games/Lumrtric/lumetric_game.rpy",
            "netcode_version": "game/separate games/netcode/netcode_complete.rpy",
            "default_route": "lumetric_version"
        },
        "binary_bistro_scene": {
            "lumetric_version": "game/separate games/Lumrtric/lumetric_game.rpy",
            "netcode_version": "game/separate games/netcode/netcode_complete.rpy",
            "default_route": "lumetric_version"
        },
        "act_4_conclusion": {
            "lumetric_version": "game/separate games/Lumrtric/lumetric_game.rpy",
            "netcode_version": "game/separate games/netcode/netcode_complete.rpy",
            "default_route": "lumetric_version"
        }
    }
    
    # Label priority system - which version to use when conflicts occur
    label_priority = {
        "BearWithUs_1": "integrated",  # Use integrated version
        "Lumetric_1": "integrated",    # Use integrated version
        "Netcode_the_protogen_bedroom_1": "integrated",  # Use integrated version
        "demo_game_start": "separate"  # Use separate version for demo
    }
    
    # Function to resolve label conflicts
    def resolve_label_namespace(label_name):
        """
        Resolve which version of a label to use when conflicts exist
        Returns the preferred namespace for the label
        """
        if label_name in label_priority:
            return label_priority[label_name]
        return "integrated"  # Default to integrated versions
    
    # Function to get label source file
    def get_label_source(label_name, namespace="auto"):
        """
        Get the source file for a label in a specific namespace
        """
        if namespace == "auto":
            namespace = resolve_label_namespace(label_name)
        
        if namespace in label_namespaces and label_name in label_namespaces[namespace]:
            return label_namespaces[namespace][label_name]
        
        return None
    
    # Function to check if label exists in namespace
    def label_exists_in_namespace(label_name, namespace):
        """
        Check if a label exists in a specific namespace
        """
        return namespace in label_namespaces and label_name in label_namespaces[namespace]

# Label conflict warning system
init python:
    conflict_warnings = []
    
    def register_label_conflict(label_name, file1, file2):
        """Register a label conflict for tracking"""
        conflict_info = {
            "label": label_name,
            "files": [file1, file2],
            "resolution": resolve_label_namespace(label_name),
            "timestamp": renpy.get_game_runtime()
        }
        conflict_warnings.append(conflict_info)
    
    def get_conflict_warnings():
        """Get all registered label conflicts"""
        return conflict_warnings

# Safe label caller that respects namespaces
init python:
    def safe_call_label(label_name, namespace="auto", fallback=True):
        """
        Safely call a label with namespace resolution
        """
        try:
            # Resolve namespace if auto
            if namespace == "auto":
                namespace = resolve_label_namespace(label_name)
            
            # Try to call the label
            if renpy.has_label(label_name):
                return label_name
            
            # If fallback is enabled, try other namespaces
            if fallback:
                for ns in ["integrated", "separate"]:
                    if ns != namespace and label_exists_in_namespace(label_name, ns):
                        return label_name
            
            return None
            
        except Exception as e:
            renpy.log(f"Error calling label {label_name}: {str(e)}")
            return None

# Namespace-aware game launcher
label namespace_game_launcher:
    "Namespace-Aware Game Launcher"
    "This launcher respects label namespaces to prevent conflicts."
    ""
    
    menu:
        "Launcher Options:"

        "View Namespace Status":
            jump display_namespace_status

        "Return to Main Menu":
            return

# Display namespace resolution status
label display_namespace_status:
    "Label Namespace Resolution Status"
    ""
    
    python:
        conflicts = get_conflict_warnings()
        total_labels = len(label_priority)
    
    "Configured label priorities: [total_labels]"
    "Detected conflicts: [len(conflicts)]"
    ""
    
    "Label Priority Configuration:"
    python:
        for label_name, namespace in label_priority.items():
            source_file = get_label_source(label_name, namespace)
            if source_file:
                renpy.say(None, f"• {label_name} → {namespace} ({source_file})")
            else:
                renpy.say(None, f"• {label_name} → {namespace} (file not found)")
    
    if len(conflicts) > 0:
        ""
        "Recent Conflicts:"
        python:
            for conflict in conflicts[-5:]:  # Show last 5 conflicts
                files_str = " vs ".join(conflict["files"])
                renpy.say(None, f"• {conflict['label']}: {files_str} → resolved to {conflict['resolution']}")
    
    ""
    "The namespace resolver ensures that the correct version of each game is launched"
    "while preserving all separate game files without deletion."
    
    return

# Integration point for existing systems
label enhanced_game_selection_safe_with_namespaces:
    menu:
        "Enhanced Game Selection with Namespace Support"
        
        "Launch games with namespace resolution":
            jump namespace_game_launcher
        
        "Launch games with universal routing":
            jump safe_game_launcher
        
        "Launch integrated games directly":
            jump enhanced_game_selection_safe
        
        "View system status":
            menu:
                "System Status Options"
                
                "View namespace status":
                    jump display_namespace_status
                
                "View routing status":
                    jump display_routing_status
                
                "View integration status":
                    jump display_integration_status
                
                "Back":
                    jump enhanced_game_selection_safe_with_namespaces
        
        "Return to main menu":
            return

# Auto-resolver for the main game selection
init python:
    def auto_resolve_game_selection():
        """
        Automatically resolve the best game selection method based on available files
        """
        # Check if integrated versions exist
        integrated_available = 0
        for label_name in ["BearWithUs_1", "Lumetric_1", "Netcode_the_protogen_bedroom_1"]:
            if renpy.has_label(label_name):
                integrated_available += 1
        
        # Check if separate versions exist  
        separate_available = 0
        for game_key in game_routes.keys():
            if check_game_exists(game_key):
                separate_available += 1
        
        return {
            "integrated_count": integrated_available,
            "separate_count": separate_available,
            "recommended_launcher": "namespace" if integrated_available > 0 else "routing"
        }


