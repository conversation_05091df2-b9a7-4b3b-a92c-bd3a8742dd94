# Adventures of Atlyss Visual Novel
# Based on the Steam game Atlyss - Official character dating simulation
# Using official character names from Atlyss Wikipedia page

# Main characters
define narrator = Character(None, color="#ffffff")
define player = Character("[povname]", color="#4a9eff")

# Main Dateable Characters from Atlyss Wikipedia
define sally = Character("<PERSON>", color="#ff6b9d")  # Bandit Guardian
define angela = Character("Angela Flux", color="#c77dff")  # Mystic Guardian
define enok = Character("Enok", color="#7209b7")  # Fighter Guardian
define vivian = Character("<PERSON>", color="#f72585")
define frankie = Character("<PERSON>", color="#4cc9f0")
define craig = Character("<PERSON>", color="#ffd60a")

# Other Dateable Characters
define imp = Character("Imp", color="#ff9500")
define poon = Character("Poon", color="#00b4d8")
define spike = Character("Spike", color="#90e0ef")
define skrit = Character("Skrit", color="#caf0f8")

# Dateable Enemies/Bosses
define slime_diva = Character("Slime Diva", color="#06ffa5")
define lord_zuulneruda = Character("<PERSON> Zuul<PERSON>uda", color="#8b0000")
define lord_kaluuz = Character("Lord Kaluuz", color="#4a0080")
define colossus = Character("Colossus", color="#654321")
define alshar = Character("Alshar", color="#ff4500")
define stezza = Character("Stezza", color="#32cd32")

# Additional Dateable Creatures
define wisp = Character("Wisp", color="#e0e0e0")
define geist = Character("Geist", color="#800080")
define carbuncle = Character("Carbuncle", color="#ffd700")
define monolith = Character("Monolith", color="#2f4f4f")

# Game variables (povname is already defined globally)
default atlyss_chosen_love_interests = []  # List to support multiple relationships
default atlyss_relationship_points = {
    "sally": 0, "angela": 0, "enok": 0, "vivian": 0, "frankie": 0, "craig": 0,
    "imp": 0, "poon": 0, "spike": 0, "skrit": 0,
    "slime_diva": 0, "lord_zuulneruda": 0, "lord_kaluuz": 0, "colossus": 0,
    "alshar": 0, "stezza": 0, "wisp": 0, "geist": 0, "carbuncle": 0, "monolith": 0
}
default atlyss_current_chapter = 1
default atlyss_player_class = None

# Main story label
label adventures_of_atlyss_start:

    # Record game selection for anti-cheat
    $ safe_record_user_action("game_start", "adventures_of_atlyss")

    # Character name input
    $ povname = renpy.input("What is your adventurer name?", length=32)
    $ povname = povname.strip()
    if povname == "":
        $ povname = "Adventurer"

    # Set the scene - The world of Atlyss
    scene bg fantasy_town
    with fade

    # Opening narration based on Atlyss Steam game lore
    narrator "Welcome to the mystical world of Atlyss, where ancient magic flows through every corner of the realm."
    narrator "You are a new adventurer who has just arrived in the central hub town, ready to explore dungeons and meet fascinating beings."
    narrator "In this world, friendships and even romance can bloom in the most unexpected places..."
    narrator "Even with creatures that others might consider enemies."

    # Show the town
    scene bg guild_hall
    with dissolve

    narrator "Your first stop is the Adventurer's Guild, where all great journeys begin."
    narrator "As you enter, you notice the hall is filled with an incredible variety of beings."
    narrator "Guardians, creatures, and even reformed enemies gather here, all seeking adventure and companionship."

    # Class selection
    menu:
        "What type of adventurer are you?"

        "Warrior - Strong and brave in battle":
            $ player_class = "warrior"
            narrator "You've chosen the path of the warrior, relying on strength and courage."

        "Mage - Master of arcane arts":
            $ player_class = "mage"
            narrator "You've chosen the path of magic, wielding mystical powers."

        "Rogue - Swift and cunning":
            $ player_class = "rogue"
            narrator "You've chosen the path of stealth and agility."

        "Guardian - Protector of others":
            $ player_class = "guardian"
            narrator "You've chosen the path of protection, defending those in need."

    narrator "As you settle into the guild hall, you notice several interesting individuals who seem drawn to your presence."
    narrator "Each one represents a different aspect of the Atlyss world - from noble guardians to mysterious creatures."

    # Character introductions - Main Characters
    narrator "Let me introduce you to some of the most notable beings you could get to know..."

    # Sally introduction
    show sally
    with dissolve

    sally "Hey there, newcomer! I'm Sally, the Bandit Guardian around these parts."
    sally "Don't let the 'bandit' part fool you - I protect travelers from the real troublemakers."
    sally "You look like you could use someone to watch your back out there, [povname]."

    if player_class == "guardian":
        sally "Another guardian! We could work together to keep everyone safe."
        $ atlyss_relationship_points["sally"] += 2
    else:
        sally "A [player_class]? Perfect! I could use someone with your skills on my team."
        $ atlyss_relationship_points["sally"] += 1

    # Angela introduction
    hide sally
    show angela
    with dissolve

    angela "Greetings, [povname]. I am Angela Flux, Mystic Guardian of the ancient arts."
    angela "I sense great potential within you... the mystical energies around you are quite fascinating."
    angela "Perhaps we could explore the deeper mysteries of Atlyss together?"

    if player_class == "mage":
        angela "A fellow practitioner of magic! How wonderful. We could share knowledge."
        $ atlyss_relationship_points["angela"] += 2
    else:
        angela "Your [player_class] abilities would complement my mystical powers beautifully."
        $ atlyss_relationship_points["angela"] += 1

    # Enok introduction
    hide angela
    show enok
    with dissolve

    enok "Welcome, [povname]! I'm Enok, Fighter Guardian of this realm."
    enok "I've dedicated my life to protecting others and honing my combat skills."
    enok "If you're looking for someone reliable to have your back, I'm your guardian."

    if player_class == "warrior":
        enok "A warrior! Excellent. We could train together and become even stronger."
        $ atlyss_relationship_points["enok"] += 2
    else:
        enok "A [player_class]? Great! Every good fighter needs diverse allies."
        $ atlyss_relationship_points["enok"] += 1

    # Vivian introduction
    hide enok
    show vivian
    with dissolve

    vivian "Oh, hello there! I'm Vivian, and I'm always excited to meet new adventurers!"
    vivian "This world can be dangerous, but it's also full of wonder and beauty."
    vivian "Would you like me to show you some of the hidden treasures around here?"

    player "I'd love to explore with someone who knows the area."

    vivian "Wonderful! I know all the best spots for both adventure and... quiet moments together."
    $ atlyss_relationship_points["vivian"] += 1

    # Frankie introduction
    hide vivian
    show frankie
    with dissolve

    frankie "Hey [povname]! I'm Frankie, and I've been exploring Atlyss for years."
    frankie "I've seen things that would amaze you - ancient ruins, magical phenomena, incredible creatures."
    frankie "Some of those creatures aren't as scary as people think, you know."

    player "Really? I'd love to hear more about that."

    frankie "Stick with me, and I'll show you that even the most fearsome beings can have gentle hearts."
    $ atlyss_relationship_points["frankie"] += 1

    # Craig introduction
    hide frankie
    show craig
    with dissolve

    craig "Greetings, [povname]. I'm Craig, a scholar of Atlyss lore and history."
    craig "I study the relationships between all beings in this realm - guardians, creatures, even the so-called 'enemies'."
    craig "Did you know that many conflicts arise from simple misunderstandings?"

    player "That's a fascinating perspective."

    craig "Indeed! Perhaps you'd like to join me in my research? I could use a field partner."
    $ atlyss_relationship_points["craig"] += 1

    narrator "As you finish meeting the main guardians and adventurers, you notice some... unusual individuals approaching."
    narrator "These beings are typically considered enemies or monsters, but here in the guild hall, they seem different."
    narrator "More... approachable. Even friendly."

    # Slime Diva introduction
    hide craig
    show slime_diva
    with dissolve

    slime_diva "Oh my, a new adventurer! I'm the Slime Diva, darling!"
    slime_diva "Don't be frightened by my reputation - I'm actually quite charming when I'm not defending my territory."
    slime_diva "I do so love meeting cultured individuals who appreciate the finer things in life."

    player "You're... not what I expected from the stories."

    slime_diva "Stories, dear? Oh, those dreadful tales of me being some sort of monster!"
    slime_diva "I'm simply a being who values elegance and sophistication. Perhaps you'd like to see my collection of rare gems?"
    $ atlyss_relationship_points["slime_diva"] += 1

    # Lord Zuulneruda introduction
    hide slime_diva
    show lord_zuulneruda
    with dissolve

    lord_zuulneruda "Greetings, mortal. I am Lord Zuulneruda, ruler of the shadow realm."
    lord_zuulneruda "Before you flee in terror, know that I am here under a truce with the guild."
    lord_zuulneruda "I find myself... curious about mortal companionship."

    player "That's... unexpectedly honest of you."

    lord_zuulneruda "Power without understanding is meaningless. Perhaps you could teach me about mortal emotions?"
    lord_zuulneruda "In return, I could show you the beauty that exists even in darkness."
    $ atlyss_relationship_points["lord_zuulneruda"] += 1

    # Lord Kaluuz introduction
    hide lord_zuulneruda
    show lord_kaluuz
    with dissolve

    lord_kaluuz "I am Lord Kaluuz, and I sense great potential in you, [povname]."
    lord_kaluuz "Many fear me, but I seek only to understand the bonds that connect all beings."
    lord_kaluuz "Would you be willing to look beyond my fearsome appearance?"

    player "I believe everyone deserves a chance to be understood."

    lord_kaluuz "Wise words. Perhaps together we could bridge the gap between our worlds."
    $ atlyss_relationship_points["lord_kaluuz"] += 1

    # Colossus introduction
    hide lord_kaluuz
    show colossus
    with dissolve

    colossus "I... am Colossus. I speak... slowly... but my heart... is warm."
    colossus "Many run... from my size... but I only wish... for friendship."
    colossus "You seem... kind, [povname]. Will you... be my friend?"

    player "Of course! Size doesn't matter when it comes to friendship."

    colossus "Thank you... that means... everything to me."
    $ atlyss_relationship_points["colossus"] += 2

    narrator "After meeting this incredible variety of beings, you realize that Atlyss is truly a world where anyone can find love and companionship."
    narrator "Now comes the moment of choice - who has captured your interest the most?"

    # Multiple character selection system
    narrator "Now comes the exciting part - in Atlyss, you can pursue relationships with multiple characters!"
    narrator "Choose who you'd like to get to know better. You can select multiple people for different types of relationships."

    jump atlyss_character_selection_hub

# Character selection hub that allows multiple choices
label atlyss_character_selection_hub:

    menu:
        "Who would you like to spend time with? (You can choose multiple people)"

        "Sally - The protective Bandit Guardian" if "sally" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("sally")
            $ atlyss_relationship_points["sally"] += 3
            "You've chosen to pursue a relationship with Sally!"
            jump sally_date_scene

        "Angela Flux - The mystical Mystic Guardian" if "angela" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("angela")
            $ atlyss_relationship_points["angela"] += 3
            "You've chosen to pursue a relationship with Angela!"
            jump angela_date_scene

        "Enok - The strong Fighter Guardian" if "enok" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("enok")
            $ atlyss_relationship_points["enok"] += 3
            "You've chosen to pursue a relationship with Enok!"
            jump enok_date_scene

        "Vivian - The cheerful adventurer" if "vivian" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("vivian")
            $ atlyss_relationship_points["vivian"] += 3
            "You've chosen to pursue a relationship with Vivian!"
            jump vivian_date_scene

        "Frankie - The experienced explorer" if "frankie" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("frankie")
            $ atlyss_relationship_points["frankie"] += 3
            "You've chosen to pursue a relationship with Frankie!"
            jump frankie_date_scene

        "Craig - The knowledgeable scholar" if "craig" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("craig")
            $ atlyss_relationship_points["craig"] += 3
            "You've chosen to pursue a relationship with Craig!"
            jump craig_date_scene

        "Slime Diva - The elegant and sophisticated" if "slime_diva" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("slime_diva")
            $ atlyss_relationship_points["slime_diva"] += 3
            "You've chosen to pursue a relationship with Slime Diva!"
            jump slime_diva_date_scene

        "Lord Zuulneruda - The mysterious shadow lord" if "lord_zuulneruda" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("lord_zuulneruda")
            $ atlyss_relationship_points["lord_zuulneruda"] += 3
            "You've chosen to pursue a relationship with Lord Zuulneruda!"
            jump zuulneruda_date_scene

        "Lord Kaluuz - The understanding dark lord" if "lord_kaluuz" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("lord_kaluuz")
            $ atlyss_relationship_points["lord_kaluuz"] += 3
            "You've chosen to pursue a relationship with Lord Kaluuz!"
            jump kaluuz_date_scene

        "Colossus - The gentle giant" if "colossus" not in atlyss_chosen_love_interests:
            $ atlyss_chosen_love_interests.append("colossus")
            $ atlyss_relationship_points["colossus"] += 3
            "You've chosen to pursue a relationship with Colossus!"
            jump colossus_date_scene

        "Continue with current relationships" if len(atlyss_chosen_love_interests) > 0:
            jump atlyss_multiple_relationships_scene

        "I want to get to know everyone better first":
            jump atlyss_group_scene

# Dating scenes for each character
label sally_date_scene:
    scene bg bandit_camp
    with fade

    narrator "Sally leads you to her hidden camp in the forest, where she keeps watch over the trade routes."

    show sally
    with dissolve

    sally "Welcome to my base of operations, [povname]. This is where I keep the roads safe."
    sally "I know it's not fancy, but it's honest work protecting travelers from real bandits."
    sally "Want to help me patrol the area? I could use a reliable partner."

    menu:
        "How do you respond?"

        "I'd be honored to patrol with you":
            $ atlyss_relationship_points["sally"] += 2
            sally "That's what I like to hear! Someone who's not afraid of real work."
            sally "Together, we could make these roads the safest in all of Atlyss."

        "Your dedication is admirable":
            $ atlyss_relationship_points["sally"] += 1
            sally "Thanks, [povname]. It means a lot to have someone who understands."
            sally "Most people just see 'bandit' and assume the worst."

    sally "You know, [povname], I've been protecting others for so long, I forgot what it felt like to have someone want to protect me too."
    sally "Maybe... maybe we could watch each other's backs? In adventure and in life?"

    narrator "You've formed a strong connection with Sally!"

    menu:
        "What would you like to do next?"

        "Pursue relationships with other characters too":
            jump atlyss_character_selection_hub

        "Focus on this relationship for now":
            jump romance_conclusion

        "Continue your adventures together":
            jump atlyss_multiple_relationships_scene

label slime_diva_date_scene:
    scene bg crystal_cavern
    with fade

    narrator "The Slime Diva brings you to her magnificent crystal cavern, filled with sparkling gems and elegant decorations."

    show slime_diva
    with dissolve

    slime_diva "Welcome to my private collection, darling! Isn't it simply divine?"
    slime_diva "Each crystal here tells a story of beauty and refinement."
    slime_diva "I do so enjoy sharing beautiful things with those who can appreciate them."

    menu:
        "How do you respond?"

        "This collection is absolutely stunning":
            $ atlyss_relationship_points["slime_diva"] += 2
            slime_diva "Oh, you have such exquisite taste! I knew you were special the moment I saw you."
            slime_diva "Perhaps you'd like to help me find more treasures? We could travel the realm together!"

        "You have a wonderful eye for beauty":
            $ atlyss_relationship_points["slime_diva"] += 1
            slime_diva "Why thank you, dear! Beauty recognizes beauty, after all."
            slime_diva "I can see we're going to get along splendidly."

    slime_diva "You know, [povname], most people only see me as a monster to be defeated."
    slime_diva "But you... you see the real me. The me that loves beautiful things and elegant company."
    slime_diva "Would you like to be my partner in both treasure hunting and... romance?"

    narrator "You've formed a beautiful connection with Slime Diva!"

    menu:
        "What would you like to do next?"

        "Explore relationships with other characters as well":
            jump atlyss_character_selection_hub

        "Focus on this elegant relationship":
            jump romance_conclusion

        "Continue building your relationships":
            jump atlyss_multiple_relationships_scene

label colossus_date_scene:
    scene bg ancient_ruins
    with fade

    narrator "Colossus takes you to ancient ruins where he likes to sit and watch the sunset."

    show colossus
    with dissolve

    colossus "This is... my favorite place... [povname]. Very peaceful... here."
    colossus "I come here... when others... are afraid of me."
    colossus "But you... are not afraid. That makes... my heart happy."

    menu:
        "How do you respond?"

        "I could never be afraid of someone with such a kind heart":
            $ atlyss_relationship_points["colossus"] += 2
            colossus "You... understand me. That is... very rare... and precious."
            colossus "I will... protect you... always."

        "This place is beautiful, just like your soul":
            $ atlyss_relationship_points["colossus"] += 1
            colossus "You see... beauty in me? No one... has ever... said that before."
            colossus "Thank you... [povname]."

    colossus "I may be... big and scary... to others. But with you... I feel... gentle and loved."
    colossus "Will you... stay with me? Forever?"

    narrator "You've formed a deep, gentle connection with Colossus!"

    menu:
        "What would you like to do next?"

        "Meet other potential companions too":
            jump atlyss_character_selection_hub

        "Focus on this tender relationship":
            jump romance_conclusion

        "Continue developing your relationships":
            jump atlyss_multiple_relationships_scene

# Romance conclusion
label romance_conclusion:
    scene bg guild_hall
    with fade

    narrator "As your special evening comes to an end, you return to the guild hall with your chosen companion."
    narrator "You've discovered that love truly can bloom anywhere in Atlyss - even with those others might fear."

    if "sally" in atlyss_chosen_love_interests:
        show sally
        sally "This has been the best night of my life, [povname]. Ready for a lifetime of adventures together?"

    if "slime_diva" in atlyss_chosen_love_interests:
        show slime_diva
        slime_diva "Darling, you've made this old diva feel young again! Shall we explore the world's beauty together?"

    if "colossus" in atlyss_chosen_love_interests:
        show colossus
        colossus "Thank you... for seeing... the real me, [povname]. I love... you."

    # Add more character conclusions as needed
    else:
        narrator "Your chosen companion looks at you with love and affection."
        narrator "This is the beginning of a beautiful relationship in the world of Atlyss."

    narrator "Your love story in Atlyss has only just begun."
    narrator "Many more adventures await you and your beloved companion."

    # Record completion for anti-cheat system
    $ safe_record_user_action("scene_complete", "atlyss_romance_intro")

    menu:
        "What would you like to do?"

        "Continue the adventure":
            jump atlyss_chapter_2

        "Play another game":
            jump enhanced_game_selection_safe

        "Return to main menu":
            return

# Chapter 2 - Future adventures placeholder
label atlyss_chapter_2:
    scene bg fantasy_town
    with fade

    narrator "Your adventures in Atlyss continue with your chosen companions by your side..."
    narrator "The world is full of mysteries to explore and love to discover."

    "More romantic adventures coming soon..."

    return

# Multiple relationships scene
label atlyss_multiple_relationships_scene:
    scene bg guild_hall
    with fade

    narrator "You've formed connections with multiple companions in Atlyss. Each relationship offers unique experiences and perspectives."

    python:
        relationship_status = []
        for character in atlyss_chosen_love_interests:
            points = atlyss_relationship_points[character]
            relationship_status.append(f"{character.title()}: {points} relationship points")

    narrator "Your current relationships:"

    python:
        for status in relationship_status:
            renpy.say(narrator, status)

    menu:
        "What would you like to do?"

        "Spend time with a specific companion":
            jump atlyss_choose_companion_scene

        "Organize a group activity":
            jump atlyss_group_activity_scene

        "Continue your adventures":
            jump atlyss_chapter_2

        "Return to character selection":
            jump atlyss_character_selection_hub

# Choose specific companion for individual time
label atlyss_choose_companion_scene:

    menu:
        "Who would you like to spend individual time with?"

        "Sally" if "sally" in atlyss_chosen_love_interests:
            jump sally_individual_scene

        "Angela" if "angela" in atlyss_chosen_love_interests:
            jump angela_individual_scene

        "Enok" if "enok" in atlyss_chosen_love_interests:
            jump enok_individual_scene

        "Vivian" if "vivian" in atlyss_chosen_love_interests:
            jump vivian_individual_scene

        "Frankie" if "frankie" in atlyss_chosen_love_interests:
            jump frankie_individual_scene

        "Craig" if "craig" in atlyss_chosen_love_interests:
            jump craig_individual_scene

        "Slime Diva" if "slime_diva" in atlyss_chosen_love_interests:
            jump slime_diva_individual_scene

        "Lord Zuulneruda" if "lord_zuulneruda" in atlyss_chosen_love_interests:
            jump zuulneruda_individual_scene

        "Lord Kaluuz" if "lord_kaluuz" in atlyss_chosen_love_interests:
            jump kaluuz_individual_scene

        "Colossus" if "colossus" in atlyss_chosen_love_interests:
            jump colossus_individual_scene

        "Return to relationship hub":
            jump atlyss_multiple_relationships_scene

# Group activity scene
label atlyss_group_activity_scene:
    scene bg adventure_location
    with fade

    narrator "You decide to organize a group adventure with all your companions!"
    narrator "This is a unique opportunity to see how everyone interacts together."

    python:
        present_characters = []
        for character in atlyss_chosen_love_interests:
            present_characters.append(character)

    narrator "Your adventure party consists of:"

    python:
        for character in present_characters:
            renpy.say(narrator, f"- {character.title()}")

    narrator "Together, you embark on a dungeon exploration that strengthens bonds between all party members."
    narrator "Each companion brings their unique skills and personality to the group dynamic."

    python:
        for character in atlyss_chosen_love_interests:
            atlyss_relationship_points[character] += 1

    narrator "Everyone's relationship with you has grown stronger through this shared adventure!"

    menu:
        "What's next?"

        "Plan another group activity":
            jump atlyss_group_activity_scene

        "Spend individual time with someone":
            jump atlyss_choose_companion_scene

        "Return to relationship hub":
            jump atlyss_multiple_relationships_scene

# Group scene for getting to know everyone
label atlyss_group_scene:
    scene bg guild_hall
    with fade

    narrator "You decide to spend time getting to know everyone better before making any romantic commitments."
    narrator "This is a wise approach that allows you to understand each character's personality and interests."

    narrator "You spend time in group conversations, learning about everyone's backgrounds, dreams, and personalities."
    narrator "This helps you make more informed decisions about potential relationships."

    python:
        # Give small relationship boosts to everyone
        for character in atlyss_relationship_points:
            atlyss_relationship_points[character] += 1

    narrator "Everyone appreciates your thoughtful approach to getting to know them!"

    menu:
        "Now that you know everyone better, what would you like to do?"

        "Choose specific people to pursue relationships with":
            jump atlyss_character_selection_hub

        "Continue getting to know everyone as friends":
            jump atlyss_friendship_route

        "Start a polyamorous relationship with multiple people":
            jump atlyss_polyamory_route

# Friendship route
label atlyss_friendship_route:
    narrator "You decide to maintain friendly relationships with everyone rather than pursuing romance."
    narrator "This creates a strong support network of friends who care about you and each other."

    python:
        for character in atlyss_relationship_points:
            atlyss_relationship_points[character] += 2

    narrator "Your friendship bonds with everyone have grown stronger!"
    narrator "Sometimes the best relationships are built on mutual respect and platonic love."

    jump atlyss_chapter_2

# Polyamory route
label atlyss_polyamory_route:
    narrator "You express interest in forming a polyamorous relationship network where everyone is aware and consenting."
    narrator "This is a complex but rewarding relationship style that requires excellent communication and mutual respect."

    narrator "You have honest conversations with everyone about your feelings and desires."
    narrator "Surprisingly, many of them are open to this arrangement, valuing honesty and emotional connection over exclusivity."

    python:
        # Add everyone to the love interests list
        for character in atlyss_relationship_points:
            if character not in atlyss_chosen_love_interests:
                atlyss_chosen_love_interests.append(character)
            atlyss_relationship_points[character] += 3

    narrator "You've formed a loving polyamorous network with all the characters!"
    narrator "This unique relationship dynamic offers diverse experiences and deep emotional connections."

    jump atlyss_multiple_relationships_scene

# Individual scene labels for multiple relationship system
label sally_individual_scene:
    narrator "You spend quality individual time with Sally, strengthening your bond."
    $ atlyss_relationship_points["sally"] += 2
    jump atlyss_multiple_relationships_scene

label angela_individual_scene:
    narrator "You have a mystical conversation with Angela about magic and the deeper mysteries of Atlyss."
    $ atlyss_relationship_points["angela"] += 2
    jump atlyss_multiple_relationships_scene

label enok_individual_scene:
    narrator "You train together with Enok, building both physical strength and emotional connection."
    $ atlyss_relationship_points["enok"] += 2
    jump atlyss_multiple_relationships_scene

label vivian_individual_scene:
    narrator "You explore hidden locations with Vivian, discovering both treasures and deeper feelings."
    $ atlyss_relationship_points["vivian"] += 2
    jump atlyss_multiple_relationships_scene

label frankie_individual_scene:
    narrator "You go on an adventure with Frankie, learning about the gentler side of dangerous creatures."
    $ atlyss_relationship_points["frankie"] += 2
    jump atlyss_multiple_relationships_scene

label craig_individual_scene:
    narrator "You engage in scholarly discussions with Craig about the lore and history of Atlyss."
    $ atlyss_relationship_points["craig"] += 2
    jump atlyss_multiple_relationships_scene

label slime_diva_individual_scene:
    narrator "You spend elegant time with Slime Diva, appreciating art and beauty together."
    $ atlyss_relationship_points["slime_diva"] += 2
    jump atlyss_multiple_relationships_scene

label zuulneruda_individual_scene:
    narrator "You explore the mysteries of shadow magic with Lord Zuulneruda."
    $ atlyss_relationship_points["lord_zuulneruda"] += 2
    jump atlyss_multiple_relationships_scene

label kaluuz_individual_scene:
    narrator "You have deep philosophical conversations with Lord Kaluuz about understanding and acceptance."
    $ atlyss_relationship_points["lord_kaluuz"] += 2
    jump atlyss_multiple_relationships_scene

label colossus_individual_scene:
    narrator "You spend peaceful, gentle time with Colossus, enjoying quiet moments together."
    $ atlyss_relationship_points["colossus"] += 2
    jump atlyss_multiple_relationships_scene

# Original dating scenes that lead to relationship selection
label angela_date_scene:
    narrator "You have a magical first date with Angela, exploring mystical arts together."
    $ atlyss_relationship_points["angela"] += 3
    menu:
        "What would you like to do next?"
        "Explore relationships with other characters as well":
            jump atlyss_character_selection_hub
        "Focus on this mystical relationship":
            jump romance_conclusion
        "Continue building your relationships":
            jump atlyss_multiple_relationships_scene

label enok_date_scene:
    narrator "You have an action-packed first date with Enok, training and bonding together."
    $ atlyss_relationship_points["enok"] += 3
    menu:
        "What would you like to do next?"
        "Meet other potential companions too":
            jump atlyss_character_selection_hub
        "Focus on this strong relationship":
            jump romance_conclusion
        "Continue developing your relationships":
            jump atlyss_multiple_relationships_scene

label vivian_date_scene:
    narrator "You have an adventurous first date with Vivian, exploring hidden treasures."
    $ atlyss_relationship_points["vivian"] += 3
    menu:
        "What would you like to do next?"
        "Explore relationships with other characters as well":
            jump atlyss_character_selection_hub
        "Focus on this adventurous relationship":
            jump romance_conclusion
        "Continue building your relationships":
            jump atlyss_multiple_relationships_scene

label frankie_date_scene:
    narrator "You have an enlightening first date with Frankie, learning about misunderstood creatures."
    $ atlyss_relationship_points["frankie"] += 3
    menu:
        "What would you like to do next?"
        "Meet other potential companions too":
            jump atlyss_character_selection_hub
        "Focus on this understanding relationship":
            jump romance_conclusion
        "Continue developing your relationships":
            jump atlyss_multiple_relationships_scene

label craig_date_scene:
    narrator "You have an intellectual first date with Craig, discussing the deep lore of Atlyss."
    $ atlyss_relationship_points["craig"] += 3
    menu:
        "What would you like to do next?"
        "Explore relationships with other characters as well":
            jump atlyss_character_selection_hub
        "Focus on this scholarly relationship":
            jump romance_conclusion
        "Continue building your relationships":
            jump atlyss_multiple_relationships_scene

label zuulneruda_date_scene:
    narrator "You have a mysterious first date with Lord Zuulneruda, exploring shadow magic."
    $ atlyss_relationship_points["lord_zuulneruda"] += 3
    menu:
        "What would you like to do next?"
        "Meet other potential companions too":
            jump atlyss_character_selection_hub
        "Focus on this mysterious relationship":
            jump romance_conclusion
        "Continue developing your relationships":
            jump atlyss_multiple_relationships_scene

label kaluuz_date_scene:
    narrator "You have a profound first date with Lord Kaluuz, discussing understanding and acceptance."
    $ atlyss_relationship_points["lord_kaluuz"] += 3
    menu:
        "What would you like to do next?"
        "Explore relationships with other characters as well":
            jump atlyss_character_selection_hub
        "Focus on this understanding relationship":
            jump romance_conclusion
        "Continue building your relationships":
            jump atlyss_multiple_relationships_scene
    narrator "Welcome to the world of Atlyss, a realm where adventurers seek glory in dangerous dungeons filled with mysterious creatures."
    narrator "You are a new adventurer who has just arrived in the central hub town, ready to begin your journey."
    narrator "The town bustles with fellow adventurers, each with their own stories and dreams of conquest."